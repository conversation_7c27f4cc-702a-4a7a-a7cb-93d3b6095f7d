import{r as e,E as a}from"./element-plus-7917fd46.js";import{l as t,_ as l,r,q as o,y as i,R as n,J as d,av as u,x as p,O as s,z as c,u as m,P as _}from"./vue-vendor-fc5a6493.js";import{_ as b}from"./_plugin-vue_export-helper-1b428a4d.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const f={class:"material-records"},h={class:"card-header"},g={class:"header-actions"},y={class:"search-section"},w={class:"pagination-section"},v=b(t({__name:"MaterialRecords",setup(t){const b=l({materialCode:"",materialName:"",operationType:"",dateRange:[]}),v=l({currentPage:1,pageSize:20,total:0}),C=r([{recordId:"R001",operationDate:"2024-01-15",materialCode:"M001",materialName:"燃气表",specification:"G2.5",unit:"个",operationType:"inbound",quantity:50,unitPrice:300,operator:"张三",remarks:"新采购入库"},{recordId:"R002",operationDate:"2024-01-14",materialCode:"M002",materialName:"镀锌管件",specification:"DN20",unit:"个",operationType:"outbound",quantity:20,unitPrice:25,operator:"李四",remarks:"工程领用"},{recordId:"R003",operationDate:"2024-01-13",materialCode:"M003",materialName:"波纹管",specification:"DN15",unit:"m",operationType:"return",quantity:5,unitPrice:12,operator:"王五",remarks:"工程退库"}]),z=e=>{switch(e){case"inbound":return"success";case"outbound":return"primary";case"return":return"warning";default:return"info"}},P=e=>{switch(e){case"inbound":return"入库";case"outbound":return"出库";case"return":return"退库";default:return"未知"}},V=()=>{a.success("搜索完成")},k=()=>{Object.keys(b).forEach(e=>{b[e]="dateRange"===e?[]:""}),a.info("搜索条件已重置")},D=()=>{a.success("数据已刷新")},M=e=>{v.pageSize=e},N=e=>{v.currentPage=e};return o(()=>{v.total=C.value.length}),(t,l)=>{const r=u("el-breadcrumb-item"),o=u("el-breadcrumb"),R=u("el-icon"),T=u("el-button"),Y=u("el-input"),j=u("el-form-item"),x=u("el-option"),I=u("el-select"),q=u("el-date-picker"),S=u("el-form"),U=u("el-table-column"),E=u("el-tag"),F=u("el-table"),O=u("el-pagination"),A=u("el-card");return p(),i("div",f,[n(o,{class:"breadcrumb",separator:">"},{default:d(()=>[n(r,{to:{path:"/dashboard"}},{default:d(()=>l[6]||(l[6]=[s("首页",-1)])),_:1,__:[6]}),n(r,{to:{path:"/warehouse/material-list"}},{default:d(()=>l[7]||(l[7]=[s("仓库管理",-1)])),_:1,__:[7]}),n(r,null,{default:d(()=>l[8]||(l[8]=[s("甲料记录",-1)])),_:1,__:[8]})]),_:1}),n(A,{class:"main-card"},{header:d(()=>[c("div",h,[l[10]||(l[10]=c("span",null,"📋 甲料记录",-1)),c("div",g,[n(T,{type:"primary",size:"small",onClick:D},{default:d(()=>[n(R,null,{default:d(()=>[n(m(e))]),_:1}),l[9]||(l[9]=s(" 刷新 ",-1))]),_:1,__:[9]})])])]),default:d(()=>[c("div",y,[n(S,{model:b,inline:""},{default:d(()=>[n(j,{label:"物料编码:"},{default:d(()=>[n(Y,{modelValue:b.materialCode,"onUpdate:modelValue":l[0]||(l[0]=e=>b.materialCode=e),placeholder:"请输入物料编码"},null,8,["modelValue"])]),_:1}),n(j,{label:"物料名称:"},{default:d(()=>[n(Y,{modelValue:b.materialName,"onUpdate:modelValue":l[1]||(l[1]=e=>b.materialName=e),placeholder:"请输入物料名称"},null,8,["modelValue"])]),_:1}),n(j,{label:"操作类型:"},{default:d(()=>[n(I,{modelValue:b.operationType,"onUpdate:modelValue":l[2]||(l[2]=e=>b.operationType=e),placeholder:"请选择操作类型"},{default:d(()=>[n(x,{label:"全部",value:""}),n(x,{label:"入库",value:"inbound"}),n(x,{label:"出库",value:"outbound"}),n(x,{label:"退库",value:"return"})]),_:1},8,["modelValue"])]),_:1}),n(j,{label:"日期范围:"},{default:d(()=>[n(q,{modelValue:b.dateRange,"onUpdate:modelValue":l[3]||(l[3]=e=>b.dateRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),n(j,null,{default:d(()=>[n(T,{type:"primary",onClick:V},{default:d(()=>l[11]||(l[11]=[s("搜索",-1)])),_:1,__:[11]}),n(T,{onClick:k},{default:d(()=>l[12]||(l[12]=[s("重置",-1)])),_:1,__:[12]})]),_:1})]),_:1},8,["model"])]),n(F,{data:C.value,border:"",style:{width:"100%"}},{default:d(()=>[n(U,{prop:"recordId",label:"记录ID",width:"100"}),n(U,{prop:"operationDate",label:"操作日期",width:"120"}),n(U,{prop:"materialCode",label:"物料编码",width:"120"}),n(U,{prop:"materialName",label:"物料名称",width:"150"}),n(U,{prop:"specification",label:"规格",width:"100"}),n(U,{prop:"unit",label:"单位",width:"80"}),n(U,{prop:"operationType",label:"操作类型",width:"100"},{default:d(({row:e})=>[n(E,{type:z(e.operationType)},{default:d(()=>[s(_(P(e.operationType)),1)]),_:2},1032,["type"])]),_:1}),n(U,{prop:"quantity",label:"数量",width:"100",align:"center"}),n(U,{prop:"unitPrice",label:"单价",width:"100",align:"center"},{default:d(({row:e})=>[s(" ¥"+_(e.unitPrice.toFixed(2)),1)]),_:1}),n(U,{prop:"totalAmount",label:"总金额",width:"120",align:"center"},{default:d(({row:e})=>[s(" ¥"+_((e.quantity*e.unitPrice).toFixed(2)),1)]),_:1}),n(U,{prop:"operator",label:"操作员",width:"100"}),n(U,{prop:"remarks",label:"备注","min-width":"150"}),n(U,{label:"操作",width:"100",fixed:"right"},{default:d(({row:e})=>[n(T,{type:"primary",size:"small",onClick:t=>(e=>{a.info(`查看记录 ${e.recordId} 的详情`)})(e)},{default:d(()=>l[13]||(l[13]=[s(" 详情 ",-1)])),_:2,__:[13]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),c("div",w,[n(O,{"current-page":v.currentPage,"onUpdate:currentPage":l[4]||(l[4]=e=>v.currentPage=e),"page-size":v.pageSize,"onUpdate:pageSize":l[5]||(l[5]=e=>v.pageSize=e),"page-sizes":[10,20,50,100],total:v.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:M,onCurrentChange:N},null,8,["current-page","page-size","total"])])]),_:1})])}}}),[["__scopeId","data-v-ba1d9089"]]);export{v as default};
