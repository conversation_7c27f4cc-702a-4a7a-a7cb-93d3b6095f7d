<template>
  <div class="order-execute-container">
    <div class="header">
      <h2>订单执行</h2>
      <el-button type="primary" @click="handleSave">保存</el-button>
    </div>

    <!-- 订单详细信息展示 -->
    <el-card class="order-info-card">
      <template #header>
        <div class="card-header">
          <span>订单详细信息</span>
        </div>
      </template>
      
      <div v-if="orderInfo">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <span class="label">甲方订单号:</span>
              <span class="value">{{ orderInfo.orderNo }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">户名:</span>
              <span class="value">{{ orderInfo.customerName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">用户编号:</span>
              <span class="value">{{ orderInfo.userNo }}</span>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <span class="label">小区名称:</span>
              <span class="value">{{ orderInfo.communityName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">楼橦:</span>
              <span class="value">{{ orderInfo.building }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">房号:</span>
              <span class="value">{{ orderInfo.roomNo }}</span>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <span class="label">电话:</span>
              <span class="value">{{ orderInfo.phone }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">联系人:</span>
              <span class="value">{{ orderInfo.contactPerson }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">订单分类:</span>
              <span class="value">{{ orderInfo.orderType }}</span>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <span class="label">订单状态:</span>
              <span class="value">
                <el-tag :type="getStatusTagType(orderInfo.status)">
                  {{ orderInfo.status }}
                </el-tag>
              </span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">跟进师傅:</span>
              <span class="value">{{ orderInfo.assignedWorker || '未分派' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">总金额:</span>
              <span class="value">¥{{ orderInfo.totalAmount }}</span>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <span class="label">预计用时:</span>
              <span class="value">{{ orderInfo.estimatedDays }}天</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">开始时间:</span>
              <span class="value">{{ orderInfo.startDate || '未开始' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">完成时间:</span>
              <span class="value">{{ orderInfo.endDate || '未完成' }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
      
      <div v-else class="no-order-info">
        <el-empty description="请选择订单以查看详细信息">
          <el-button type="primary" @click="openOrderDialog">选择订单</el-button>
        </el-empty>
      </div>
    </el-card>

    <el-card class="form-card">
      <template #header>
        <div class="card-header">
          <span>执行信息</span>
        </div>
      </template>
      
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="订单号" prop="orderNo">
              <el-input v-model="form.orderNo" placeholder="请选择订单" readonly>
                <template #append>
                  <el-button @click="openOrderDialog">
                    <el-icon><Search /></el-icon>
                  </el-button>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="完成时间" prop="completionDate">
              <el-date-picker
                v-model="form.completionDate"
                type="date"
                placeholder="请选择完成时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用时(天)" prop="daysUsed">
              <el-input-number
                v-model="form.daysUsed"
                :min="0"
                :step="0.5"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input
                v-model="form.remark"
                type="textarea"
                :rows="2"
                placeholder="请输入备注"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <el-card class="material-card">
      <template #header>
        <div class="card-header">
          <span>物料使用列表</span>
          <el-button type="primary" @click="addMaterial">添加物料</el-button>
        </div>
      </template>

      <el-table :data="form.materials" border>
        <el-table-column type="index" label="#" width="60" />
        <el-table-column label="公司物料编码" width="180">
          <template #default="{ row, $index }">
            <el-input v-model="row.materialCode" placeholder="请选择物料">
              <template #append>
                <el-button @click="openMaterialDialog($index)">
                  <el-icon><Search /></el-icon>
                </el-button>
              </template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column label="物料名称" width="150">
          <template #default="{ row }">
            <el-input v-model="row.materialName" disabled />
          </template>
        </el-table-column>
        <el-table-column label="规格型号" width="150">
          <template #default="{ row }">
            <el-input v-model="row.specification" disabled />
          </template>
        </el-table-column>
        <el-table-column label="单位" width="80">
          <template #default="{ row }">
            <el-input v-model="row.unit" disabled />
          </template>
        </el-table-column>
        <el-table-column label="数量" width="120">
          <template #default="{ row }">
            <el-input-number
              v-model="row.quantity"
              :min="1"
              style="width: 100%"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80">
          <template #default="{ $index }">
            <el-button
              type="danger"
              size="small"
              @click="removeMaterial($index)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 订单选择对话框 -->
    <el-dialog
      v-model="orderDialogVisible"
      title="选择订单"
      width="1000px"
      @close="handleOrderDialogClose"
    >
      <el-table
        :data="orderList"
        border
        highlight-current-row
        @current-change="handleOrderSelect"
      >
        <el-table-column prop="orderNo" label="甲方订单号" width="150" />
        <el-table-column prop="customerName" label="户名" width="100" />
        <el-table-column prop="communityName" label="小区名称" width="120" />
        <el-table-column prop="building" label="楼橦" width="80" />
        <el-table-column prop="roomNo" label="房号" width="80" />
        <el-table-column prop="phone" label="电话" width="120" />
        <el-table-column prop="orderType" label="订单分类" width="100" />
        <el-table-column prop="status" label="订单状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="assignedWorker" label="跟进师傅" width="100" />
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="orderPagination.page"
          v-model:page-size="orderPagination.pageSize"
          :total="orderPagination.total"
          layout="total, prev, pager, next"
          @current-change="handleOrderPageChange"
        />
      </div>

      <template #footer>
        <el-button @click="orderDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmOrder">确定</el-button>
      </template>
    </el-dialog>

    <!-- 物料选择对话框 -->
    <el-dialog
      v-model="materialDialogVisible"
      title="选择物料"
      width="900px"
      @close="handleMaterialDialogClose"
    >
      <div class="search-form">
        <el-form :model="materialSearchForm" inline>
          <el-form-item label="物料编码">
            <el-input
              v-model="materialSearchForm.code"
              placeholder="请输入物料编码"
              clearable
            />
          </el-form-item>
          <el-form-item label="物料名称">
            <el-input
              v-model="materialSearchForm.name"
              placeholder="请输入物料名称"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchMaterials">搜索</el-button>
            <el-button @click="resetMaterialSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-table
        :data="materialList"
        border
        highlight-current-row
        @current-change="handleMaterialSelect"
      >
        <el-table-column prop="code" label="公司物料编码" width="150" />
        <el-table-column prop="name" label="物料名称" width="120" />
        <el-table-column prop="model" label="型号" width="120" />
        <el-table-column prop="specification" label="规格" width="120" />
        <el-table-column prop="unit" label="单位" width="80" />
        <el-table-column prop="category" label="分类" width="100">
          <template #default="{ row }">
            <el-tag :type="getCategoryTagType(row.category)">
              {{ row.category }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="quantity" label="库存数量" width="100" />
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="materialPagination.page"
          v-model:page-size="materialPagination.pageSize"
          :total="materialPagination.total"
          layout="total, prev, pager, next"
          @current-change="handleMaterialPageChange"
        />
      </div>

      <template #footer>
        <el-button @click="materialDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmMaterial">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute } from 'vue-router'
import type { FormInstance } from 'element-plus'

// 路由参数
const route = useRoute()

// 表单引用
const formRef = ref<FormInstance>()

// 表单数据
const form = reactive({
  orderNo: '',
  completionDate: '',
  daysUsed: 1,
  remark: '',
  materials: [] as Array<{
    materialCode: string
    materialName: string
    specification: string
    unit: string
    quantity: number
  }>
})

// 表单验证规则
const rules = {
  orderNo: [{ required: true, message: '请选择订单', trigger: 'change' }],
  completionDate: [{ required: true, message: '请选择完成时间', trigger: 'change' }],
  daysUsed: [{ required: true, message: '请输入用时天数', trigger: 'change' }]
}

// 订单详细信息
const orderInfo = ref<any>(null)

// 订单选择对话框
const orderDialogVisible = ref(false)
const orderList = ref([
  {
    orderNo: 'JD202401001',
    customerName: '张先生',
    userNo: 'YH001234',
    communityName: '阳光小区',
    building: '1栋',
    roomNo: '101',
    address: '阳光小区1栋101',
    phone: '138****1234',
    contactPerson: '张先生',
    orderType: '水电安装',
    status: '待执行',
    assignedWorker: '张三',
    totalAmount: 1500.00,
    estimatedDays: 3,
    startDate: '2024-01-15',
    endDate: ''
  },
  {
    orderNo: 'JD202401002',
    customerName: '李女士',
    userNo: 'YH001235',
    communityName: '花园小区',
    building: '2栋',
    roomNo: '202',
    address: '花园小区2栋202',
    phone: '139****5678',
    contactPerson: '李女士',
    orderType: '一次挂表',
    status: '执行中',
    assignedWorker: '李四',
    totalAmount: 800.00,
    estimatedDays: 2,
    startDate: '2024-01-16',
    endDate: ''
  }
])

const orderPagination = reactive({
  page: 1,
  pageSize: 10,
  total: 2
})

// 物料选择对话框
const materialDialogVisible = ref(false)
const currentMaterialIndex = ref(-1)
const materialList = ref([
  {
    code: 'WL001',
    name: '电缆线',
    model: 'YJV',
    specification: '3*4mm²',
    unit: '米',
    category: '甲料',
    quantity: 1000
  },
  {
    code: 'WL002',
    name: '开关面板',
    model: '86型',
    specification: '单控',
    unit: '个',
    category: '辅料',
    quantity: 500
  }
])

const materialSearchForm = reactive({
  code: '',
  name: ''
})

const materialPagination = reactive({
  page: 1,
  pageSize: 10,
  total: 2
})

// 初始化订单信息
const initOrderInfo = () => {
  const orderInfoParam = route.query.orderInfo
  if (orderInfoParam) {
    try {
      const orderData = JSON.parse(orderInfoParam as string)
      orderInfo.value = orderData
      
      // 自动填充表单
      form.orderNo = orderData.orderNo
      
      ElMessage.success(`已加载订单信息: ${orderData.orderNo}`)
    } catch (error) {
      ElMessage.error('订单信息解析失败')
      orderInfo.value = null
    }
  } else {
    // 如果没有传递订单信息，清空订单信息
    orderInfo.value = null
  }
}

// 打开订单选择对话框
const openOrderDialog = () => {
  orderDialogVisible.value = true
}

// 处理订单选择
const selectedOrder = ref<any>(null)
const handleOrderSelect = (row: any) => {
  selectedOrder.value = row
}

// 确认订单选择
const confirmOrder = () => {
  if (!selectedOrder.value) {
    ElMessage.warning('请先选择订单')
    return
  }
  
  // 更新订单信息
  orderInfo.value = selectedOrder.value
  form.orderNo = selectedOrder.value.orderNo
  orderDialogVisible.value = false
  
  ElMessage.success(`已选择订单: ${selectedOrder.value.orderNo}`)
}

// 处理订单对话框关闭
const handleOrderDialogClose = () => {
  selectedOrder.value = null
}

// 订单分页变化
const handleOrderPageChange = (page: number) => {
  orderPagination.page = page
  // 这里应该重新加载订单数据
}

// 打开物料选择对话框
const openMaterialDialog = (index: number) => {
  currentMaterialIndex.value = index
  materialDialogVisible.value = true
}

// 处理物料选择
const selectedMaterial = ref<any>(null)
const handleMaterialSelect = (row: any) => {
  selectedMaterial.value = row
}

// 确认物料选择
const confirmMaterial = () => {
  if (!selectedMaterial.value) {
    ElMessage.warning('请先选择物料')
    return
  }
  
  if (currentMaterialIndex.value >= 0) {
    // 更新已有的物料行
    const material = form.materials[currentMaterialIndex.value]
    material.materialCode = selectedMaterial.value.code
    material.materialName = selectedMaterial.value.name
    material.specification = selectedMaterial.value.specification
    material.unit = selectedMaterial.value.unit
  }
  
  materialDialogVisible.value = false
}

// 处理物料对话框关闭
const handleMaterialDialogClose = () => {
  selectedMaterial.value = null
  currentMaterialIndex.value = -1
}

// 物料分页变化
const handleMaterialPageChange = (page: number) => {
  materialPagination.page = page
  // 这里应该重新加载物料数据
}

// 搜索物料
const searchMaterials = () => {
  // 执行物料搜索逻辑
  ElMessage.info('执行物料搜索')
}

// 重置物料搜索
const resetMaterialSearch = () => {
  materialSearchForm.code = ''
  materialSearchForm.name = ''
}

// 添加物料
const addMaterial = () => {
  form.materials.push({
    materialCode: '',
    materialName: '',
    specification: '',
    unit: '',
    quantity: 1
  })
}

// 删除物料
const removeMaterial = (index: number) => {
  form.materials.splice(index, 1)
}

// 获取分类标签类型
const getCategoryTagType = (category: string) => {
  switch (category) {
    case '甲料': return 'primary'
    case '辅料': return 'success'
    case '商品': return 'warning'
    default: return 'info'
  }
}

// 获取订单状态标签类型
const getStatusTagType = (status: string) => {
  switch (status) {
    case '待执行': return 'info'
    case '执行中': return 'success'
    case '已完成': return 'success'
    case '已取消': return 'danger'
    default: return 'info'
  }
}

// 保存表单
const handleSave = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (!valid) {
      ElMessage.error('请填写必填项')
      return
    }

    // 准备API请求数据
    const payload = {
      orderNo: form.orderNo,
      daysUsed: form.daysUsed,
      materials: form.materials.map(m => ({
        materialCode: m.materialCode,
        quantity: m.quantity
      })),
      remark: form.remark
    }

    // 调用API保存数据
    // const response = await executeLooseOrder(payload)
    ElMessage.success('订单执行信息保存成功')
    // 这里可以添加跳转或其他成功处理逻辑
  } catch (error) {
    ElMessage.error('保存失败: ' + (error as Error).message)
  }
}

// 组件挂载时初始化订单信息
onMounted(() => {
  initOrderInfo()
})
</script>

<style scoped>
.order-execute-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.order-info-card {
  margin-bottom: 20px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
}

.no-order-info {
  text-align: center;
  padding: 40px 0;
}

.form-card, .material-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  line-height: 1.6;
}

.label {
  font-weight: bold;
  margin-right: 10px;
  color: #606266;
  min-width: 100px;
}

.value {
  font-weight: normal;
  color: #303133;
}

.pagination-container {
  margin-top: 20px;
  text-align: center;
}

.search-form {
  margin-bottom: 20px;
}
</style>