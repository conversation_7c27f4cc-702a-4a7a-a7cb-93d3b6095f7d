var e=Object.defineProperty,l=Object.defineProperties,a=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable,s=(l,a,t)=>a in l?e(l,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):l[a]=t,u=(e,l,a)=>new Promise((t,r)=>{var o=e=>{try{u(a.next(e))}catch(l){r(l)}},s=e=>{try{u(a.throw(e))}catch(l){r(l)}},u=e=>e.done?t(e.value):Promise.resolve(e.value).then(o,s);u((a=a.apply(e,l)).next())});import{E as n,e as d}from"./element-plus-7917fd46.js";import{l as i,_ as p,r as m,q as c,y as f,z as _,R as g,J as b,K as h,I as y,av as v,aD as w,x as V,O as k,P as C,M as P}from"./vue-vendor-fc5a6493.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const z={class:"page-container"},U={class:"search-form"},j={class:"action-buttons"},O={class:"table-container"},x={class:"pagination-container"},S=i({__name:"UserManagement",setup(e){const i=p({username:"",role:"",status:""}),S=m([]),T=m(!1),q=m([]),E=p({page:1,pageSize:20,total:0}),L=m(!1),D=m(""),I=m(),M=p({id:"",username:"",role:"",phone:"",email:"",password:"",confirmPassword:"",status:1,remark:""}),R={username:[{required:!0,message:"请输入用户名",trigger:"blur"}],role:[{required:!0,message:"请选择角色",trigger:"change"}],phone:[{required:!0,message:"请输入手机号",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"}],confirmPassword:[{required:!0,message:"请确认密码",trigger:"blur"},{validator:(e,l,a)=>{l!==M.password?a(new Error("两次输入的密码不一致")):a()},trigger:"blur"}]},$=()=>{E.page=1,ee()},F=()=>{Object.assign(i,{username:"",role:"",status:""}),$()},J=e=>{q.value=e},K=()=>{D.value="新增用户",Object.assign(M,{id:"",username:"",role:"",phone:"",email:"",password:"",confirmPassword:"",status:1,remark:""}),L.value=!0},A=e=>{var u;D.value="编辑用户",Object.assign(M,(u=((e,l)=>{for(var a in l||(l={}))r.call(l,a)&&s(e,a,l[a]);if(t)for(var a of t(l))o.call(l,a)&&s(e,a,l[a]);return e})({},e),l(u,a({password:"",confirmPassword:""})))),L.value=!0},B=e=>u(this,null,function*(){const l=e.status?"禁用":"启用";try{yield d.confirm(`确定要${l}该用户吗？`,"提示",{type:"warning"}),n.success(`${l}成功`),ee()}catch(a){}}),G=e=>u(this,null,function*(){try{yield d.confirm("确定要删除该用户吗？","提示",{type:"warning"}),n.success("删除成功"),ee()}catch(e){}}),H=()=>{0!==q.value.length?(n.success("批量启用成功"),ee()):n.warning("请选择要启用的用户")},N=()=>{0!==q.value.length?(n.success("批量禁用成功"),ee()):n.warning("请选择要禁用的用户")},Q=()=>u(this,null,function*(){if(0!==q.value.length)try{yield d.confirm("确定要删除选中的用户吗？","提示",{type:"warning"}),n.success("批量删除成功"),ee()}catch(e){}else n.warning("请选择要删除的用户")}),W=()=>u(this,null,function*(){if(I.value)try{yield I.value.validate(),n.success(M.id?"更新成功":"新增成功"),L.value=!1,ee()}catch(e){n.error("表单验证失败")}}),X=()=>{var e;null==(e=I.value)||e.resetFields()},Y=e=>{E.pageSize=e,ee()},Z=e=>{E.page=e,ee()},ee=()=>u(this,null,function*(){T.value=!0;try{yield new Promise(e=>setTimeout(e,1e3)),S.value=[{id:1,username:"admin",role:"系统管理员",phone:"13800138000",email:"<EMAIL>",status:1,lastLoginTime:"2024-01-15 14:30:00",createTime:"2023-01-01 10:00:00"},{id:2,username:"warehouse",role:"仓库管理员",phone:"13800138001",email:"<EMAIL>",status:1,lastLoginTime:"2024-01-14 16:20:00",createTime:"2023-02-01 10:00:00"}],E.total=25}catch(e){n.error("加载数据失败")}finally{T.value=!1}});return c(()=>{ee()}),(e,l)=>{const a=v("el-input"),t=v("el-form-item"),r=v("el-option"),o=v("el-select"),s=v("el-button"),u=v("el-form"),d=v("el-table-column"),p=v("el-tag"),m=v("el-table"),c=v("el-pagination"),q=v("el-col"),ee=v("el-row"),le=v("el-radio"),ae=v("el-radio-group"),te=v("el-dialog"),re=w("loading");return V(),f("div",z,[_("div",U,[g(u,{model:i,inline:""},{default:b(()=>[g(t,{label:"用户名"},{default:b(()=>[g(a,{modelValue:i.username,"onUpdate:modelValue":l[0]||(l[0]=e=>i.username=e),placeholder:"请输入用户名",clearable:"",style:{width:"150px"}},null,8,["modelValue"])]),_:1}),g(t,{label:"角色"},{default:b(()=>[g(o,{modelValue:i.role,"onUpdate:modelValue":l[1]||(l[1]=e=>i.role=e),placeholder:"请选择角色",clearable:""},{default:b(()=>[g(r,{label:"系统管理员",value:"系统管理员"}),g(r,{label:"仓库管理员",value:"仓库管理员"}),g(r,{label:"订单管理员",value:"订单管理员"}),g(r,{label:"普通用户",value:"普通用户"})]),_:1},8,["modelValue"])]),_:1}),g(t,{label:"状态"},{default:b(()=>[g(o,{modelValue:i.status,"onUpdate:modelValue":l[2]||(l[2]=e=>i.status=e),placeholder:"请选择状态",clearable:""},{default:b(()=>[g(r,{label:"启用",value:1}),g(r,{label:"禁用",value:0})]),_:1},8,["modelValue"])]),_:1}),g(t,null,{default:b(()=>[g(s,{type:"primary",onClick:$},{default:b(()=>l[15]||(l[15]=[k("搜索",-1)])),_:1,__:[15]}),g(s,{onClick:F},{default:b(()=>l[16]||(l[16]=[k("重置",-1)])),_:1,__:[16]})]),_:1})]),_:1},8,["model"])]),_("div",j,[g(s,{type:"primary",onClick:K},{default:b(()=>l[17]||(l[17]=[k("新增用户",-1)])),_:1,__:[17]}),g(s,{type:"success",onClick:H},{default:b(()=>l[18]||(l[18]=[k("批量启用",-1)])),_:1,__:[18]}),g(s,{type:"warning",onClick:N},{default:b(()=>l[19]||(l[19]=[k("批量禁用",-1)])),_:1,__:[19]}),g(s,{type:"danger",onClick:Q},{default:b(()=>l[20]||(l[20]=[k("批量删除",-1)])),_:1,__:[20]})]),_("div",O,[h((V(),y(m,{data:S.value,border:"",stripe:"",onSelectionChange:J},{default:b(()=>[g(d,{type:"selection",width:"55"}),g(d,{prop:"username",label:"用户名",width:"120"}),g(d,{prop:"role",label:"角色",width:"120"},{default:b(({row:e})=>{return[g(p,{type:(l=e.role,{"系统管理员":"danger","仓库管理员":"warning","订单管理员":"primary","普通用户":"info"}[l]||"info")},{default:b(()=>[k(C(e.role),1)]),_:2},1032,["type"])];var l}),_:1}),g(d,{prop:"phone",label:"手机号",width:"120"}),g(d,{prop:"email",label:"邮箱",width:"180"}),g(d,{prop:"status",label:"状态",width:"80"},{default:b(({row:e})=>[g(p,{type:e.status?"success":"danger"},{default:b(()=>[k(C(e.status?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),g(d,{prop:"lastLoginTime",label:"最后登录时间",width:"160"}),g(d,{prop:"createTime",label:"创建时间",width:"160"}),g(d,{label:"操作",width:"250",fixed:"right"},{default:b(({row:e})=>[g(s,{type:"primary",size:"small",onClick:l=>A(e)},{default:b(()=>l[21]||(l[21]=[k("编辑",-1)])),_:2,__:[21]},1032,["onClick"]),g(s,{type:"info",size:"small",onClick:e=>{n.info("查看用户详情功能待实现")}},{default:b(()=>l[22]||(l[22]=[k("查看",-1)])),_:2,__:[22]},1032,["onClick"]),g(s,{type:e.status?"warning":"success",size:"small",onClick:l=>B(e)},{default:b(()=>[k(C(e.status?"禁用":"启用"),1)]),_:2},1032,["type","onClick"]),g(s,{type:"danger",size:"small",onClick:e=>G()},{default:b(()=>l[23]||(l[23]=[k("删除",-1)])),_:2,__:[23]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[re,T.value]]),_("div",x,[g(c,{"current-page":E.page,"onUpdate:currentPage":l[3]||(l[3]=e=>E.page=e),"page-size":E.pageSize,"onUpdate:pageSize":l[4]||(l[4]=e=>E.pageSize=e),total:E.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Y,onCurrentChange:Z},null,8,["current-page","page-size","total"])])]),g(te,{modelValue:L.value,"onUpdate:modelValue":l[14]||(l[14]=e=>L.value=e),title:D.value,width:"600px",onClose:X},{footer:b(()=>[g(s,{onClick:l[13]||(l[13]=e=>L.value=!1)},{default:b(()=>l[26]||(l[26]=[k("取消",-1)])),_:1,__:[26]}),g(s,{type:"primary",onClick:W},{default:b(()=>l[27]||(l[27]=[k("确定",-1)])),_:1,__:[27]})]),default:b(()=>[g(u,{ref_key:"formRef",ref:I,model:M,rules:R,"label-width":"120px"},{default:b(()=>[g(ee,{gutter:20},{default:b(()=>[g(q,{span:12},{default:b(()=>[g(t,{label:"用户名",prop:"username"},{default:b(()=>[g(a,{modelValue:M.username,"onUpdate:modelValue":l[5]||(l[5]=e=>M.username=e),placeholder:"请输入用户名"},null,8,["modelValue"])]),_:1})]),_:1}),g(q,{span:12},{default:b(()=>[g(t,{label:"角色",prop:"role"},{default:b(()=>[g(o,{modelValue:M.role,"onUpdate:modelValue":l[6]||(l[6]=e=>M.role=e),placeholder:"请选择角色"},{default:b(()=>[g(r,{label:"系统管理员",value:"系统管理员"}),g(r,{label:"仓库管理员",value:"仓库管理员"}),g(r,{label:"订单管理员",value:"订单管理员"}),g(r,{label:"普通用户",value:"普通用户"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),g(ee,{gutter:20},{default:b(()=>[g(q,{span:12},{default:b(()=>[g(t,{label:"手机号",prop:"phone"},{default:b(()=>[g(a,{modelValue:M.phone,"onUpdate:modelValue":l[7]||(l[7]=e=>M.phone=e),placeholder:"请输入手机号"},null,8,["modelValue"])]),_:1})]),_:1}),g(q,{span:12},{default:b(()=>[g(t,{label:"邮箱",prop:"email"},{default:b(()=>[g(a,{modelValue:M.email,"onUpdate:modelValue":l[8]||(l[8]=e=>M.email=e),placeholder:"请输入邮箱"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),M.id?P("",!0):(V(),y(ee,{key:0,gutter:20},{default:b(()=>[g(q,{span:12},{default:b(()=>[g(t,{label:"密码",prop:"password"},{default:b(()=>[g(a,{modelValue:M.password,"onUpdate:modelValue":l[9]||(l[9]=e=>M.password=e),type:"password",placeholder:"请输入密码"},null,8,["modelValue"])]),_:1})]),_:1}),g(q,{span:12},{default:b(()=>[g(t,{label:"确认密码",prop:"confirmPassword"},{default:b(()=>[g(a,{modelValue:M.confirmPassword,"onUpdate:modelValue":l[10]||(l[10]=e=>M.confirmPassword=e),type:"password",placeholder:"请确认密码"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})),g(t,{label:"状态",prop:"status"},{default:b(()=>[g(ae,{modelValue:M.status,"onUpdate:modelValue":l[11]||(l[11]=e=>M.status=e)},{default:b(()=>[g(le,{label:1},{default:b(()=>l[24]||(l[24]=[k("启用",-1)])),_:1,__:[24]}),g(le,{label:0},{default:b(()=>l[25]||(l[25]=[k("禁用",-1)])),_:1,__:[25]})]),_:1},8,["modelValue"])]),_:1}),g(t,{label:"备注",prop:"remark"},{default:b(()=>[g(a,{modelValue:M.remark,"onUpdate:modelValue":l[12]||(l[12]=e=>M.remark=e),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}});export{S as default};
