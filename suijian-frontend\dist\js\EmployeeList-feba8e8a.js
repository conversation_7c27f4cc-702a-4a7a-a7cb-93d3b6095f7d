var e=(e,l,a)=>new Promise((t,o)=>{var r=e=>{try{u(a.next(e))}catch(l){o(l)}},d=e=>{try{u(a.throw(e))}catch(l){o(l)}},u=e=>e.done?t(e.value):Promise.resolve(e.value).then(r,d);u((a=a.apply(e,l)).next())});import{E as l,e as a}from"./element-plus-ad78a7bf.js";import{l as t,_ as o,r,q as d,y as u,z as i,R as p,J as n,K as s,I as m,av as c,aD as _,x as f,O as y,P as g}from"./vue-vendor-fc5a6493.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const h={class:"page-container"},b={class:"search-form"},v={class:"action-buttons"},w={class:"table-container"},k={class:"pagination-container"},V=t({__name:"EmployeeList",setup(t){const V=o({name:"",workType:"",status:""}),C=r([]),T=r(!1),z=o({page:1,pageSize:20,total:0}),U=r(!1),x=r(""),W=r(),D=o({id:"",name:"",workType:"",dailyWage:0,phone:"",idCard:"",status:1,hireDate:"",remark:""}),j={name:[{required:!0,message:"请输入员工姓名",trigger:"blur"}],workType:[{required:!0,message:"请选择工种",trigger:"change"}],dailyWage:[{required:!0,message:"请输入日工资",trigger:"blur"}],phone:[{required:!0,message:"请输入联系电话",trigger:"blur"}]},S=()=>{z.page=1,L()},q=()=>{Object.assign(V,{name:"",workType:"",status:""}),S()},P=()=>{x.value="新增员工",Object.assign(D,{id:"",name:"",workType:"",dailyWage:0,phone:"",idCard:"",status:1,hireDate:"",remark:""}),U.value=!0},E=t=>e(this,null,function*(){try{yield a.confirm("确定要删除该员工吗？","提示",{type:"warning"}),l.success("删除成功"),L()}catch(e){}}),O=()=>{l.info("导入功能待实现")},R=()=>{l.info("导出功能待实现")},F=()=>e(this,null,function*(){if(W.value)try{yield W.value.validate(),l.success(D.id?"更新成功":"新增成功"),U.value=!1,L()}catch(e){l.error("表单验证失败")}}),I=()=>{var e;null==(e=W.value)||e.resetFields()},J=e=>{z.pageSize=e,L()},K=e=>{z.page=e,L()},L=()=>e(this,null,function*(){T.value=!0;try{yield new Promise(e=>setTimeout(e,1e3)),C.value=[{id:1,name:"张师傅",workType:"电工",dailyWage:300,phone:"13800138000",idCard:"110101199001011234",status:1,hireDate:"2023-01-15",remark:"经验丰富，技术过硬"},{id:2,name:"李师傅",workType:"水工",dailyWage:280,phone:"13800138001",idCard:"110101199002022345",status:1,hireDate:"2023-02-20",remark:"工作认真负责"}],z.total=89}catch(e){l.error("加载数据失败")}finally{T.value=!1}});return d(()=>{L()}),(e,a)=>{const t=c("el-input"),o=c("el-form-item"),r=c("el-option"),d=c("el-select"),L=c("el-button"),A=c("el-form"),B=c("el-table-column"),G=c("el-tag"),H=c("el-table"),M=c("el-pagination"),N=c("el-col"),Q=c("el-row"),X=c("el-input-number"),Y=c("el-date-picker"),Z=c("el-radio"),$=c("el-radio-group"),ee=c("el-dialog"),le=_("loading");return f(),u("div",h,[i("div",b,[p(A,{model:V,inline:""},{default:n(()=>[p(o,{label:"员工姓名"},{default:n(()=>[p(t,{modelValue:V.name,"onUpdate:modelValue":a[0]||(a[0]=e=>V.name=e),placeholder:"请输入员工姓名",clearable:"",style:{width:"150px"}},null,8,["modelValue"])]),_:1}),p(o,{label:"工种"},{default:n(()=>[p(d,{modelValue:V.workType,"onUpdate:modelValue":a[1]||(a[1]=e=>V.workType=e),placeholder:"请选择工种",clearable:""},{default:n(()=>[p(r,{label:"电工",value:"电工"}),p(r,{label:"水工",value:"水工"}),p(r,{label:"安装工",value:"安装工"}),p(r,{label:"维修工",value:"维修工"})]),_:1},8,["modelValue"])]),_:1}),p(o,{label:"状态"},{default:n(()=>[p(d,{modelValue:V.status,"onUpdate:modelValue":a[2]||(a[2]=e=>V.status=e),placeholder:"请选择状态",clearable:""},{default:n(()=>[p(r,{label:"在职",value:1}),p(r,{label:"离职",value:0})]),_:1},8,["modelValue"])]),_:1}),p(o,null,{default:n(()=>[p(L,{type:"primary",onClick:S},{default:n(()=>a[15]||(a[15]=[y("搜索",-1)])),_:1,__:[15]}),p(L,{onClick:q},{default:n(()=>a[16]||(a[16]=[y("重置",-1)])),_:1,__:[16]})]),_:1})]),_:1},8,["model"])]),i("div",v,[p(L,{type:"primary",onClick:P},{default:n(()=>a[17]||(a[17]=[y("新增员工",-1)])),_:1,__:[17]}),p(L,{type:"success",onClick:O},{default:n(()=>a[18]||(a[18]=[y("导入Excel",-1)])),_:1,__:[18]}),p(L,{type:"warning",onClick:R},{default:n(()=>a[19]||(a[19]=[y("导出Excel",-1)])),_:1,__:[19]})]),i("div",w,[s((f(),m(H,{data:C.value,border:"",stripe:"",style:{width:"100%"}},{default:n(()=>[p(B,{prop:"name",label:"员工姓名",width:"120"}),p(B,{prop:"workType",label:"工种",width:"100"},{default:n(({row:e})=>{return[p(G,{type:(l=e.workType,{"电工":"primary","水工":"success","安装工":"warning","维修工":"info"}[l]||"info")},{default:n(()=>[y(g(e.workType),1)]),_:2},1032,["type"])];var l}),_:1}),p(B,{prop:"dailyWage",label:"日工资",width:"100"},{default:n(({row:e})=>[y(" ¥"+g(e.dailyWage),1)]),_:1}),p(B,{prop:"phone",label:"联系电话",width:"120"}),p(B,{prop:"idCard",label:"身份证号",width:"180"}),p(B,{prop:"status",label:"状态",width:"80"},{default:n(({row:e})=>[p(G,{type:e.status?"success":"danger"},{default:n(()=>[y(g(e.status?"在职":"离职"),1)]),_:2},1032,["type"])]),_:1}),p(B,{prop:"hireDate",label:"入职时间",width:"120"}),p(B,{prop:"remark",label:"备注","min-width":"150","show-overflow-tooltip":""}),p(B,{label:"操作",width:"200",fixed:"right"},{default:n(({row:e})=>[p(L,{type:"primary",size:"small",onClick:l=>(e=>{x.value="编辑员工",Object.assign(D,e),U.value=!0})(e)},{default:n(()=>a[20]||(a[20]=[y("编辑",-1)])),_:2,__:[20]},1032,["onClick"]),p(L,{type:"info",size:"small",onClick:e=>{l.info("查看员工详情功能待实现")}},{default:n(()=>a[21]||(a[21]=[y("查看",-1)])),_:2,__:[21]},1032,["onClick"]),p(L,{type:"danger",size:"small",onClick:e=>E()},{default:n(()=>a[22]||(a[22]=[y("删除",-1)])),_:2,__:[22]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[le,T.value]]),i("div",k,[p(M,{"current-page":z.page,"onUpdate:currentPage":a[3]||(a[3]=e=>z.page=e),"page-size":z.pageSize,"onUpdate:pageSize":a[4]||(a[4]=e=>z.pageSize=e),total:z.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:J,onCurrentChange:K},null,8,["current-page","page-size","total"])])]),p(ee,{modelValue:U.value,"onUpdate:modelValue":a[14]||(a[14]=e=>U.value=e),title:x.value,width:"600px",onClose:I},{footer:n(()=>[p(L,{onClick:a[13]||(a[13]=e=>U.value=!1)},{default:n(()=>a[25]||(a[25]=[y("取消",-1)])),_:1,__:[25]}),p(L,{type:"primary",onClick:F},{default:n(()=>a[26]||(a[26]=[y("确定",-1)])),_:1,__:[26]})]),default:n(()=>[p(A,{ref_key:"formRef",ref:W,model:D,rules:j,"label-width":"120px"},{default:n(()=>[p(Q,{gutter:20},{default:n(()=>[p(N,{span:12},{default:n(()=>[p(o,{label:"员工姓名",prop:"name"},{default:n(()=>[p(t,{modelValue:D.name,"onUpdate:modelValue":a[5]||(a[5]=e=>D.name=e),placeholder:"请输入员工姓名"},null,8,["modelValue"])]),_:1})]),_:1}),p(N,{span:12},{default:n(()=>[p(o,{label:"工种",prop:"workType"},{default:n(()=>[p(d,{modelValue:D.workType,"onUpdate:modelValue":a[6]||(a[6]=e=>D.workType=e),placeholder:"请选择工种"},{default:n(()=>[p(r,{label:"电工",value:"电工"}),p(r,{label:"水工",value:"水工"}),p(r,{label:"安装工",value:"安装工"}),p(r,{label:"维修工",value:"维修工"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),p(Q,{gutter:20},{default:n(()=>[p(N,{span:12},{default:n(()=>[p(o,{label:"日工资",prop:"dailyWage"},{default:n(()=>[p(X,{modelValue:D.dailyWage,"onUpdate:modelValue":a[7]||(a[7]=e=>D.dailyWage=e),precision:2,min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),p(N,{span:12},{default:n(()=>[p(o,{label:"联系电话",prop:"phone"},{default:n(()=>[p(t,{modelValue:D.phone,"onUpdate:modelValue":a[8]||(a[8]=e=>D.phone=e),placeholder:"请输入联系电话"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),p(Q,{gutter:20},{default:n(()=>[p(N,{span:12},{default:n(()=>[p(o,{label:"身份证号",prop:"idCard"},{default:n(()=>[p(t,{modelValue:D.idCard,"onUpdate:modelValue":a[9]||(a[9]=e=>D.idCard=e),placeholder:"请输入身份证号"},null,8,["modelValue"])]),_:1})]),_:1}),p(N,{span:12},{default:n(()=>[p(o,{label:"入职时间",prop:"hireDate"},{default:n(()=>[p(Y,{modelValue:D.hireDate,"onUpdate:modelValue":a[10]||(a[10]=e=>D.hireDate=e),type:"date",placeholder:"选择入职时间",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),p(o,{label:"状态",prop:"status"},{default:n(()=>[p($,{modelValue:D.status,"onUpdate:modelValue":a[11]||(a[11]=e=>D.status=e)},{default:n(()=>[p(Z,{label:1},{default:n(()=>a[23]||(a[23]=[y("在职",-1)])),_:1,__:[23]}),p(Z,{label:0},{default:n(()=>a[24]||(a[24]=[y("离职",-1)])),_:1,__:[24]})]),_:1},8,["modelValue"])]),_:1}),p(o,{label:"备注",prop:"remark"},{default:n(()=>[p(t,{modelValue:D.remark,"onUpdate:modelValue":a[12]||(a[12]=e=>D.remark=e),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}});export{V as default};
