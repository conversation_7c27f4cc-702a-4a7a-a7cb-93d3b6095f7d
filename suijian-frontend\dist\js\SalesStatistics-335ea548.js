import{E as a,r as s}from"./element-plus-ad78a7bf.js";import{l as e,r as l,c as t,q as o,y as n,R as r,J as i,z as c,av as u,x as d,u as p,O as m,P as v,B as _}from"./vue-vendor-fc5a6493.js";import{_ as f}from"./_plugin-vue_export-helper-1b428a4d.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const w={class:"sales-statistics"},h={class:"card-header"},g={class:"header-actions"},b={class:"sales-summary"},k={class:"summary-row"},y={class:"summary-item"},C={class:"value"},A={class:"amount"},z={class:"summary-row"},j={class:"summary-item"},D={class:"target"},N={class:"summary-row"},x={class:"summary-item"},M={class:"value completion"},S={class:"remaining"},E={class:"rank-icon"},L={class:"product-icon"},$={class:"action-buttons"},q=f(e({__name:"SalesStatistics",setup(e){const f=l(15),q=l(35680),B=l(5e4),F=l(12.5),I=l([{rank:1,salesperson:"张三",salesCount:25,salesAmount:8500,commission:425},{rank:2,salesperson:"李四",salesCount:18,salesAmount:6200,commission:310},{rank:3,salesperson:"王五",salesCount:15,salesAmount:5100,commission:255},{rank:4,salesperson:"赵六",salesCount:12,salesAmount:4200,commission:210}]),J=l([{id:1,productName:"智能开关",icon:"🔌",salesCount:45,salesAmount:13500,stock:28},{id:2,productName:"LED灯具",icon:"💡",salesCount:38,salesAmount:11400,stock:15},{id:3,productName:"插座面板",icon:"🔌",salesCount:32,salesAmount:9600,stock:42},{id:4,productName:"燃气配件",icon:"🔧",salesCount:28,salesAmount:8400,stock:8}]),O=t(()=>Math.round(q.value/B.value*100*10)/10),P=t(()=>{const a=new Date;return new Date(a.getFullYear(),a.getMonth()+1,0).getDate()-a.getDate()}),R=a=>a.toLocaleString(),Y=()=>{a.success("数据已刷新")},G=()=>{a.info("生成销售统计报表")},H=()=>{a.info("查看员工绩效详情")};return o(()=>{}),(e,l)=>{const t=u("el-icon"),o=u("el-button"),K=u("el-card"),Q=u("el-table-column"),T=u("el-table");return d(),n("div",w,[r(K,{class:"sales-summary-card",shadow:"hover"},{header:i(()=>[c("div",h,[l[1]||(l[1]=c("span",null,"💰 本月售卖统计",-1)),c("div",g,[r(o,{type:"primary",size:"small",onClick:Y},{default:i(()=>[r(t,null,{default:i(()=>[r(p(s))]),_:1}),l[0]||(l[0]=m(" 刷新 ",-1))]),_:1,__:[0]})])])]),default:i(()=>[c("div",b,[c("div",k,[c("div",y,[l[2]||(l[2]=c("span",{class:"icon"},"📦",-1)),l[3]||(l[3]=c("span",{class:"label"},"本月售卖:",-1)),c("span",C,v(f.value)+"项",1),c("span",A,"💵 总价: ¥"+v(R(q.value)),1)])]),c("div",z,[c("div",j,[l[4]||(l[4]=c("span",{class:"icon"},"📈",-1)),l[5]||(l[5]=c("span",{class:"label"},"较上月:",-1)),c("span",{class:_(["value",{positive:F.value>0,negative:F.value<0}])},v(F.value>0?"+":"")+v(F.value)+"% ",3),c("span",D,"🎯 月度目标: ¥"+v(R(B.value)),1)])]),c("div",N,[c("div",x,[l[6]||(l[6]=c("span",{class:"icon"},"📊",-1)),l[7]||(l[7]=c("span",{class:"label"},"完成度:",-1)),c("span",M,v(O.value)+"%",1),c("span",S,"📅 剩余天数: "+v(P.value)+"天",1)])])])]),_:1}),r(K,{class:"ranking-card",shadow:"hover"},{header:i(()=>l[8]||(l[8]=[c("div",{class:"card-header"},[c("span",null,"🏆 销售排行榜")],-1)])),default:i(()=>[r(T,{data:I.value,style:{width:"100%"},border:""},{default:i(()=>[r(Q,{prop:"rank",label:"排名",width:"80",align:"center"},{default:i(({row:a})=>{return[c("span",E,v((s=a.rank,["","🥇","🥈","🥉","4️⃣","5️⃣"][s]||`${s}️⃣`)),1)];var s}),_:1}),r(Q,{prop:"salesperson",label:"销售人",width:"120"}),r(Q,{prop:"salesCount",label:"销售数量",width:"120",align:"center"},{default:i(({row:a})=>[m(v(a.salesCount)+"件 ",1)]),_:1}),r(Q,{prop:"salesAmount",label:"销售总价",width:"150",align:"center"},{default:i(({row:a})=>[m(" ¥"+v(R(a.salesAmount)),1)]),_:1}),r(Q,{prop:"commission",label:"提成",width:"120",align:"center"},{default:i(({row:a})=>[m(" ¥"+v(R(a.commission)),1)]),_:1}),r(Q,{label:"操作",width:"100",align:"center"},{default:i(({row:s})=>[r(o,{type:"primary",size:"small",onClick:e=>{return l=s,void a.info(`查看 ${l.salesperson} 的详细业绩`);var l}},{default:i(()=>l[9]||(l[9]=[m(" 查看 ",-1)])),_:2,__:[9]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),l[10]||(l[10]=c("div",{class:"table-footer"},[c("span",null,"📋 查看完整排行榜...")],-1))]),_:1,__:[10]}),r(K,{class:"product-ranking-card",shadow:"hover"},{header:i(()=>l[11]||(l[11]=[c("div",{class:"card-header"},[c("span",null,"🔥 畅销排行榜")],-1)])),default:i(()=>[r(T,{data:J.value,style:{width:"100%"},border:""},{default:i(()=>[r(Q,{prop:"icon",label:"",width:"50",align:"center"},{default:i(({row:a})=>[c("span",L,v(a.icon),1)]),_:1}),r(Q,{prop:"productName",label:"商品名称",width:"150"}),r(Q,{prop:"salesCount",label:"销售数量",width:"120",align:"center"},{default:i(({row:a})=>[m(v(a.salesCount)+"个 ",1)]),_:1}),r(Q,{prop:"salesAmount",label:"销售总价",width:"150",align:"center"},{default:i(({row:a})=>[m(" ¥"+v(R(a.salesAmount)),1)]),_:1}),r(Q,{prop:"stock",label:"库存",width:"100",align:"center"},{default:i(({row:a})=>[m(v(a.stock)+"个 ",1)]),_:1})]),_:1},8,["data"]),l[12]||(l[12]=c("div",{class:"table-footer"},[c("span",null,"📋 查看完整排行榜...")],-1))]),_:1,__:[12]}),c("div",$,[r(o,{type:"primary",size:"large",onClick:G},{default:i(()=>l[13]||(l[13]=[m(" 📊 销售报表 ",-1)])),_:1,__:[13]}),r(o,{type:"success",size:"large",onClick:H},{default:i(()=>l[14]||(l[14]=[m(" 👥 员工绩效 ",-1)])),_:1,__:[14]}),r(o,{type:"default",size:"large",onClick:Y},{default:i(()=>l[15]||(l[15]=[m(" 🔄 刷新 ",-1)])),_:1,__:[15]})])])}}}),[["__scopeId","data-v-487cf72f"]]);export{q as default};
