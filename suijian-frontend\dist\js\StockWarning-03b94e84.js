var e=Object.defineProperty,a=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty,t=Object.prototype.propertyIsEnumerable,r=(a,l,t)=>l in a?e(a,l,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[l]=t;import{E as o}from"./element-plus-ad78a7bf.js";import{l as n,_ as d,r as i,c as u,y as s,R as p,J as m,av as c,x as g,z as _,O as h,Q as w,aa as f,P as b,I as v}from"./vue-vendor-fc5a6493.js";import{_ as y}from"./_plugin-vue_export-helper-1b428a4d.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const k={class:"stock-warning-container"},V={class:"form-actions"},T={class:"search-buttons"},S=y(n({__name:"StockWarning",setup(e){const n=d({warningMethods:["system","email"],warningFrequency:"realtime",lowStockThreshold:"10",overstockThreshold:"1000",recipients:[1,2]}),y=i([{id:1,name:"张三"},{id:2,name:"李四"},{id:3,name:"王五"},{id:4,name:"赵六"}]),S=d({currentPage:1,pageSize:10,total:25}),C=i([{id:1,materialCode:"WL001",materialName:"电缆线",model:"YJV-3*4",specification:"3*4mm²",unit:"米",currentStock:5,warningType:"lowStock",warningThreshold:"10",status:"enabled"},{id:2,materialCode:"WL002",materialName:"开关面板",model:"KP-86",specification:"86型",unit:"个",currentStock:1200,warningType:"overstock",warningThreshold:"1000",status:"enabled"},{id:3,materialCode:"WL003",materialName:"电线",model:"BV-2.5",specification:"2.5mm²",unit:"米",currentStock:8,warningType:"lowStock",warningThreshold:"10",status:"enabled"},{id:4,materialCode:"WL004",materialName:"水管",model:"PPR-20",specification:"20mm",unit:"米",currentStock:1500,warningType:"overstock",warningThreshold:"1000",status:"disabled"},{id:5,materialCode:"WL005",materialName:"灯具",model:"LED-12W",specification:"12W",unit:"个",currentStock:3,warningType:"lowStock",warningThreshold:"10",status:"enabled"}]),U=i({id:0,materialCode:"",materialName:"",model:"",specification:"",unit:"",currentStock:0,warningType:"lowStock",warningThreshold:"",status:"enabled",remarks:""}),z=d({dateRange:["",""],warningType:""}),P=d({currentPage:1,pageSize:10,total:126}),N=i([{id:1,warningTime:"2024-01-15 10:30:25",materialCode:"WL001",materialName:"电缆线",warningType:"lowStock",currentStock:5,warningThreshold:"10",operator:"系统",status:"handled"},{id:2,warningTime:"2024-01-15 09:15:42",materialCode:"WL003",materialName:"电线",warningType:"lowStock",currentStock:8,warningThreshold:"10",operator:"系统",status:"handled"},{id:3,warningTime:"2024-01-14 16:22:18",materialCode:"WL002",materialName:"开关面板",warningType:"overstock",currentStock:1200,warningThreshold:"1000",operator:"系统",status:"handled"},{id:4,warningTime:"2024-01-14 14:05:33",materialCode:"WL005",materialName:"灯具",warningType:"lowStock",currentStock:3,warningThreshold:"10",operator:"系统",status:"unhandled"}]),x=i(!1),W=i(!1),j=i(),L=u(()=>U.value.id?"修改预警规则":"新增预警规则"),R={materialCode:[{required:!0,message:"请输入公司物料编码",trigger:"blur"}],materialName:[{required:!0,message:"请输入物料名称",trigger:"blur"}],unit:[{required:!0,message:"请输入单位",trigger:"blur"}],warningType:[{required:!0,message:"请选择预警类型",trigger:"blur"}],warningThreshold:[{required:!0,message:"请输入预警阈值",trigger:"blur"}]},Y=e=>{switch(e){case"lowStock":return"warning";case"overstock":return"danger";default:return"info"}},q=e=>{switch(e){case"lowStock":return"库存不足";case"overstock":return"库存积压";default:return""}},M=e=>{S.pageSize=e},O=e=>{S.currentPage=e},D=()=>{U.value={id:0,materialCode:"",materialName:"",model:"",specification:"",unit:"",currentStock:0,warningType:"lowStock",warningThreshold:"",status:"enabled",remarks:""},x.value=!0},F=e=>{U.value=((e,o)=>{for(var n in o||(o={}))l.call(o,n)&&r(e,n,o[n]);if(a)for(var n of a(o))t.call(o,n)&&r(e,n,o[n]);return e})({},e),x.value=!0},E=()=>{j.value&&j.value.validate(e=>{e&&(o.success("保存成功"),x.value=!1)})},I=()=>{j.value&&j.value.resetFields()},J=()=>{o.success("批量设置")},A=()=>{o.success("刷新预警")},B=()=>{W.value=!0},K=()=>{o.success("搜索历史")},Q=()=>{z.dateRange=["",""],z.warningType=""},$=e=>{P.pageSize=e},G=e=>{P.currentPage=e};return(e,a)=>{const l=c("el-col"),t=c("el-checkbox"),r=c("el-checkbox-group"),d=c("el-form-item"),i=c("el-option"),u=c("el-select"),H=c("el-input"),X=c("el-row"),Z=c("el-table-column"),ee=c("el-tag"),ae=c("el-button"),le=c("el-table"),te=c("el-pagination"),re=c("el-input-number"),oe=c("el-radio"),ne=c("el-radio-group"),de=c("el-form"),ie=c("el-dialog"),ue=c("el-date-picker"),se=c("el-card");return g(),s("div",k,[p(se,{class:"main-card"},{header:m(()=>a[25]||(a[25]=[_("div",{class:"card-header"},[_("span",null,"库存预警")],-1)])),default:m(()=>[p(X,{gutter:20,class:"form-section"},{default:m(()=>[p(l,{span:24},{default:m(()=>a[26]||(a[26]=[_("div",{class:"section-title"},"预警设置",-1)])),_:1,__:[26]}),p(l,{span:12},{default:m(()=>[p(d,{label:"预警方式:"},{default:m(()=>[p(r,{modelValue:n.warningMethods,"onUpdate:modelValue":a[0]||(a[0]=e=>n.warningMethods=e)},{default:m(()=>[p(t,{label:"system"},{default:m(()=>a[27]||(a[27]=[h("系统消息",-1)])),_:1,__:[27]}),p(t,{label:"email"},{default:m(()=>a[28]||(a[28]=[h("邮件通知",-1)])),_:1,__:[28]}),p(t,{label:"sms"},{default:m(()=>a[29]||(a[29]=[h("短信通知",-1)])),_:1,__:[29]}),p(t,{label:"wechat"},{default:m(()=>a[30]||(a[30]=[h("微信通知",-1)])),_:1,__:[30]})]),_:1},8,["modelValue"])]),_:1})]),_:1}),p(l,{span:12},{default:m(()=>[p(d,{label:"预警频率:"},{default:m(()=>[p(u,{modelValue:n.warningFrequency,"onUpdate:modelValue":a[1]||(a[1]=e=>n.warningFrequency=e),placeholder:"请选择预警频率",style:{width:"100%"}},{default:m(()=>[p(i,{label:"实时预警",value:"realtime"}),p(i,{label:"每日汇总",value:"daily"}),p(i,{label:"每周汇总",value:"weekly"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),p(l,{span:12},{default:m(()=>[p(d,{label:"库存不足预警:"},{default:m(()=>[p(H,{modelValue:n.lowStockThreshold,"onUpdate:modelValue":a[2]||(a[2]=e=>n.lowStockThreshold=e),placeholder:"请输入库存不足预警阈值"},{append:m(()=>a[31]||(a[31]=[h("件",-1)])),_:1},8,["modelValue"])]),_:1})]),_:1}),p(l,{span:12},{default:m(()=>[p(d,{label:"库存积压预警:"},{default:m(()=>[p(H,{modelValue:n.overstockThreshold,"onUpdate:modelValue":a[3]||(a[3]=e=>n.overstockThreshold=e),placeholder:"请输入库存积压预警阈值"},{append:m(()=>a[32]||(a[32]=[h("件",-1)])),_:1},8,["modelValue"])]),_:1})]),_:1}),p(l,{span:24},{default:m(()=>[p(d,{label:"接收人员:"},{default:m(()=>[p(u,{modelValue:n.recipients,"onUpdate:modelValue":a[4]||(a[4]=e=>n.recipients=e),multiple:"",placeholder:"请选择接收人员",style:{width:"100%"}},{default:m(()=>[(g(!0),s(w,null,f(y.value,e=>(g(),v(i,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),p(X,{gutter:20,class:"form-section"},{default:m(()=>[p(l,{span:24},{default:m(()=>a[33]||(a[33]=[_("div",{class:"section-title"},"预警规则",-1)])),_:1,__:[33]}),p(l,{span:24},{default:m(()=>[p(le,{data:C.value,border:"",class:"warning-rules-table"},{default:m(()=>[p(Z,{prop:"materialCode",label:"公司物料编码","min-width":"120"}),p(Z,{prop:"materialName",label:"物料名称","min-width":"120"}),p(Z,{prop:"model",label:"型号","min-width":"100"}),p(Z,{prop:"specification",label:"规格","min-width":"100"}),p(Z,{prop:"unit",label:"单位","min-width":"80"}),p(Z,{prop:"currentStock",label:"当前库存","min-width":"100"}),p(Z,{prop:"warningType",label:"预警类型","min-width":"100"},{default:m(e=>[p(ee,{type:Y(e.row.warningType)},{default:m(()=>[h(b(q(e.row.warningType)),1)]),_:2},1032,["type"])]),_:1}),p(Z,{prop:"warningThreshold",label:"预警阈值","min-width":"100"}),p(Z,{label:"操作","min-width":"150",fixed:"right"},{default:m(e=>[p(ae,{type:"primary",link:"",onClick:a=>F(e.row)},{default:m(()=>a[34]||(a[34]=[h("修改",-1)])),_:2,__:[34]},1032,["onClick"]),p(ae,{type:"primary",link:"",onClick:a=>(e=>{const a="enabled"===e.status?"disabled":"enabled";o.success(`已${"enabled"===a?"启用":"禁用"}规则`),e.status=a})(e.row)},{default:m(()=>[h(b("enabled"===e.row.status?"禁用":"启用"),1)]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1})]),_:1}),p(te,{"current-page":S.currentPage,"onUpdate:currentPage":a[5]||(a[5]=e=>S.currentPage=e),"page-size":S.pageSize,"onUpdate:pageSize":a[6]||(a[6]=e=>S.pageSize=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:S.total,onSizeChange:M,onCurrentChange:O,class:"pagination"},null,8,["current-page","page-size","total"]),_("div",V,[p(ae,{type:"primary",icon:"Plus",onClick:D},{default:m(()=>a[35]||(a[35]=[h("新增规则",-1)])),_:1,__:[35]}),p(ae,{icon:"Setting",onClick:J},{default:m(()=>a[36]||(a[36]=[h("批量设置",-1)])),_:1,__:[36]}),p(ae,{icon:"Refresh",onClick:A},{default:m(()=>a[37]||(a[37]=[h("刷新预警",-1)])),_:1,__:[37]}),p(ae,{icon:"DataAnalysis",onClick:B},{default:m(()=>a[38]||(a[38]=[h("预警历史",-1)])),_:1,__:[38]})]),p(ie,{modelValue:x.value,"onUpdate:modelValue":a[18]||(a[18]=e=>x.value=e),title:L.value,width:"600",onClose:I},{footer:m(()=>[p(ae,{onClick:a[17]||(a[17]=e=>x.value=!1)},{default:m(()=>a[42]||(a[42]=[h("取消",-1)])),_:1,__:[42]}),p(ae,{type:"primary",onClick:E},{default:m(()=>a[43]||(a[43]=[h("保存",-1)])),_:1,__:[43]})]),default:m(()=>[p(de,{ref_key:"ruleFormRef",ref:j,model:U.value,rules:R,"label-width":"120px"},{default:m(()=>[p(X,{gutter:20},{default:m(()=>[p(l,{span:12},{default:m(()=>[p(d,{label:"公司物料编码:",prop:"materialCode"},{default:m(()=>[p(H,{modelValue:U.value.materialCode,"onUpdate:modelValue":a[7]||(a[7]=e=>U.value.materialCode=e),placeholder:"请输入公司物料编码"},null,8,["modelValue"])]),_:1})]),_:1}),p(l,{span:12},{default:m(()=>[p(d,{label:"物料名称:",prop:"materialName"},{default:m(()=>[p(H,{modelValue:U.value.materialName,"onUpdate:modelValue":a[8]||(a[8]=e=>U.value.materialName=e),placeholder:"请输入物料名称"},null,8,["modelValue"])]),_:1})]),_:1}),p(l,{span:12},{default:m(()=>[p(d,{label:"型号:",prop:"model"},{default:m(()=>[p(H,{modelValue:U.value.model,"onUpdate:modelValue":a[9]||(a[9]=e=>U.value.model=e),placeholder:"请输入型号"},null,8,["modelValue"])]),_:1})]),_:1}),p(l,{span:12},{default:m(()=>[p(d,{label:"规格:",prop:"specification"},{default:m(()=>[p(H,{modelValue:U.value.specification,"onUpdate:modelValue":a[10]||(a[10]=e=>U.value.specification=e),placeholder:"请输入规格"},null,8,["modelValue"])]),_:1})]),_:1}),p(l,{span:12},{default:m(()=>[p(d,{label:"单位:",prop:"unit"},{default:m(()=>[p(H,{modelValue:U.value.unit,"onUpdate:modelValue":a[11]||(a[11]=e=>U.value.unit=e),placeholder:"请输入单位"},null,8,["modelValue"])]),_:1})]),_:1}),p(l,{span:12},{default:m(()=>[p(d,{label:"当前库存:",prop:"currentStock"},{default:m(()=>[p(re,{modelValue:U.value.currentStock,"onUpdate:modelValue":a[12]||(a[12]=e=>U.value.currentStock=e),min:0,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),p(l,{span:12},{default:m(()=>[p(d,{label:"预警类型:",prop:"warningType"},{default:m(()=>[p(u,{modelValue:U.value.warningType,"onUpdate:modelValue":a[13]||(a[13]=e=>U.value.warningType=e),placeholder:"请选择预警类型",style:{width:"100%"}},{default:m(()=>[p(i,{label:"库存不足",value:"lowStock"}),p(i,{label:"库存积压",value:"overstock"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),p(l,{span:12},{default:m(()=>[p(d,{label:"预警阈值:",prop:"warningThreshold"},{default:m(()=>[p(H,{modelValue:U.value.warningThreshold,"onUpdate:modelValue":a[14]||(a[14]=e=>U.value.warningThreshold=e),placeholder:"请输入预警阈值"},{append:m(()=>a[39]||(a[39]=[h("件",-1)])),_:1},8,["modelValue"])]),_:1})]),_:1}),p(l,{span:12},{default:m(()=>[p(d,{label:"状态:",prop:"status"},{default:m(()=>[p(ne,{modelValue:U.value.status,"onUpdate:modelValue":a[15]||(a[15]=e=>U.value.status=e)},{default:m(()=>[p(oe,{label:"enabled"},{default:m(()=>a[40]||(a[40]=[h("启用",-1)])),_:1,__:[40]}),p(oe,{label:"disabled"},{default:m(()=>a[41]||(a[41]=[h("禁用",-1)])),_:1,__:[41]})]),_:1},8,["modelValue"])]),_:1})]),_:1}),p(l,{span:24},{default:m(()=>[p(d,{label:"备注:"},{default:m(()=>[p(H,{modelValue:U.value.remarks,"onUpdate:modelValue":a[16]||(a[16]=e=>U.value.remarks=e),type:"textarea",rows:2,placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),p(ie,{modelValue:W.value,"onUpdate:modelValue":a[24]||(a[24]=e=>W.value=e),title:"预警历史",width:"800"},{footer:m(()=>[p(ae,{onClick:a[23]||(a[23]=e=>W.value=!1)},{default:m(()=>a[46]||(a[46]=[h("关闭",-1)])),_:1,__:[46]})]),default:m(()=>[p(X,{gutter:20,class:"history-search"},{default:m(()=>[p(l,{span:8},{default:m(()=>[p(ue,{modelValue:z.dateRange,"onUpdate:modelValue":a[19]||(a[19]=e=>z.dateRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),p(l,{span:8},{default:m(()=>[p(u,{modelValue:z.warningType,"onUpdate:modelValue":a[20]||(a[20]=e=>z.warningType=e),placeholder:"预警类型",clearable:"",style:{width:"100%"}},{default:m(()=>[p(i,{label:"库存不足",value:"lowStock"}),p(i,{label:"库存积压",value:"overstock"})]),_:1},8,["modelValue"])]),_:1}),p(l,{span:8},{default:m(()=>[_("div",T,[p(ae,{type:"primary",icon:"Search",onClick:K},{default:m(()=>a[44]||(a[44]=[h("搜索",-1)])),_:1,__:[44]}),p(ae,{icon:"Refresh",onClick:Q},{default:m(()=>a[45]||(a[45]=[h("重置",-1)])),_:1,__:[45]})])]),_:1})]),_:1}),p(le,{data:N.value,border:"",class:"history-table",style:{"margin-top":"20px"}},{default:m(()=>[p(Z,{prop:"warningTime",label:"预警时间","min-width":"150"}),p(Z,{prop:"materialCode",label:"公司物料编码","min-width":"120"}),p(Z,{prop:"materialName",label:"物料名称","min-width":"120"}),p(Z,{prop:"warningType",label:"预警类型","min-width":"100"},{default:m(e=>[p(ee,{type:Y(e.row.warningType)},{default:m(()=>[h(b(q(e.row.warningType)),1)]),_:2},1032,["type"])]),_:1}),p(Z,{prop:"currentStock",label:"当前库存","min-width":"100"}),p(Z,{prop:"warningThreshold",label:"预警阈值","min-width":"100"}),p(Z,{prop:"operator",label:"处理人","min-width":"100"}),p(Z,{prop:"status",label:"处理状态","min-width":"100"},{default:m(e=>[p(ee,{type:"handled"===e.row.status?"success":"warning"},{default:m(()=>[h(b("handled"===e.row.status?"已处理":"未处理"),1)]),_:2},1032,["type"])]),_:1})]),_:1},8,["data"]),p(te,{"current-page":P.currentPage,"onUpdate:currentPage":a[21]||(a[21]=e=>P.currentPage=e),"page-size":P.pageSize,"onUpdate:pageSize":a[22]||(a[22]=e=>P.pageSize=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:P.total,onSizeChange:$,onCurrentChange:G,class:"pagination",style:{"margin-top":"20px","text-align":"right"}},null,8,["current-page","page-size","total"])]),_:1},8,["modelValue"])]),_:1})])}}}),[["__scopeId","data-v-90dde593"]]);export{S as default};
