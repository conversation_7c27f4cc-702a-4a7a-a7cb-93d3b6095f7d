var e=(e,l,a)=>new Promise((t,s)=>{var r=e=>{try{i(a.next(e))}catch(l){s(l)}},o=e=>{try{i(a.throw(e))}catch(l){s(l)}},i=e=>e.done?t(e.value):Promise.resolve(e.value).then(r,o);i((a=a.apply(e,l)).next())});import{p as l,r as a,E as t,e as s}from"./element-plus-ad78a7bf.js";import{l as r,r as o,_ as i,q as d,y as u,R as c,J as n,av as p,x as m,O as b,z as v,u as h,P as _}from"./vue-vendor-fc5a6493.js";import{_ as f}from"./_plugin-vue_export-helper-1b428a4d.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const y={class:"permission-management"},w={class:"card-header"},g={class:"header-actions"},k={class:"dialog-footer"},C={class:"permission-tree"},j={class:"dialog-footer"},x=f(r({__name:"PermissionManagement",setup(r){const f=o(),x=o(),D=o(!1),N=o(!1),V=o(""),T=o(!1),I=o(null),R=i({roleName:"",roleDescription:""}),z={roleName:[{required:!0,message:"请输入角色名称",trigger:"blur"}],roleDescription:[{required:!0,message:"请输入角色描述",trigger:"blur"}]},P=o([{roleId:"R001",roleName:"系统管理员",roleDescription:"拥有系统所有权限",userCount:2,status:"active",createTime:"2024-01-01 10:00:00"},{roleId:"R002",roleName:"仓库管理员",roleDescription:"负责仓库物料管理",userCount:5,status:"active",createTime:"2024-01-02 10:00:00"},{roleId:"R003",roleName:"工程负责人",roleDescription:"负责工程项目管理",userCount:8,status:"active",createTime:"2024-01-03 10:00:00"},{roleId:"R004",roleName:"普通员工",roleDescription:"基础操作权限",userCount:15,status:"active",createTime:"2024-01-04 10:00:00"}]),U={children:"children",label:"label"},q=o([{id:"dashboard",label:"首页",children:[{id:"dashboard.view",label:"查看首页"},{id:"dashboard.statistics",label:"查看统计"}]},{id:"warehouse",label:"仓库管理",children:[{id:"warehouse.view",label:"查看物料"},{id:"warehouse.add",label:"添加物料"},{id:"warehouse.edit",label:"编辑物料"},{id:"warehouse.delete",label:"删除物料"},{id:"warehouse.inbound",label:"物料入库"},{id:"warehouse.outbound",label:"物料出库"}]},{id:"projects",label:"工程订单",children:[{id:"projects.view",label:"查看工程"},{id:"projects.add",label:"新建工程"},{id:"projects.edit",label:"编辑工程"},{id:"projects.delete",label:"删除工程"},{id:"projects.assign",label:"甲方派单"},{id:"projects.progress",label:"工程推进"}]},{id:"loose",label:"散户订单",children:[{id:"loose.view",label:"查看订单"},{id:"loose.add",label:"新建订单"},{id:"loose.edit",label:"编辑订单"},{id:"loose.balance",label:"月度平账"}]},{id:"employees",label:"员工管理",children:[{id:"employees.view",label:"查看员工"},{id:"employees.add",label:"添加员工"},{id:"employees.edit",label:"编辑员工"},{id:"employees.delete",label:"删除员工"}]},{id:"system",label:"系统设置",children:[{id:"system.user",label:"用户管理"},{id:"system.permission",label:"权限管理"},{id:"system.log",label:"系统日志"},{id:"system.backup",label:"数据备份"}]}]),$=o([]),B=()=>{t.success("数据已刷新")},E=()=>{V.value="新增角色",T.value=!1,Object.keys(R).forEach(e=>{R[e]=""}),D.value=!0},K=l=>e(this,null,function*(){try{const e="active"===l.status?"禁用":"启用";yield s.confirm(`确定要${e}角色"${l.roleName}"吗？`,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),l.status="active"===l.status?"inactive":"active",t.success(`角色${e}成功`)}catch(e){}}),O=()=>e(this,null,function*(){if(f.value)try{yield f.value.validate(),t.success(T.value?"角色更新成功":"角色创建成功"),D.value=!1}catch(e){t.error("请检查表单填写是否正确")}}),F=()=>{x.value.getCheckedKeys(),x.value.getHalfCheckedKeys();t.success("权限设置成功"),N.value=!1};return d(()=>{}),(e,t)=>{const s=p("el-breadcrumb-item"),r=p("el-breadcrumb"),o=p("el-icon"),i=p("el-button"),d=p("el-table-column"),H=p("el-tag"),J=p("el-table"),L=p("el-card"),M=p("el-input"),A=p("el-form-item"),G=p("el-form"),Q=p("el-dialog"),S=p("el-tree");return m(),u("div",y,[c(r,{class:"breadcrumb",separator:">"},{default:n(()=>[c(s,{to:{path:"/dashboard"}},{default:n(()=>t[6]||(t[6]=[b("首页",-1)])),_:1,__:[6]}),c(s,{to:{path:"/system/user-management"}},{default:n(()=>t[7]||(t[7]=[b("系统设置",-1)])),_:1,__:[7]}),c(s,null,{default:n(()=>t[8]||(t[8]=[b("权限管理",-1)])),_:1,__:[8]})]),_:1}),c(L,{class:"main-card"},{header:n(()=>[v("div",w,[t[11]||(t[11]=v("span",null,"🔐 权限管理",-1)),v("div",g,[c(i,{type:"primary",size:"small",onClick:E},{default:n(()=>[c(o,null,{default:n(()=>[c(h(l))]),_:1}),t[9]||(t[9]=b(" 新增角色 ",-1))]),_:1,__:[9]}),c(i,{type:"success",size:"small",onClick:B},{default:n(()=>[c(o,null,{default:n(()=>[c(h(a))]),_:1}),t[10]||(t[10]=b(" 刷新 ",-1))]),_:1,__:[10]})])])]),default:n(()=>[c(J,{data:P.value,border:"",style:{width:"100%"}},{default:n(()=>[c(d,{prop:"roleId",label:"角色ID",width:"100"}),c(d,{prop:"roleName",label:"角色名称",width:"150"}),c(d,{prop:"roleDescription",label:"角色描述","min-width":"200"}),c(d,{prop:"userCount",label:"用户数量",width:"100",align:"center"}),c(d,{prop:"status",label:"状态",width:"100"},{default:n(({row:e})=>[c(H,{type:"active"===e.status?"success":"danger"},{default:n(()=>[b(_("active"===e.status?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),c(d,{prop:"createTime",label:"创建时间",width:"180"}),c(d,{label:"操作",width:"200",fixed:"right"},{default:n(({row:e})=>[c(i,{type:"primary",size:"small",onClick:l=>(e=>{V.value="编辑角色",T.value=!0,I.value=e,R.roleName=e.roleName,R.roleDescription=e.roleDescription,D.value=!0})(e)},{default:n(()=>t[12]||(t[12]=[b(" 编辑 ",-1)])),_:2,__:[12]},1032,["onClick"]),c(i,{type:"info",size:"small",onClick:l=>(e=>{I.value=e,$.value=["dashboard.view","warehouse.view","warehouse.add","projects.view"],N.value=!0})(e)},{default:n(()=>t[13]||(t[13]=[b(" 权限 ",-1)])),_:2,__:[13]},1032,["onClick"]),c(i,{type:"active"===e.status?"warning":"success",size:"small",onClick:l=>K(e)},{default:n(()=>[b(_("active"===e.status?"禁用":"启用"),1)]),_:2},1032,["type","onClick"])]),_:1})]),_:1},8,["data"])]),_:1}),c(Q,{modelValue:D.value,"onUpdate:modelValue":t[3]||(t[3]=e=>D.value=e),title:V.value,width:"500px"},{footer:n(()=>[v("div",k,[c(i,{onClick:t[2]||(t[2]=e=>D.value=!1)},{default:n(()=>t[14]||(t[14]=[b("取消",-1)])),_:1,__:[14]}),c(i,{type:"primary",onClick:O},{default:n(()=>t[15]||(t[15]=[b("确定",-1)])),_:1,__:[15]})])]),default:n(()=>[c(G,{model:R,rules:z,ref_key:"roleFormRef",ref:f,"label-width":"100px"},{default:n(()=>[c(A,{label:"角色名称:",prop:"roleName"},{default:n(()=>[c(M,{modelValue:R.roleName,"onUpdate:modelValue":t[0]||(t[0]=e=>R.roleName=e),placeholder:"请输入角色名称"},null,8,["modelValue"])]),_:1}),c(A,{label:"角色描述:",prop:"roleDescription"},{default:n(()=>[c(M,{modelValue:R.roleDescription,"onUpdate:modelValue":t[1]||(t[1]=e=>R.roleDescription=e),type:"textarea",rows:3,placeholder:"请输入角色描述"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),c(Q,{modelValue:N.value,"onUpdate:modelValue":t[5]||(t[5]=e=>N.value=e),title:"权限设置",width:"600px"},{footer:n(()=>[v("div",j,[c(i,{onClick:t[4]||(t[4]=e=>N.value=!1)},{default:n(()=>t[16]||(t[16]=[b("取消",-1)])),_:1,__:[16]}),c(i,{type:"primary",onClick:F},{default:n(()=>t[17]||(t[17]=[b("确定",-1)])),_:1,__:[17]})])]),default:n(()=>[v("div",C,[c(S,{ref_key:"permissionTreeRef",ref:x,data:q.value,props:U,"show-checkbox":"","node-key":"id","default-checked-keys":$.value,"default-expand-all":!0},null,8,["data","default-checked-keys"])])]),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-b4fe9c3b"]]);export{x as default};
