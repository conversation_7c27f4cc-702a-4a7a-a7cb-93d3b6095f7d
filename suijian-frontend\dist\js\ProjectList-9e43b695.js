var e=Object.defineProperty,a=Object.getOwnPropertySymbols,t=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable,r=(a,t,l)=>t in a?e(a,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):a[t]=l;import{E as s}from"./element-plus-ad78a7bf.js";import{l as o,_ as d,r as i,y as u,R as p,J as n,av as c,x as m,z as f,O as _,P as y,Q as j,aa as b}from"./vue-vendor-fc5a6493.js";import{_ as g}from"./_plugin-vue_export-helper-1b428a4d.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const v={class:"project-list-container"},S={class:"search-buttons"},h={class:"form-actions"},w=g(o({__name:"ProjectList",setup(e){const o=d({partyOrderNo:"",projectName:"",projectAddress:"",projectStatus:"",materialCode:""}),g=d({currentPage:1,pageSize:10,total:0}),w=i([{id:1,partyOrderNo:"GC202401001",projectName:"阳光小区A栋",projectAddress:"阳光小区A栋",projectStatus:"building",estimatedStartTime:"2024-01-10",estimatedEndTime:"2024-03-15",progress:"45%",actualStartTime:"2024-01-12",staffList:[{workType:"电工",name:"李师傅",dailyWage:300},{workType:"水工",name:"王师傅",dailyWage:280},{workType:"安装工",name:"张师傅",dailyWage:320}],materialList:[{name:"电缆线",quantity:200,unit:"米"},{name:"开关面板",quantity:50,unit:"个"},{name:"插座",quantity:100,unit:"个"}],remarks:"需要注意地下管线布局"},{id:2,partyOrderNo:"GC202401002",projectName:"花园广场项目",projectAddress:"花园广场B区",projectStatus:"notStarted",estimatedStartTime:"2024-02-01",estimatedEndTime:"2024-04-30",progress:"0%",actualStartTime:"",staffList:[],materialList:[],remarks:""},{id:3,partyOrderNo:"GC202401003",projectName:"商业中心B区",projectAddress:"商业街88号",projectStatus:"paused",estimatedStartTime:"2023-12-01",estimatedEndTime:"2024-02-28",progress:"65%",actualStartTime:"2023-12-01",staffList:[],materialList:[],remarks:""},{id:4,partyOrderNo:"GC202401004",projectName:"住宅楼C座",projectAddress:"住宅区C栋",projectStatus:"completed",estimatedStartTime:"2023-10-01",estimatedEndTime:"2023-12-31",progress:"100%",actualStartTime:"2023-10-01",staffList:[],materialList:[],remarks:""}]),k=i({id:0,partyOrderNo:"",projectName:"",projectAddress:"",projectStatus:"",estimatedStartTime:"",estimatedEndTime:"",progress:"",actualStartTime:"",staffList:[],materialList:[],remarks:""}),C=i(!1),T=e=>{switch(e){case"notStarted":default:return"info";case"building":return"primary";case"paused":return"warning";case"completed":return"success"}},N=e=>{switch(e){case"notStarted":return"未开始";case"building":return"在建";case"paused":return"暂停";case"completed":return"完成";default:return""}},O=()=>{s.success("搜索成功")},V=()=>{o.partyOrderNo="",o.projectName="",o.projectAddress="",o.projectStatus="",o.materialCode=""},L=e=>{g.pageSize=e},A=e=>{g.currentPage=e},P=e=>{k.value=((e,s)=>{for(var o in s||(s={}))t.call(s,o)&&r(e,o,s[o]);if(a)for(var o of a(s))l.call(s,o)&&r(e,o,s[o]);return e})({},e),C.value=!0},z=()=>{s.success("跳转到新增订单页面")},E=()=>{s.success("导出Excel")},x=()=>{s.success("显示统计图表")};return(e,a)=>{const t=c("el-input"),l=c("el-col"),r=c("el-option"),d=c("el-select"),i=c("el-row"),U=c("el-button"),q=c("el-table-column"),G=c("el-tag"),W=c("el-table"),B=c("el-pagination"),I=c("el-form-item"),R=c("el-divider"),D=c("el-form"),J=c("el-dialog"),Q=c("el-card");return m(),u("div",v,[p(Q,{class:"main-card"},{header:n(()=>a[9]||(a[9]=[f("div",{class:"card-header"},[f("span",null,"工程订单列表")],-1)])),default:n(()=>[p(i,{gutter:20,class:"search-section"},{default:n(()=>[p(l,{span:6},{default:n(()=>[p(t,{modelValue:o.partyOrderNo,"onUpdate:modelValue":a[0]||(a[0]=e=>o.partyOrderNo=e),placeholder:"甲方订单号",clearable:""},null,8,["modelValue"])]),_:1}),p(l,{span:6},{default:n(()=>[p(t,{modelValue:o.projectName,"onUpdate:modelValue":a[1]||(a[1]=e=>o.projectName=e),placeholder:"工程名称",clearable:""},null,8,["modelValue"])]),_:1}),p(l,{span:6},{default:n(()=>[p(t,{modelValue:o.projectAddress,"onUpdate:modelValue":a[2]||(a[2]=e=>o.projectAddress=e),placeholder:"工程地址",clearable:""},null,8,["modelValue"])]),_:1}),p(l,{span:6},{default:n(()=>[p(d,{modelValue:o.projectStatus,"onUpdate:modelValue":a[3]||(a[3]=e=>o.projectStatus=e),placeholder:"工程状态",clearable:"",style:{width:"100%"}},{default:n(()=>[p(r,{label:"未开始",value:"notStarted"}),p(r,{label:"在建",value:"building"}),p(r,{label:"暂停",value:"paused"}),p(r,{label:"完成",value:"completed"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),p(i,{gutter:20,class:"search-section",style:{"margin-top":"15px"}},{default:n(()=>[p(l,{span:6},{default:n(()=>[p(t,{modelValue:o.materialCode,"onUpdate:modelValue":a[4]||(a[4]=e=>o.materialCode=e),placeholder:"物料编码",clearable:""},null,8,["modelValue"])]),_:1}),p(l,{span:12},{default:n(()=>[f("div",S,[p(U,{type:"primary",icon:"Search",onClick:O},{default:n(()=>a[10]||(a[10]=[_("搜索",-1)])),_:1,__:[10]}),p(U,{icon:"Refresh",onClick:V},{default:n(()=>a[11]||(a[11]=[_("重置",-1)])),_:1,__:[11]})])]),_:1})]),_:1}),p(W,{data:w.value,border:"",class:"project-table",style:{width:"100%","margin-top":"20px"}},{default:n(()=>[p(q,{prop:"partyOrderNo",label:"甲方订单号","min-width":"120"}),p(q,{prop:"projectName",label:"工程名称","min-width":"120"}),p(q,{prop:"projectAddress",label:"工程地址","min-width":"150"}),p(q,{prop:"projectStatus",label:"工程状态","min-width":"80"},{default:n(e=>[p(G,{type:T(e.row.projectStatus)},{default:n(()=>[_(y(N(e.row.projectStatus)),1)]),_:2},1032,["type"])]),_:1}),p(q,{prop:"estimatedStartTime",label:"预估开始时间","min-width":"120"}),p(q,{prop:"estimatedEndTime",label:"预估结束时间","min-width":"120"}),p(q,{prop:"progress",label:"进度","min-width":"80"}),p(q,{label:"操作","min-width":"120",fixed:"right"},{default:n(e=>[p(U,{type:"primary",link:"",onClick:a=>P(e.row)},{default:n(()=>a[12]||(a[12]=[_("详情",-1)])),_:2,__:[12]},1032,["onClick"]),p(U,{type:"primary",link:"",onClick:a=>(e.row,void s.success("跳转到工程推进页面"))},{default:n(()=>a[13]||(a[13]=[_("推进",-1)])),_:2,__:[13]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),p(B,{"current-page":g.currentPage,"onUpdate:currentPage":a[5]||(a[5]=e=>g.currentPage=e),"page-size":g.pageSize,"onUpdate:pageSize":a[6]||(a[6]=e=>g.pageSize=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:g.total,onSizeChange:L,onCurrentChange:A,class:"pagination"},null,8,["current-page","page-size","total"]),f("div",h,[p(U,{type:"primary",icon:"Plus",onClick:z},{default:n(()=>a[14]||(a[14]=[_("新增订单",-1)])),_:1,__:[14]}),p(U,{icon:"Download",onClick:E},{default:n(()=>a[15]||(a[15]=[_("导出Excel",-1)])),_:1,__:[15]}),p(U,{icon:"PieChart",onClick:x},{default:n(()=>a[16]||(a[16]=[_("统计图表",-1)])),_:1,__:[16]})]),p(J,{modelValue:C.value,"onUpdate:modelValue":a[8]||(a[8]=e=>C.value=e),title:"工程详情",width:"600"},{footer:n(()=>[p(U,{onClick:a[7]||(a[7]=e=>C.value=!1)},{default:n(()=>a[17]||(a[17]=[_("关闭",-1)])),_:1,__:[17]})]),default:n(()=>[p(D,{model:k.value,"label-width":"100px"},{default:n(()=>[p(I,{label:"甲方订单号:"},{default:n(()=>[f("span",null,y(k.value.partyOrderNo),1)]),_:1}),p(I,{label:"工程名称:"},{default:n(()=>[f("span",null,y(k.value.projectName),1)]),_:1}),p(I,{label:"工程地址:"},{default:n(()=>[f("span",null,y(k.value.projectAddress),1)]),_:1}),p(I,{label:"工程状态:"},{default:n(()=>[p(G,{type:T(k.value.projectStatus)},{default:n(()=>[_(y(N(k.value.projectStatus)),1)]),_:1},8,["type"])]),_:1}),p(I,{label:"预估开始时间:"},{default:n(()=>[f("span",null,y(k.value.estimatedStartTime),1)]),_:1}),p(I,{label:"预估结束时间:"},{default:n(()=>[f("span",null,y(k.value.estimatedEndTime),1)]),_:1}),p(I,{label:"实际开始时间:"},{default:n(()=>[f("span",null,y(k.value.actualStartTime),1)]),_:1}),p(I,{label:"当前进度:"},{default:n(()=>[f("span",null,y(k.value.progress),1)]),_:1}),p(R),p(I,{label:"人员列表:"},{default:n(()=>[(m(!0),u(j,null,b(k.value.staffList,(e,a)=>(m(),u("div",{key:a}," - "+y(e.workType)+": "+y(e.name)+"("+y(e.dailyWage)+"元/天) ",1))),128))]),_:1}),p(I,{label:"物料清单:"},{default:n(()=>[(m(!0),u(j,null,b(k.value.materialList,(e,a)=>(m(),u("div",{key:a}," - "+y(e.name)+" "+y(e.quantity)+y(e.unit),1))),128))]),_:1}),p(I,{label:"备注:"},{default:n(()=>[f("span",null,y(k.value.remarks),1)]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])]),_:1})])}}}),[["__scopeId","data-v-f76a2e3f"]]);export{w as default};
