// 全局样式重置
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  width: 100%;
  overflow-x: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  height: 100%;
  width: 100%;
  overflow-x: hidden;
}

// 全局变量
:root {
  --primary-color: #409eff;
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  --info-color: #909399;
  
  --text-color: #303133;
  --text-color-secondary: #606266;
  --text-color-placeholder: #c0c4cc;
  
  --border-color: #dcdfe6;
  --border-color-light: #e4e7ed;
  
  --background-color: #f5f7fa;
  --background-color-light: #fafafa;
}

// 工具类
.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.mt-10 { margin-top: 10px; }
.mt-20 { margin-top: 20px; }
.mb-10 { margin-bottom: 10px; }
.mb-20 { margin-bottom: 20px; }
.ml-10 { margin-left: 10px; }
.mr-10 { margin-right: 10px; }

.p-10 { padding: 10px; }
.p-20 { padding: 20px; }
.pt-10 { padding-top: 10px; }
.pb-10 { padding-bottom: 10px; }
.pl-10 { padding-left: 10px; }
.pr-10 { padding-right: 10px; }

// 页面容器
.page-container {
  padding: 20px;
  background-color: var(--background-color);
  min-height: calc(100vh - 60px);
}

// 卡片样式
.card {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

// 表格样式
.table-container {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

// 表单样式
.form-container {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;
}

// 搜索表单
.search-form {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

// 操作按钮组
.action-buttons {
  margin-bottom: 20px;
  
  .el-button {
    margin-right: 10px;
  }
}

// 分页容器
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

// 加载状态
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

// 空状态
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--text-color-secondary);
  
  .empty-icon {
    font-size: 48px;
    margin-bottom: 10px;
  }
  
  .empty-text {
    font-size: 14px;
  }
} 