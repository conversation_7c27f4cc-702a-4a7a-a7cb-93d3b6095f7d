<template>
  <div class="material-statistics">

    <!-- 总体统计 -->
    <el-card class="summary-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>📊 总体统计</span>
        </div>
      </template>
      <div class="summary-content">
        <div class="summary-item">
          <span class="label">📦 已领出物料总计:</span>
          <span class="value">{{ totalItems }}项</span>
          <span class="amount">总价: ¥{{ formatNumber(totalAmount) }}</span>
        </div>
      </div>
    </el-card>

    <!-- 未使用物料统计 -->
    <el-card class="statistics-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>🔴 未使用物料统计</span>
          <div class="header-actions">
            <el-button type="primary" size="small" @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新数据
            </el-button>
            <el-button type="success" size="small" @click="exportData">
              <el-icon><Download /></el-icon>
              导出统计
            </el-button>
          </div>
        </div>
      </template>
      
      <div class="statistics-summary">
        <span>{{ unusedMaterials.length }}项，数量: {{ formatNumber(unusedTotalQuantity) }}</span>
        <span>总价: ¥{{ formatNumber(unusedTotalAmount) }}</span>
      </div>

      <el-table :data="paginatedUnusedMaterials" style="width: 100%" border>
        <el-table-column prop="icon" label="" width="50" align="center">
          <template #default="{ row }">
            <span class="material-icon">{{ row.icon }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="名称分类" width="150" />
        <el-table-column prop="model" label="型号" width="120" />
        <el-table-column prop="specification" label="规格" width="120" />
        <el-table-column prop="unit" label="单位" width="80" align="center" />
        <el-table-column prop="quantity" label="数量" width="100" align="center" />
        <el-table-column prop="totalPrice" label="总价" width="120" align="center">
          <template #default="{ row }">
            ¥{{ formatNumber(row.totalPrice) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewDetails(row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="unusedPagination.currentPage"
          v-model:page-size="unusedPagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="unusedMaterials.length"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleUnusedSizeChange"
          @current-change="handleUnusedCurrentChange"
        />
      </div>
    </el-card>

    <!-- 已使用物料统计 -->
    <el-card class="statistics-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>✅ 已使用物料统计</span>
        </div>
      </template>
      
      <div class="statistics-summary">
        <span>{{ usedMaterials.length }}项，数量: {{ formatNumber(usedTotalQuantity) }}</span>
        <span>总价: ¥{{ formatNumber(usedTotalAmount) }}</span>
      </div>

      <el-table :data="paginatedUsedMaterials" style="width: 100%" border>
        <el-table-column prop="icon" label="" width="50" align="center">
          <template #default="{ row }">
            <span class="material-icon">{{ row.icon }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="名称分类" width="150" />
        <el-table-column prop="model" label="型号" width="120" />
        <el-table-column prop="specification" label="规格" width="120" />
        <el-table-column prop="unit" label="单位" width="80" align="center" />
        <el-table-column prop="quantity" label="数量" width="100" align="center" />
        <el-table-column prop="totalPrice" label="总价" width="120" align="center">
          <template #default="{ row }">
            ¥{{ formatNumber(row.totalPrice) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewDetails(row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="usedPagination.currentPage"
          v-model:page-size="usedPagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="usedMaterials.length"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleUsedSizeChange"
          @current-change="handleUsedCurrentChange"
        />
      </div>
    </el-card>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <el-button type="primary" size="large" @click="viewDetailedReport">
        📈 查看详细报表
      </el-button>
      <el-button type="default" size="large" @click="refreshData">
        🔄 刷新数据
      </el-button>
      <el-button type="success" size="large" @click="exportData">
        📊 导出统计
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, Download } from '@element-plus/icons-vue'

// 数据定义
interface Material {
  id: number
  name: string
  icon: string
  model: string
  specification: string
  quantity: number
  unit: string
  unitPrice: number
  totalPrice: number
  category: string
}

// 分页数据
const unusedPagination = reactive({
  currentPage: 1,
  pageSize: 10
})

const usedPagination = reactive({
  currentPage: 1,
  pageSize: 10
})

// 响应式数据
const unusedMaterials = ref<Material[]>([
  { id: 1, name: '燃气表', icon: '🔧', model: 'G2.5', specification: 'DN20', quantity: 50, unit: '个', unitPrice: 300, totalPrice: 15000, category: 'meter' },
  { id: 2, name: '镀锌管件', icon: '🔩', model: 'ZG-001', specification: '20*1.5', quantity: 200, unit: '个', unitPrice: 62.5, totalPrice: 12500, category: 'fitting' },
  { id: 3, name: '波纹管', icon: '⚡', model: 'BW-15', specification: 'DN15*500', quantity: 150, unit: 'm', unitPrice: 69.33, totalPrice: 10400, category: 'pipe' },
  { id: 4, name: '连接件', icon: '🔗', model: 'LJ-20', specification: '20mm', quantity: 80, unit: '个', unitPrice: 102.5, totalPrice: 8200, category: 'connector' },
  { id: 9, name: '阀门', icon: '🚰', model: 'FM-25', specification: 'DN25', quantity: 30, unit: '个', unitPrice: 150, totalPrice: 4500, category: 'valve' },
  { id: 10, name: '法兰', icon: '⭕', model: 'FL-32', specification: 'DN32', quantity: 25, unit: '个', unitPrice: 80, totalPrice: 2000, category: 'flange' }
])

const usedMaterials = ref<Material[]>([
  { id: 5, name: '燃气表', icon: '🔧', model: 'G2.5', specification: 'DN20', quantity: 30, unit: '个', unitPrice: 300, totalPrice: 9000, category: 'meter' },
  { id: 6, name: '镀锌管件', icon: '🔩', model: 'ZG-001', specification: '20*1.5', quantity: 120, unit: '个', unitPrice: 62.5, totalPrice: 7500, category: 'fitting' },
  { id: 7, name: '波纹管', icon: '⚡', model: 'BW-15', specification: 'DN15*500', quantity: 100, unit: 'm', unitPrice: 68, totalPrice: 6800, category: 'pipe' },
  { id: 8, name: '连接件', icon: '🔗', model: 'LJ-20', specification: '20mm', quantity: 60, unit: '个', unitPrice: 100, totalPrice: 6000, category: 'connector' },
  { id: 11, name: '阀门', icon: '🚰', model: 'FM-25', specification: 'DN25', quantity: 20, unit: '个', unitPrice: 150, totalPrice: 3000, category: 'valve' },
  { id: 12, name: '法兰', icon: '⭕', model: 'FL-32', specification: 'DN32', quantity: 15, unit: '个', unitPrice: 80, totalPrice: 1200, category: 'flange' }
])

// 计算属性
const totalItems = computed(() => unusedMaterials.value.length + usedMaterials.value.length)
const totalAmount = computed(() => unusedTotalAmount.value + usedTotalAmount.value)
const unusedTotalAmount = computed(() => unusedMaterials.value.reduce((sum, item) => sum + item.totalPrice, 0))
const usedTotalAmount = computed(() => usedMaterials.value.reduce((sum, item) => sum + item.totalPrice, 0))
const unusedTotalQuantity = computed(() => unusedMaterials.value.reduce((sum, item) => sum + item.quantity, 0))
const usedTotalQuantity = computed(() => usedMaterials.value.reduce((sum, item) => sum + item.quantity, 0))

// 分页计算属性
const paginatedUnusedMaterials = computed(() => {
  const start = (unusedPagination.currentPage - 1) * unusedPagination.pageSize
  const end = start + unusedPagination.pageSize
  return unusedMaterials.value.slice(start, end)
})

const paginatedUsedMaterials = computed(() => {
  const start = (usedPagination.currentPage - 1) * usedPagination.pageSize
  const end = start + usedPagination.pageSize
  return usedMaterials.value.slice(start, end)
})

// 方法
const formatNumber = (num: number): string => {
  return num.toLocaleString()
}

const refreshData = () => {
  ElMessage.success('数据已刷新')
  // 这里可以调用API刷新数据
}

const exportData = () => {
  ElMessage.success('数据导出成功')
  // 这里可以实现数据导出功能
}

const viewDetails = (material: Material) => {
  ElMessage.info(`查看 ${material.name} 的详细信息`)
  // 这里可以打开详情弹窗或跳转到详情页面
}

const viewDetailedReport = () => {
  ElMessage.info('跳转到详细报表页面')
  // 这里可以跳转到详细报表页面
}

// 分页处理方法
const handleUnusedSizeChange = (size: number) => {
  unusedPagination.pageSize = size
  unusedPagination.currentPage = 1
}

const handleUnusedCurrentChange = (page: number) => {
  unusedPagination.currentPage = page
}

const handleUsedSizeChange = (size: number) => {
  usedPagination.pageSize = size
  usedPagination.currentPage = 1
}

const handleUsedCurrentChange = (page: number) => {
  usedPagination.currentPage = page
}

// 生命周期
onMounted(() => {
  // 页面加载时获取数据
})
</script>

<style scoped>
.material-statistics {
  padding: 20px;
}

.breadcrumb {
  margin-bottom: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: bold;
  margin: 0 0 8px 0;
  color: #303133;
}

.page-description {
  color: #606266;
  margin: 0;
}

.summary-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.summary-content {
  padding: 10px 0;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 16px;
}

.label {
  color: #606266;
}

.value {
  font-weight: bold;
  color: #409EFF;
}

.amount {
  font-weight: bold;
  color: #67C23A;
}

.statistics-card {
  margin-bottom: 20px;
}

.statistics-summary {
  display: flex;
  gap: 30px;
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  font-weight: bold;
}

.material-icon {
  font-size: 18px;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 30px;
}

.pagination-section {
  margin-top: 20px;
  text-align: right;
}
</style>
