var __defProp=Object.defineProperty,__defProps=Object.defineProperties,__getOwnPropDescs=Object.getOwnPropertyDescriptors,__getOwnPropSymbols=Object.getOwnPropertySymbols,__hasOwnProp=Object.prototype.hasOwnProperty,__propIsEnum=Object.prototype.propertyIsEnumerable,__defNormalProp=(e,t,n)=>t in e?__defProp(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,__spreadValues=(e,t)=>{for(var n in t||(t={}))__hasOwnProp.call(t,n)&&__defNormalProp(e,n,t[n]);if(__getOwnPropSymbols)for(var n of __getOwnPropSymbols(t))__propIsEnum.call(t,n)&&__defNormalProp(e,n,t[n]);return e},__spreadProps=(e,t)=>__defProps(e,__getOwnPropDescs(t)),__objRest=(e,t)=>{var n={};for(var r in e)__hasOwnProp.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&__getOwnPropSymbols)for(var r of __getOwnPropSymbols(e))t.indexOf(r)<0&&__propIsEnum.call(e,r)&&(n[r]=e[r]);return n},__async=(e,t,n)=>new Promise((r,o)=>{var a=e=>{try{s(n.next(e))}catch(t){o(t)}},i=e=>{try{s(n.throw(e))}catch(t){o(t)}},s=e=>e.done?r(e.value):Promise.resolve(e.value).then(a,i);s((n=n.apply(e,t)).next())});import{E as getDefaultExportFromCjs,F as commonjsGlobal}from"./utils-c1f3ec4c.js";var E$1="top",R="bottom",W="right",P$1="left",me="auto",G=[E$1,R,W,P$1],U$1="start",J="end",Xe="clippingParents",je="viewport",K="popper",Ye="reference",De=G.reduce(function(e,t){return e.concat([t+"-"+U$1,t+"-"+J])},[]),Ee=[].concat(G,[me]).reduce(function(e,t){return e.concat([t,t+"-"+U$1,t+"-"+J])},[]),Ge="beforeRead",Je="read",Ke="afterRead",Qe="beforeMain",Ze="main",et="afterMain",tt="beforeWrite",nt="write",rt="afterWrite",ot=[Ge,Je,Ke,Qe,Ze,et,tt,nt,rt];function C(e){return e?(e.nodeName||"").toLowerCase():null}function H(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function Q(e){return e instanceof H(e).Element||e instanceof Element}function B(e){return e instanceof H(e).HTMLElement||e instanceof HTMLElement}function Pe(e){return"undefined"!=typeof ShadowRoot&&(e instanceof H(e).ShadowRoot||e instanceof ShadowRoot)}function Mt(e){var t=e.state;Object.keys(t.elements).forEach(function(e){var n=t.styles[e]||{},r=t.attributes[e]||{},o=t.elements[e];!B(o)||!C(o)||(Object.assign(o.style,n),Object.keys(r).forEach(function(e){var t=r[e];!1===t?o.removeAttribute(e):o.setAttribute(e,!0===t?"":t)}))})}function Rt(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach(function(e){var r=t.elements[e],o=t.attributes[e]||{},a=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:n[e]).reduce(function(e,t){return e[t]="",e},{});!B(r)||!C(r)||(Object.assign(r.style,a),Object.keys(o).forEach(function(e){r.removeAttribute(e)}))})}}var Ae={name:"applyStyles",enabled:!0,phase:"write",fn:Mt,effect:Rt,requires:["computeStyles"]};function q(e){return e.split("-")[0]}var X$1=Math.max,ve=Math.min,Z=Math.round;function ee(e,t){void 0===t&&(t=!1);var n=e.getBoundingClientRect(),r=1,o=1;if(B(e)&&t){var a=e.offsetHeight,i=e.offsetWidth;i>0&&(r=Z(n.width)/i||1),a>0&&(o=Z(n.height)/a||1)}return{width:n.width/r,height:n.height/o,top:n.top/o,right:n.right/r,bottom:n.bottom/o,left:n.left/r,x:n.left/r,y:n.top/o}}function ke(e){var t=ee(e),n=e.offsetWidth,r=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-r)<=1&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:r}}function it(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&Pe(n)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function N$1(e){return H(e).getComputedStyle(e)}function Wt(e){return["table","td","th"].indexOf(C(e))>=0}function I$1(e){return((Q(e)?e.ownerDocument:e.document)||window.document).documentElement}function ge(e){return"html"===C(e)?e:e.assignedSlot||e.parentNode||(Pe(e)?e.host:null)||I$1(e)}function at(e){return B(e)&&"fixed"!==N$1(e).position?e.offsetParent:null}function Bt(e){var t=-1!==navigator.userAgent.toLowerCase().indexOf("firefox");if(-1!==navigator.userAgent.indexOf("Trident")&&B(e)&&"fixed"===N$1(e).position)return null;var n=ge(e);for(Pe(n)&&(n=n.host);B(n)&&["html","body"].indexOf(C(n))<0;){var r=N$1(n);if("none"!==r.transform||"none"!==r.perspective||"paint"===r.contain||-1!==["transform","perspective"].indexOf(r.willChange)||t&&"filter"===r.willChange||t&&r.filter&&"none"!==r.filter)return n;n=n.parentNode}return null}function se(e){for(var t=H(e),n=at(e);n&&Wt(n)&&"static"===N$1(n).position;)n=at(n);return n&&("html"===C(n)||"body"===C(n)&&"static"===N$1(n).position)?t:n||Bt(e)||t}function Le(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function fe(e,t,n){return X$1(e,ve(t,n))}function St(e,t,n){var r=fe(e,t,n);return r>n?n:r}function st(){return{top:0,right:0,bottom:0,left:0}}function ft(e){return Object.assign({},st(),e)}function ct(e,t){return t.reduce(function(t,n){return t[n]=e,t},{})}var Tt=function(e,t){return ft("number"!=typeof(e="function"==typeof e?e(Object.assign({},t.rects,{placement:t.placement})):e)?e:ct(e,G))};function Ht(e){var t,n=e.state,r=e.name,o=e.options,a=n.elements.arrow,i=n.modifiersData.popperOffsets,s=q(n.placement),l=Le(s),u=[P$1,W].indexOf(s)>=0?"height":"width";if(a&&i){var c=Tt(o.padding,n),f=ke(a),p="y"===l?E$1:P$1,d="y"===l?R:W,h=n.rects.reference[u]+n.rects.reference[l]-i[l]-n.rects.popper[u],m=i[l]-n.rects.reference[l],g=se(a),v=g?"y"===l?g.clientHeight||0:g.clientWidth||0:0,y=h/2-m/2,b=c[p],x=v-f[u]-c[d],w=v/2-f[u]/2+y,E=fe(b,w,x),A=l;n.modifiersData[r]=((t={})[A]=E,t.centerOffset=E-w,t)}}function Ct(e){var t=e.state,n=e.options.element,r=void 0===n?"[data-popper-arrow]":n;null!=r&&("string"==typeof r&&!(r=t.elements.popper.querySelector(r))||!it(t.elements.popper,r)||(t.elements.arrow=r))}var pt={name:"arrow",enabled:!0,phase:"main",fn:Ht,effect:Ct,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function te(e){return e.split("-")[1]}var qt={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Vt(e){var t=e.x,n=e.y,r=window.devicePixelRatio||1;return{x:Z(t*r)/r||0,y:Z(n*r)/r||0}}function ut(e){var t,n=e.popper,r=e.popperRect,o=e.placement,a=e.variation,i=e.offsets,s=e.position,l=e.gpuAcceleration,u=e.adaptive,c=e.roundOffsets,f=e.isFixed,p=i.x,d=void 0===p?0:p,h=i.y,m=void 0===h?0:h,g="function"==typeof c?c({x:d,y:m}):{x:d,y:m};d=g.x,m=g.y;var v=i.hasOwnProperty("x"),y=i.hasOwnProperty("y"),b=P$1,x=E$1,w=window;if(u){var E=se(n),A="clientHeight",O="clientWidth";if(E===H(n)&&("static"!==N$1(E=I$1(n)).position&&"absolute"===s&&(A="scrollHeight",O="scrollWidth")),o===E$1||(o===P$1||o===W)&&a===J)x=R,m-=(f&&E===w&&w.visualViewport?w.visualViewport.height:E[A])-r.height,m*=l?1:-1;if(o===P$1||(o===E$1||o===R)&&a===J)b=W,d-=(f&&E===w&&w.visualViewport?w.visualViewport.width:E[O])-r.width,d*=l?1:-1}var C,S=Object.assign({position:s},u&&qt),_=!0===c?Vt({x:d,y:m}):{x:d,y:m};return d=_.x,m=_.y,l?Object.assign({},S,((C={})[x]=y?"0":"",C[b]=v?"0":"",C.transform=(w.devicePixelRatio||1)<=1?"translate("+d+"px, "+m+"px)":"translate3d("+d+"px, "+m+"px, 0)",C)):Object.assign({},S,((t={})[x]=y?m+"px":"",t[b]=v?d+"px":"",t.transform="",t))}function Nt(e){var t=e.state,n=e.options,r=n.gpuAcceleration,o=void 0===r||r,a=n.adaptive,i=void 0===a||a,s=n.roundOffsets,l=void 0===s||s,u={placement:q(t.placement),variation:te(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:o,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,ut(Object.assign({},u,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:i,roundOffsets:l})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,ut(Object.assign({},u,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:l})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})}var Me={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:Nt,data:{}},ye={passive:!0};function It(e){var t=e.state,n=e.instance,r=e.options,o=r.scroll,a=void 0===o||o,i=r.resize,s=void 0===i||i,l=H(t.elements.popper),u=[].concat(t.scrollParents.reference,t.scrollParents.popper);return a&&u.forEach(function(e){e.addEventListener("scroll",n.update,ye)}),s&&l.addEventListener("resize",n.update,ye),function(){a&&u.forEach(function(e){e.removeEventListener("scroll",n.update,ye)}),s&&l.removeEventListener("resize",n.update,ye)}}var Re={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:It,data:{}},_t={left:"right",right:"left",bottom:"top",top:"bottom"};function be(e){return e.replace(/left|right|bottom|top/g,function(e){return _t[e]})}var zt={start:"end",end:"start"};function lt(e){return e.replace(/start|end/g,function(e){return zt[e]})}function We(e){var t=H(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function Be(e){return ee(I$1(e)).left+We(e).scrollLeft}function Ft(e){var t=H(e),n=I$1(e),r=t.visualViewport,o=n.clientWidth,a=n.clientHeight,i=0,s=0;return r&&(o=r.width,a=r.height,/^((?!chrome|android).)*safari/i.test(navigator.userAgent)||(i=r.offsetLeft,s=r.offsetTop)),{width:o,height:a,x:i+Be(e),y:s}}function Ut(e){var t,n=I$1(e),r=We(e),o=null==(t=e.ownerDocument)?void 0:t.body,a=X$1(n.scrollWidth,n.clientWidth,o?o.scrollWidth:0,o?o.clientWidth:0),i=X$1(n.scrollHeight,n.clientHeight,o?o.scrollHeight:0,o?o.clientHeight:0),s=-r.scrollLeft+Be(e),l=-r.scrollTop;return"rtl"===N$1(o||n).direction&&(s+=X$1(n.clientWidth,o?o.clientWidth:0)-a),{width:a,height:i,x:s,y:l}}function Se(e){var t=N$1(e),n=t.overflow,r=t.overflowX,o=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+o+r)}function dt(e){return["html","body","#document"].indexOf(C(e))>=0?e.ownerDocument.body:B(e)&&Se(e)?e:dt(ge(e))}function ce(e,t){var n;void 0===t&&(t=[]);var r=dt(e),o=r===(null==(n=e.ownerDocument)?void 0:n.body),a=H(r),i=o?[a].concat(a.visualViewport||[],Se(r)?r:[]):r,s=t.concat(i);return o?s:s.concat(ce(ge(i)))}function Te(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function Xt(e){var t=ee(e);return t.top=t.top+e.clientTop,t.left=t.left+e.clientLeft,t.bottom=t.top+e.clientHeight,t.right=t.left+e.clientWidth,t.width=e.clientWidth,t.height=e.clientHeight,t.x=t.left,t.y=t.top,t}function ht(e,t){return t===je?Te(Ft(e)):Q(t)?Xt(t):Te(Ut(I$1(e)))}function Yt(e){var t=ce(ge(e)),n=["absolute","fixed"].indexOf(N$1(e).position)>=0&&B(e)?se(e):e;return Q(n)?t.filter(function(e){return Q(e)&&it(e,n)&&"body"!==C(e)}):[]}function Gt(e,t,n){var r="clippingParents"===t?Yt(e):[].concat(t),o=[].concat(r,[n]),a=o[0],i=o.reduce(function(t,n){var r=ht(e,n);return t.top=X$1(r.top,t.top),t.right=ve(r.right,t.right),t.bottom=ve(r.bottom,t.bottom),t.left=X$1(r.left,t.left),t},ht(e,a));return i.width=i.right-i.left,i.height=i.bottom-i.top,i.x=i.left,i.y=i.top,i}function mt(e){var t,n=e.reference,r=e.element,o=e.placement,a=o?q(o):null,i=o?te(o):null,s=n.x+n.width/2-r.width/2,l=n.y+n.height/2-r.height/2;switch(a){case E$1:t={x:s,y:n.y-r.height};break;case R:t={x:s,y:n.y+n.height};break;case W:t={x:n.x+n.width,y:l};break;case P$1:t={x:n.x-r.width,y:l};break;default:t={x:n.x,y:n.y}}var u=a?Le(a):null;if(null!=u){var c="y"===u?"height":"width";switch(i){case U$1:t[u]=t[u]-(n[c]/2-r[c]/2);break;case J:t[u]=t[u]+(n[c]/2-r[c]/2)}}return t}function ne(e,t){void 0===t&&(t={});var n=t,r=n.placement,o=void 0===r?e.placement:r,a=n.boundary,i=void 0===a?Xe:a,s=n.rootBoundary,l=void 0===s?je:s,u=n.elementContext,c=void 0===u?K:u,f=n.altBoundary,p=void 0!==f&&f,d=n.padding,h=void 0===d?0:d,m=ft("number"!=typeof h?h:ct(h,G)),g=c===K?Ye:K,v=e.rects.popper,y=e.elements[p?g:c],b=Gt(Q(y)?y:y.contextElement||I$1(e.elements.popper),i,l),x=ee(e.elements.reference),w=mt({reference:x,element:v,strategy:"absolute",placement:o}),E=Te(Object.assign({},v,w)),A=c===K?E:x,O={top:b.top-A.top+m.top,bottom:A.bottom-b.bottom+m.bottom,left:b.left-A.left+m.left,right:A.right-b.right+m.right},C=e.modifiersData.offset;if(c===K&&C){var S=C[o];Object.keys(O).forEach(function(e){var t=[W,R].indexOf(e)>=0?1:-1,n=[E$1,R].indexOf(e)>=0?"y":"x";O[e]+=S[n]*t})}return O}function Jt(e,t){void 0===t&&(t={});var n=t,r=n.placement,o=n.boundary,a=n.rootBoundary,i=n.padding,s=n.flipVariations,l=n.allowedAutoPlacements,u=void 0===l?Ee:l,c=te(r),f=c?s?De:De.filter(function(e){return te(e)===c}):G,p=f.filter(function(e){return u.indexOf(e)>=0});0===p.length&&(p=f);var d=p.reduce(function(t,n){return t[n]=ne(e,{placement:n,boundary:o,rootBoundary:a,padding:i})[q(n)],t},{});return Object.keys(d).sort(function(e,t){return d[e]-d[t]})}function Kt(e){if(q(e)===me)return[];var t=be(e);return[lt(e),t,lt(t)]}function Qt(e){var t=e.state,n=e.options,r=e.name;if(!t.modifiersData[r]._skip){for(var o=n.mainAxis,a=void 0===o||o,i=n.altAxis,s=void 0===i||i,l=n.fallbackPlacements,u=n.padding,c=n.boundary,f=n.rootBoundary,p=n.altBoundary,d=n.flipVariations,h=void 0===d||d,m=n.allowedAutoPlacements,g=t.options.placement,v=q(g),y=l||(v===g||!h?[be(g)]:Kt(g)),b=[g].concat(y).reduce(function(e,n){return e.concat(q(n)===me?Jt(t,{placement:n,boundary:c,rootBoundary:f,padding:u,flipVariations:h,allowedAutoPlacements:m}):n)},[]),x=t.rects.reference,w=t.rects.popper,E=new Map,A=!0,O=b[0],C=0;C<b.length;C++){var S=b[C],_=q(S),P=te(S)===U$1,k=[E$1,R].indexOf(_)>=0,M=k?"width":"height",T=ne(t,{placement:S,boundary:c,rootBoundary:f,altBoundary:p,padding:u}),F=k?P?W:P$1:P?R:E$1;x[M]>w[M]&&(F=be(F));var H=be(F),D=[];if(a&&D.push(T[_]<=0),s&&D.push(T[F]<=0,T[H]<=0),D.every(function(e){return e})){O=S,A=!1;break}E.set(S,D)}if(A)for(var N=function(e){var t=b.find(function(t){var n=E.get(t);if(n)return n.slice(0,e).every(function(e){return e})});if(t)return O=t,"break"},L=h?3:1;L>0;L--){if("break"===N(L))break}t.placement!==O&&(t.modifiersData[r]._skip=!0,t.placement=O,t.reset=!0)}}var vt={name:"flip",enabled:!0,phase:"main",fn:Qt,requiresIfExists:["offset"],data:{_skip:!1}};function gt(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function yt(e){return[E$1,W,R,P$1].some(function(t){return e[t]>=0})}function Zt(e){var t=e.state,n=e.name,r=t.rects.reference,o=t.rects.popper,a=t.modifiersData.preventOverflow,i=ne(t,{elementContext:"reference"}),s=ne(t,{altBoundary:!0}),l=gt(i,r),u=gt(s,o,a),c=yt(l),f=yt(u);t.modifiersData[n]={referenceClippingOffsets:l,popperEscapeOffsets:u,isReferenceHidden:c,hasPopperEscaped:f},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":c,"data-popper-escaped":f})}var bt={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:Zt};function en(e,t,n){var r=q(e),o=[P$1,E$1].indexOf(r)>=0?-1:1,a="function"==typeof n?n(Object.assign({},t,{placement:e})):n,i=a[0],s=a[1];return i=i||0,s=(s||0)*o,[P$1,W].indexOf(r)>=0?{x:s,y:i}:{x:i,y:s}}function tn(e){var t=e.state,n=e.options,r=e.name,o=n.offset,a=void 0===o?[0,0]:o,i=Ee.reduce(function(e,n){return e[n]=en(n,t.rects,a),e},{}),s=i[t.placement],l=s.x,u=s.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=l,t.modifiersData.popperOffsets.y+=u),t.modifiersData[r]=i}var wt={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:tn};function nn(e){var t=e.state,n=e.name;t.modifiersData[n]=mt({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})}var He={name:"popperOffsets",enabled:!0,phase:"read",fn:nn,data:{}};function rn(e){return"x"===e?"y":"x"}function on(e){var t=e.state,n=e.options,r=e.name,o=n.mainAxis,a=void 0===o||o,i=n.altAxis,s=void 0!==i&&i,l=n.boundary,u=n.rootBoundary,c=n.altBoundary,f=n.padding,p=n.tether,d=void 0===p||p,h=n.tetherOffset,m=void 0===h?0:h,g=ne(t,{boundary:l,rootBoundary:u,padding:f,altBoundary:c}),v=q(t.placement),y=te(t.placement),b=!y,x=Le(v),w=rn(x),E=t.modifiersData.popperOffsets,A=t.rects.reference,O=t.rects.popper,C="function"==typeof m?m(Object.assign({},t.rects,{placement:t.placement})):m,S="number"==typeof C?{mainAxis:C,altAxis:C}:Object.assign({mainAxis:0,altAxis:0},C),_=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,P={x:0,y:0};if(E){if(a){var k,M="y"===x?E$1:P$1,T="y"===x?R:W,F="y"===x?"height":"width",H=E[x],D=H+g[M],N=H-g[T],L=d?-O[F]/2:0,j=y===U$1?A[F]:O[F],I=y===U$1?-O[F]:-A[F],$=t.elements.arrow,V=d&&$?ke($):{width:0,height:0},B=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:st(),U=B[M],z=B[T],X=fe(0,A[F],V[F]),G=b?A[F]/2-L-X-U-S.mainAxis:j-X-U-S.mainAxis,Y=b?-A[F]/2+L+X+z+S.mainAxis:I+X+z+S.mainAxis,K=t.elements.arrow&&se(t.elements.arrow),J=K?"y"===x?K.clientTop||0:K.clientLeft||0:0,Z=null!=(k=null==_?void 0:_[x])?k:0,Q=H+Y-Z,ee=fe(d?ve(D,H+G-Z-J):D,H,d?X$1(N,Q):N);E[x]=ee,P[x]=ee-H}if(s){var re,oe="x"===x?E$1:P$1,ae="x"===x?R:W,ie=E[w],le="y"===w?"height":"width",ue=ie+g[oe],ce=ie-g[ae],pe=-1!==[E$1,P$1].indexOf(v),de=null!=(re=null==_?void 0:_[w])?re:0,he=pe?ue:ie-A[le]-O[le]-de+S.altAxis,me=pe?ie+A[le]+O[le]-de-S.altAxis:ce,ge=d&&pe?St(he,ie,me):fe(d?he:ue,ie,d?me:ce);E[w]=ge,P[w]=ge-ie}t.modifiersData[r]=P}}var xt={name:"preventOverflow",enabled:!0,phase:"main",fn:on,requiresIfExists:["offset"]};function an(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function sn(e){return e!==H(e)&&B(e)?an(e):We(e)}function fn(e){var t=e.getBoundingClientRect(),n=Z(t.width)/e.offsetWidth||1,r=Z(t.height)/e.offsetHeight||1;return 1!==n||1!==r}function cn(e,t,n){void 0===n&&(n=!1);var r=B(t),o=B(t)&&fn(t),a=I$1(t),i=ee(e,o),s={scrollLeft:0,scrollTop:0},l={x:0,y:0};return(r||!r&&!n)&&(("body"!==C(t)||Se(a))&&(s=sn(t)),B(t)?((l=ee(t,!0)).x+=t.clientLeft,l.y+=t.clientTop):a&&(l.x=Be(a))),{x:i.left+s.scrollLeft-l.x,y:i.top+s.scrollTop-l.y,width:i.width,height:i.height}}function pn(e){var t=new Map,n=new Set,r=[];function o(e){n.add(e.name),[].concat(e.requires||[],e.requiresIfExists||[]).forEach(function(e){if(!n.has(e)){var r=t.get(e);r&&o(r)}}),r.push(e)}return e.forEach(function(e){t.set(e.name,e)}),e.forEach(function(e){n.has(e.name)||o(e)}),r}function un(e){var t=pn(e);return ot.reduce(function(e,n){return e.concat(t.filter(function(e){return e.phase===n}))},[])}function ln(e){var t;return function(){return t||(t=new Promise(function(n){Promise.resolve().then(function(){t=void 0,n(e())})})),t}}function dn(e){var t=e.reduce(function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e},{});return Object.keys(t).map(function(e){return t[e]})}var Ot={placement:"bottom",modifiers:[],strategy:"absolute"};function $t(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some(function(e){return!(e&&"function"==typeof e.getBoundingClientRect)})}function we(e){void 0===e&&(e={});var t=e,n=t.defaultModifiers,r=void 0===n?[]:n,o=t.defaultOptions,a=void 0===o?Ot:o;return function(e,t,n){void 0===n&&(n=a);var o={placement:"bottom",orderedModifiers:[],options:Object.assign({},Ot,a),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},i=[],s=!1,l={state:o,setOptions:function(n){var s="function"==typeof n?n(o.options):n;u(),o.options=Object.assign({},a,o.options,s),o.scrollParents={reference:Q(e)?ce(e):e.contextElement?ce(e.contextElement):[],popper:ce(t)};var c=un(dn([].concat(r,o.options.modifiers)));return o.orderedModifiers=c.filter(function(e){return e.enabled}),o.orderedModifiers.forEach(function(e){var t=e.name,n=e.options,r=void 0===n?{}:n,a=e.effect;if("function"==typeof a){var s=a({state:o,name:t,instance:l,options:r}),u=function(){};i.push(s||u)}}),l.update()},forceUpdate:function(){if(!s){var e=o.elements,t=e.reference,n=e.popper;if($t(t,n)){o.rects={reference:cn(t,se(n),"fixed"===o.options.strategy),popper:ke(n)},o.reset=!1,o.placement=o.options.placement,o.orderedModifiers.forEach(function(e){return o.modifiersData[e.name]=Object.assign({},e.data)});for(var r=0;r<o.orderedModifiers.length;r++)if(!0!==o.reset){var a=o.orderedModifiers[r],i=a.fn,u=a.options,c=void 0===u?{}:u,f=a.name;"function"==typeof i&&(o=i({state:o,options:c,name:f,instance:l})||o)}else o.reset=!1,r=-1}}},update:ln(function(){return new Promise(function(e){l.forceUpdate(),e(o)})}),destroy:function(){u(),s=!0}};if(!$t(e,t))return l;function u(){i.forEach(function(e){return e()}),i=[]}return l.setOptions(n).then(function(e){!s&&n.onFirstUpdate&&n.onFirstUpdate(e)}),l}}we();var mn=[Re,He,Me,Ae];we({defaultModifiers:mn});var gn=[Re,He,Me,Ae,wt,vt,xt,pt,bt],yn=we({defaultModifiers:gn});function bound01(e,t){isOnePointZero(e)&&(e="100%");var n=isPercentage(e);return e=360===t?e:Math.min(t,Math.max(0,parseFloat(e))),n&&(e=parseInt(String(e*t),10)/100),Math.abs(e-t)<1e-6?1:e=360===t?(e<0?e%t+t:e%t)/parseFloat(String(t)):e%t/parseFloat(String(t))}function clamp01(e){return Math.min(1,Math.max(0,e))}function isOnePointZero(e){return"string"==typeof e&&-1!==e.indexOf(".")&&1===parseFloat(e)}function isPercentage(e){return"string"==typeof e&&-1!==e.indexOf("%")}function boundAlpha(e){return e=parseFloat(e),(isNaN(e)||e<0||e>1)&&(e=1),e}function convertToPercentage(e){return e<=1?"".concat(100*Number(e),"%"):e}function pad2(e){return 1===e.length?"0"+e:String(e)}function rgbToRgb(e,t,n){return{r:255*bound01(e,255),g:255*bound01(t,255),b:255*bound01(n,255)}}function rgbToHsl(e,t,n){e=bound01(e,255),t=bound01(t,255),n=bound01(n,255);var r=Math.max(e,t,n),o=Math.min(e,t,n),a=0,i=0,s=(r+o)/2;if(r===o)i=0,a=0;else{var l=r-o;switch(i=s>.5?l/(2-r-o):l/(r+o),r){case e:a=(t-n)/l+(t<n?6:0);break;case t:a=(n-e)/l+2;break;case n:a=(e-t)/l+4}a/=6}return{h:a,s:i,l:s}}function hue2rgb(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+6*n*(t-e):n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function hslToRgb(e,t,n){var r,o,a;if(e=bound01(e,360),t=bound01(t,100),n=bound01(n,100),0===t)o=n,a=n,r=n;else{var i=n<.5?n*(1+t):n+t-n*t,s=2*n-i;r=hue2rgb(s,i,e+1/3),o=hue2rgb(s,i,e),a=hue2rgb(s,i,e-1/3)}return{r:255*r,g:255*o,b:255*a}}function rgbToHsv(e,t,n){e=bound01(e,255),t=bound01(t,255),n=bound01(n,255);var r=Math.max(e,t,n),o=Math.min(e,t,n),a=0,i=r,s=r-o,l=0===r?0:s/r;if(r===o)a=0;else{switch(r){case e:a=(t-n)/s+(t<n?6:0);break;case t:a=(n-e)/s+2;break;case n:a=(e-t)/s+4}a/=6}return{h:a,s:l,v:i}}function hsvToRgb(e,t,n){e=6*bound01(e,360),t=bound01(t,100),n=bound01(n,100);var r=Math.floor(e),o=e-r,a=n*(1-t),i=n*(1-o*t),s=n*(1-(1-o)*t),l=r%6;return{r:255*[n,i,a,a,s,n][l],g:255*[s,n,n,i,a,a][l],b:255*[a,a,s,n,n,i][l]}}function rgbToHex(e,t,n,r){var o=[pad2(Math.round(e).toString(16)),pad2(Math.round(t).toString(16)),pad2(Math.round(n).toString(16))];return r&&o[0].startsWith(o[0].charAt(1))&&o[1].startsWith(o[1].charAt(1))&&o[2].startsWith(o[2].charAt(1))?o[0].charAt(0)+o[1].charAt(0)+o[2].charAt(0):o.join("")}function rgbaToHex(e,t,n,r,o){var a=[pad2(Math.round(e).toString(16)),pad2(Math.round(t).toString(16)),pad2(Math.round(n).toString(16)),pad2(convertDecimalToHex(r))];return o&&a[0].startsWith(a[0].charAt(1))&&a[1].startsWith(a[1].charAt(1))&&a[2].startsWith(a[2].charAt(1))&&a[3].startsWith(a[3].charAt(1))?a[0].charAt(0)+a[1].charAt(0)+a[2].charAt(0)+a[3].charAt(0):a.join("")}function convertDecimalToHex(e){return Math.round(255*parseFloat(e)).toString(16)}function convertHexToDecimal(e){return parseIntFromHex(e)/255}function parseIntFromHex(e){return parseInt(e,16)}function numberInputToObject(e){return{r:e>>16,g:(65280&e)>>8,b:255&e}}var names={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",goldenrod:"#daa520",gold:"#ffd700",gray:"#808080",green:"#008000",greenyellow:"#adff2f",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavenderblush:"#fff0f5",lavender:"#e6e6fa",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgreen:"#90ee90",lightgrey:"#d3d3d3",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"};function inputToRGB(e){var t={r:0,g:0,b:0},n=1,r=null,o=null,a=null,i=!1,s=!1;return"string"==typeof e&&(e=stringInputToObject(e)),"object"==typeof e&&(isValidCSSUnit(e.r)&&isValidCSSUnit(e.g)&&isValidCSSUnit(e.b)?(t=rgbToRgb(e.r,e.g,e.b),i=!0,s="%"===String(e.r).substr(-1)?"prgb":"rgb"):isValidCSSUnit(e.h)&&isValidCSSUnit(e.s)&&isValidCSSUnit(e.v)?(r=convertToPercentage(e.s),o=convertToPercentage(e.v),t=hsvToRgb(e.h,r,o),i=!0,s="hsv"):isValidCSSUnit(e.h)&&isValidCSSUnit(e.s)&&isValidCSSUnit(e.l)&&(r=convertToPercentage(e.s),a=convertToPercentage(e.l),t=hslToRgb(e.h,r,a),i=!0,s="hsl"),Object.prototype.hasOwnProperty.call(e,"a")&&(n=e.a)),n=boundAlpha(n),{ok:i,format:e.format||s,r:Math.min(255,Math.max(t.r,0)),g:Math.min(255,Math.max(t.g,0)),b:Math.min(255,Math.max(t.b,0)),a:n}}var CSS_INTEGER="[-\\+]?\\d+%?",CSS_NUMBER="[-\\+]?\\d*\\.\\d+%?",CSS_UNIT="(?:".concat(CSS_NUMBER,")|(?:").concat(CSS_INTEGER,")"),PERMISSIVE_MATCH3="[\\s|\\(]+(".concat(CSS_UNIT,")[,|\\s]+(").concat(CSS_UNIT,")[,|\\s]+(").concat(CSS_UNIT,")\\s*\\)?"),PERMISSIVE_MATCH4="[\\s|\\(]+(".concat(CSS_UNIT,")[,|\\s]+(").concat(CSS_UNIT,")[,|\\s]+(").concat(CSS_UNIT,")[,|\\s]+(").concat(CSS_UNIT,")\\s*\\)?"),matchers={CSS_UNIT:new RegExp(CSS_UNIT),rgb:new RegExp("rgb"+PERMISSIVE_MATCH3),rgba:new RegExp("rgba"+PERMISSIVE_MATCH4),hsl:new RegExp("hsl"+PERMISSIVE_MATCH3),hsla:new RegExp("hsla"+PERMISSIVE_MATCH4),hsv:new RegExp("hsv"+PERMISSIVE_MATCH3),hsva:new RegExp("hsva"+PERMISSIVE_MATCH4),hex3:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex6:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,hex4:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex8:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/};function stringInputToObject(e){if(0===(e=e.trim().toLowerCase()).length)return!1;var t=!1;if(names[e])e=names[e],t=!0;else if("transparent"===e)return{r:0,g:0,b:0,a:0,format:"name"};var n=matchers.rgb.exec(e);return n?{r:n[1],g:n[2],b:n[3]}:(n=matchers.rgba.exec(e))?{r:n[1],g:n[2],b:n[3],a:n[4]}:(n=matchers.hsl.exec(e))?{h:n[1],s:n[2],l:n[3]}:(n=matchers.hsla.exec(e))?{h:n[1],s:n[2],l:n[3],a:n[4]}:(n=matchers.hsv.exec(e))?{h:n[1],s:n[2],v:n[3]}:(n=matchers.hsva.exec(e))?{h:n[1],s:n[2],v:n[3],a:n[4]}:(n=matchers.hex8.exec(e))?{r:parseIntFromHex(n[1]),g:parseIntFromHex(n[2]),b:parseIntFromHex(n[3]),a:convertHexToDecimal(n[4]),format:t?"name":"hex8"}:(n=matchers.hex6.exec(e))?{r:parseIntFromHex(n[1]),g:parseIntFromHex(n[2]),b:parseIntFromHex(n[3]),format:t?"name":"hex"}:(n=matchers.hex4.exec(e))?{r:parseIntFromHex(n[1]+n[1]),g:parseIntFromHex(n[2]+n[2]),b:parseIntFromHex(n[3]+n[3]),a:convertHexToDecimal(n[4]+n[4]),format:t?"name":"hex8"}:!!(n=matchers.hex3.exec(e))&&{r:parseIntFromHex(n[1]+n[1]),g:parseIntFromHex(n[2]+n[2]),b:parseIntFromHex(n[3]+n[3]),format:t?"name":"hex"}}function isValidCSSUnit(e){return Boolean(matchers.CSS_UNIT.exec(String(e)))}var TinyColor=function(){function e(t,n){var r;if(void 0===t&&(t=""),void 0===n&&(n={}),t instanceof e)return t;"number"==typeof t&&(t=numberInputToObject(t)),this.originalInput=t;var o=inputToRGB(t);this.originalInput=t,this.r=o.r,this.g=o.g,this.b=o.b,this.a=o.a,this.roundA=Math.round(100*this.a)/100,this.format=null!==(r=n.format)&&void 0!==r?r:o.format,this.gradientType=n.gradientType,this.r<1&&(this.r=Math.round(this.r)),this.g<1&&(this.g=Math.round(this.g)),this.b<1&&(this.b=Math.round(this.b)),this.isValid=o.ok}return e.prototype.isDark=function(){return this.getBrightness()<128},e.prototype.isLight=function(){return!this.isDark()},e.prototype.getBrightness=function(){var e=this.toRgb();return(299*e.r+587*e.g+114*e.b)/1e3},e.prototype.getLuminance=function(){var e=this.toRgb(),t=e.r/255,n=e.g/255,r=e.b/255;return.2126*(t<=.03928?t/12.92:Math.pow((t+.055)/1.055,2.4))+.7152*(n<=.03928?n/12.92:Math.pow((n+.055)/1.055,2.4))+.0722*(r<=.03928?r/12.92:Math.pow((r+.055)/1.055,2.4))},e.prototype.getAlpha=function(){return this.a},e.prototype.setAlpha=function(e){return this.a=boundAlpha(e),this.roundA=Math.round(100*this.a)/100,this},e.prototype.isMonochrome=function(){return 0===this.toHsl().s},e.prototype.toHsv=function(){var e=rgbToHsv(this.r,this.g,this.b);return{h:360*e.h,s:e.s,v:e.v,a:this.a}},e.prototype.toHsvString=function(){var e=rgbToHsv(this.r,this.g,this.b),t=Math.round(360*e.h),n=Math.round(100*e.s),r=Math.round(100*e.v);return 1===this.a?"hsv(".concat(t,", ").concat(n,"%, ").concat(r,"%)"):"hsva(".concat(t,", ").concat(n,"%, ").concat(r,"%, ").concat(this.roundA,")")},e.prototype.toHsl=function(){var e=rgbToHsl(this.r,this.g,this.b);return{h:360*e.h,s:e.s,l:e.l,a:this.a}},e.prototype.toHslString=function(){var e=rgbToHsl(this.r,this.g,this.b),t=Math.round(360*e.h),n=Math.round(100*e.s),r=Math.round(100*e.l);return 1===this.a?"hsl(".concat(t,", ").concat(n,"%, ").concat(r,"%)"):"hsla(".concat(t,", ").concat(n,"%, ").concat(r,"%, ").concat(this.roundA,")")},e.prototype.toHex=function(e){return void 0===e&&(e=!1),rgbToHex(this.r,this.g,this.b,e)},e.prototype.toHexString=function(e){return void 0===e&&(e=!1),"#"+this.toHex(e)},e.prototype.toHex8=function(e){return void 0===e&&(e=!1),rgbaToHex(this.r,this.g,this.b,this.a,e)},e.prototype.toHex8String=function(e){return void 0===e&&(e=!1),"#"+this.toHex8(e)},e.prototype.toHexShortString=function(e){return void 0===e&&(e=!1),1===this.a?this.toHexString(e):this.toHex8String(e)},e.prototype.toRgb=function(){return{r:Math.round(this.r),g:Math.round(this.g),b:Math.round(this.b),a:this.a}},e.prototype.toRgbString=function(){var e=Math.round(this.r),t=Math.round(this.g),n=Math.round(this.b);return 1===this.a?"rgb(".concat(e,", ").concat(t,", ").concat(n,")"):"rgba(".concat(e,", ").concat(t,", ").concat(n,", ").concat(this.roundA,")")},e.prototype.toPercentageRgb=function(){var e=function(e){return"".concat(Math.round(100*bound01(e,255)),"%")};return{r:e(this.r),g:e(this.g),b:e(this.b),a:this.a}},e.prototype.toPercentageRgbString=function(){var e=function(e){return Math.round(100*bound01(e,255))};return 1===this.a?"rgb(".concat(e(this.r),"%, ").concat(e(this.g),"%, ").concat(e(this.b),"%)"):"rgba(".concat(e(this.r),"%, ").concat(e(this.g),"%, ").concat(e(this.b),"%, ").concat(this.roundA,")")},e.prototype.toName=function(){if(0===this.a)return"transparent";if(this.a<1)return!1;for(var e="#"+rgbToHex(this.r,this.g,this.b,!1),t=0,n=Object.entries(names);t<n.length;t++){var r=n[t],o=r[0];if(e===r[1])return o}return!1},e.prototype.toString=function(e){var t=Boolean(e);e=null!=e?e:this.format;var n=!1,r=this.a<1&&this.a>=0;return t||!r||!e.startsWith("hex")&&"name"!==e?("rgb"===e&&(n=this.toRgbString()),"prgb"===e&&(n=this.toPercentageRgbString()),"hex"!==e&&"hex6"!==e||(n=this.toHexString()),"hex3"===e&&(n=this.toHexString(!0)),"hex4"===e&&(n=this.toHex8String(!0)),"hex8"===e&&(n=this.toHex8String()),"name"===e&&(n=this.toName()),"hsl"===e&&(n=this.toHslString()),"hsv"===e&&(n=this.toHsvString()),n||this.toHexString()):"name"===e&&0===this.a?this.toName():this.toRgbString()},e.prototype.toNumber=function(){return(Math.round(this.r)<<16)+(Math.round(this.g)<<8)+Math.round(this.b)},e.prototype.clone=function(){return new e(this.toString())},e.prototype.lighten=function(t){void 0===t&&(t=10);var n=this.toHsl();return n.l+=t/100,n.l=clamp01(n.l),new e(n)},e.prototype.brighten=function(t){void 0===t&&(t=10);var n=this.toRgb();return n.r=Math.max(0,Math.min(255,n.r-Math.round(-t/100*255))),n.g=Math.max(0,Math.min(255,n.g-Math.round(-t/100*255))),n.b=Math.max(0,Math.min(255,n.b-Math.round(-t/100*255))),new e(n)},e.prototype.darken=function(t){void 0===t&&(t=10);var n=this.toHsl();return n.l-=t/100,n.l=clamp01(n.l),new e(n)},e.prototype.tint=function(e){return void 0===e&&(e=10),this.mix("white",e)},e.prototype.shade=function(e){return void 0===e&&(e=10),this.mix("black",e)},e.prototype.desaturate=function(t){void 0===t&&(t=10);var n=this.toHsl();return n.s-=t/100,n.s=clamp01(n.s),new e(n)},e.prototype.saturate=function(t){void 0===t&&(t=10);var n=this.toHsl();return n.s+=t/100,n.s=clamp01(n.s),new e(n)},e.prototype.greyscale=function(){return this.desaturate(100)},e.prototype.spin=function(t){var n=this.toHsl(),r=(n.h+t)%360;return n.h=r<0?360+r:r,new e(n)},e.prototype.mix=function(t,n){void 0===n&&(n=50);var r=this.toRgb(),o=new e(t).toRgb(),a=n/100;return new e({r:(o.r-r.r)*a+r.r,g:(o.g-r.g)*a+r.g,b:(o.b-r.b)*a+r.b,a:(o.a-r.a)*a+r.a})},e.prototype.analogous=function(t,n){void 0===t&&(t=6),void 0===n&&(n=30);var r=this.toHsl(),o=360/n,a=[this];for(r.h=(r.h-(o*t>>1)+720)%360;--t;)r.h=(r.h+o)%360,a.push(new e(r));return a},e.prototype.complement=function(){var t=this.toHsl();return t.h=(t.h+180)%360,new e(t)},e.prototype.monochromatic=function(t){void 0===t&&(t=6);for(var n=this.toHsv(),r=n.h,o=n.s,a=n.v,i=[],s=1/t;t--;)i.push(new e({h:r,s:o,v:a})),a=(a+s)%1;return i},e.prototype.splitcomplement=function(){var t=this.toHsl(),n=t.h;return[this,new e({h:(n+72)%360,s:t.s,l:t.l}),new e({h:(n+216)%360,s:t.s,l:t.l})]},e.prototype.onBackground=function(t){var n=this.toRgb(),r=new e(t).toRgb(),o=n.a+r.a*(1-n.a);return new e({r:(n.r*n.a+r.r*r.a*(1-n.a))/o,g:(n.g*n.a+r.g*r.a*(1-n.a))/o,b:(n.b*n.a+r.b*r.a*(1-n.a))/o,a:o})},e.prototype.triad=function(){return this.polyad(3)},e.prototype.tetrad=function(){return this.polyad(4)},e.prototype.polyad=function(t){for(var n=this.toHsl(),r=n.h,o=[this],a=360/t,i=1;i<t;i++)o.push(new e({h:(r+i*a)%360,s:n.s,l:n.l}));return o},e.prototype.equals=function(t){return this.toRgbString()===new e(t).toRgbString()},e}();function _extends(){return _extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},_extends.apply(this,arguments)}function _inheritsLoose(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,_setPrototypeOf(e,t)}function _getPrototypeOf(e){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function _setPrototypeOf(e,t){return(_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}function _construct(e,t,n){return(_construct=_isNativeReflectConstruct()?Reflect.construct.bind():function(e,t,n){var r=[null];r.push.apply(r,t);var o=new(Function.bind.apply(e,r));return n&&_setPrototypeOf(o,n.prototype),o}).apply(null,arguments)}function _isNativeFunction(e){return-1!==Function.toString.call(e).indexOf("[native code]")}function _wrapNativeSuper(e){var t="function"==typeof Map?new Map:void 0;return _wrapNativeSuper=function(e){if(null===e||!_isNativeFunction(e))return e;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,n)}function n(){return _construct(e,arguments,_getPrototypeOf(this).constructor)}return n.prototype=Object.create(e.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),_setPrototypeOf(n,e)},_wrapNativeSuper(e)}var formatRegExp=/%[sdj%]/g,warning=function(){};function convertFieldsError(e){if(!e||!e.length)return null;var t={};return e.forEach(function(e){var n=e.field;t[n]=t[n]||[],t[n].push(e)}),t}function format(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];var o=0,a=n.length;return"function"==typeof e?e.apply(null,n):"string"==typeof e?e.replace(formatRegExp,function(e){if("%%"===e)return"%";if(o>=a)return e;switch(e){case"%s":return String(n[o++]);case"%d":return Number(n[o++]);case"%j":try{return JSON.stringify(n[o++])}catch(t){return"[Circular]"}break;default:return e}}):e}function isNativeStringType(e){return"string"===e||"url"===e||"hex"===e||"email"===e||"date"===e||"pattern"===e}function isEmptyValue(e,t){return null==e||(!("array"!==t||!Array.isArray(e)||e.length)||!(!isNativeStringType(t)||"string"!=typeof e||e))}function asyncParallelArray(e,t,n){var r=[],o=0,a=e.length;function i(e){r.push.apply(r,e||[]),++o===a&&n(r)}e.forEach(function(e){t(e,i)})}function asyncSerialArray(e,t,n){var r=0,o=e.length;!function a(i){if(i&&i.length)n(i);else{var s=r;r+=1,s<o?t(e[s],a):n([])}}([])}function flattenObjArr(e){var t=[];return Object.keys(e).forEach(function(n){t.push.apply(t,e[n]||[])}),t}"undefined"!=typeof process&&process.env;var AsyncValidationError=function(e){function t(t,n){var r;return(r=e.call(this,"Async Validation Error")||this).errors=t,r.fields=n,r}return _inheritsLoose(t,e),t}(_wrapNativeSuper(Error));function asyncMap(e,t,n,r,o){if(t.first){var a=new Promise(function(t,a){asyncSerialArray(flattenObjArr(e),n,function(e){return r(e),e.length?a(new AsyncValidationError(e,convertFieldsError(e))):t(o)})});return a.catch(function(e){return e}),a}var i=!0===t.firstFields?Object.keys(e):t.firstFields||[],s=Object.keys(e),l=s.length,u=0,c=[],f=new Promise(function(t,a){var f=function(e){if(c.push.apply(c,e),++u===l)return r(c),c.length?a(new AsyncValidationError(c,convertFieldsError(c))):t(o)};s.length||(r(c),t(o)),s.forEach(function(t){var r=e[t];-1!==i.indexOf(t)?asyncSerialArray(r,n,f):asyncParallelArray(r,n,f)})});return f.catch(function(e){return e}),f}function isErrorObj(e){return!(!e||void 0===e.message)}function getValue(e,t){for(var n=e,r=0;r<t.length;r++){if(null==n)return n;n=n[t[r]]}return n}function complementError(e,t){return function(n){var r;return r=e.fullFields?getValue(t,e.fullFields):t[n.field||e.fullField],isErrorObj(n)?(n.field=n.field||e.fullField,n.fieldValue=r,n):{message:"function"==typeof n?n():n,fieldValue:r,field:n.field||e.fullField}}}function deepMerge(e,t){if(t)for(var n in t)if(t.hasOwnProperty(n)){var r=t[n];"object"==typeof r&&"object"==typeof e[n]?e[n]=_extends({},e[n],r):e[n]=r}return e}var required$1=function(e,t,n,r,o,a){!e.required||n.hasOwnProperty(e.field)&&!isEmptyValue(t,a||e.type)||r.push(format(o.messages.required,e.fullField))},whitespace=function(e,t,n,r,o){(/^\s+$/.test(t)||""===t)&&r.push(format(o.messages.whitespace,e.fullField))},urlReg,getUrlRegex=function(){if(urlReg)return urlReg;var e="[a-fA-F\\d:]",t=function(t){return t&&t.includeBoundaries?"(?:(?<=\\s|^)(?="+e+")|(?<="+e+")(?=\\s|$))":""},n="(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}",r="[a-fA-F\\d]{1,4}",o=("\n(?:\n(?:"+r+":){7}(?:"+r+"|:)|                                    // 1:2:3:4:5:6:7::  1:2:3:4:5:6:7:8\n(?:"+r+":){6}(?:"+n+"|:"+r+"|:)|                             // 1:2:3:4:5:6::    1:2:3:4:5:6::8   1:2:3:4:5:6::8  1:2:3:4:5:6::1.2.3.4\n(?:"+r+":){5}(?::"+n+"|(?::"+r+"){1,2}|:)|                   // 1:2:3:4:5::      1:2:3:4:5::7:8   1:2:3:4:5::8    1:2:3:4:5::7:1.2.3.4\n(?:"+r+":){4}(?:(?::"+r+"){0,1}:"+n+"|(?::"+r+"){1,3}|:)| // 1:2:3:4::        1:2:3:4::6:7:8   1:2:3:4::8      1:2:3:4::6:7:1.2.3.4\n(?:"+r+":){3}(?:(?::"+r+"){0,2}:"+n+"|(?::"+r+"){1,4}|:)| // 1:2:3::          1:2:3::5:6:7:8   1:2:3::8        1:2:3::5:6:7:1.2.3.4\n(?:"+r+":){2}(?:(?::"+r+"){0,3}:"+n+"|(?::"+r+"){1,5}|:)| // 1:2::            1:2::4:5:6:7:8   1:2::8          1:2::4:5:6:7:1.2.3.4\n(?:"+r+":){1}(?:(?::"+r+"){0,4}:"+n+"|(?::"+r+"){1,6}|:)| // 1::              1::3:4:5:6:7:8   1::8            1::3:4:5:6:7:1.2.3.4\n(?::(?:(?::"+r+"){0,5}:"+n+"|(?::"+r+"){1,7}|:))             // ::2:3:4:5:6:7:8  ::2:3:4:5:6:7:8  ::8             ::1.2.3.4\n)(?:%[0-9a-zA-Z]{1,})?                                             // %eth0            %1\n").replace(/\s*\/\/.*$/gm,"").replace(/\n/g,"").trim(),a=new RegExp("(?:^"+n+"$)|(?:^"+o+"$)"),i=new RegExp("^"+n+"$"),s=new RegExp("^"+o+"$"),l=function(e){return e&&e.exact?a:new RegExp("(?:"+t(e)+n+t(e)+")|(?:"+t(e)+o+t(e)+")","g")};l.v4=function(e){return e&&e.exact?i:new RegExp(""+t(e)+n+t(e),"g")},l.v6=function(e){return e&&e.exact?s:new RegExp(""+t(e)+o+t(e),"g")};var u=l.v4().source,c=l.v6().source;return urlReg=new RegExp("(?:^"+("(?:(?:(?:[a-z]+:)?//)|www\\.)(?:\\S+(?::\\S*)?@)?(?:localhost|"+u+"|"+c+'|(?:(?:[a-z\\u00a1-\\uffff0-9][-_]*)*[a-z\\u00a1-\\uffff0-9]+)(?:\\.(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)*(?:\\.(?:[a-z\\u00a1-\\uffff]{2,})))(?::\\d{2,5})?(?:[/?#][^\\s"]*)?')+"$)","i")},pattern$2={email:/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+\.)+[a-zA-Z\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]{2,}))$/,hex:/^#?([a-f0-9]{6}|[a-f0-9]{3})$/i},types={integer:function(e){return types.number(e)&&parseInt(e,10)===e},float:function(e){return types.number(e)&&!types.integer(e)},array:function(e){return Array.isArray(e)},regexp:function(e){if(e instanceof RegExp)return!0;try{return!!new RegExp(e)}catch(t){return!1}},date:function(e){return"function"==typeof e.getTime&&"function"==typeof e.getMonth&&"function"==typeof e.getYear&&!isNaN(e.getTime())},number:function(e){return!isNaN(e)&&"number"==typeof e},object:function(e){return"object"==typeof e&&!types.array(e)},method:function(e){return"function"==typeof e},email:function(e){return"string"==typeof e&&e.length<=320&&!!e.match(pattern$2.email)},url:function(e){return"string"==typeof e&&e.length<=2048&&!!e.match(getUrlRegex())},hex:function(e){return"string"==typeof e&&!!e.match(pattern$2.hex)}},type$1=function(e,t,n,r,o){if(e.required&&void 0===t)required$1(e,t,n,r,o);else{var a=e.type;["integer","float","array","regexp","object","method","email","number","date","url","hex"].indexOf(a)>-1?types[a](t)||r.push(format(o.messages.types[a],e.fullField,e.type)):a&&typeof t!==e.type&&r.push(format(o.messages.types[a],e.fullField,e.type))}},range=function(e,t,n,r,o){var a="number"==typeof e.len,i="number"==typeof e.min,s="number"==typeof e.max,l=t,u=null,c="number"==typeof t,f="string"==typeof t,p=Array.isArray(t);if(c?u="number":f?u="string":p&&(u="array"),!u)return!1;p&&(l=t.length),f&&(l=t.replace(/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,"_").length),a?l!==e.len&&r.push(format(o.messages[u].len,e.fullField,e.len)):i&&!s&&l<e.min?r.push(format(o.messages[u].min,e.fullField,e.min)):s&&!i&&l>e.max?r.push(format(o.messages[u].max,e.fullField,e.max)):i&&s&&(l<e.min||l>e.max)&&r.push(format(o.messages[u].range,e.fullField,e.min,e.max))},ENUM$1="enum",enumerable$1=function(e,t,n,r,o){e[ENUM$1]=Array.isArray(e[ENUM$1])?e[ENUM$1]:[],-1===e[ENUM$1].indexOf(t)&&r.push(format(o.messages[ENUM$1],e.fullField,e[ENUM$1].join(", ")))},pattern$1=function(e,t,n,r,o){if(e.pattern)if(e.pattern instanceof RegExp)e.pattern.lastIndex=0,e.pattern.test(t)||r.push(format(o.messages.pattern.mismatch,e.fullField,t,e.pattern));else if("string"==typeof e.pattern){new RegExp(e.pattern).test(t)||r.push(format(o.messages.pattern.mismatch,e.fullField,t,e.pattern))}},rules={required:required$1,whitespace:whitespace,type:type$1,range:range,enum:enumerable$1,pattern:pattern$1},string=function(e,t,n,r,o){var a=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(isEmptyValue(t,"string")&&!e.required)return n();rules.required(e,t,r,a,o,"string"),isEmptyValue(t,"string")||(rules.type(e,t,r,a,o),rules.range(e,t,r,a,o),rules.pattern(e,t,r,a,o),!0===e.whitespace&&rules.whitespace(e,t,r,a,o))}n(a)},method=function(e,t,n,r,o){var a=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(isEmptyValue(t)&&!e.required)return n();rules.required(e,t,r,a,o),void 0!==t&&rules.type(e,t,r,a,o)}n(a)},number=function(e,t,n,r,o){var a=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(""===t&&(t=void 0),isEmptyValue(t)&&!e.required)return n();rules.required(e,t,r,a,o),void 0!==t&&(rules.type(e,t,r,a,o),rules.range(e,t,r,a,o))}n(a)},_boolean=function(e,t,n,r,o){var a=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(isEmptyValue(t)&&!e.required)return n();rules.required(e,t,r,a,o),void 0!==t&&rules.type(e,t,r,a,o)}n(a)},regexp=function(e,t,n,r,o){var a=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(isEmptyValue(t)&&!e.required)return n();rules.required(e,t,r,a,o),isEmptyValue(t)||rules.type(e,t,r,a,o)}n(a)},integer=function(e,t,n,r,o){var a=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(isEmptyValue(t)&&!e.required)return n();rules.required(e,t,r,a,o),void 0!==t&&(rules.type(e,t,r,a,o),rules.range(e,t,r,a,o))}n(a)},floatFn=function(e,t,n,r,o){var a=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(isEmptyValue(t)&&!e.required)return n();rules.required(e,t,r,a,o),void 0!==t&&(rules.type(e,t,r,a,o),rules.range(e,t,r,a,o))}n(a)},array=function(e,t,n,r,o){var a=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(null==t&&!e.required)return n();rules.required(e,t,r,a,o,"array"),null!=t&&(rules.type(e,t,r,a,o),rules.range(e,t,r,a,o))}n(a)},object=function(e,t,n,r,o){var a=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(isEmptyValue(t)&&!e.required)return n();rules.required(e,t,r,a,o),void 0!==t&&rules.type(e,t,r,a,o)}n(a)},ENUM="enum",enumerable=function(e,t,n,r,o){var a=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(isEmptyValue(t)&&!e.required)return n();rules.required(e,t,r,a,o),void 0!==t&&rules[ENUM](e,t,r,a,o)}n(a)},pattern=function(e,t,n,r,o){var a=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(isEmptyValue(t,"string")&&!e.required)return n();rules.required(e,t,r,a,o),isEmptyValue(t,"string")||rules.pattern(e,t,r,a,o)}n(a)},date=function(e,t,n,r,o){var a=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(isEmptyValue(t,"date")&&!e.required)return n();var i;if(rules.required(e,t,r,a,o),!isEmptyValue(t,"date"))i=t instanceof Date?t:new Date(t),rules.type(e,i,r,a,o),i&&rules.range(e,i.getTime(),r,a,o)}n(a)},required=function(e,t,n,r,o){var a=[],i=Array.isArray(t)?"array":typeof t;rules.required(e,t,r,a,o,i),n(a)},type=function(e,t,n,r,o){var a=e.type,i=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(isEmptyValue(t,a)&&!e.required)return n();rules.required(e,t,r,i,o,a),isEmptyValue(t,a)||rules.type(e,t,r,i,o)}n(i)},any=function(e,t,n,r,o){var a=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(isEmptyValue(t)&&!e.required)return n();rules.required(e,t,r,a,o)}n(a)},validators={string:string,method:method,number:number,boolean:_boolean,regexp:regexp,integer:integer,float:floatFn,array:array,object:object,enum:enumerable,pattern:pattern,date:date,url:type,hex:type,email:type,required:required,any:any};function newMessages(){return{default:"Validation error on field %s",required:"%s is required",enum:"%s must be one of %s",whitespace:"%s cannot be empty",date:{format:"%s date %s is invalid for format %s",parse:"%s date could not be parsed, %s is invalid ",invalid:"%s date %s is invalid"},types:{string:"%s is not a %s",method:"%s is not a %s (function)",array:"%s is not an %s",object:"%s is not an %s",number:"%s is not a %s",date:"%s is not a %s",boolean:"%s is not a %s",integer:"%s is not an %s",float:"%s is not a %s",regexp:"%s is not a valid %s",email:"%s is not a valid %s",url:"%s is not a valid %s",hex:"%s is not a valid %s"},string:{len:"%s must be exactly %s characters",min:"%s must be at least %s characters",max:"%s cannot be longer than %s characters",range:"%s must be between %s and %s characters"},number:{len:"%s must equal %s",min:"%s cannot be less than %s",max:"%s cannot be greater than %s",range:"%s must be between %s and %s"},array:{len:"%s must be exactly %s in length",min:"%s cannot be less than %s in length",max:"%s cannot be greater than %s in length",range:"%s must be between %s and %s in length"},pattern:{mismatch:"%s value %s does not match pattern %s"},clone:function(){var e=JSON.parse(JSON.stringify(this));return e.clone=this.clone,e}}}var messages=newMessages(),Schema=function(){function e(e){this.rules=null,this._messages=messages,this.define(e)}var t=e.prototype;return t.define=function(e){var t=this;if(!e)throw new Error("Cannot configure a schema with no rules");if("object"!=typeof e||Array.isArray(e))throw new Error("Rules must be an object");this.rules={},Object.keys(e).forEach(function(n){var r=e[n];t.rules[n]=Array.isArray(r)?r:[r]})},t.messages=function(e){return e&&(this._messages=deepMerge(newMessages(),e)),this._messages},t.validate=function(t,n,r){var o=this;void 0===n&&(n={}),void 0===r&&(r=function(){});var a=t,i=n,s=r;if("function"==typeof i&&(s=i,i={}),!this.rules||0===Object.keys(this.rules).length)return s&&s(null,a),Promise.resolve(a);if(i.messages){var l=this.messages();l===messages&&(l=newMessages()),deepMerge(l,i.messages),i.messages=l}else i.messages=this.messages();var u={};(i.keys||Object.keys(this.rules)).forEach(function(e){var n=o.rules[e],r=a[e];n.forEach(function(n){var i=n;"function"==typeof i.transform&&(a===t&&(a=_extends({},a)),r=a[e]=i.transform(r)),(i="function"==typeof i?{validator:i}:_extends({},i)).validator=o.getValidationMethod(i),i.validator&&(i.field=e,i.fullField=i.fullField||e,i.type=o.getType(i),u[e]=u[e]||[],u[e].push({rule:i,value:r,source:a,field:e}))})});var c={};return asyncMap(u,i,function(t,n){var r,o=t.rule,s=!("object"!==o.type&&"array"!==o.type||"object"!=typeof o.fields&&"object"!=typeof o.defaultField);function l(e,t){return _extends({},t,{fullField:o.fullField+"."+e,fullFields:o.fullFields?[].concat(o.fullFields,[e]):[e]})}function u(r){void 0===r&&(r=[]);var u=Array.isArray(r)?r:[r];!i.suppressWarning&&u.length&&e.warning("async-validator:",u),u.length&&void 0!==o.message&&(u=[].concat(o.message));var f=u.map(complementError(o,a));if(i.first&&f.length)return c[o.field]=1,n(f);if(s){if(o.required&&!t.value)return void 0!==o.message?f=[].concat(o.message).map(complementError(o,a)):i.error&&(f=[i.error(o,format(i.messages.required,o.field))]),n(f);var p={};o.defaultField&&Object.keys(t.value).map(function(e){p[e]=o.defaultField}),p=_extends({},p,t.rule.fields);var d={};Object.keys(p).forEach(function(e){var t=p[e],n=Array.isArray(t)?t:[t];d[e]=n.map(l.bind(null,e))});var h=new e(d);h.messages(i.messages),t.rule.options&&(t.rule.options.messages=i.messages,t.rule.options.error=i.error),h.validate(t.value,t.rule.options||i,function(e){var t=[];f&&f.length&&t.push.apply(t,f),e&&e.length&&t.push.apply(t,e),n(t.length?t:null)})}else n(f)}if(s=s&&(o.required||!o.required&&t.value),o.field=t.field,o.asyncValidator)r=o.asyncValidator(o,t.value,u,t.source,i);else if(o.validator){try{r=o.validator(o,t.value,u,t.source,i)}catch(f){console.error,i.suppressValidatorError||setTimeout(function(){throw f},0),u(f.message)}!0===r?u():!1===r?u("function"==typeof o.message?o.message(o.fullField||o.field):o.message||(o.fullField||o.field)+" fails"):r instanceof Array?u(r):r instanceof Error&&u(r.message)}r&&r.then&&r.then(function(){return u()},function(e){return u(e)})},function(e){!function(e){var t=[],n={};function r(e){var n;Array.isArray(e)?t=(n=t).concat.apply(n,e):t.push(e)}for(var o=0;o<e.length;o++)r(e[o]);t.length?(n=convertFieldsError(t),s(t,n)):s(null,a)}(e)},a)},t.getType=function(e){if(void 0===e.type&&e.pattern instanceof RegExp&&(e.type="pattern"),"function"!=typeof e.validator&&e.type&&!validators.hasOwnProperty(e.type))throw new Error(format("Unknown rule type %s",e.type));return e.type||"string"},t.getValidationMethod=function(e){if("function"==typeof e.validator)return e.validator;var t=Object.keys(e),n=t.indexOf("message");return-1!==n&&t.splice(n,1),1===t.length&&"required"===t[0]?validators.required:validators[this.getType(e)]||void 0},e}();Schema.register=function(e,t){if("function"!=typeof t)throw new Error("Cannot register a validator by type, validator is not a function");validators[e]=t},Schema.warning=warning,Schema.messages=messages,Schema.validators=validators;var safeIsNaN=Number.isNaN||function(e){return"number"==typeof e&&e!=e};function isEqual(e,t){return e===t||!(!safeIsNaN(e)||!safeIsNaN(t))}function areInputsEqual(e,t){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++)if(!isEqual(e[n],t[n]))return!1;return!0}function memoizeOne(e,t){void 0===t&&(t=areInputsEqual);var n=null;function r(){for(var r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];if(n&&n.lastThis===this&&t(r,n.lastArgs))return n.lastResult;var a=e.apply(this,r);return n={lastResult:a,lastArgs:r,lastThis:this},a}return r.clear=function(){n=null},r}var v=!1,o,f,s,u,d,N,l,p,m,w,D,x,E,M,F;function a(){if(!v){v=!0;var e=navigator.userAgent,t=/(?:MSIE.(\d+\.\d+))|(?:(?:Firefox|GranParadiso|Iceweasel).(\d+\.\d+))|(?:Opera(?:.+Version.|.)(\d+\.\d+))|(?:AppleWebKit.(\d+(?:\.\d+)?))|(?:Trident\/\d+\.\d+.*rv:(\d+\.\d+))/.exec(e),n=/(Mac OS X)|(Windows)|(Linux)/.exec(e);if(x=/\b(iPhone|iP[ao]d)/.exec(e),E=/\b(iP[ao]d)/.exec(e),w=/Android/i.exec(e),M=/FBAN\/\w+;/i.exec(e),F=/Mobile/i.exec(e),D=!!/Win64/.exec(e),t){(o=t[1]?parseFloat(t[1]):t[5]?parseFloat(t[5]):NaN)&&document&&document.documentMode&&(o=document.documentMode);var r=/(?:Trident\/(\d+.\d+))/.exec(e);N=r?parseFloat(r[1])+4:o,f=t[2]?parseFloat(t[2]):NaN,s=t[3]?parseFloat(t[3]):NaN,(u=t[4]?parseFloat(t[4]):NaN)?(t=/(?:Chrome\/(\d+\.\d+))/.exec(e),d=t&&t[1]?parseFloat(t[1]):NaN):d=NaN}else o=f=s=d=u=NaN;if(n){if(n[1]){var a=/(?:Mac OS X (\d+(?:[._]\d+)?))/.exec(e);l=!a||parseFloat(a[1].replace("_","."))}else l=!1;p=!!n[2],m=!!n[3]}else l=p=m=!1}}var _={ie:function(){return a()||o},ieCompatibilityMode:function(){return a()||N>o},ie64:function(){return _.ie()&&D},firefox:function(){return a()||f},opera:function(){return a()||s},webkit:function(){return a()||u},safari:function(){return _.webkit()},chrome:function(){return a()||d},windows:function(){return a()||p},osx:function(){return a()||l},linux:function(){return a()||m},iphone:function(){return a()||x},mobile:function(){return a()||x||E||w||F},nativeApp:function(){return a()||M},android:function(){return a()||w},ipad:function(){return a()||E}},A=_,c=!!(typeof window<"u"&&window.document&&window.document.createElement),U={canUseDOM:c,canUseWorkers:typeof Worker<"u",canUseEventListeners:c&&!(!window.addEventListener&&!window.attachEvent),canUseViewport:c&&!!window.screen,isInWorker:!c},h=U,X;function S(e,t){if(!h.canUseDOM||t&&!("addEventListener"in document))return!1;var n="on"+e,r=n in document;if(!r){var o=document.createElement("div");o.setAttribute(n,"return;"),r="function"==typeof o[n]}return!r&&X&&"wheel"===e&&(r=document.implementation.hasFeature("Events.wheel","3.0")),r}h.canUseDOM&&(X=document.implementation&&document.implementation.hasFeature&&!0!==document.implementation.hasFeature("",""));var b=S,O=10,I=40,P=800;function T(e){var t=0,n=0,r=0,o=0;return"detail"in e&&(n=e.detail),"wheelDelta"in e&&(n=-e.wheelDelta/120),"wheelDeltaY"in e&&(n=-e.wheelDeltaY/120),"wheelDeltaX"in e&&(t=-e.wheelDeltaX/120),"axis"in e&&e.axis===e.HORIZONTAL_AXIS&&(t=n,n=0),r=t*O,o=n*O,"deltaY"in e&&(o=e.deltaY),"deltaX"in e&&(r=e.deltaX),(r||o)&&e.deltaMode&&(1==e.deltaMode?(r*=I,o*=I):(r*=P,o*=P)),r&&!t&&(t=r<1?-1:1),o&&!n&&(n=o<1?-1:1),{spinX:t,spinY:n,pixelX:r,pixelY:o}}T.getEventType=function(){return A.firefox()?"DOMMouseScroll":b("wheel")?"wheel":"mousewheel"};var Y=T;
/**
* Checks if an event is supported in the current execution environment.
*
* NOTE: This will not work correctly for non-generic events such as `change`,
* `reset`, `load`, `error`, and `select`.
*
* Borrows from Modernizr.
*
* @param {string} eventNameSuffix Event name, e.g. "click".
* @param {?boolean} capture Check if the capture phase is supported.
* @return {boolean} True if the event is supported.
* @internal
* @license Modernizr 3.0.0pre (Custom Build) | MIT
*/const min=Math.min,max=Math.max,round=Math.round,floor=Math.floor,createCoords=e=>({x:e,y:e}),oppositeSideMap={left:"right",right:"left",bottom:"top",top:"bottom"},oppositeAlignmentMap={start:"end",end:"start"};function clamp(e,t,n){return max(e,min(t,n))}function evaluate(e,t){return"function"==typeof e?e(t):e}function getSide(e){return e.split("-")[0]}function getAlignment(e){return e.split("-")[1]}function getOppositeAxis(e){return"x"===e?"y":"x"}function getAxisLength(e){return"y"===e?"height":"width"}const yAxisSides=new Set(["top","bottom"]);function getSideAxis(e){return yAxisSides.has(getSide(e))?"y":"x"}function getAlignmentAxis(e){return getOppositeAxis(getSideAxis(e))}function getAlignmentSides(e,t,n){void 0===n&&(n=!1);const r=getAlignment(e),o=getAlignmentAxis(e),a=getAxisLength(o);let i="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[a]>t.floating[a]&&(i=getOppositePlacement(i)),[i,getOppositePlacement(i)]}function getExpandedPlacements(e){const t=getOppositePlacement(e);return[getOppositeAlignmentPlacement(e),t,getOppositeAlignmentPlacement(t)]}function getOppositeAlignmentPlacement(e){return e.replace(/start|end/g,e=>oppositeAlignmentMap[e])}const lrPlacement=["left","right"],rlPlacement=["right","left"],tbPlacement=["top","bottom"],btPlacement=["bottom","top"];function getSideList(e,t,n){switch(e){case"top":case"bottom":return n?t?rlPlacement:lrPlacement:t?lrPlacement:rlPlacement;case"left":case"right":return t?tbPlacement:btPlacement;default:return[]}}function getOppositeAxisPlacements(e,t,n,r){const o=getAlignment(e);let a=getSideList(getSide(e),"start"===n,r);return o&&(a=a.map(e=>e+"-"+o),t&&(a=a.concat(a.map(getOppositeAlignmentPlacement)))),a}function getOppositePlacement(e){return e.replace(/left|right|bottom|top/g,e=>oppositeSideMap[e])}function expandPaddingObject(e){return __spreadValues({top:0,right:0,bottom:0,left:0},e)}function getPaddingObject(e){return"number"!=typeof e?expandPaddingObject(e):{top:e,right:e,bottom:e,left:e}}function rectToClientRect(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function computeCoordsFromPlacement(e,t,n){let{reference:r,floating:o}=e;const a=getSideAxis(t),i=getAlignmentAxis(t),s=getAxisLength(i),l=getSide(t),u="y"===a,c=r.x+r.width/2-o.width/2,f=r.y+r.height/2-o.height/2,p=r[s]/2-o[s]/2;let d;switch(l){case"top":d={x:c,y:r.y-o.height};break;case"bottom":d={x:c,y:r.y+r.height};break;case"right":d={x:r.x+r.width,y:f};break;case"left":d={x:r.x-o.width,y:f};break;default:d={x:r.x,y:r.y}}switch(getAlignment(t)){case"start":d[i]-=p*(n&&u?-1:1);break;case"end":d[i]+=p*(n&&u?-1:1)}return d}const computePosition$1=(e,t,n)=>__async(void 0,null,function*(){const{placement:r="bottom",strategy:o="absolute",middleware:a=[],platform:i}=n,s=a.filter(Boolean),l=yield null==i.isRTL?void 0:i.isRTL(t);let u=yield i.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:f}=computeCoordsFromPlacement(u,r,l),p=r,d={},h=0;for(let n=0;n<s.length;n++){const{name:a,fn:m}=s[n],{x:g,y:v,data:y,reset:b}=yield m({x:c,y:f,initialPlacement:r,placement:p,strategy:o,middlewareData:d,rects:u,platform:i,elements:{reference:e,floating:t}});c=null!=g?g:c,f=null!=v?v:f,d=__spreadProps(__spreadValues({},d),{[a]:__spreadValues(__spreadValues({},d[a]),y)}),b&&h<=50&&(h++,"object"==typeof b&&(b.placement&&(p=b.placement),b.rects&&(u=!0===b.rects?yield i.getElementRects({reference:e,floating:t,strategy:o}):b.rects),({x:c,y:f}=computeCoordsFromPlacement(u,p,l))),n=-1)}return{x:c,y:f,placement:p,strategy:o,middlewareData:d}});function detectOverflow$1(e,t){return __async(this,null,function*(){var n;void 0===t&&(t={});const{x:r,y:o,platform:a,rects:i,elements:s,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:f="floating",altBoundary:p=!1,padding:d=0}=evaluate(t,e),h=getPaddingObject(d),m=s[p?"floating"===f?"reference":"floating":f],g=rectToClientRect(yield a.getClippingRect({element:null==(n=yield null==a.isElement?void 0:a.isElement(m))||n?m:m.contextElement||(yield null==a.getDocumentElement?void 0:a.getDocumentElement(s.floating)),boundary:u,rootBoundary:c,strategy:l})),v="floating"===f?{x:r,y:o,width:i.floating.width,height:i.floating.height}:i.reference,y=yield null==a.getOffsetParent?void 0:a.getOffsetParent(s.floating),b=(yield null==a.isElement?void 0:a.isElement(y))&&(yield null==a.getScale?void 0:a.getScale(y))||{x:1,y:1},x=rectToClientRect(a.convertOffsetParentRelativeRectToViewportRelativeRect?yield a.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:v,offsetParent:y,strategy:l}):v);return{top:(g.top-x.top+h.top)/b.y,bottom:(x.bottom-g.bottom+h.bottom)/b.y,left:(g.left-x.left+h.left)/b.x,right:(x.right-g.right+h.right)/b.x}})}const arrow$1=e=>({name:"arrow",options:e,fn(t){return __async(this,null,function*(){const{x:n,y:r,placement:o,rects:a,platform:i,elements:s,middlewareData:l}=t,{element:u,padding:c=0}=evaluate(e,t)||{};if(null==u)return{};const f=getPaddingObject(c),p={x:n,y:r},d=getAlignmentAxis(o),h=getAxisLength(d),m=yield i.getDimensions(u),g="y"===d,v=g?"top":"left",y=g?"bottom":"right",b=g?"clientHeight":"clientWidth",x=a.reference[h]+a.reference[d]-p[d]-a.floating[h],w=p[d]-a.reference[d],E=yield null==i.getOffsetParent?void 0:i.getOffsetParent(u);let A=E?E[b]:0;A&&(yield null==i.isElement?void 0:i.isElement(E))||(A=s.floating[b]||a.floating[h]);const O=x/2-w/2,C=A/2-m[h]/2-1,R=min(f[v],C),S=min(f[y],C),_=R,P=A-m[h]-S,k=A/2-m[h]/2+O,M=clamp(_,k,P),T=!l.arrow&&null!=getAlignment(o)&&k!==M&&a.reference[h]/2-(k<_?R:S)-m[h]/2<0,F=T?k<_?k-_:k-P:0;return{[d]:p[d]+F,data:__spreadValues({[d]:M,centerOffset:k-M-F},T&&{alignmentOffset:F}),reset:T}})}}),flip$1=function(e){return void 0===e&&(e={}),{name:"flip",options:e,fn(t){return __async(this,null,function*(){var n,r;const{placement:o,middlewareData:a,rects:i,initialPlacement:s,platform:l,elements:u}=t,c=evaluate(e,t),{mainAxis:f=!0,crossAxis:p=!0,fallbackPlacements:d,fallbackStrategy:h="bestFit",fallbackAxisSideDirection:m="none",flipAlignment:g=!0}=c,v=__objRest(c,["mainAxis","crossAxis","fallbackPlacements","fallbackStrategy","fallbackAxisSideDirection","flipAlignment"]);if(null!=(n=a.arrow)&&n.alignmentOffset)return{};const y=getSide(o),b=getSideAxis(s),x=getSide(s)===s,w=yield null==l.isRTL?void 0:l.isRTL(u.floating),E=d||(x||!g?[getOppositePlacement(s)]:getExpandedPlacements(s)),A="none"!==m;!d&&A&&E.push(...getOppositeAxisPlacements(s,g,m,w));const O=[s,...E],C=yield detectOverflow$1(t,v),R=[];let S=(null==(r=a.flip)?void 0:r.overflows)||[];if(f&&R.push(C[y]),p){const e=getAlignmentSides(o,i,w);R.push(C[e[0]],C[e[1]])}if(S=[...S,{placement:o,overflows:R}],!R.every(e=>e<=0)){var _,P;const e=((null==(_=a.flip)?void 0:_.index)||0)+1,t=O[e];if(t){if(!("alignment"===p&&b!==getSideAxis(t))||S.every(e=>e.overflows[0]>0&&getSideAxis(e.placement)===b))return{data:{index:e,overflows:S},reset:{placement:t}}}let n=null==(P=S.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:P.placement;if(!n)switch(h){case"bestFit":{var k;const e=null==(k=S.filter(e=>{if(A){const t=getSideAxis(e.placement);return t===b||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:k[0];e&&(n=e);break}case"initialPlacement":n=s}if(o!==n)return{reset:{placement:n}}}return{}})}}},originSides=new Set(["left","top"]);function convertValueToCoords(e,t){return __async(this,null,function*(){const{placement:n,platform:r,elements:o}=e,a=yield null==r.isRTL?void 0:r.isRTL(o.floating),i=getSide(n),s=getAlignment(n),l="y"===getSideAxis(n),u=originSides.has(i)?-1:1,c=a&&l?-1:1,f=evaluate(t,e);let{mainAxis:p,crossAxis:d,alignmentAxis:h}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return s&&"number"==typeof h&&(d="end"===s?-1*h:h),l?{x:d*c,y:p*u}:{x:p*u,y:d*c}})}const offset$1=function(e){return void 0===e&&(e=0),{name:"offset",options:e,fn(t){return __async(this,null,function*(){var n,r;const{x:o,y:a,placement:i,middlewareData:s}=t,l=yield convertValueToCoords(t,e);return i===(null==(n=s.offset)?void 0:n.placement)&&null!=(r=s.arrow)&&r.alignmentOffset?{}:{x:o+l.x,y:a+l.y,data:__spreadProps(__spreadValues({},l),{placement:i})}})}}},shift$1=function(e){return void 0===e&&(e={}),{name:"shift",options:e,fn(t){return __async(this,null,function*(){const{x:n,y:r,placement:o}=t,a=evaluate(e,t),{mainAxis:i=!0,crossAxis:s=!1,limiter:l={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}}}=a,u=__objRest(a,["mainAxis","crossAxis","limiter"]),c={x:n,y:r},f=yield detectOverflow$1(t,u),p=getSideAxis(getSide(o)),d=getOppositeAxis(p);let h=c[d],m=c[p];if(i){const e="y"===d?"bottom":"right";h=clamp(h+f["y"===d?"top":"left"],h,h-f[e])}if(s){const e="y"===p?"bottom":"right";m=clamp(m+f["y"===p?"top":"left"],m,m-f[e])}const g=l.fn(__spreadProps(__spreadValues({},t),{[d]:h,[p]:m}));return __spreadProps(__spreadValues({},g),{data:{x:g.x-n,y:g.y-r,enabled:{[d]:i,[p]:s}}})})}}};function hasWindow(){return"undefined"!=typeof window}function getNodeName(e){return isNode(e)?(e.nodeName||"").toLowerCase():"#document"}function getWindow(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function getDocumentElement(e){var t;return null==(t=(isNode(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function isNode(e){return!!hasWindow()&&(e instanceof Node||e instanceof getWindow(e).Node)}function isElement(e){return!!hasWindow()&&(e instanceof Element||e instanceof getWindow(e).Element)}function isHTMLElement(e){return!!hasWindow()&&(e instanceof HTMLElement||e instanceof getWindow(e).HTMLElement)}function isShadowRoot(e){return!(!hasWindow()||"undefined"==typeof ShadowRoot)&&(e instanceof ShadowRoot||e instanceof getWindow(e).ShadowRoot)}const invalidOverflowDisplayValues=new Set(["inline","contents"]);function isOverflowElement(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=getComputedStyle(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!invalidOverflowDisplayValues.has(o)}const tableElements=new Set(["table","td","th"]);function isTableElement(e){return tableElements.has(getNodeName(e))}const topLayerSelectors=[":popover-open",":modal"];function isTopLayer(e){return topLayerSelectors.some(t=>{try{return e.matches(t)}catch(n){return!1}})}const transformProperties=["transform","translate","scale","rotate","perspective"],willChangeValues=["transform","translate","scale","rotate","perspective","filter"],containValues=["paint","layout","strict","content"];function isContainingBlock(e){const t=isWebKit(),n=isElement(e)?getComputedStyle(e):e;return transformProperties.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||willChangeValues.some(e=>(n.willChange||"").includes(e))||containValues.some(e=>(n.contain||"").includes(e))}function getContainingBlock(e){let t=getParentNode(e);for(;isHTMLElement(t)&&!isLastTraversableNode(t);){if(isContainingBlock(t))return t;if(isTopLayer(t))return null;t=getParentNode(t)}return null}function isWebKit(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}const lastTraversableNodeNames=new Set(["html","body","#document"]);function isLastTraversableNode(e){return lastTraversableNodeNames.has(getNodeName(e))}function getComputedStyle(e){return getWindow(e).getComputedStyle(e)}function getNodeScroll(e){return isElement(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function getParentNode(e){if("html"===getNodeName(e))return e;const t=e.assignedSlot||e.parentNode||isShadowRoot(e)&&e.host||getDocumentElement(e);return isShadowRoot(t)?t.host:t}function getNearestOverflowAncestor(e){const t=getParentNode(e);return isLastTraversableNode(t)?e.ownerDocument?e.ownerDocument.body:e.body:isHTMLElement(t)&&isOverflowElement(t)?t:getNearestOverflowAncestor(t)}function getOverflowAncestors(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);const o=getNearestOverflowAncestor(e),a=o===(null==(r=e.ownerDocument)?void 0:r.body),i=getWindow(o);if(a){const e=getFrameElement(i);return t.concat(i,i.visualViewport||[],isOverflowElement(o)?o:[],e&&n?getOverflowAncestors(e):[])}return t.concat(o,getOverflowAncestors(o,[],n))}function getFrameElement(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function getCssDimensions(e){const t=getComputedStyle(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=isHTMLElement(e),a=o?e.offsetWidth:n,i=o?e.offsetHeight:r,s=round(n)!==a||round(r)!==i;return s&&(n=a,r=i),{width:n,height:r,$:s}}function unwrapElement(e){return isElement(e)?e:e.contextElement}function getScale(e){const t=unwrapElement(e);if(!isHTMLElement(t))return createCoords(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:a}=getCssDimensions(t);let i=(a?round(n.width):n.width)/r,s=(a?round(n.height):n.height)/o;return i&&Number.isFinite(i)||(i=1),s&&Number.isFinite(s)||(s=1),{x:i,y:s}}const noOffsets=createCoords(0);function getVisualOffsets(e){const t=getWindow(e);return isWebKit()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:noOffsets}function shouldAddVisualOffsets(e,t,n){return void 0===t&&(t=!1),!(!n||t&&n!==getWindow(e))&&t}function getBoundingClientRect(e,t,n,r){void 0===t&&(t=!1),void 0===n&&(n=!1);const o=e.getBoundingClientRect(),a=unwrapElement(e);let i=createCoords(1);t&&(r?isElement(r)&&(i=getScale(r)):i=getScale(e));const s=shouldAddVisualOffsets(a,n,r)?getVisualOffsets(a):createCoords(0);let l=(o.left+s.x)/i.x,u=(o.top+s.y)/i.y,c=o.width/i.x,f=o.height/i.y;if(a){const e=getWindow(a),t=r&&isElement(r)?getWindow(r):r;let n=e,o=getFrameElement(n);for(;o&&r&&t!==n;){const e=getScale(o),t=o.getBoundingClientRect(),r=getComputedStyle(o),a=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,i=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;l*=e.x,u*=e.y,c*=e.x,f*=e.y,l+=a,u+=i,n=getWindow(o),o=getFrameElement(n)}}return rectToClientRect({width:c,height:f,x:l,y:u})}function getWindowScrollBarX(e,t){const n=getNodeScroll(e).scrollLeft;return t?t.left+n:getBoundingClientRect(getDocumentElement(e)).left+n}function getHTMLOffset(e,t,n){void 0===n&&(n=!1);const r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:getWindowScrollBarX(e,r)),y:r.top+t.scrollTop}}function convertOffsetParentRelativeRectToViewportRelativeRect(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const a="fixed"===o,i=getDocumentElement(r),s=!!t&&isTopLayer(t.floating);if(r===i||s&&a)return n;let l={scrollLeft:0,scrollTop:0},u=createCoords(1);const c=createCoords(0),f=isHTMLElement(r);if((f||!f&&!a)&&(("body"!==getNodeName(r)||isOverflowElement(i))&&(l=getNodeScroll(r)),isHTMLElement(r))){const e=getBoundingClientRect(r);u=getScale(r),c.x=e.x+r.clientLeft,c.y=e.y+r.clientTop}const p=!i||f||a?createCoords(0):getHTMLOffset(i,l,!0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-l.scrollLeft*u.x+c.x+p.x,y:n.y*u.y-l.scrollTop*u.y+c.y+p.y}}function getClientRects(e){return Array.from(e.getClientRects())}function getDocumentRect(e){const t=getDocumentElement(e),n=getNodeScroll(e),r=e.ownerDocument.body,o=max(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),a=max(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let i=-n.scrollLeft+getWindowScrollBarX(e);const s=-n.scrollTop;return"rtl"===getComputedStyle(r).direction&&(i+=max(t.clientWidth,r.clientWidth)-o),{width:o,height:a,x:i,y:s}}function getViewportRect(e,t){const n=getWindow(e),r=getDocumentElement(e),o=n.visualViewport;let a=r.clientWidth,i=r.clientHeight,s=0,l=0;if(o){a=o.width,i=o.height;const e=isWebKit();(!e||e&&"fixed"===t)&&(s=o.offsetLeft,l=o.offsetTop)}return{width:a,height:i,x:s,y:l}}const absoluteOrFixed=new Set(["absolute","fixed"]);function getInnerBoundingClientRect(e,t){const n=getBoundingClientRect(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,a=isHTMLElement(e)?getScale(e):createCoords(1);return{width:e.clientWidth*a.x,height:e.clientHeight*a.y,x:o*a.x,y:r*a.y}}function getClientRectFromClippingAncestor(e,t,n){let r;if("viewport"===t)r=getViewportRect(e,n);else if("document"===t)r=getDocumentRect(getDocumentElement(e));else if(isElement(t))r=getInnerBoundingClientRect(t,n);else{const n=getVisualOffsets(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return rectToClientRect(r)}function hasFixedPositionAncestor(e,t){const n=getParentNode(e);return!(n===t||!isElement(n)||isLastTraversableNode(n))&&("fixed"===getComputedStyle(n).position||hasFixedPositionAncestor(n,t))}function getClippingElementAncestors(e,t){const n=t.get(e);if(n)return n;let r=getOverflowAncestors(e,[],!1).filter(e=>isElement(e)&&"body"!==getNodeName(e)),o=null;const a="fixed"===getComputedStyle(e).position;let i=a?getParentNode(e):e;for(;isElement(i)&&!isLastTraversableNode(i);){const t=getComputedStyle(i),n=isContainingBlock(i);n||"fixed"!==t.position||(o=null);(a?!n&&!o:!n&&"static"===t.position&&!!o&&absoluteOrFixed.has(o.position)||isOverflowElement(i)&&!n&&hasFixedPositionAncestor(e,i))?r=r.filter(e=>e!==i):o=t,i=getParentNode(i)}return t.set(e,r),r}function getClippingRect(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const a=[..."clippingAncestors"===n?isTopLayer(t)?[]:getClippingElementAncestors(t,this._c):[].concat(n),r],i=a[0],s=a.reduce((e,n)=>{const r=getClientRectFromClippingAncestor(t,n,o);return e.top=max(r.top,e.top),e.right=min(r.right,e.right),e.bottom=min(r.bottom,e.bottom),e.left=max(r.left,e.left),e},getClientRectFromClippingAncestor(t,i,o));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}}function getDimensions(e){const{width:t,height:n}=getCssDimensions(e);return{width:t,height:n}}function getRectRelativeToOffsetParent(e,t,n){const r=isHTMLElement(t),o=getDocumentElement(t),a="fixed"===n,i=getBoundingClientRect(e,!0,a,t);let s={scrollLeft:0,scrollTop:0};const l=createCoords(0);function u(){l.x=getWindowScrollBarX(o)}if(r||!r&&!a)if(("body"!==getNodeName(t)||isOverflowElement(o))&&(s=getNodeScroll(t)),r){const e=getBoundingClientRect(t,!0,a,t);l.x=e.x+t.clientLeft,l.y=e.y+t.clientTop}else o&&u();a&&!r&&o&&u();const c=!o||r||a?createCoords(0):getHTMLOffset(o,s);return{x:i.left+s.scrollLeft-l.x-c.x,y:i.top+s.scrollTop-l.y-c.y,width:i.width,height:i.height}}function isStaticPositioned(e){return"static"===getComputedStyle(e).position}function getTrueOffsetParent(e,t){if(!isHTMLElement(e)||"fixed"===getComputedStyle(e).position)return null;if(t)return t(e);let n=e.offsetParent;return getDocumentElement(e)===n&&(n=n.ownerDocument.body),n}function getOffsetParent(e,t){const n=getWindow(e);if(isTopLayer(e))return n;if(!isHTMLElement(e)){let t=getParentNode(e);for(;t&&!isLastTraversableNode(t);){if(isElement(t)&&!isStaticPositioned(t))return t;t=getParentNode(t)}return n}let r=getTrueOffsetParent(e,t);for(;r&&isTableElement(r)&&isStaticPositioned(r);)r=getTrueOffsetParent(r,t);return r&&isLastTraversableNode(r)&&isStaticPositioned(r)&&!isContainingBlock(r)?n:r||getContainingBlock(e)||n}const getElementRects=function(e){return __async(this,null,function*(){const t=this.getOffsetParent||getOffsetParent,n=this.getDimensions,r=yield n(e.floating);return{reference:getRectRelativeToOffsetParent(e.reference,yield t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}})};function isRTL(e){return"rtl"===getComputedStyle(e).direction}const platform={convertOffsetParentRelativeRectToViewportRelativeRect:convertOffsetParentRelativeRectToViewportRelativeRect,getDocumentElement:getDocumentElement,getClippingRect:getClippingRect,getOffsetParent:getOffsetParent,getElementRects:getElementRects,getClientRects:getClientRects,getDimensions:getDimensions,getScale:getScale,isElement:isElement,isRTL:isRTL};function rectsAreEqual(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function observeMove(e,t){let n,r=null;const o=getDocumentElement(e);function a(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return function i(s,l){void 0===s&&(s=!1),void 0===l&&(l=1),a();const u=e.getBoundingClientRect(),{left:c,top:f,width:p,height:d}=u;if(s||t(),!p||!d)return;const h={rootMargin:-floor(f)+"px "+-floor(o.clientWidth-(c+p))+"px "+-floor(o.clientHeight-(f+d))+"px "+-floor(c)+"px",threshold:max(0,min(1,l))||1};let m=!0;function g(t){const r=t[0].intersectionRatio;if(r!==l){if(!m)return i();r?i(!1,r):n=setTimeout(()=>{i(!1,1e-7)},1e3)}1!==r||rectsAreEqual(u,e.getBoundingClientRect())||i(),m=!1}try{r=new IntersectionObserver(g,__spreadProps(__spreadValues({},h),{root:o.ownerDocument}))}catch(v){r=new IntersectionObserver(g,h)}r.observe(e)}(!0),a}function autoUpdate(e,t,n,r){void 0===r&&(r={});const{ancestorScroll:o=!0,ancestorResize:a=!0,elementResize:i="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:l=!1}=r,u=unwrapElement(e),c=o||a?[...u?getOverflowAncestors(u):[],...getOverflowAncestors(t)]:[];c.forEach(e=>{o&&e.addEventListener("scroll",n,{passive:!0}),a&&e.addEventListener("resize",n)});const f=u&&s?observeMove(u,n):null;let p,d=-1,h=null;i&&(h=new ResizeObserver(e=>{let[r]=e;r&&r.target===u&&h&&(h.unobserve(t),cancelAnimationFrame(d),d=requestAnimationFrame(()=>{var e;null==(e=h)||e.observe(t)})),n()}),u&&!l&&h.observe(u),h.observe(t));let m=l?getBoundingClientRect(e):null;return l&&function t(){const r=getBoundingClientRect(e);m&&!rectsAreEqual(m,r)&&n();m=r,p=requestAnimationFrame(t)}(),n(),()=>{var e;c.forEach(e=>{o&&e.removeEventListener("scroll",n),a&&e.removeEventListener("resize",n)}),null==f||f(),null==(e=h)||e.disconnect(),h=null,l&&cancelAnimationFrame(p)}}const detectOverflow=detectOverflow$1,offset=offset$1,shift=shift$1,flip=flip$1,arrow=arrow$1,computePosition=(e,t,n)=>{const r=new Map,o=__spreadValues({platform:platform},n),a=__spreadProps(__spreadValues({},o.platform),{_c:r});return computePosition$1(e,t,__spreadProps(__spreadValues({},o),{platform:a}))};var mock={exports:{}};(function(module,exports){var factory;factory=function(){return function(e){var t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={exports:{},id:r,loaded:!1};return e[r].call(o.exports,o,o.exports,n),o.loaded=!0,o.exports}return n.m=e,n.c=t,n.p="",n(0)}([function(e,t,n){var r,o=n(1),a=n(3),i=n(5),s=n(20),l=n(23),u=n(25);"undefined"!=typeof window&&(r=n(27))
/*!
              Mock - 模拟请求 & 模拟数据
              https://github.com/nuysoft/Mock
              墨智 <EMAIL> <EMAIL>
          */;var c={Handler:o,Random:i,Util:a,XHR:r,RE:s,toJSONSchema:l,valid:u,heredoc:a.heredoc,setup:function(e){return r.setup(e)},_mocked:{},version:"1.0.1-beta3"};r&&(r.Mock=c),c.mock=function(e,t,n){return 1===arguments.length?o.gen(e):(2===arguments.length&&(n=t,t=void 0),r&&(window.XMLHttpRequest=r),c._mocked[e+(t||"")]={rurl:e,rtype:t,template:n},c)},e.exports=c},function(module,exports,__webpack_require__){var Constant=__webpack_require__(2),Util=__webpack_require__(3),Parser=__webpack_require__(4),Random=__webpack_require__(5),RE=__webpack_require__(20),Handler={extend:Util.extend,gen:function(e,t,n){t=null==t?"":t+"",n={path:(n=n||{}).path||[Constant.GUID],templatePath:n.templatePath||[Constant.GUID++],currentContext:n.currentContext,templateCurrentContext:n.templateCurrentContext||e,root:n.root||n.currentContext,templateRoot:n.templateRoot||n.templateCurrentContext||e};var r,o=Parser.parse(t),a=Util.type(e);return Handler[a]?(r=Handler[a]({type:a,template:e,name:t,parsedName:t?t.replace(Constant.RE_KEY,"$1"):t,rule:o,context:n}),n.root||(n.root=r),r):e}};Handler.extend({array:function(e){var t,n,r=[];if(0===e.template.length)return r;if(e.rule.parameters)if(1===e.rule.min&&void 0===e.rule.max)e.context.path.push(e.name),e.context.templatePath.push(e.name),r=Random.pick(Handler.gen(e.template,void 0,{path:e.context.path,templatePath:e.context.templatePath,currentContext:r,templateCurrentContext:e.template,root:e.context.root||r,templateRoot:e.context.templateRoot||e.template})),e.context.path.pop(),e.context.templatePath.pop();else if(e.rule.parameters[2])e.template.__order_index=e.template.__order_index||0,e.context.path.push(e.name),e.context.templatePath.push(e.name),r=Handler.gen(e.template,void 0,{path:e.context.path,templatePath:e.context.templatePath,currentContext:r,templateCurrentContext:e.template,root:e.context.root||r,templateRoot:e.context.templateRoot||e.template})[e.template.__order_index%e.template.length],e.template.__order_index+=+e.rule.parameters[2],e.context.path.pop(),e.context.templatePath.pop();else for(t=0;t<e.rule.count;t++)for(n=0;n<e.template.length;n++)e.context.path.push(r.length),e.context.templatePath.push(n),r.push(Handler.gen(e.template[n],r.length,{path:e.context.path,templatePath:e.context.templatePath,currentContext:r,templateCurrentContext:e.template,root:e.context.root||r,templateRoot:e.context.templateRoot||e.template})),e.context.path.pop(),e.context.templatePath.pop();else for(t=0;t<e.template.length;t++)e.context.path.push(t),e.context.templatePath.push(t),r.push(Handler.gen(e.template[t],t,{path:e.context.path,templatePath:e.context.templatePath,currentContext:r,templateCurrentContext:e.template,root:e.context.root||r,templateRoot:e.context.templateRoot||e.template})),e.context.path.pop(),e.context.templatePath.pop();return r},object:function(e){var t,n,r,o,a,i,s={};if(null!=e.rule.min)for(t=Util.keys(e.template),t=(t=Random.shuffle(t)).slice(0,e.rule.count),i=0;i<t.length;i++)o=(r=t[i]).replace(Constant.RE_KEY,"$1"),e.context.path.push(o),e.context.templatePath.push(r),s[o]=Handler.gen(e.template[r],r,{path:e.context.path,templatePath:e.context.templatePath,currentContext:s,templateCurrentContext:e.template,root:e.context.root||s,templateRoot:e.context.templateRoot||e.template}),e.context.path.pop(),e.context.templatePath.pop();else{for(r in t=[],n=[],e.template)("function"==typeof e.template[r]?n:t).push(r);for(t=t.concat(n),i=0;i<t.length;i++)o=(r=t[i]).replace(Constant.RE_KEY,"$1"),e.context.path.push(o),e.context.templatePath.push(r),s[o]=Handler.gen(e.template[r],r,{path:e.context.path,templatePath:e.context.templatePath,currentContext:s,templateCurrentContext:e.template,root:e.context.root||s,templateRoot:e.context.templateRoot||e.template}),e.context.path.pop(),e.context.templatePath.pop(),(a=r.match(Constant.RE_KEY))&&a[2]&&"number"===Util.type(e.template[r])&&(e.template[r]+=parseInt(a[2],10))}return s},number:function(e){var t,n;if(e.rule.decimal){for(e.template+="",(n=e.template.split("."))[0]=e.rule.range?e.rule.count:n[0],n[1]=(n[1]||"").slice(0,e.rule.dcount);n[1].length<e.rule.dcount;)n[1]+=n[1].length<e.rule.dcount-1?Random.character("number"):Random.character("123456789");t=parseFloat(n.join("."),10)}else t=e.rule.range&&!e.rule.parameters[2]?e.rule.count:e.template;return t},boolean:function(e){return e.rule.parameters?Random.bool(e.rule.min,e.rule.max,e.template):e.template},string:function(e){var t,n,r,o,a="";if(e.template.length){for(null==e.rule.count&&(a+=e.template),t=0;t<e.rule.count;t++)a+=e.template;for(n=a.match(Constant.RE_PLACEHOLDER)||[],t=0;t<n.length;t++)if(r=n[t],/^\\/.test(r))n.splice(t--,1);else{if(o=Handler.placeholder(r,e.context.currentContext,e.context.templateCurrentContext,e),1===n.length&&r===a&&typeof o!=typeof a){a=o;break}a=a.replace(r,o)}}else a=e.rule.range?Random.string(e.rule.count):e.template;return a},function:function(e){return e.template.call(e.context.currentContext,e)},regexp:function(e){var t="";null==e.rule.count&&(t+=e.template.source);for(var n=0;n<e.rule.count;n++)t+=e.template.source;return RE.Handler.gen(RE.Parser.parse(t))}}),Handler.extend({_all:function(){var e={};for(var t in Random)e[t.toLowerCase()]=t;return e},placeholder:function(placeholder,obj,templateContext,options){Constant.RE_PLACEHOLDER.exec("");var parts=Constant.RE_PLACEHOLDER.exec(placeholder),key=parts&&parts[1],lkey=key&&key.toLowerCase(),okey=this._all()[lkey],params=parts&&parts[2]||"",pathParts=this.splitPathToArray(key);try{params=eval("(function(){ return [].splice.call(arguments, 0 ) })("+params+")")}catch(error){params=parts[2].split(/,\s*/)}if(obj&&key in obj)return obj[key];if("/"===key.charAt(0)||pathParts.length>1)return this.getValueByKeyPath(key,options);if(templateContext&&"object"==typeof templateContext&&key in templateContext&&placeholder!==templateContext[key])return templateContext[key]=Handler.gen(templateContext[key],key,{currentContext:obj,templateCurrentContext:templateContext}),templateContext[key];if(!(key in Random)&&!(lkey in Random)&&!(okey in Random))return placeholder;for(var i=0;i<params.length;i++)Constant.RE_PLACEHOLDER.exec(""),Constant.RE_PLACEHOLDER.test(params[i])&&(params[i]=Handler.placeholder(params[i],obj,templateContext,options));var handle=Random[key]||Random[lkey]||Random[okey];switch(Util.type(handle)){case"array":return Random.pick(handle);case"function":handle.options=options;var re=handle.apply(Random,params);return void 0===re&&(re=""),delete handle.options,re}},getValueByKeyPath:function(e,t){var n=e,r=this.splitPathToArray(e),o=[];"/"===e.charAt(0)?o=[t.context.path[0]].concat(this.normalizePath(r)):r.length>1&&((o=t.context.path.slice(0)).pop(),o=this.normalizePath(o.concat(r)));try{e=r[r.length-1];for(var a=t.context.root,i=t.context.templateRoot,s=1;s<o.length-1;s++)a=a[o[s]],i=i[o[s]];if(a&&e in a)return a[e];if(i&&"object"==typeof i&&e in i&&n!==i[e])return i[e]=Handler.gen(i[e],e,{currentContext:a,templateCurrentContext:i}),i[e]}catch(l){}return"@"+r.join("/")},normalizePath:function(e){for(var t=[],n=0;n<e.length;n++)switch(e[n]){case"..":t.pop();break;case".":break;default:t.push(e[n])}return t},splitPathToArray:function(e){var t=e.split(/\/+/);return t[t.length-1]||(t=t.slice(0,-1)),t[0]||(t=t.slice(1)),t}}),module.exports=Handler},function(e,t){e.exports={GUID:1,RE_KEY:/(.+)\|(?:\+(\d+)|([\+\-]?\d+-?[\+\-]?\d*)?(?:\.(\d+-?\d*))?)/,RE_RANGE:/([\+\-]?\d+)-?([\+\-]?\d+)?/,RE_PLACEHOLDER:/\\*@([^@#%&()\?\s]+)(?:\((.*?)\))?/g}},function(e,t){var n={extend:function(){var e,t,r,o,a,i=arguments[0]||{},s=1,l=arguments.length;for(1===l&&(i=this,s=0);s<l;s++)if(e=arguments[s])for(t in e)r=i[t],i!==(o=e[t])&&void 0!==o&&(n.isArray(o)||n.isObject(o)?(n.isArray(o)&&(a=r&&n.isArray(r)?r:[]),n.isObject(o)&&(a=r&&n.isObject(r)?r:{}),i[t]=n.extend(a,o)):i[t]=o);return i},each:function(e,t,n){var r,o;if("number"===this.type(e))for(r=0;r<e;r++)t(r,r);else if(e.length===+e.length)for(r=0;r<e.length&&!1!==t.call(n,e[r],r,e);r++);else for(o in e)if(!1===t.call(n,e[o],o,e))break},type:function(e){return null==e?String(e):Object.prototype.toString.call(e).match(/\[object (\w+)\]/)[1].toLowerCase()}};n.each("String Object Array RegExp Function".split(" "),function(e){n["is"+e]=function(t){return n.type(t)===e.toLowerCase()}}),n.isObjectOrArray=function(e){return n.isObject(e)||n.isArray(e)},n.isNumeric=function(e){return!isNaN(parseFloat(e))&&isFinite(e)},n.keys=function(e){var t=[];for(var n in e)e.hasOwnProperty(n)&&t.push(n);return t},n.values=function(e){var t=[];for(var n in e)e.hasOwnProperty(n)&&t.push(e[n]);return t},n.heredoc=function(e){return e.toString().replace(/^[^\/]+\/\*!?/,"").replace(/\*\/[^\/]+$/,"").replace(/^[\s\xA0]+/,"").replace(/[\s\xA0]+$/,"")},n.noop=function(){},e.exports=n},function(e,t,n){var r=n(2),o=n(5);e.exports={parse:function(e){var t=((e=null==e?"":e+"")||"").match(r.RE_KEY),n=t&&t[3]&&t[3].match(r.RE_RANGE),a=n&&n[1]&&parseInt(n[1],10),i=n&&n[2]&&parseInt(n[2],10),s=n?n[2]?o.integer(a,i):parseInt(n[1],10):void 0,l=t&&t[4]&&t[4].match(r.RE_RANGE),u=l&&l[1]&&parseInt(l[1],10),c=l&&l[2]&&parseInt(l[2],10),f={parameters:t,range:n,min:a,max:i,count:s,decimal:l,dmin:u,dmax:c,dcount:l?!l[2]&&parseInt(l[1],10)||o.integer(u,c):void 0};for(var p in f)if(null!=f[p])return f;return{}}}},function(e,t,n){var r={extend:n(3).extend};r.extend(n(6)),r.extend(n(7)),r.extend(n(8)),r.extend(n(10)),r.extend(n(13)),r.extend(n(15)),r.extend(n(16)),r.extend(n(17)),r.extend(n(14)),r.extend(n(19)),e.exports=r},function(e,t){e.exports={boolean:function(e,t,n){return void 0!==n?(e=void 0===e||isNaN(e)?1:parseInt(e,10),t=void 0===t||isNaN(t)?1:parseInt(t,10),Math.random()>1/(e+t)*e?!n:n):Math.random()>=.5},bool:function(e,t,n){return this.boolean(e,t,n)},natural:function(e,t){return e=void 0!==e?parseInt(e,10):0,t=void 0!==t?parseInt(t,10):9007199254740992,Math.round(Math.random()*(t-e))+e},integer:function(e,t){return e=void 0!==e?parseInt(e,10):-9007199254740992,t=void 0!==t?parseInt(t,10):9007199254740992,Math.round(Math.random()*(t-e))+e},int:function(e,t){return this.integer(e,t)},float:function(e,t,n,r){n=void 0===n?0:n,n=Math.max(Math.min(n,17),0),r=void 0===r?17:r,r=Math.max(Math.min(r,17),0);for(var o=this.integer(e,t)+".",a=0,i=this.natural(n,r);a<i;a++)o+=a<i-1?this.character("number"):this.character("123456789");return parseFloat(o,10)},character:function(e){var t={lower:"abcdefghijklmnopqrstuvwxyz",upper:"ABCDEFGHIJKLMNOPQRSTUVWXYZ",number:"0123456789",symbol:"!@#$%^&*()[]"};return t.alpha=t.lower+t.upper,t[void 0]=t.lower+t.upper+t.number+t.symbol,(e=t[(""+e).toLowerCase()]||e).charAt(this.natural(0,e.length-1))},char:function(e){return this.character(e)},string:function(e,t,n){var r;switch(arguments.length){case 0:r=this.natural(3,7);break;case 1:r=e,e=void 0;break;case 2:"string"==typeof arguments[0]?r=t:(r=this.natural(e,t),e=void 0);break;case 3:r=this.natural(t,n)}for(var o="",a=0;a<r;a++)o+=this.character(e);return o},str:function(){return this.string.apply(this,arguments)},range:function(e,t,n){arguments.length<=1&&(t=e||0,e=0),e=+e,t=+t,n=+(n=arguments[2]||1);for(var r=Math.max(Math.ceil((t-e)/n),0),o=0,a=new Array(r);o<r;)a[o++]=e,e+=n;return a}}},function(e,t){var n={yyyy:"getFullYear",yy:function(e){return(""+e.getFullYear()).slice(2)},y:"yy",MM:function(e){var t=e.getMonth()+1;return t<10?"0"+t:t},M:function(e){return e.getMonth()+1},dd:function(e){var t=e.getDate();return t<10?"0"+t:t},d:"getDate",HH:function(e){var t=e.getHours();return t<10?"0"+t:t},H:"getHours",hh:function(e){var t=e.getHours()%12;return t<10?"0"+t:t},h:function(e){return e.getHours()%12},mm:function(e){var t=e.getMinutes();return t<10?"0"+t:t},m:"getMinutes",ss:function(e){var t=e.getSeconds();return t<10?"0"+t:t},s:"getSeconds",SS:function(e){var t=e.getMilliseconds();return t<10&&"00"+t||t<100&&"0"+t||t},S:"getMilliseconds",A:function(e){return e.getHours()<12?"AM":"PM"},a:function(e){return e.getHours()<12?"am":"pm"},T:"getTime"};e.exports={_patternLetters:n,_rformat:new RegExp(function(){var e=[];for(var t in n)e.push(t);return"("+e.join("|")+")"}(),"g"),_formatDate:function(e,t){return t.replace(this._rformat,function t(r,o){return"function"==typeof n[o]?n[o](e):n[o]in n?t(r,n[o]):e[n[o]]()})},_randomDate:function(e,t){return e=void 0===e?new Date(0):e,t=void 0===t?new Date:t,new Date(Math.random()*(t.getTime()-e.getTime()))},date:function(e){return e=e||"yyyy-MM-dd",this._formatDate(this._randomDate(),e)},time:function(e){return e=e||"HH:mm:ss",this._formatDate(this._randomDate(),e)},datetime:function(e){return e=e||"yyyy-MM-dd HH:mm:ss",this._formatDate(this._randomDate(),e)},now:function(e,t){1===arguments.length&&(/year|month|day|hour|minute|second|week/.test(e)||(t=e,e="")),e=(e||"").toLowerCase(),t=t||"yyyy-MM-dd HH:mm:ss";var n=new Date;switch(e){case"year":n.setMonth(0);case"month":n.setDate(1);case"week":case"day":n.setHours(0);case"hour":n.setMinutes(0);case"minute":n.setSeconds(0);case"second":n.setMilliseconds(0)}return"week"===e&&n.setDate(n.getDate()-n.getDay()),this._formatDate(n,t)}}},function(e,t,n){(function(e){e.exports={_adSize:["300x250","250x250","240x400","336x280","180x150","720x300","468x60","234x60","88x31","120x90","120x60","120x240","125x125","728x90","160x600","120x600","300x600"],_screenSize:["320x200","320x240","640x480","800x480","800x480","1024x600","1024x768","1280x800","1440x900","1920x1200","2560x1600"],_videoSize:["720x480","768x576","1280x720","1920x1080"],image:function(e,t,n,r,o){return 4===arguments.length&&(o=r,r=void 0),3===arguments.length&&(o=n,n=void 0),e||(e=this.pick(this._adSize)),t&&~t.indexOf("#")&&(t=t.slice(1)),n&&~n.indexOf("#")&&(n=n.slice(1)),"http://dummyimage.com/"+e+(t?"/"+t:"")+(n?"/"+n:"")+(r?"."+r:"")+(o?"&text="+o:"")},img:function(){return this.image.apply(this,arguments)},_brandColors:{"4ormat":"#fb0a2a","500px":"#02adea","About.me (blue)":"#00405d","About.me (yellow)":"#ffcc33",Addvocate:"#ff6138",Adobe:"#ff0000",Aim:"#fcd20b",Amazon:"#e47911",Android:"#a4c639","Angie's List":"#7fbb00",AOL:"#0060a3",Atlassian:"#003366",Behance:"#053eff","Big Cartel":"#97b538",bitly:"#ee6123",Blogger:"#fc4f08",Boeing:"#0039a6","Booking.com":"#003580",Carbonmade:"#613854",Cheddar:"#ff7243","Code School":"#3d4944",Delicious:"#205cc0",Dell:"#3287c1",Designmoo:"#e54a4f",Deviantart:"#4e6252","Designer News":"#2d72da",Devour:"#fd0001",DEWALT:"#febd17","Disqus (blue)":"#59a3fc","Disqus (orange)":"#db7132",Dribbble:"#ea4c89",Dropbox:"#3d9ae8",Drupal:"#0c76ab",Dunked:"#2a323a",eBay:"#89c507",Ember:"#f05e1b",Engadget:"#00bdf6",Envato:"#528036",Etsy:"#eb6d20",Evernote:"#5ba525","Fab.com":"#dd0017",Facebook:"#3b5998",Firefox:"#e66000","Flickr (blue)":"#0063dc","Flickr (pink)":"#ff0084",Forrst:"#5b9a68",Foursquare:"#25a0ca",Garmin:"#007cc3",GetGlue:"#2d75a2",Gimmebar:"#f70078",GitHub:"#171515","Google Blue":"#0140ca","Google Green":"#16a61e","Google Red":"#dd1812","Google Yellow":"#fcca03","Google+":"#dd4b39",Grooveshark:"#f77f00",Groupon:"#82b548","Hacker News":"#ff6600",HelloWallet:"#0085ca","Heroku (light)":"#c7c5e6","Heroku (dark)":"#6567a5",HootSuite:"#003366",Houzz:"#73ba37",HTML5:"#ec6231",IKEA:"#ffcc33",IMDb:"#f3ce13",Instagram:"#3f729b",Intel:"#0071c5",Intuit:"#365ebf",Kickstarter:"#76cc1e",kippt:"#e03500",Kodery:"#00af81",LastFM:"#c3000d",LinkedIn:"#0e76a8",Livestream:"#cf0005",Lumo:"#576396",Mixpanel:"#a086d3",Meetup:"#e51937",Nokia:"#183693",NVIDIA:"#76b900",Opera:"#cc0f16",Path:"#e41f11","PayPal (dark)":"#1e477a","PayPal (light)":"#3b7bbf",Pinboard:"#0000e6",Pinterest:"#c8232c",PlayStation:"#665cbe",Pocket:"#ee4056",Prezi:"#318bff",Pusha:"#0f71b4",Quora:"#a82400","QUOTE.fm":"#66ceff",Rdio:"#008fd5",Readability:"#9c0000","Red Hat":"#cc0000",Resource:"#7eb400",Rockpack:"#0ba6ab",Roon:"#62b0d9",RSS:"#ee802f",Salesforce:"#1798c1",Samsung:"#0c4da2",Shopify:"#96bf48",Skype:"#00aff0",Snagajob:"#f47a20",Softonic:"#008ace",SoundCloud:"#ff7700","Space Box":"#f86960",Spotify:"#81b71a",Sprint:"#fee100",Squarespace:"#121212",StackOverflow:"#ef8236",Staples:"#cc0000","Status Chart":"#d7584f",Stripe:"#008cdd",StudyBlue:"#00afe1",StumbleUpon:"#f74425","T-Mobile":"#ea0a8e",Technorati:"#40a800","The Next Web":"#ef4423",Treehouse:"#5cb868",Trulia:"#5eab1f",Tumblr:"#34526f","Twitch.tv":"#6441a5",Twitter:"#00acee",TYPO3:"#ff8700",Ubuntu:"#dd4814",Ustream:"#3388ff",Verizon:"#ef1d1d",Vimeo:"#86c9ef",Vine:"#00a478",Virb:"#06afd8","Virgin Media":"#cc0000",Wooga:"#5b009c","WordPress (blue)":"#21759b","WordPress (orange)":"#d54e21","WordPress (grey)":"#464646",Wunderlist:"#2b88d9",XBOX:"#9bc848",XING:"#126567","Yahoo!":"#720e9e",Yandex:"#ffcc00",Yelp:"#c41200",YouTube:"#c4302b",Zalongo:"#5498dc",Zendesk:"#78a300",Zerply:"#9dcc7a",Zootool:"#5e8b1d"},_brandNames:function(){var e=[];for(var t in this._brandColors)e.push(t);return e},dataImage:function(t,n){var r,o=(r="undefined"!=typeof document?document.createElement("canvas"):new(e.require("canvas")))&&r.getContext&&r.getContext("2d");if(!r||!o)return"";t||(t=this.pick(this._adSize)),n=void 0!==n?n:t,t=t.split("x");var a=parseInt(t[0],10),i=parseInt(t[1],10),s=this._brandColors[this.pick(this._brandNames())];return r.width=a,r.height=i,o.textAlign="center",o.textBaseline="middle",o.fillStyle=s,o.fillRect(0,0,a,i),o.fillStyle="#FFF",o.font="bold 14px sans-serif",o.fillText(n,a/2,i/2,a),r.toDataURL("image/png")}}}).call(t,n(9)(e))},function(e,t){e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children=[],e.webpackPolyfill=1),e}},function(e,t,n){var r=n(11),o=n(12);e.exports={color:function(e){return e||o[e]?o[e].nicer:this.hex()},hex:function(){var e=this._goldenRatioColor(),t=r.hsv2rgb(e);return r.rgb2hex(t[0],t[1],t[2])},rgb:function(){var e=this._goldenRatioColor(),t=r.hsv2rgb(e);return"rgb("+parseInt(t[0],10)+", "+parseInt(t[1],10)+", "+parseInt(t[2],10)+")"},rgba:function(){var e=this._goldenRatioColor(),t=r.hsv2rgb(e);return"rgba("+parseInt(t[0],10)+", "+parseInt(t[1],10)+", "+parseInt(t[2],10)+", "+Math.random().toFixed(2)+")"},hsl:function(){var e=this._goldenRatioColor(),t=r.hsv2hsl(e);return"hsl("+parseInt(t[0],10)+", "+parseInt(t[1],10)+", "+parseInt(t[2],10)+")"},_goldenRatioColor:function(e,t){return this._goldenRatio=.618033988749895,this._hue=this._hue||Math.random(),this._hue+=this._goldenRatio,this._hue%=1,"number"!=typeof e&&(e=.5),"number"!=typeof t&&(t=.95),[360*this._hue,100*e,100*t]}}},function(e,t){e.exports={rgb2hsl:function(e){var t,n,r=e[0]/255,o=e[1]/255,a=e[2]/255,i=Math.min(r,o,a),s=Math.max(r,o,a),l=s-i;return s==i?t=0:r==s?t=(o-a)/l:o==s?t=2+(a-r)/l:a==s&&(t=4+(r-o)/l),(t=Math.min(60*t,360))<0&&(t+=360),n=(i+s)/2,[t,100*(s==i?0:n<=.5?l/(s+i):l/(2-s-i)),100*n]},rgb2hsv:function(e){var t,n,r=e[0],o=e[1],a=e[2],i=Math.min(r,o,a),s=Math.max(r,o,a),l=s-i;return n=0===s?0:l/s*1e3/10,s==i?t=0:r==s?t=(o-a)/l:o==s?t=2+(a-r)/l:a==s&&(t=4+(r-o)/l),(t=Math.min(60*t,360))<0&&(t+=360),[t,n,s/255*1e3/10]},hsl2rgb:function(e){var t,n,r,o,a,i=e[0]/360,s=e[1]/100,l=e[2]/100;if(0===s)return[a=255*l,a,a];t=2*l-(n=l<.5?l*(1+s):l+s-l*s),o=[0,0,0];for(var u=0;u<3;u++)(r=i+1/3*-(u-1))<0&&r++,r>1&&r--,a=6*r<1?t+6*(n-t)*r:2*r<1?n:3*r<2?t+(n-t)*(2/3-r)*6:t,o[u]=255*a;return o},hsl2hsv:function(e){var t=e[0],n=e[1]/100,r=e[2]/100;return[t,2*(n*=(r*=2)<=1?r:2-r)/(r+n)*100,(r+n)/2*100]},hsv2rgb:function(e){var t=e[0]/60,n=e[1]/100,r=e[2]/100,o=Math.floor(t)%6,a=t-Math.floor(t),i=255*r*(1-n),s=255*r*(1-n*a),l=255*r*(1-n*(1-a));switch(r*=255,o){case 0:return[r,l,i];case 1:return[s,r,i];case 2:return[i,r,l];case 3:return[i,s,r];case 4:return[l,i,r];case 5:return[r,i,s]}},hsv2hsl:function(e){var t,n,r=e[0],o=e[1]/100,a=e[2]/100;return t=o*a,[r,100*(t/=(n=(2-o)*a)<=1?n:2-n),100*(n/=2)]},rgb2hex:function(e,t,n){return"#"+((256+e<<8|t)<<8|n).toString(16).slice(1)},hex2rgb:function(e){return[(e="0x"+e.slice(1).replace(e.length>4?e:/./g,"$&$&")|0)>>16,e>>8&255,255&e]}}},function(e,t){e.exports={navy:{value:"#000080",nicer:"#001F3F"},blue:{value:"#0000ff",nicer:"#0074D9"},aqua:{value:"#00ffff",nicer:"#7FDBFF"},teal:{value:"#008080",nicer:"#39CCCC"},olive:{value:"#008000",nicer:"#3D9970"},green:{value:"#008000",nicer:"#2ECC40"},lime:{value:"#00ff00",nicer:"#01FF70"},yellow:{value:"#ffff00",nicer:"#FFDC00"},orange:{value:"#ffa500",nicer:"#FF851B"},red:{value:"#ff0000",nicer:"#FF4136"},maroon:{value:"#800000",nicer:"#85144B"},fuchsia:{value:"#ff00ff",nicer:"#F012BE"},purple:{value:"#800080",nicer:"#B10DC9"},silver:{value:"#c0c0c0",nicer:"#DDDDDD"},gray:{value:"#808080",nicer:"#AAAAAA"},black:{value:"#000000",nicer:"#111111"},white:{value:"#FFFFFF",nicer:"#FFFFFF"}}},function(e,t,n){var r=n(6),o=n(14);function a(e,t,n,o){return void 0===n?r.natural(e,t):void 0===o?n:r.natural(parseInt(n,10),parseInt(o,10))}e.exports={paragraph:function(e,t){for(var n=a(3,7,e,t),r=[],o=0;o<n;o++)r.push(this.sentence());return r.join(" ")},cparagraph:function(e,t){for(var n=a(3,7,e,t),r=[],o=0;o<n;o++)r.push(this.csentence());return r.join("")},sentence:function(e,t){for(var n=a(12,18,e,t),r=[],i=0;i<n;i++)r.push(this.word());return o.capitalize(r.join(" "))+"."},csentence:function(e,t){for(var n=a(12,18,e,t),r=[],o=0;o<n;o++)r.push(this.cword());return r.join("")+"。"},word:function(e,t){for(var n=a(3,10,e,t),o="",i=0;i<n;i++)o+=r.character("lower");return o},cword:function(e,t,n){var r,o="的一是在不了有和人这中大为上个国我以要他时来用们生到作地于出就分对成会可主发年动同工也能下过子说产种面而方后多定行学法所民得经十三之进着等部度家电力里如水化高自二理起小物现实加量都两体制机当使点从业本去把性好应开它合还因由其些然前外天政四日那社义事平形相全表间样与关各重新线内数正心反你明看原又么利比或但质气第向道命此变条只没结解问意建月公无系军很情者最立代想已通并提直题党程展五果料象员革位入常文总次品式活设及管特件长求老头基资边流路级少图山统接知较将组见计别她手角期根论运农指几九区强放决西被干做必战先回则任取据处队南给色光门即保治北造百规热领七海口东导器压志世金增争济阶油思术极交受联什认六共权收证改清己美再采转更单风切打白教速花带安场身车例真务具万每目至达走积示议声报斗完类八离华名确才科张信马节话米整空元况今集温传土许步群广石记需段研界拉林律叫且究观越织装影算低持音众书布复容儿须际商非验连断深难近矿千周委素技备半办青省列习响约支般史感劳便团往酸历市克何除消构府称太准精值号率族维划选标写存候毛亲快效斯院查江型眼王按格养易置派层片始却专状育厂京识适属圆包火住调满县局照参红细引听该铁价严龙飞";switch(arguments.length){case 0:e=o,r=1;break;case 1:"string"==typeof arguments[0]?r=1:(r=e,e=o);break;case 2:"string"==typeof arguments[0]?r=t:(r=this.natural(e,t),e=o);break;case 3:r=this.natural(t,n)}for(var a="",i=0;i<r;i++)a+=e.charAt(this.natural(0,e.length-1));return a},title:function(e,t){for(var n=a(3,7,e,t),r=[],o=0;o<n;o++)r.push(this.capitalize(this.word()));return r.join(" ")},ctitle:function(e,t){for(var n=a(3,7,e,t),r=[],o=0;o<n;o++)r.push(this.cword());return r.join("")}}},function(e,t,n){var r=n(3);e.exports={capitalize:function(e){return(e+"").charAt(0).toUpperCase()+(e+"").substr(1)},upper:function(e){return(e+"").toUpperCase()},lower:function(e){return(e+"").toLowerCase()},pick:function(e,t,n){return r.isArray(e)?(void 0===t&&(t=1),void 0===n&&(n=t)):(e=[].slice.call(arguments),t=1,n=1),1===t&&1===n?e[this.natural(0,e.length-1)]:this.shuffle(e,t,n)},shuffle:function(e,t,n){for(var r=(e=e||[]).slice(0),o=[],a=0,i=r.length,s=0;s<i;s++)a=this.natural(0,r.length-1),o.push(r[a]),r.splice(a,1);switch(arguments.length){case 0:case 1:return o;case 2:n=t;case 3:return t=parseInt(t,10),n=parseInt(n,10),o.slice(0,this.natural(t,n))}},order:function e(t){e.cache=e.cache||{},arguments.length>1&&(t=[].slice.call(arguments,0));var n=e.options.context.templatePath.join("."),r=e.cache[n]=e.cache[n]||{index:0,array:t};return r.array[r.index++%r.array.length]}}},function(e,t){e.exports={first:function(){var e=["James","John","Robert","Michael","William","David","Richard","Charles","Joseph","Thomas","Christopher","Daniel","Paul","Mark","Donald","George","Kenneth","Steven","Edward","Brian","Ronald","Anthony","Kevin","Jason","Matthew","Gary","Timothy","Jose","Larry","Jeffrey","Frank","Scott","Eric"].concat(["Mary","Patricia","Linda","Barbara","Elizabeth","Jennifer","Maria","Susan","Margaret","Dorothy","Lisa","Nancy","Karen","Betty","Helen","Sandra","Donna","Carol","Ruth","Sharon","Michelle","Laura","Sarah","Kimberly","Deborah","Jessica","Shirley","Cynthia","Angela","Melissa","Brenda","Amy","Anna"]);return this.pick(e)},last:function(){return this.pick(["Smith","Johnson","Williams","Brown","Jones","Miller","Davis","Garcia","Rodriguez","Wilson","Martinez","Anderson","Taylor","Thomas","Hernandez","Moore","Martin","Jackson","Thompson","White","Lopez","Lee","Gonzalez","Harris","Clark","Lewis","Robinson","Walker","Perez","Hall","Young","Allen"])},name:function(e){return this.first()+" "+(e?this.first()+" ":"")+this.last()},cfirst:function(){var e="王 李 张 刘 陈 杨 赵 黄 周 吴 徐 孙 胡 朱 高 林 何 郭 马 罗 梁 宋 郑 谢 韩 唐 冯 于 董 萧 程 曹 袁 邓 许 傅 沈 曾 彭 吕 苏 卢 蒋 蔡 贾 丁 魏 薛 叶 阎 余 潘 杜 戴 夏 锺 汪 田 任 姜 范 方 石 姚 谭 廖 邹 熊 金 陆 郝 孔 白 崔 康 毛 邱 秦 江 史 顾 侯 邵 孟 龙 万 段 雷 钱 汤 尹 黎 易 常 武 乔 贺 赖 龚 文".split(" ");return this.pick(e)},clast:function(){var e="伟 芳 娜 秀英 敏 静 丽 强 磊 军 洋 勇 艳 杰 娟 涛 明 超 秀兰 霞 平 刚 桂英".split(" ");return this.pick(e)},cname:function(){return this.cfirst()+this.clast()}}},function(e,t){e.exports={url:function(e,t){return(e||this.protocol())+"://"+(t||this.domain())+"/"+this.word()},protocol:function(){return this.pick("http ftp gopher mailto mid cid news nntp prospero telnet rlogin tn3270 wais".split(" "))},domain:function(e){return this.word()+"."+(e||this.tld())},tld:function(){return this.pick("com net org edu gov int mil cn com.cn net.cn gov.cn org.cn 中国 中国互联.公司 中国互联.网络 tel biz cc tv info name hk mobi asia cd travel pro museum coop aero ad ae af ag ai al am an ao aq ar as at au aw az ba bb bd be bf bg bh bi bj bm bn bo br bs bt bv bw by bz ca cc cf cg ch ci ck cl cm cn co cq cr cu cv cx cy cz de dj dk dm do dz ec ee eg eh es et ev fi fj fk fm fo fr ga gb gd ge gf gh gi gl gm gn gp gr gt gu gw gy hk hm hn hr ht hu id ie il in io iq ir is it jm jo jp ke kg kh ki km kn kp kr kw ky kz la lb lc li lk lr ls lt lu lv ly ma mc md mg mh ml mm mn mo mp mq mr ms mt mv mw mx my mz na nc ne nf ng ni nl no np nr nt nu nz om qa pa pe pf pg ph pk pl pm pn pr pt pw py re ro ru rw sa sb sc sd se sg sh si sj sk sl sm sn so sr st su sy sz tc td tf tg th tj tk tm tn to tp tr tt tv tw tz ua ug uk us uy va vc ve vg vn vu wf ws ye yu za zm zr zw".split(" "))},email:function(e){return this.character("lower")+"."+this.word()+"@"+(e||this.word()+"."+this.tld())},ip:function(){return this.natural(0,255)+"."+this.natural(0,255)+"."+this.natural(0,255)+"."+this.natural(0,255)}}},function(e,t,n){var r=n(18),o=["东北","华北","华东","华中","华南","西南","西北"];e.exports={region:function(){return this.pick(o)},province:function(){return this.pick(r).name},city:function(e){var t=this.pick(r),n=this.pick(t.children);return e?[t.name,n.name].join(" "):n.name},county:function(e){var t=this.pick(r),n=this.pick(t.children),o=this.pick(n.children)||{name:"-"};return e?[t.name,n.name,o.name].join(" "):o.name},zip:function(e){for(var t="",n=0;n<(e||6);n++)t+=this.natural(0,9);return t}}},function(e,t){var n={11e4:"北京",110100:"北京市",110101:"东城区",110102:"西城区",110105:"朝阳区",110106:"丰台区",110107:"石景山区",110108:"海淀区",110109:"门头沟区",110111:"房山区",110112:"通州区",110113:"顺义区",110114:"昌平区",110115:"大兴区",110116:"怀柔区",110117:"平谷区",110228:"密云县",110229:"延庆县",110230:"其它区",12e4:"天津",120100:"天津市",120101:"和平区",120102:"河东区",120103:"河西区",120104:"南开区",120105:"河北区",120106:"红桥区",120110:"东丽区",120111:"西青区",120112:"津南区",120113:"北辰区",120114:"武清区",120115:"宝坻区",120116:"滨海新区",120221:"宁河县",120223:"静海县",120225:"蓟县",120226:"其它区",13e4:"河北省",130100:"石家庄市",130102:"长安区",130103:"桥东区",130104:"桥西区",130105:"新华区",130107:"井陉矿区",130108:"裕华区",130121:"井陉县",130123:"正定县",130124:"栾城县",130125:"行唐县",130126:"灵寿县",130127:"高邑县",130128:"深泽县",130129:"赞皇县",130130:"无极县",130131:"平山县",130132:"元氏县",130133:"赵县",130181:"辛集市",130182:"藁城市",130183:"晋州市",130184:"新乐市",130185:"鹿泉市",130186:"其它区",130200:"唐山市",130202:"路南区",130203:"路北区",130204:"古冶区",130205:"开平区",130207:"丰南区",130208:"丰润区",130223:"滦县",130224:"滦南县",130225:"乐亭县",130227:"迁西县",130229:"玉田县",130230:"曹妃甸区",130281:"遵化市",130283:"迁安市",130284:"其它区",130300:"秦皇岛市",130302:"海港区",130303:"山海关区",130304:"北戴河区",130321:"青龙满族自治县",130322:"昌黎县",130323:"抚宁县",130324:"卢龙县",130398:"其它区",130400:"邯郸市",130402:"邯山区",130403:"丛台区",130404:"复兴区",130406:"峰峰矿区",130421:"邯郸县",130423:"临漳县",130424:"成安县",130425:"大名县",130426:"涉县",130427:"磁县",130428:"肥乡县",130429:"永年县",130430:"邱县",130431:"鸡泽县",130432:"广平县",130433:"馆陶县",130434:"魏县",130435:"曲周县",130481:"武安市",130482:"其它区",130500:"邢台市",130502:"桥东区",130503:"桥西区",130521:"邢台县",130522:"临城县",130523:"内丘县",130524:"柏乡县",130525:"隆尧县",130526:"任县",130527:"南和县",130528:"宁晋县",130529:"巨鹿县",130530:"新河县",130531:"广宗县",130532:"平乡县",130533:"威县",130534:"清河县",130535:"临西县",130581:"南宫市",130582:"沙河市",130583:"其它区",130600:"保定市",130602:"新市区",130603:"北市区",130604:"南市区",130621:"满城县",130622:"清苑县",130623:"涞水县",130624:"阜平县",130625:"徐水县",130626:"定兴县",130627:"唐县",130628:"高阳县",130629:"容城县",130630:"涞源县",130631:"望都县",130632:"安新县",130633:"易县",130634:"曲阳县",130635:"蠡县",130636:"顺平县",130637:"博野县",130638:"雄县",130681:"涿州市",130682:"定州市",130683:"安国市",130684:"高碑店市",130699:"其它区",130700:"张家口市",130702:"桥东区",130703:"桥西区",130705:"宣化区",130706:"下花园区",130721:"宣化县",130722:"张北县",130723:"康保县",130724:"沽源县",130725:"尚义县",130726:"蔚县",130727:"阳原县",130728:"怀安县",130729:"万全县",130730:"怀来县",130731:"涿鹿县",130732:"赤城县",130733:"崇礼县",130734:"其它区",130800:"承德市",130802:"双桥区",130803:"双滦区",130804:"鹰手营子矿区",130821:"承德县",130822:"兴隆县",130823:"平泉县",130824:"滦平县",130825:"隆化县",130826:"丰宁满族自治县",130827:"宽城满族自治县",130828:"围场满族蒙古族自治县",130829:"其它区",130900:"沧州市",130902:"新华区",130903:"运河区",130921:"沧县",130922:"青县",130923:"东光县",130924:"海兴县",130925:"盐山县",130926:"肃宁县",130927:"南皮县",130928:"吴桥县",130929:"献县",130930:"孟村回族自治县",130981:"泊头市",130982:"任丘市",130983:"黄骅市",130984:"河间市",130985:"其它区",131e3:"廊坊市",131002:"安次区",131003:"广阳区",131022:"固安县",131023:"永清县",131024:"香河县",131025:"大城县",131026:"文安县",131028:"大厂回族自治县",131081:"霸州市",131082:"三河市",131083:"其它区",131100:"衡水市",131102:"桃城区",131121:"枣强县",131122:"武邑县",131123:"武强县",131124:"饶阳县",131125:"安平县",131126:"故城县",131127:"景县",131128:"阜城县",131181:"冀州市",131182:"深州市",131183:"其它区",14e4:"山西省",140100:"太原市",140105:"小店区",140106:"迎泽区",140107:"杏花岭区",140108:"尖草坪区",140109:"万柏林区",140110:"晋源区",140121:"清徐县",140122:"阳曲县",140123:"娄烦县",140181:"古交市",140182:"其它区",140200:"大同市",140202:"城区",140203:"矿区",140211:"南郊区",140212:"新荣区",140221:"阳高县",140222:"天镇县",140223:"广灵县",140224:"灵丘县",140225:"浑源县",140226:"左云县",140227:"大同县",140228:"其它区",140300:"阳泉市",140302:"城区",140303:"矿区",140311:"郊区",140321:"平定县",140322:"盂县",140323:"其它区",140400:"长治市",140421:"长治县",140423:"襄垣县",140424:"屯留县",140425:"平顺县",140426:"黎城县",140427:"壶关县",140428:"长子县",140429:"武乡县",140430:"沁县",140431:"沁源县",140481:"潞城市",140482:"城区",140483:"郊区",140485:"其它区",140500:"晋城市",140502:"城区",140521:"沁水县",140522:"阳城县",140524:"陵川县",140525:"泽州县",140581:"高平市",140582:"其它区",140600:"朔州市",140602:"朔城区",140603:"平鲁区",140621:"山阴县",140622:"应县",140623:"右玉县",140624:"怀仁县",140625:"其它区",140700:"晋中市",140702:"榆次区",140721:"榆社县",140722:"左权县",140723:"和顺县",140724:"昔阳县",140725:"寿阳县",140726:"太谷县",140727:"祁县",140728:"平遥县",140729:"灵石县",140781:"介休市",140782:"其它区",140800:"运城市",140802:"盐湖区",140821:"临猗县",140822:"万荣县",140823:"闻喜县",140824:"稷山县",140825:"新绛县",140826:"绛县",140827:"垣曲县",140828:"夏县",140829:"平陆县",140830:"芮城县",140881:"永济市",140882:"河津市",140883:"其它区",140900:"忻州市",140902:"忻府区",140921:"定襄县",140922:"五台县",140923:"代县",140924:"繁峙县",140925:"宁武县",140926:"静乐县",140927:"神池县",140928:"五寨县",140929:"岢岚县",140930:"河曲县",140931:"保德县",140932:"偏关县",140981:"原平市",140982:"其它区",141e3:"临汾市",141002:"尧都区",141021:"曲沃县",141022:"翼城县",141023:"襄汾县",141024:"洪洞县",141025:"古县",141026:"安泽县",141027:"浮山县",141028:"吉县",141029:"乡宁县",141030:"大宁县",141031:"隰县",141032:"永和县",141033:"蒲县",141034:"汾西县",141081:"侯马市",141082:"霍州市",141083:"其它区",141100:"吕梁市",141102:"离石区",141121:"文水县",141122:"交城县",141123:"兴县",141124:"临县",141125:"柳林县",141126:"石楼县",141127:"岚县",141128:"方山县",141129:"中阳县",141130:"交口县",141181:"孝义市",141182:"汾阳市",141183:"其它区",15e4:"内蒙古自治区",150100:"呼和浩特市",150102:"新城区",150103:"回民区",150104:"玉泉区",150105:"赛罕区",150121:"土默特左旗",150122:"托克托县",150123:"和林格尔县",150124:"清水河县",150125:"武川县",150126:"其它区",150200:"包头市",150202:"东河区",150203:"昆都仑区",150204:"青山区",150205:"石拐区",150206:"白云鄂博矿区",150207:"九原区",150221:"土默特右旗",150222:"固阳县",150223:"达尔罕茂明安联合旗",150224:"其它区",150300:"乌海市",150302:"海勃湾区",150303:"海南区",150304:"乌达区",150305:"其它区",150400:"赤峰市",150402:"红山区",150403:"元宝山区",150404:"松山区",150421:"阿鲁科尔沁旗",150422:"巴林左旗",150423:"巴林右旗",150424:"林西县",150425:"克什克腾旗",150426:"翁牛特旗",150428:"喀喇沁旗",150429:"宁城县",150430:"敖汉旗",150431:"其它区",150500:"通辽市",150502:"科尔沁区",150521:"科尔沁左翼中旗",150522:"科尔沁左翼后旗",150523:"开鲁县",150524:"库伦旗",150525:"奈曼旗",150526:"扎鲁特旗",150581:"霍林郭勒市",150582:"其它区",150600:"鄂尔多斯市",150602:"东胜区",150621:"达拉特旗",150622:"准格尔旗",150623:"鄂托克前旗",150624:"鄂托克旗",150625:"杭锦旗",150626:"乌审旗",150627:"伊金霍洛旗",150628:"其它区",150700:"呼伦贝尔市",150702:"海拉尔区",150703:"扎赉诺尔区",150721:"阿荣旗",150722:"莫力达瓦达斡尔族自治旗",150723:"鄂伦春自治旗",150724:"鄂温克族自治旗",150725:"陈巴尔虎旗",150726:"新巴尔虎左旗",150727:"新巴尔虎右旗",150781:"满洲里市",150782:"牙克石市",150783:"扎兰屯市",150784:"额尔古纳市",150785:"根河市",150786:"其它区",150800:"巴彦淖尔市",150802:"临河区",150821:"五原县",150822:"磴口县",150823:"乌拉特前旗",150824:"乌拉特中旗",150825:"乌拉特后旗",150826:"杭锦后旗",150827:"其它区",150900:"乌兰察布市",150902:"集宁区",150921:"卓资县",150922:"化德县",150923:"商都县",150924:"兴和县",150925:"凉城县",150926:"察哈尔右翼前旗",150927:"察哈尔右翼中旗",150928:"察哈尔右翼后旗",150929:"四子王旗",150981:"丰镇市",150982:"其它区",152200:"兴安盟",152201:"乌兰浩特市",152202:"阿尔山市",152221:"科尔沁右翼前旗",152222:"科尔沁右翼中旗",152223:"扎赉特旗",152224:"突泉县",152225:"其它区",152500:"锡林郭勒盟",152501:"二连浩特市",152502:"锡林浩特市",152522:"阿巴嘎旗",152523:"苏尼特左旗",152524:"苏尼特右旗",152525:"东乌珠穆沁旗",152526:"西乌珠穆沁旗",152527:"太仆寺旗",152528:"镶黄旗",152529:"正镶白旗",152530:"正蓝旗",152531:"多伦县",152532:"其它区",152900:"阿拉善盟",152921:"阿拉善左旗",152922:"阿拉善右旗",152923:"额济纳旗",152924:"其它区",21e4:"辽宁省",210100:"沈阳市",210102:"和平区",210103:"沈河区",210104:"大东区",210105:"皇姑区",210106:"铁西区",210111:"苏家屯区",210112:"东陵区",210113:"新城子区",210114:"于洪区",210122:"辽中县",210123:"康平县",210124:"法库县",210181:"新民市",210184:"沈北新区",210185:"其它区",210200:"大连市",210202:"中山区",210203:"西岗区",210204:"沙河口区",210211:"甘井子区",210212:"旅顺口区",210213:"金州区",210224:"长海县",210281:"瓦房店市",210282:"普兰店市",210283:"庄河市",210298:"其它区",210300:"鞍山市",210302:"铁东区",210303:"铁西区",210304:"立山区",210311:"千山区",210321:"台安县",210323:"岫岩满族自治县",210381:"海城市",210382:"其它区",210400:"抚顺市",210402:"新抚区",210403:"东洲区",210404:"望花区",210411:"顺城区",210421:"抚顺县",210422:"新宾满族自治县",210423:"清原满族自治县",210424:"其它区",210500:"本溪市",210502:"平山区",210503:"溪湖区",210504:"明山区",210505:"南芬区",210521:"本溪满族自治县",210522:"桓仁满族自治县",210523:"其它区",210600:"丹东市",210602:"元宝区",210603:"振兴区",210604:"振安区",210624:"宽甸满族自治县",210681:"东港市",210682:"凤城市",210683:"其它区",210700:"锦州市",210702:"古塔区",210703:"凌河区",210711:"太和区",210726:"黑山县",210727:"义县",210781:"凌海市",210782:"北镇市",210783:"其它区",210800:"营口市",210802:"站前区",210803:"西市区",210804:"鲅鱼圈区",210811:"老边区",210881:"盖州市",210882:"大石桥市",210883:"其它区",210900:"阜新市",210902:"海州区",210903:"新邱区",210904:"太平区",210905:"清河门区",210911:"细河区",210921:"阜新蒙古族自治县",210922:"彰武县",210923:"其它区",211e3:"辽阳市",211002:"白塔区",211003:"文圣区",211004:"宏伟区",211005:"弓长岭区",211011:"太子河区",211021:"辽阳县",211081:"灯塔市",211082:"其它区",211100:"盘锦市",211102:"双台子区",211103:"兴隆台区",211121:"大洼县",211122:"盘山县",211123:"其它区",211200:"铁岭市",211202:"银州区",211204:"清河区",211221:"铁岭县",211223:"西丰县",211224:"昌图县",211281:"调兵山市",211282:"开原市",211283:"其它区",211300:"朝阳市",211302:"双塔区",211303:"龙城区",211321:"朝阳县",211322:"建平县",211324:"喀喇沁左翼蒙古族自治县",211381:"北票市",211382:"凌源市",211383:"其它区",211400:"葫芦岛市",211402:"连山区",211403:"龙港区",211404:"南票区",211421:"绥中县",211422:"建昌县",211481:"兴城市",211482:"其它区",22e4:"吉林省",220100:"长春市",220102:"南关区",220103:"宽城区",220104:"朝阳区",220105:"二道区",220106:"绿园区",220112:"双阳区",220122:"农安县",220181:"九台市",220182:"榆树市",220183:"德惠市",220188:"其它区",220200:"吉林市",220202:"昌邑区",220203:"龙潭区",220204:"船营区",220211:"丰满区",220221:"永吉县",220281:"蛟河市",220282:"桦甸市",220283:"舒兰市",220284:"磐石市",220285:"其它区",220300:"四平市",220302:"铁西区",220303:"铁东区",220322:"梨树县",220323:"伊通满族自治县",220381:"公主岭市",220382:"双辽市",220383:"其它区",220400:"辽源市",220402:"龙山区",220403:"西安区",220421:"东丰县",220422:"东辽县",220423:"其它区",220500:"通化市",220502:"东昌区",220503:"二道江区",220521:"通化县",220523:"辉南县",220524:"柳河县",220581:"梅河口市",220582:"集安市",220583:"其它区",220600:"白山市",220602:"浑江区",220621:"抚松县",220622:"靖宇县",220623:"长白朝鲜族自治县",220625:"江源区",220681:"临江市",220682:"其它区",220700:"松原市",220702:"宁江区",220721:"前郭尔罗斯蒙古族自治县",220722:"长岭县",220723:"乾安县",220724:"扶余市",220725:"其它区",220800:"白城市",220802:"洮北区",220821:"镇赉县",220822:"通榆县",220881:"洮南市",220882:"大安市",220883:"其它区",222400:"延边朝鲜族自治州",222401:"延吉市",222402:"图们市",222403:"敦化市",222404:"珲春市",222405:"龙井市",222406:"和龙市",222424:"汪清县",222426:"安图县",222427:"其它区",23e4:"黑龙江省",230100:"哈尔滨市",230102:"道里区",230103:"南岗区",230104:"道外区",230106:"香坊区",230108:"平房区",230109:"松北区",230111:"呼兰区",230123:"依兰县",230124:"方正县",230125:"宾县",230126:"巴彦县",230127:"木兰县",230128:"通河县",230129:"延寿县",230181:"阿城区",230182:"双城市",230183:"尚志市",230184:"五常市",230186:"其它区",230200:"齐齐哈尔市",230202:"龙沙区",230203:"建华区",230204:"铁锋区",230205:"昂昂溪区",230206:"富拉尔基区",230207:"碾子山区",230208:"梅里斯达斡尔族区",230221:"龙江县",230223:"依安县",230224:"泰来县",230225:"甘南县",230227:"富裕县",230229:"克山县",230230:"克东县",230231:"拜泉县",230281:"讷河市",230282:"其它区",230300:"鸡西市",230302:"鸡冠区",230303:"恒山区",230304:"滴道区",230305:"梨树区",230306:"城子河区",230307:"麻山区",230321:"鸡东县",230381:"虎林市",230382:"密山市",230383:"其它区",230400:"鹤岗市",230402:"向阳区",230403:"工农区",230404:"南山区",230405:"兴安区",230406:"东山区",230407:"兴山区",230421:"萝北县",230422:"绥滨县",230423:"其它区",230500:"双鸭山市",230502:"尖山区",230503:"岭东区",230505:"四方台区",230506:"宝山区",230521:"集贤县",230522:"友谊县",230523:"宝清县",230524:"饶河县",230525:"其它区",230600:"大庆市",230602:"萨尔图区",230603:"龙凤区",230604:"让胡路区",230605:"红岗区",230606:"大同区",230621:"肇州县",230622:"肇源县",230623:"林甸县",230624:"杜尔伯特蒙古族自治县",230625:"其它区",230700:"伊春市",230702:"伊春区",230703:"南岔区",230704:"友好区",230705:"西林区",230706:"翠峦区",230707:"新青区",230708:"美溪区",230709:"金山屯区",230710:"五营区",230711:"乌马河区",230712:"汤旺河区",230713:"带岭区",230714:"乌伊岭区",230715:"红星区",230716:"上甘岭区",230722:"嘉荫县",230781:"铁力市",230782:"其它区",230800:"佳木斯市",230803:"向阳区",230804:"前进区",230805:"东风区",230811:"郊区",230822:"桦南县",230826:"桦川县",230828:"汤原县",230833:"抚远县",230881:"同江市",230882:"富锦市",230883:"其它区",230900:"七台河市",230902:"新兴区",230903:"桃山区",230904:"茄子河区",230921:"勃利县",230922:"其它区",231e3:"牡丹江市",231002:"东安区",231003:"阳明区",231004:"爱民区",231005:"西安区",231024:"东宁县",231025:"林口县",231081:"绥芬河市",231083:"海林市",231084:"宁安市",231085:"穆棱市",231086:"其它区",231100:"黑河市",231102:"爱辉区",231121:"嫩江县",231123:"逊克县",231124:"孙吴县",231181:"北安市",231182:"五大连池市",231183:"其它区",231200:"绥化市",231202:"北林区",231221:"望奎县",231222:"兰西县",231223:"青冈县",231224:"庆安县",231225:"明水县",231226:"绥棱县",231281:"安达市",231282:"肇东市",231283:"海伦市",231284:"其它区",232700:"大兴安岭地区",232702:"松岭区",232703:"新林区",232704:"呼中区",232721:"呼玛县",232722:"塔河县",232723:"漠河县",232724:"加格达奇区",232725:"其它区",31e4:"上海",310100:"上海市",310101:"黄浦区",310104:"徐汇区",310105:"长宁区",310106:"静安区",310107:"普陀区",310108:"闸北区",310109:"虹口区",310110:"杨浦区",310112:"闵行区",310113:"宝山区",310114:"嘉定区",310115:"浦东新区",310116:"金山区",310117:"松江区",310118:"青浦区",310120:"奉贤区",310230:"崇明县",310231:"其它区",32e4:"江苏省",320100:"南京市",320102:"玄武区",320104:"秦淮区",320105:"建邺区",320106:"鼓楼区",320111:"浦口区",320113:"栖霞区",320114:"雨花台区",320115:"江宁区",320116:"六合区",320124:"溧水区",320125:"高淳区",320126:"其它区",320200:"无锡市",320202:"崇安区",320203:"南长区",320204:"北塘区",320205:"锡山区",320206:"惠山区",320211:"滨湖区",320281:"江阴市",320282:"宜兴市",320297:"其它区",320300:"徐州市",320302:"鼓楼区",320303:"云龙区",320305:"贾汪区",320311:"泉山区",320321:"丰县",320322:"沛县",320323:"铜山区",320324:"睢宁县",320381:"新沂市",320382:"邳州市",320383:"其它区",320400:"常州市",320402:"天宁区",320404:"钟楼区",320405:"戚墅堰区",320411:"新北区",320412:"武进区",320481:"溧阳市",320482:"金坛市",320483:"其它区",320500:"苏州市",320505:"虎丘区",320506:"吴中区",320507:"相城区",320508:"姑苏区",320581:"常熟市",320582:"张家港市",320583:"昆山市",320584:"吴江区",320585:"太仓市",320596:"其它区",320600:"南通市",320602:"崇川区",320611:"港闸区",320612:"通州区",320621:"海安县",320623:"如东县",320681:"启东市",320682:"如皋市",320684:"海门市",320694:"其它区",320700:"连云港市",320703:"连云区",320705:"新浦区",320706:"海州区",320721:"赣榆县",320722:"东海县",320723:"灌云县",320724:"灌南县",320725:"其它区",320800:"淮安市",320802:"清河区",320803:"淮安区",320804:"淮阴区",320811:"清浦区",320826:"涟水县",320829:"洪泽县",320830:"盱眙县",320831:"金湖县",320832:"其它区",320900:"盐城市",320902:"亭湖区",320903:"盐都区",320921:"响水县",320922:"滨海县",320923:"阜宁县",320924:"射阳县",320925:"建湖县",320981:"东台市",320982:"大丰市",320983:"其它区",321e3:"扬州市",321002:"广陵区",321003:"邗江区",321023:"宝应县",321081:"仪征市",321084:"高邮市",321088:"江都区",321093:"其它区",321100:"镇江市",321102:"京口区",321111:"润州区",321112:"丹徒区",321181:"丹阳市",321182:"扬中市",321183:"句容市",321184:"其它区",321200:"泰州市",321202:"海陵区",321203:"高港区",321281:"兴化市",321282:"靖江市",321283:"泰兴市",321284:"姜堰区",321285:"其它区",321300:"宿迁市",321302:"宿城区",321311:"宿豫区",321322:"沭阳县",321323:"泗阳县",321324:"泗洪县",321325:"其它区",33e4:"浙江省",330100:"杭州市",330102:"上城区",330103:"下城区",330104:"江干区",330105:"拱墅区",330106:"西湖区",330108:"滨江区",330109:"萧山区",330110:"余杭区",330122:"桐庐县",330127:"淳安县",330182:"建德市",330183:"富阳市",330185:"临安市",330186:"其它区",330200:"宁波市",330203:"海曙区",330204:"江东区",330205:"江北区",330206:"北仑区",330211:"镇海区",330212:"鄞州区",330225:"象山县",330226:"宁海县",330281:"余姚市",330282:"慈溪市",330283:"奉化市",330284:"其它区",330300:"温州市",330302:"鹿城区",330303:"龙湾区",330304:"瓯海区",330322:"洞头县",330324:"永嘉县",330326:"平阳县",330327:"苍南县",330328:"文成县",330329:"泰顺县",330381:"瑞安市",330382:"乐清市",330383:"其它区",330400:"嘉兴市",330402:"南湖区",330411:"秀洲区",330421:"嘉善县",330424:"海盐县",330481:"海宁市",330482:"平湖市",330483:"桐乡市",330484:"其它区",330500:"湖州市",330502:"吴兴区",330503:"南浔区",330521:"德清县",330522:"长兴县",330523:"安吉县",330524:"其它区",330600:"绍兴市",330602:"越城区",330621:"绍兴县",330624:"新昌县",330681:"诸暨市",330682:"上虞市",330683:"嵊州市",330684:"其它区",330700:"金华市",330702:"婺城区",330703:"金东区",330723:"武义县",330726:"浦江县",330727:"磐安县",330781:"兰溪市",330782:"义乌市",330783:"东阳市",330784:"永康市",330785:"其它区",330800:"衢州市",330802:"柯城区",330803:"衢江区",330822:"常山县",330824:"开化县",330825:"龙游县",330881:"江山市",330882:"其它区",330900:"舟山市",330902:"定海区",330903:"普陀区",330921:"岱山县",330922:"嵊泗县",330923:"其它区",331e3:"台州市",331002:"椒江区",331003:"黄岩区",331004:"路桥区",331021:"玉环县",331022:"三门县",331023:"天台县",331024:"仙居县",331081:"温岭市",331082:"临海市",331083:"其它区",331100:"丽水市",331102:"莲都区",331121:"青田县",331122:"缙云县",331123:"遂昌县",331124:"松阳县",331125:"云和县",331126:"庆元县",331127:"景宁畲族自治县",331181:"龙泉市",331182:"其它区",34e4:"安徽省",340100:"合肥市",340102:"瑶海区",340103:"庐阳区",340104:"蜀山区",340111:"包河区",340121:"长丰县",340122:"肥东县",340123:"肥西县",340192:"其它区",340200:"芜湖市",340202:"镜湖区",340203:"弋江区",340207:"鸠江区",340208:"三山区",340221:"芜湖县",340222:"繁昌县",340223:"南陵县",340224:"其它区",340300:"蚌埠市",340302:"龙子湖区",340303:"蚌山区",340304:"禹会区",340311:"淮上区",340321:"怀远县",340322:"五河县",340323:"固镇县",340324:"其它区",340400:"淮南市",340402:"大通区",340403:"田家庵区",340404:"谢家集区",340405:"八公山区",340406:"潘集区",340421:"凤台县",340422:"其它区",340500:"马鞍山市",340503:"花山区",340504:"雨山区",340506:"博望区",340521:"当涂县",340522:"其它区",340600:"淮北市",340602:"杜集区",340603:"相山区",340604:"烈山区",340621:"濉溪县",340622:"其它区",340700:"铜陵市",340702:"铜官山区",340703:"狮子山区",340711:"郊区",340721:"铜陵县",340722:"其它区",340800:"安庆市",340802:"迎江区",340803:"大观区",340811:"宜秀区",340822:"怀宁县",340823:"枞阳县",340824:"潜山县",340825:"太湖县",340826:"宿松县",340827:"望江县",340828:"岳西县",340881:"桐城市",340882:"其它区",341e3:"黄山市",341002:"屯溪区",341003:"黄山区",341004:"徽州区",341021:"歙县",341022:"休宁县",341023:"黟县",341024:"祁门县",341025:"其它区",341100:"滁州市",341102:"琅琊区",341103:"南谯区",341122:"来安县",341124:"全椒县",341125:"定远县",341126:"凤阳县",341181:"天长市",341182:"明光市",341183:"其它区",341200:"阜阳市",341202:"颍州区",341203:"颍东区",341204:"颍泉区",341221:"临泉县",341222:"太和县",341225:"阜南县",341226:"颍上县",341282:"界首市",341283:"其它区",341300:"宿州市",341302:"埇桥区",341321:"砀山县",341322:"萧县",341323:"灵璧县",341324:"泗县",341325:"其它区",341400:"巢湖市",341421:"庐江县",341422:"无为县",341423:"含山县",341424:"和县",341500:"六安市",341502:"金安区",341503:"裕安区",341521:"寿县",341522:"霍邱县",341523:"舒城县",341524:"金寨县",341525:"霍山县",341526:"其它区",341600:"亳州市",341602:"谯城区",341621:"涡阳县",341622:"蒙城县",341623:"利辛县",341624:"其它区",341700:"池州市",341702:"贵池区",341721:"东至县",341722:"石台县",341723:"青阳县",341724:"其它区",341800:"宣城市",341802:"宣州区",341821:"郎溪县",341822:"广德县",341823:"泾县",341824:"绩溪县",341825:"旌德县",341881:"宁国市",341882:"其它区",35e4:"福建省",350100:"福州市",350102:"鼓楼区",350103:"台江区",350104:"仓山区",350105:"马尾区",350111:"晋安区",350121:"闽侯县",350122:"连江县",350123:"罗源县",350124:"闽清县",350125:"永泰县",350128:"平潭县",350181:"福清市",350182:"长乐市",350183:"其它区",350200:"厦门市",350203:"思明区",350205:"海沧区",350206:"湖里区",350211:"集美区",350212:"同安区",350213:"翔安区",350214:"其它区",350300:"莆田市",350302:"城厢区",350303:"涵江区",350304:"荔城区",350305:"秀屿区",350322:"仙游县",350323:"其它区",350400:"三明市",350402:"梅列区",350403:"三元区",350421:"明溪县",350423:"清流县",350424:"宁化县",350425:"大田县",350426:"尤溪县",350427:"沙县",350428:"将乐县",350429:"泰宁县",350430:"建宁县",350481:"永安市",350482:"其它区",350500:"泉州市",350502:"鲤城区",350503:"丰泽区",350504:"洛江区",350505:"泉港区",350521:"惠安县",350524:"安溪县",350525:"永春县",350526:"德化县",350527:"金门县",350581:"石狮市",350582:"晋江市",350583:"南安市",350584:"其它区",350600:"漳州市",350602:"芗城区",350603:"龙文区",350622:"云霄县",350623:"漳浦县",350624:"诏安县",350625:"长泰县",350626:"东山县",350627:"南靖县",350628:"平和县",350629:"华安县",350681:"龙海市",350682:"其它区",350700:"南平市",350702:"延平区",350721:"顺昌县",350722:"浦城县",350723:"光泽县",350724:"松溪县",350725:"政和县",350781:"邵武市",350782:"武夷山市",350783:"建瓯市",350784:"建阳市",350785:"其它区",350800:"龙岩市",350802:"新罗区",350821:"长汀县",350822:"永定县",350823:"上杭县",350824:"武平县",350825:"连城县",350881:"漳平市",350882:"其它区",350900:"宁德市",350902:"蕉城区",350921:"霞浦县",350922:"古田县",350923:"屏南县",350924:"寿宁县",350925:"周宁县",350926:"柘荣县",350981:"福安市",350982:"福鼎市",350983:"其它区",36e4:"江西省",360100:"南昌市",360102:"东湖区",360103:"西湖区",360104:"青云谱区",360105:"湾里区",360111:"青山湖区",360121:"南昌县",360122:"新建县",360123:"安义县",360124:"进贤县",360128:"其它区",360200:"景德镇市",360202:"昌江区",360203:"珠山区",360222:"浮梁县",360281:"乐平市",360282:"其它区",360300:"萍乡市",360302:"安源区",360313:"湘东区",360321:"莲花县",360322:"上栗县",360323:"芦溪县",360324:"其它区",360400:"九江市",360402:"庐山区",360403:"浔阳区",360421:"九江县",360423:"武宁县",360424:"修水县",360425:"永修县",360426:"德安县",360427:"星子县",360428:"都昌县",360429:"湖口县",360430:"彭泽县",360481:"瑞昌市",360482:"其它区",360483:"共青城市",360500:"新余市",360502:"渝水区",360521:"分宜县",360522:"其它区",360600:"鹰潭市",360602:"月湖区",360622:"余江县",360681:"贵溪市",360682:"其它区",360700:"赣州市",360702:"章贡区",360721:"赣县",360722:"信丰县",360723:"大余县",360724:"上犹县",360725:"崇义县",360726:"安远县",360727:"龙南县",360728:"定南县",360729:"全南县",360730:"宁都县",360731:"于都县",360732:"兴国县",360733:"会昌县",360734:"寻乌县",360735:"石城县",360781:"瑞金市",360782:"南康市",360783:"其它区",360800:"吉安市",360802:"吉州区",360803:"青原区",360821:"吉安县",360822:"吉水县",360823:"峡江县",360824:"新干县",360825:"永丰县",360826:"泰和县",360827:"遂川县",360828:"万安县",360829:"安福县",360830:"永新县",360881:"井冈山市",360882:"其它区",360900:"宜春市",360902:"袁州区",360921:"奉新县",360922:"万载县",360923:"上高县",360924:"宜丰县",360925:"靖安县",360926:"铜鼓县",360981:"丰城市",360982:"樟树市",360983:"高安市",360984:"其它区",361e3:"抚州市",361002:"临川区",361021:"南城县",361022:"黎川县",361023:"南丰县",361024:"崇仁县",361025:"乐安县",361026:"宜黄县",361027:"金溪县",361028:"资溪县",361029:"东乡县",361030:"广昌县",361031:"其它区",361100:"上饶市",361102:"信州区",361121:"上饶县",361122:"广丰县",361123:"玉山县",361124:"铅山县",361125:"横峰县",361126:"弋阳县",361127:"余干县",361128:"鄱阳县",361129:"万年县",361130:"婺源县",361181:"德兴市",361182:"其它区",37e4:"山东省",370100:"济南市",370102:"历下区",370103:"市中区",370104:"槐荫区",370105:"天桥区",370112:"历城区",370113:"长清区",370124:"平阴县",370125:"济阳县",370126:"商河县",370181:"章丘市",370182:"其它区",370200:"青岛市",370202:"市南区",370203:"市北区",370211:"黄岛区",370212:"崂山区",370213:"李沧区",370214:"城阳区",370281:"胶州市",370282:"即墨市",370283:"平度市",370285:"莱西市",370286:"其它区",370300:"淄博市",370302:"淄川区",370303:"张店区",370304:"博山区",370305:"临淄区",370306:"周村区",370321:"桓台县",370322:"高青县",370323:"沂源县",370324:"其它区",370400:"枣庄市",370402:"市中区",370403:"薛城区",370404:"峄城区",370405:"台儿庄区",370406:"山亭区",370481:"滕州市",370482:"其它区",370500:"东营市",370502:"东营区",370503:"河口区",370521:"垦利县",370522:"利津县",370523:"广饶县",370591:"其它区",370600:"烟台市",370602:"芝罘区",370611:"福山区",370612:"牟平区",370613:"莱山区",370634:"长岛县",370681:"龙口市",370682:"莱阳市",370683:"莱州市",370684:"蓬莱市",370685:"招远市",370686:"栖霞市",370687:"海阳市",370688:"其它区",370700:"潍坊市",370702:"潍城区",370703:"寒亭区",370704:"坊子区",370705:"奎文区",370724:"临朐县",370725:"昌乐县",370781:"青州市",370782:"诸城市",370783:"寿光市",370784:"安丘市",370785:"高密市",370786:"昌邑市",370787:"其它区",370800:"济宁市",370802:"市中区",370811:"任城区",370826:"微山县",370827:"鱼台县",370828:"金乡县",370829:"嘉祥县",370830:"汶上县",370831:"泗水县",370832:"梁山县",370881:"曲阜市",370882:"兖州市",370883:"邹城市",370884:"其它区",370900:"泰安市",370902:"泰山区",370903:"岱岳区",370921:"宁阳县",370923:"东平县",370982:"新泰市",370983:"肥城市",370984:"其它区",371e3:"威海市",371002:"环翠区",371081:"文登市",371082:"荣成市",371083:"乳山市",371084:"其它区",371100:"日照市",371102:"东港区",371103:"岚山区",371121:"五莲县",371122:"莒县",371123:"其它区",371200:"莱芜市",371202:"莱城区",371203:"钢城区",371204:"其它区",371300:"临沂市",371302:"兰山区",371311:"罗庄区",371312:"河东区",371321:"沂南县",371322:"郯城县",371323:"沂水县",371324:"苍山县",371325:"费县",371326:"平邑县",371327:"莒南县",371328:"蒙阴县",371329:"临沭县",371330:"其它区",371400:"德州市",371402:"德城区",371421:"陵县",371422:"宁津县",371423:"庆云县",371424:"临邑县",371425:"齐河县",371426:"平原县",371427:"夏津县",371428:"武城县",371481:"乐陵市",371482:"禹城市",371483:"其它区",371500:"聊城市",371502:"东昌府区",371521:"阳谷县",371522:"莘县",371523:"茌平县",371524:"东阿县",371525:"冠县",371526:"高唐县",371581:"临清市",371582:"其它区",371600:"滨州市",371602:"滨城区",371621:"惠民县",371622:"阳信县",371623:"无棣县",371624:"沾化县",371625:"博兴县",371626:"邹平县",371627:"其它区",371700:"菏泽市",371702:"牡丹区",371721:"曹县",371722:"单县",371723:"成武县",371724:"巨野县",371725:"郓城县",371726:"鄄城县",371727:"定陶县",371728:"东明县",371729:"其它区",41e4:"河南省",410100:"郑州市",410102:"中原区",410103:"二七区",410104:"管城回族区",410105:"金水区",410106:"上街区",410108:"惠济区",410122:"中牟县",410181:"巩义市",410182:"荥阳市",410183:"新密市",410184:"新郑市",410185:"登封市",410188:"其它区",410200:"开封市",410202:"龙亭区",410203:"顺河回族区",410204:"鼓楼区",410205:"禹王台区",410211:"金明区",410221:"杞县",410222:"通许县",410223:"尉氏县",410224:"开封县",410225:"兰考县",410226:"其它区",410300:"洛阳市",410302:"老城区",410303:"西工区",410304:"瀍河回族区",410305:"涧西区",410306:"吉利区",410307:"洛龙区",410322:"孟津县",410323:"新安县",410324:"栾川县",410325:"嵩县",410326:"汝阳县",410327:"宜阳县",410328:"洛宁县",410329:"伊川县",410381:"偃师市",410400:"平顶山市",410402:"新华区",410403:"卫东区",410404:"石龙区",410411:"湛河区",410421:"宝丰县",410422:"叶县",410423:"鲁山县",410425:"郏县",410481:"舞钢市",410482:"汝州市",410483:"其它区",410500:"安阳市",410502:"文峰区",410503:"北关区",410505:"殷都区",410506:"龙安区",410522:"安阳县",410523:"汤阴县",410526:"滑县",410527:"内黄县",410581:"林州市",410582:"其它区",410600:"鹤壁市",410602:"鹤山区",410603:"山城区",410611:"淇滨区",410621:"浚县",410622:"淇县",410623:"其它区",410700:"新乡市",410702:"红旗区",410703:"卫滨区",410704:"凤泉区",410711:"牧野区",410721:"新乡县",410724:"获嘉县",410725:"原阳县",410726:"延津县",410727:"封丘县",410728:"长垣县",410781:"卫辉市",410782:"辉县市",410783:"其它区",410800:"焦作市",410802:"解放区",410803:"中站区",410804:"马村区",410811:"山阳区",410821:"修武县",410822:"博爱县",410823:"武陟县",410825:"温县",410881:"济源市",410882:"沁阳市",410883:"孟州市",410884:"其它区",410900:"濮阳市",410902:"华龙区",410922:"清丰县",410923:"南乐县",410926:"范县",410927:"台前县",410928:"濮阳县",410929:"其它区",411e3:"许昌市",411002:"魏都区",411023:"许昌县",411024:"鄢陵县",411025:"襄城县",411081:"禹州市",411082:"长葛市",411083:"其它区",411100:"漯河市",411102:"源汇区",411103:"郾城区",411104:"召陵区",411121:"舞阳县",411122:"临颍县",411123:"其它区",411200:"三门峡市",411202:"湖滨区",411221:"渑池县",411222:"陕县",411224:"卢氏县",411281:"义马市",411282:"灵宝市",411283:"其它区",411300:"南阳市",411302:"宛城区",411303:"卧龙区",411321:"南召县",411322:"方城县",411323:"西峡县",411324:"镇平县",411325:"内乡县",411326:"淅川县",411327:"社旗县",411328:"唐河县",411329:"新野县",411330:"桐柏县",411381:"邓州市",411382:"其它区",411400:"商丘市",411402:"梁园区",411403:"睢阳区",411421:"民权县",411422:"睢县",411423:"宁陵县",411424:"柘城县",411425:"虞城县",411426:"夏邑县",411481:"永城市",411482:"其它区",411500:"信阳市",411502:"浉河区",411503:"平桥区",411521:"罗山县",411522:"光山县",411523:"新县",411524:"商城县",411525:"固始县",411526:"潢川县",411527:"淮滨县",411528:"息县",411529:"其它区",411600:"周口市",411602:"川汇区",411621:"扶沟县",411622:"西华县",411623:"商水县",411624:"沈丘县",411625:"郸城县",411626:"淮阳县",411627:"太康县",411628:"鹿邑县",411681:"项城市",411682:"其它区",411700:"驻马店市",411702:"驿城区",411721:"西平县",411722:"上蔡县",411723:"平舆县",411724:"正阳县",411725:"确山县",411726:"泌阳县",411727:"汝南县",411728:"遂平县",411729:"新蔡县",411730:"其它区",42e4:"湖北省",420100:"武汉市",420102:"江岸区",420103:"江汉区",420104:"硚口区",420105:"汉阳区",420106:"武昌区",420107:"青山区",420111:"洪山区",420112:"东西湖区",420113:"汉南区",420114:"蔡甸区",420115:"江夏区",420116:"黄陂区",420117:"新洲区",420118:"其它区",420200:"黄石市",420202:"黄石港区",420203:"西塞山区",420204:"下陆区",420205:"铁山区",420222:"阳新县",420281:"大冶市",420282:"其它区",420300:"十堰市",420302:"茅箭区",420303:"张湾区",420321:"郧县",420322:"郧西县",420323:"竹山县",420324:"竹溪县",420325:"房县",420381:"丹江口市",420383:"其它区",420500:"宜昌市",420502:"西陵区",420503:"伍家岗区",420504:"点军区",420505:"猇亭区",420506:"夷陵区",420525:"远安县",420526:"兴山县",420527:"秭归县",420528:"长阳土家族自治县",420529:"五峰土家族自治县",420581:"宜都市",420582:"当阳市",420583:"枝江市",420584:"其它区",420600:"襄阳市",420602:"襄城区",420606:"樊城区",420607:"襄州区",420624:"南漳县",420625:"谷城县",420626:"保康县",420682:"老河口市",420683:"枣阳市",420684:"宜城市",420685:"其它区",420700:"鄂州市",420702:"梁子湖区",420703:"华容区",420704:"鄂城区",420705:"其它区",420800:"荆门市",420802:"东宝区",420804:"掇刀区",420821:"京山县",420822:"沙洋县",420881:"钟祥市",420882:"其它区",420900:"孝感市",420902:"孝南区",420921:"孝昌县",420922:"大悟县",420923:"云梦县",420981:"应城市",420982:"安陆市",420984:"汉川市",420985:"其它区",421e3:"荆州市",421002:"沙市区",421003:"荆州区",421022:"公安县",421023:"监利县",421024:"江陵县",421081:"石首市",421083:"洪湖市",421087:"松滋市",421088:"其它区",421100:"黄冈市",421102:"黄州区",421121:"团风县",421122:"红安县",421123:"罗田县",421124:"英山县",421125:"浠水县",421126:"蕲春县",421127:"黄梅县",421181:"麻城市",421182:"武穴市",421183:"其它区",421200:"咸宁市",421202:"咸安区",421221:"嘉鱼县",421222:"通城县",421223:"崇阳县",421224:"通山县",421281:"赤壁市",421283:"其它区",421300:"随州市",421302:"曾都区",421321:"随县",421381:"广水市",421382:"其它区",422800:"恩施土家族苗族自治州",422801:"恩施市",422802:"利川市",422822:"建始县",422823:"巴东县",422825:"宣恩县",422826:"咸丰县",422827:"来凤县",422828:"鹤峰县",422829:"其它区",429004:"仙桃市",429005:"潜江市",429006:"天门市",429021:"神农架林区",43e4:"湖南省",430100:"长沙市",430102:"芙蓉区",430103:"天心区",430104:"岳麓区",430105:"开福区",430111:"雨花区",430121:"长沙县",430122:"望城区",430124:"宁乡县",430181:"浏阳市",430182:"其它区",430200:"株洲市",430202:"荷塘区",430203:"芦淞区",430204:"石峰区",430211:"天元区",430221:"株洲县",430223:"攸县",430224:"茶陵县",430225:"炎陵县",430281:"醴陵市",430282:"其它区",430300:"湘潭市",430302:"雨湖区",430304:"岳塘区",430321:"湘潭县",430381:"湘乡市",430382:"韶山市",430383:"其它区",430400:"衡阳市",430405:"珠晖区",430406:"雁峰区",430407:"石鼓区",430408:"蒸湘区",430412:"南岳区",430421:"衡阳县",430422:"衡南县",430423:"衡山县",430424:"衡东县",430426:"祁东县",430481:"耒阳市",430482:"常宁市",430483:"其它区",430500:"邵阳市",430502:"双清区",430503:"大祥区",430511:"北塔区",430521:"邵东县",430522:"新邵县",430523:"邵阳县",430524:"隆回县",430525:"洞口县",430527:"绥宁县",430528:"新宁县",430529:"城步苗族自治县",430581:"武冈市",430582:"其它区",430600:"岳阳市",430602:"岳阳楼区",430603:"云溪区",430611:"君山区",430621:"岳阳县",430623:"华容县",430624:"湘阴县",430626:"平江县",430681:"汨罗市",430682:"临湘市",430683:"其它区",430700:"常德市",430702:"武陵区",430703:"鼎城区",430721:"安乡县",430722:"汉寿县",430723:"澧县",430724:"临澧县",430725:"桃源县",430726:"石门县",430781:"津市市",430782:"其它区",430800:"张家界市",430802:"永定区",430811:"武陵源区",430821:"慈利县",430822:"桑植县",430823:"其它区",430900:"益阳市",430902:"资阳区",430903:"赫山区",430921:"南县",430922:"桃江县",430923:"安化县",430981:"沅江市",430982:"其它区",431e3:"郴州市",431002:"北湖区",431003:"苏仙区",431021:"桂阳县",431022:"宜章县",431023:"永兴县",431024:"嘉禾县",431025:"临武县",431026:"汝城县",431027:"桂东县",431028:"安仁县",431081:"资兴市",431082:"其它区",431100:"永州市",431102:"零陵区",431103:"冷水滩区",431121:"祁阳县",431122:"东安县",431123:"双牌县",431124:"道县",431125:"江永县",431126:"宁远县",431127:"蓝山县",431128:"新田县",431129:"江华瑶族自治县",431130:"其它区",431200:"怀化市",431202:"鹤城区",431221:"中方县",431222:"沅陵县",431223:"辰溪县",431224:"溆浦县",431225:"会同县",431226:"麻阳苗族自治县",431227:"新晃侗族自治县",431228:"芷江侗族自治县",431229:"靖州苗族侗族自治县",431230:"通道侗族自治县",431281:"洪江市",431282:"其它区",431300:"娄底市",431302:"娄星区",431321:"双峰县",431322:"新化县",431381:"冷水江市",431382:"涟源市",431383:"其它区",433100:"湘西土家族苗族自治州",433101:"吉首市",433122:"泸溪县",433123:"凤凰县",433124:"花垣县",433125:"保靖县",433126:"古丈县",433127:"永顺县",433130:"龙山县",433131:"其它区",44e4:"广东省",440100:"广州市",440103:"荔湾区",440104:"越秀区",440105:"海珠区",440106:"天河区",440111:"白云区",440112:"黄埔区",440113:"番禺区",440114:"花都区",440115:"南沙区",440116:"萝岗区",440183:"增城市",440184:"从化市",440189:"其它区",440200:"韶关市",440203:"武江区",440204:"浈江区",440205:"曲江区",440222:"始兴县",440224:"仁化县",440229:"翁源县",440232:"乳源瑶族自治县",440233:"新丰县",440281:"乐昌市",440282:"南雄市",440283:"其它区",440300:"深圳市",440303:"罗湖区",440304:"福田区",440305:"南山区",440306:"宝安区",440307:"龙岗区",440308:"盐田区",440309:"其它区",440320:"光明新区",440321:"坪山新区",440322:"大鹏新区",440323:"龙华新区",440400:"珠海市",440402:"香洲区",440403:"斗门区",440404:"金湾区",440488:"其它区",440500:"汕头市",440507:"龙湖区",440511:"金平区",440512:"濠江区",440513:"潮阳区",440514:"潮南区",440515:"澄海区",440523:"南澳县",440524:"其它区",440600:"佛山市",440604:"禅城区",440605:"南海区",440606:"顺德区",440607:"三水区",440608:"高明区",440609:"其它区",440700:"江门市",440703:"蓬江区",440704:"江海区",440705:"新会区",440781:"台山市",440783:"开平市",440784:"鹤山市",440785:"恩平市",440786:"其它区",440800:"湛江市",440802:"赤坎区",440803:"霞山区",440804:"坡头区",440811:"麻章区",440823:"遂溪县",440825:"徐闻县",440881:"廉江市",440882:"雷州市",440883:"吴川市",440884:"其它区",440900:"茂名市",440902:"茂南区",440903:"茂港区",440923:"电白县",440981:"高州市",440982:"化州市",440983:"信宜市",440984:"其它区",441200:"肇庆市",441202:"端州区",441203:"鼎湖区",441223:"广宁县",441224:"怀集县",441225:"封开县",441226:"德庆县",441283:"高要市",441284:"四会市",441285:"其它区",441300:"惠州市",441302:"惠城区",441303:"惠阳区",441322:"博罗县",441323:"惠东县",441324:"龙门县",441325:"其它区",441400:"梅州市",441402:"梅江区",441421:"梅县",441422:"大埔县",441423:"丰顺县",441424:"五华县",441426:"平远县",441427:"蕉岭县",441481:"兴宁市",441482:"其它区",441500:"汕尾市",441502:"城区",441521:"海丰县",441523:"陆河县",441581:"陆丰市",441582:"其它区",441600:"河源市",441602:"源城区",441621:"紫金县",441622:"龙川县",441623:"连平县",441624:"和平县",441625:"东源县",441626:"其它区",441700:"阳江市",441702:"江城区",441721:"阳西县",441723:"阳东县",441781:"阳春市",441782:"其它区",441800:"清远市",441802:"清城区",441821:"佛冈县",441823:"阳山县",441825:"连山壮族瑶族自治县",441826:"连南瑶族自治县",441827:"清新区",441881:"英德市",441882:"连州市",441883:"其它区",441900:"东莞市",442e3:"中山市",442101:"东沙群岛",445100:"潮州市",445102:"湘桥区",445121:"潮安区",445122:"饶平县",445186:"其它区",445200:"揭阳市",445202:"榕城区",445221:"揭东区",445222:"揭西县",445224:"惠来县",445281:"普宁市",445285:"其它区",445300:"云浮市",445302:"云城区",445321:"新兴县",445322:"郁南县",445323:"云安县",445381:"罗定市",445382:"其它区",45e4:"广西壮族自治区",450100:"南宁市",450102:"兴宁区",450103:"青秀区",450105:"江南区",450107:"西乡塘区",450108:"良庆区",450109:"邕宁区",450122:"武鸣县",450123:"隆安县",450124:"马山县",450125:"上林县",450126:"宾阳县",450127:"横县",450128:"其它区",450200:"柳州市",450202:"城中区",450203:"鱼峰区",450204:"柳南区",450205:"柳北区",450221:"柳江县",450222:"柳城县",450223:"鹿寨县",450224:"融安县",450225:"融水苗族自治县",450226:"三江侗族自治县",450227:"其它区",450300:"桂林市",450302:"秀峰区",450303:"叠彩区",450304:"象山区",450305:"七星区",450311:"雁山区",450321:"阳朔县",450322:"临桂区",450323:"灵川县",450324:"全州县",450325:"兴安县",450326:"永福县",450327:"灌阳县",450328:"龙胜各族自治县",450329:"资源县",450330:"平乐县",450331:"荔浦县",450332:"恭城瑶族自治县",450333:"其它区",450400:"梧州市",450403:"万秀区",450405:"长洲区",450406:"龙圩区",450421:"苍梧县",450422:"藤县",450423:"蒙山县",450481:"岑溪市",450482:"其它区",450500:"北海市",450502:"海城区",450503:"银海区",450512:"铁山港区",450521:"合浦县",450522:"其它区",450600:"防城港市",450602:"港口区",450603:"防城区",450621:"上思县",450681:"东兴市",450682:"其它区",450700:"钦州市",450702:"钦南区",450703:"钦北区",450721:"灵山县",450722:"浦北县",450723:"其它区",450800:"贵港市",450802:"港北区",450803:"港南区",450804:"覃塘区",450821:"平南县",450881:"桂平市",450882:"其它区",450900:"玉林市",450902:"玉州区",450903:"福绵区",450921:"容县",450922:"陆川县",450923:"博白县",450924:"兴业县",450981:"北流市",450982:"其它区",451e3:"百色市",451002:"右江区",451021:"田阳县",451022:"田东县",451023:"平果县",451024:"德保县",451025:"靖西县",451026:"那坡县",451027:"凌云县",451028:"乐业县",451029:"田林县",451030:"西林县",451031:"隆林各族自治县",451032:"其它区",451100:"贺州市",451102:"八步区",451119:"平桂管理区",451121:"昭平县",451122:"钟山县",451123:"富川瑶族自治县",451124:"其它区",451200:"河池市",451202:"金城江区",451221:"南丹县",451222:"天峨县",451223:"凤山县",451224:"东兰县",451225:"罗城仫佬族自治县",451226:"环江毛南族自治县",451227:"巴马瑶族自治县",451228:"都安瑶族自治县",451229:"大化瑶族自治县",451281:"宜州市",451282:"其它区",451300:"来宾市",451302:"兴宾区",451321:"忻城县",451322:"象州县",451323:"武宣县",451324:"金秀瑶族自治县",451381:"合山市",451382:"其它区",451400:"崇左市",451402:"江州区",451421:"扶绥县",451422:"宁明县",451423:"龙州县",451424:"大新县",451425:"天等县",451481:"凭祥市",451482:"其它区",46e4:"海南省",460100:"海口市",460105:"秀英区",460106:"龙华区",460107:"琼山区",460108:"美兰区",460109:"其它区",460200:"三亚市",460300:"三沙市",460321:"西沙群岛",460322:"南沙群岛",460323:"中沙群岛的岛礁及其海域",469001:"五指山市",469002:"琼海市",469003:"儋州市",469005:"文昌市",469006:"万宁市",469007:"东方市",469025:"定安县",469026:"屯昌县",469027:"澄迈县",469028:"临高县",469030:"白沙黎族自治县",469031:"昌江黎族自治县",469033:"乐东黎族自治县",469034:"陵水黎族自治县",469035:"保亭黎族苗族自治县",469036:"琼中黎族苗族自治县",471005:"其它区",5e5:"重庆",500100:"重庆市",500101:"万州区",500102:"涪陵区",500103:"渝中区",500104:"大渡口区",500105:"江北区",500106:"沙坪坝区",500107:"九龙坡区",500108:"南岸区",500109:"北碚区",500110:"万盛区",500111:"双桥区",500112:"渝北区",500113:"巴南区",500114:"黔江区",500115:"长寿区",500222:"綦江区",500223:"潼南县",500224:"铜梁县",500225:"大足区",500226:"荣昌县",500227:"璧山县",500228:"梁平县",500229:"城口县",500230:"丰都县",500231:"垫江县",500232:"武隆县",500233:"忠县",500234:"开县",500235:"云阳县",500236:"奉节县",500237:"巫山县",500238:"巫溪县",500240:"石柱土家族自治县",500241:"秀山土家族苗族自治县",500242:"酉阳土家族苗族自治县",500243:"彭水苗族土家族自治县",500381:"江津区",500382:"合川区",500383:"永川区",500384:"南川区",500385:"其它区",51e4:"四川省",510100:"成都市",510104:"锦江区",510105:"青羊区",510106:"金牛区",510107:"武侯区",510108:"成华区",510112:"龙泉驿区",510113:"青白江区",510114:"新都区",510115:"温江区",510121:"金堂县",510122:"双流县",510124:"郫县",510129:"大邑县",510131:"蒲江县",510132:"新津县",510181:"都江堰市",510182:"彭州市",510183:"邛崃市",510184:"崇州市",510185:"其它区",510300:"自贡市",510302:"自流井区",510303:"贡井区",510304:"大安区",510311:"沿滩区",510321:"荣县",510322:"富顺县",510323:"其它区",510400:"攀枝花市",510402:"东区",510403:"西区",510411:"仁和区",510421:"米易县",510422:"盐边县",510423:"其它区",510500:"泸州市",510502:"江阳区",510503:"纳溪区",510504:"龙马潭区",510521:"泸县",510522:"合江县",510524:"叙永县",510525:"古蔺县",510526:"其它区",510600:"德阳市",510603:"旌阳区",510623:"中江县",510626:"罗江县",510681:"广汉市",510682:"什邡市",510683:"绵竹市",510684:"其它区",510700:"绵阳市",510703:"涪城区",510704:"游仙区",510722:"三台县",510723:"盐亭县",510724:"安县",510725:"梓潼县",510726:"北川羌族自治县",510727:"平武县",510781:"江油市",510782:"其它区",510800:"广元市",510802:"利州区",510811:"昭化区",510812:"朝天区",510821:"旺苍县",510822:"青川县",510823:"剑阁县",510824:"苍溪县",510825:"其它区",510900:"遂宁市",510903:"船山区",510904:"安居区",510921:"蓬溪县",510922:"射洪县",510923:"大英县",510924:"其它区",511e3:"内江市",511002:"市中区",511011:"东兴区",511024:"威远县",511025:"资中县",511028:"隆昌县",511029:"其它区",511100:"乐山市",511102:"市中区",511111:"沙湾区",511112:"五通桥区",511113:"金口河区",511123:"犍为县",511124:"井研县",511126:"夹江县",511129:"沐川县",511132:"峨边彝族自治县",511133:"马边彝族自治县",511181:"峨眉山市",511182:"其它区",511300:"南充市",511302:"顺庆区",511303:"高坪区",511304:"嘉陵区",511321:"南部县",511322:"营山县",511323:"蓬安县",511324:"仪陇县",511325:"西充县",511381:"阆中市",511382:"其它区",511400:"眉山市",511402:"东坡区",511421:"仁寿县",511422:"彭山县",511423:"洪雅县",511424:"丹棱县",511425:"青神县",511426:"其它区",511500:"宜宾市",511502:"翠屏区",511521:"宜宾县",511522:"南溪区",511523:"江安县",511524:"长宁县",511525:"高县",511526:"珙县",511527:"筠连县",511528:"兴文县",511529:"屏山县",511530:"其它区",511600:"广安市",511602:"广安区",511603:"前锋区",511621:"岳池县",511622:"武胜县",511623:"邻水县",511681:"华蓥市",511683:"其它区",511700:"达州市",511702:"通川区",511721:"达川区",511722:"宣汉县",511723:"开江县",511724:"大竹县",511725:"渠县",511781:"万源市",511782:"其它区",511800:"雅安市",511802:"雨城区",511821:"名山区",511822:"荥经县",511823:"汉源县",511824:"石棉县",511825:"天全县",511826:"芦山县",511827:"宝兴县",511828:"其它区",511900:"巴中市",511902:"巴州区",511903:"恩阳区",511921:"通江县",511922:"南江县",511923:"平昌县",511924:"其它区",512e3:"资阳市",512002:"雁江区",512021:"安岳县",512022:"乐至县",512081:"简阳市",512082:"其它区",513200:"阿坝藏族羌族自治州",513221:"汶川县",513222:"理县",513223:"茂县",513224:"松潘县",513225:"九寨沟县",513226:"金川县",513227:"小金县",513228:"黑水县",513229:"马尔康县",513230:"壤塘县",513231:"阿坝县",513232:"若尔盖县",513233:"红原县",513234:"其它区",513300:"甘孜藏族自治州",513321:"康定县",513322:"泸定县",513323:"丹巴县",513324:"九龙县",513325:"雅江县",513326:"道孚县",513327:"炉霍县",513328:"甘孜县",513329:"新龙县",513330:"德格县",513331:"白玉县",513332:"石渠县",513333:"色达县",513334:"理塘县",513335:"巴塘县",513336:"乡城县",513337:"稻城县",513338:"得荣县",513339:"其它区",513400:"凉山彝族自治州",513401:"西昌市",513422:"木里藏族自治县",513423:"盐源县",513424:"德昌县",513425:"会理县",513426:"会东县",513427:"宁南县",513428:"普格县",513429:"布拖县",513430:"金阳县",513431:"昭觉县",513432:"喜德县",513433:"冕宁县",513434:"越西县",513435:"甘洛县",513436:"美姑县",513437:"雷波县",513438:"其它区",52e4:"贵州省",520100:"贵阳市",520102:"南明区",520103:"云岩区",520111:"花溪区",520112:"乌当区",520113:"白云区",520121:"开阳县",520122:"息烽县",520123:"修文县",520151:"观山湖区",520181:"清镇市",520182:"其它区",520200:"六盘水市",520201:"钟山区",520203:"六枝特区",520221:"水城县",520222:"盘县",520223:"其它区",520300:"遵义市",520302:"红花岗区",520303:"汇川区",520321:"遵义县",520322:"桐梓县",520323:"绥阳县",520324:"正安县",520325:"道真仡佬族苗族自治县",520326:"务川仡佬族苗族自治县",520327:"凤冈县",520328:"湄潭县",520329:"余庆县",520330:"习水县",520381:"赤水市",520382:"仁怀市",520383:"其它区",520400:"安顺市",520402:"西秀区",520421:"平坝县",520422:"普定县",520423:"镇宁布依族苗族自治县",520424:"关岭布依族苗族自治县",520425:"紫云苗族布依族自治县",520426:"其它区",522200:"铜仁市",522201:"碧江区",522222:"江口县",522223:"玉屏侗族自治县",522224:"石阡县",522225:"思南县",522226:"印江土家族苗族自治县",522227:"德江县",522228:"沿河土家族自治县",522229:"松桃苗族自治县",522230:"万山区",522231:"其它区",522300:"黔西南布依族苗族自治州",522301:"兴义市",522322:"兴仁县",522323:"普安县",522324:"晴隆县",522325:"贞丰县",522326:"望谟县",522327:"册亨县",522328:"安龙县",522329:"其它区",522400:"毕节市",522401:"七星关区",522422:"大方县",522423:"黔西县",522424:"金沙县",522425:"织金县",522426:"纳雍县",522427:"威宁彝族回族苗族自治县",522428:"赫章县",522429:"其它区",522600:"黔东南苗族侗族自治州",522601:"凯里市",522622:"黄平县",522623:"施秉县",522624:"三穗县",522625:"镇远县",522626:"岑巩县",522627:"天柱县",522628:"锦屏县",522629:"剑河县",522630:"台江县",522631:"黎平县",522632:"榕江县",522633:"从江县",522634:"雷山县",522635:"麻江县",522636:"丹寨县",522637:"其它区",522700:"黔南布依族苗族自治州",522701:"都匀市",522702:"福泉市",522722:"荔波县",522723:"贵定县",522725:"瓮安县",522726:"独山县",522727:"平塘县",522728:"罗甸县",522729:"长顺县",522730:"龙里县",522731:"惠水县",522732:"三都水族自治县",522733:"其它区",53e4:"云南省",530100:"昆明市",530102:"五华区",530103:"盘龙区",530111:"官渡区",530112:"西山区",530113:"东川区",530121:"呈贡区",530122:"晋宁县",530124:"富民县",530125:"宜良县",530126:"石林彝族自治县",530127:"嵩明县",530128:"禄劝彝族苗族自治县",530129:"寻甸回族彝族自治县",530181:"安宁市",530182:"其它区",530300:"曲靖市",530302:"麒麟区",530321:"马龙县",530322:"陆良县",530323:"师宗县",530324:"罗平县",530325:"富源县",530326:"会泽县",530328:"沾益县",530381:"宣威市",530382:"其它区",530400:"玉溪市",530402:"红塔区",530421:"江川县",530422:"澄江县",530423:"通海县",530424:"华宁县",530425:"易门县",530426:"峨山彝族自治县",530427:"新平彝族傣族自治县",530428:"元江哈尼族彝族傣族自治县",530429:"其它区",530500:"保山市",530502:"隆阳区",530521:"施甸县",530522:"腾冲县",530523:"龙陵县",530524:"昌宁县",530525:"其它区",530600:"昭通市",530602:"昭阳区",530621:"鲁甸县",530622:"巧家县",530623:"盐津县",530624:"大关县",530625:"永善县",530626:"绥江县",530627:"镇雄县",530628:"彝良县",530629:"威信县",530630:"水富县",530631:"其它区",530700:"丽江市",530702:"古城区",530721:"玉龙纳西族自治县",530722:"永胜县",530723:"华坪县",530724:"宁蒗彝族自治县",530725:"其它区",530800:"普洱市",530802:"思茅区",530821:"宁洱哈尼族彝族自治县",530822:"墨江哈尼族自治县",530823:"景东彝族自治县",530824:"景谷傣族彝族自治县",530825:"镇沅彝族哈尼族拉祜族自治县",530826:"江城哈尼族彝族自治县",530827:"孟连傣族拉祜族佤族自治县",530828:"澜沧拉祜族自治县",530829:"西盟佤族自治县",530830:"其它区",530900:"临沧市",530902:"临翔区",530921:"凤庆县",530922:"云县",530923:"永德县",530924:"镇康县",530925:"双江拉祜族佤族布朗族傣族自治县",530926:"耿马傣族佤族自治县",530927:"沧源佤族自治县",530928:"其它区",532300:"楚雄彝族自治州",532301:"楚雄市",532322:"双柏县",532323:"牟定县",532324:"南华县",532325:"姚安县",532326:"大姚县",532327:"永仁县",532328:"元谋县",532329:"武定县",532331:"禄丰县",532332:"其它区",532500:"红河哈尼族彝族自治州",532501:"个旧市",532502:"开远市",532522:"蒙自市",532523:"屏边苗族自治县",532524:"建水县",532525:"石屏县",532526:"弥勒市",532527:"泸西县",532528:"元阳县",532529:"红河县",532530:"金平苗族瑶族傣族自治县",532531:"绿春县",532532:"河口瑶族自治县",532533:"其它区",532600:"文山壮族苗族自治州",532621:"文山市",532622:"砚山县",532623:"西畴县",532624:"麻栗坡县",532625:"马关县",532626:"丘北县",532627:"广南县",532628:"富宁县",532629:"其它区",532800:"西双版纳傣族自治州",532801:"景洪市",532822:"勐海县",532823:"勐腊县",532824:"其它区",532900:"大理白族自治州",532901:"大理市",532922:"漾濞彝族自治县",532923:"祥云县",532924:"宾川县",532925:"弥渡县",532926:"南涧彝族自治县",532927:"巍山彝族回族自治县",532928:"永平县",532929:"云龙县",532930:"洱源县",532931:"剑川县",532932:"鹤庆县",532933:"其它区",533100:"德宏傣族景颇族自治州",533102:"瑞丽市",533103:"芒市",533122:"梁河县",533123:"盈江县",533124:"陇川县",533125:"其它区",533300:"怒江傈僳族自治州",533321:"泸水县",533323:"福贡县",533324:"贡山独龙族怒族自治县",533325:"兰坪白族普米族自治县",533326:"其它区",533400:"迪庆藏族自治州",533421:"香格里拉县",533422:"德钦县",533423:"维西傈僳族自治县",533424:"其它区",54e4:"西藏自治区",540100:"拉萨市",540102:"城关区",540121:"林周县",540122:"当雄县",540123:"尼木县",540124:"曲水县",540125:"堆龙德庆县",540126:"达孜县",540127:"墨竹工卡县",540128:"其它区",542100:"昌都地区",542121:"昌都县",542122:"江达县",542123:"贡觉县",542124:"类乌齐县",542125:"丁青县",542126:"察雅县",542127:"八宿县",542128:"左贡县",542129:"芒康县",542132:"洛隆县",542133:"边坝县",542134:"其它区",542200:"山南地区",542221:"乃东县",542222:"扎囊县",542223:"贡嘎县",542224:"桑日县",542225:"琼结县",542226:"曲松县",542227:"措美县",542228:"洛扎县",542229:"加查县",542231:"隆子县",542232:"错那县",542233:"浪卡子县",542234:"其它区",542300:"日喀则地区",542301:"日喀则市",542322:"南木林县",542323:"江孜县",542324:"定日县",542325:"萨迦县",542326:"拉孜县",542327:"昂仁县",542328:"谢通门县",542329:"白朗县",542330:"仁布县",542331:"康马县",542332:"定结县",542333:"仲巴县",542334:"亚东县",542335:"吉隆县",542336:"聂拉木县",542337:"萨嘎县",542338:"岗巴县",542339:"其它区",542400:"那曲地区",542421:"那曲县",542422:"嘉黎县",542423:"比如县",542424:"聂荣县",542425:"安多县",542426:"申扎县",542427:"索县",542428:"班戈县",542429:"巴青县",542430:"尼玛县",542431:"其它区",542432:"双湖县",542500:"阿里地区",542521:"普兰县",542522:"札达县",542523:"噶尔县",542524:"日土县",542525:"革吉县",542526:"改则县",542527:"措勤县",542528:"其它区",542600:"林芝地区",542621:"林芝县",542622:"工布江达县",542623:"米林县",542624:"墨脱县",542625:"波密县",542626:"察隅县",542627:"朗县",542628:"其它区",61e4:"陕西省",610100:"西安市",610102:"新城区",610103:"碑林区",610104:"莲湖区",610111:"灞桥区",610112:"未央区",610113:"雁塔区",610114:"阎良区",610115:"临潼区",610116:"长安区",610122:"蓝田县",610124:"周至县",610125:"户县",610126:"高陵县",610127:"其它区",610200:"铜川市",610202:"王益区",610203:"印台区",610204:"耀州区",610222:"宜君县",610223:"其它区",610300:"宝鸡市",610302:"渭滨区",610303:"金台区",610304:"陈仓区",610322:"凤翔县",610323:"岐山县",610324:"扶风县",610326:"眉县",610327:"陇县",610328:"千阳县",610329:"麟游县",610330:"凤县",610331:"太白县",610332:"其它区",610400:"咸阳市",610402:"秦都区",610403:"杨陵区",610404:"渭城区",610422:"三原县",610423:"泾阳县",610424:"乾县",610425:"礼泉县",610426:"永寿县",610427:"彬县",610428:"长武县",610429:"旬邑县",610430:"淳化县",610431:"武功县",610481:"兴平市",610482:"其它区",610500:"渭南市",610502:"临渭区",610521:"华县",610522:"潼关县",610523:"大荔县",610524:"合阳县",610525:"澄城县",610526:"蒲城县",610527:"白水县",610528:"富平县",610581:"韩城市",610582:"华阴市",610583:"其它区",610600:"延安市",610602:"宝塔区",610621:"延长县",610622:"延川县",610623:"子长县",610624:"安塞县",610625:"志丹县",610626:"吴起县",610627:"甘泉县",610628:"富县",610629:"洛川县",610630:"宜川县",610631:"黄龙县",610632:"黄陵县",610633:"其它区",610700:"汉中市",610702:"汉台区",610721:"南郑县",610722:"城固县",610723:"洋县",610724:"西乡县",610725:"勉县",610726:"宁强县",610727:"略阳县",610728:"镇巴县",610729:"留坝县",610730:"佛坪县",610731:"其它区",610800:"榆林市",610802:"榆阳区",610821:"神木县",610822:"府谷县",610823:"横山县",610824:"靖边县",610825:"定边县",610826:"绥德县",610827:"米脂县",610828:"佳县",610829:"吴堡县",610830:"清涧县",610831:"子洲县",610832:"其它区",610900:"安康市",610902:"汉滨区",610921:"汉阴县",610922:"石泉县",610923:"宁陕县",610924:"紫阳县",610925:"岚皋县",610926:"平利县",610927:"镇坪县",610928:"旬阳县",610929:"白河县",610930:"其它区",611e3:"商洛市",611002:"商州区",611021:"洛南县",611022:"丹凤县",611023:"商南县",611024:"山阳县",611025:"镇安县",611026:"柞水县",611027:"其它区",62e4:"甘肃省",620100:"兰州市",620102:"城关区",620103:"七里河区",620104:"西固区",620105:"安宁区",620111:"红古区",620121:"永登县",620122:"皋兰县",620123:"榆中县",620124:"其它区",620200:"嘉峪关市",620300:"金昌市",620302:"金川区",620321:"永昌县",620322:"其它区",620400:"白银市",620402:"白银区",620403:"平川区",620421:"靖远县",620422:"会宁县",620423:"景泰县",620424:"其它区",620500:"天水市",620502:"秦州区",620503:"麦积区",620521:"清水县",620522:"秦安县",620523:"甘谷县",620524:"武山县",620525:"张家川回族自治县",620526:"其它区",620600:"武威市",620602:"凉州区",620621:"民勤县",620622:"古浪县",620623:"天祝藏族自治县",620624:"其它区",620700:"张掖市",620702:"甘州区",620721:"肃南裕固族自治县",620722:"民乐县",620723:"临泽县",620724:"高台县",620725:"山丹县",620726:"其它区",620800:"平凉市",620802:"崆峒区",620821:"泾川县",620822:"灵台县",620823:"崇信县",620824:"华亭县",620825:"庄浪县",620826:"静宁县",620827:"其它区",620900:"酒泉市",620902:"肃州区",620921:"金塔县",620922:"瓜州县",620923:"肃北蒙古族自治县",620924:"阿克塞哈萨克族自治县",620981:"玉门市",620982:"敦煌市",620983:"其它区",621e3:"庆阳市",621002:"西峰区",621021:"庆城县",621022:"环县",621023:"华池县",621024:"合水县",621025:"正宁县",621026:"宁县",621027:"镇原县",621028:"其它区",621100:"定西市",621102:"安定区",621121:"通渭县",621122:"陇西县",621123:"渭源县",621124:"临洮县",621125:"漳县",621126:"岷县",621127:"其它区",621200:"陇南市",621202:"武都区",621221:"成县",621222:"文县",621223:"宕昌县",621224:"康县",621225:"西和县",621226:"礼县",621227:"徽县",621228:"两当县",621229:"其它区",622900:"临夏回族自治州",622901:"临夏市",622921:"临夏县",622922:"康乐县",622923:"永靖县",622924:"广河县",622925:"和政县",622926:"东乡族自治县",622927:"积石山保安族东乡族撒拉族自治县",622928:"其它区",623e3:"甘南藏族自治州",623001:"合作市",623021:"临潭县",623022:"卓尼县",623023:"舟曲县",623024:"迭部县",623025:"玛曲县",623026:"碌曲县",623027:"夏河县",623028:"其它区",63e4:"青海省",630100:"西宁市",630102:"城东区",630103:"城中区",630104:"城西区",630105:"城北区",630121:"大通回族土族自治县",630122:"湟中县",630123:"湟源县",630124:"其它区",632100:"海东市",632121:"平安县",632122:"民和回族土族自治县",632123:"乐都区",632126:"互助土族自治县",632127:"化隆回族自治县",632128:"循化撒拉族自治县",632129:"其它区",632200:"海北藏族自治州",632221:"门源回族自治县",632222:"祁连县",632223:"海晏县",632224:"刚察县",632225:"其它区",632300:"黄南藏族自治州",632321:"同仁县",632322:"尖扎县",632323:"泽库县",632324:"河南蒙古族自治县",632325:"其它区",632500:"海南藏族自治州",632521:"共和县",632522:"同德县",632523:"贵德县",632524:"兴海县",632525:"贵南县",632526:"其它区",632600:"果洛藏族自治州",632621:"玛沁县",632622:"班玛县",632623:"甘德县",632624:"达日县",632625:"久治县",632626:"玛多县",632627:"其它区",632700:"玉树藏族自治州",632721:"玉树市",632722:"杂多县",632723:"称多县",632724:"治多县",632725:"囊谦县",632726:"曲麻莱县",632727:"其它区",632800:"海西蒙古族藏族自治州",632801:"格尔木市",632802:"德令哈市",632821:"乌兰县",632822:"都兰县",632823:"天峻县",632824:"其它区",64e4:"宁夏回族自治区",640100:"银川市",640104:"兴庆区",640105:"西夏区",640106:"金凤区",640121:"永宁县",640122:"贺兰县",640181:"灵武市",640182:"其它区",640200:"石嘴山市",640202:"大武口区",640205:"惠农区",640221:"平罗县",640222:"其它区",640300:"吴忠市",640302:"利通区",640303:"红寺堡区",640323:"盐池县",640324:"同心县",640381:"青铜峡市",640382:"其它区",640400:"固原市",640402:"原州区",640422:"西吉县",640423:"隆德县",640424:"泾源县",640425:"彭阳县",640426:"其它区",640500:"中卫市",640502:"沙坡头区",640521:"中宁县",640522:"海原县",640523:"其它区",65e4:"新疆维吾尔自治区",650100:"乌鲁木齐市",650102:"天山区",650103:"沙依巴克区",650104:"新市区",650105:"水磨沟区",650106:"头屯河区",650107:"达坂城区",650109:"米东区",650121:"乌鲁木齐县",650122:"其它区",650200:"克拉玛依市",650202:"独山子区",650203:"克拉玛依区",650204:"白碱滩区",650205:"乌尔禾区",650206:"其它区",652100:"吐鲁番地区",652101:"吐鲁番市",652122:"鄯善县",652123:"托克逊县",652124:"其它区",652200:"哈密地区",652201:"哈密市",652222:"巴里坤哈萨克自治县",652223:"伊吾县",652224:"其它区",652300:"昌吉回族自治州",652301:"昌吉市",652302:"阜康市",652323:"呼图壁县",652324:"玛纳斯县",652325:"奇台县",652327:"吉木萨尔县",652328:"木垒哈萨克自治县",652329:"其它区",652700:"博尔塔拉蒙古自治州",652701:"博乐市",652702:"阿拉山口市",652722:"精河县",652723:"温泉县",652724:"其它区",652800:"巴音郭楞蒙古自治州",652801:"库尔勒市",652822:"轮台县",652823:"尉犁县",652824:"若羌县",652825:"且末县",652826:"焉耆回族自治县",652827:"和静县",652828:"和硕县",652829:"博湖县",652830:"其它区",652900:"阿克苏地区",652901:"阿克苏市",652922:"温宿县",652923:"库车县",652924:"沙雅县",652925:"新和县",652926:"拜城县",652927:"乌什县",652928:"阿瓦提县",652929:"柯坪县",652930:"其它区",653e3:"克孜勒苏柯尔克孜自治州",653001:"阿图什市",653022:"阿克陶县",653023:"阿合奇县",653024:"乌恰县",653025:"其它区",653100:"喀什地区",653101:"喀什市",653121:"疏附县",653122:"疏勒县",653123:"英吉沙县",653124:"泽普县",653125:"莎车县",653126:"叶城县",653127:"麦盖提县",653128:"岳普湖县",653129:"伽师县",653130:"巴楚县",653131:"塔什库尔干塔吉克自治县",653132:"其它区",653200:"和田地区",653201:"和田市",653221:"和田县",653222:"墨玉县",653223:"皮山县",653224:"洛浦县",653225:"策勒县",653226:"于田县",653227:"民丰县",653228:"其它区",654e3:"伊犁哈萨克自治州",654002:"伊宁市",654003:"奎屯市",654021:"伊宁县",654022:"察布查尔锡伯自治县",654023:"霍城县",654024:"巩留县",654025:"新源县",654026:"昭苏县",654027:"特克斯县",654028:"尼勒克县",654029:"其它区",654200:"塔城地区",654201:"塔城市",654202:"乌苏市",654221:"额敏县",654223:"沙湾县",654224:"托里县",654225:"裕民县",654226:"和布克赛尔蒙古自治县",654227:"其它区",654300:"阿勒泰地区",654301:"阿勒泰市",654321:"布尔津县",654322:"富蕴县",654323:"福海县",654324:"哈巴河县",654325:"青河县",654326:"吉木乃县",654327:"其它区",659001:"石河子市",659002:"阿拉尔市",659003:"图木舒克市",659004:"五家渠市",71e4:"台湾",710100:"台北市",710101:"中正区",710102:"大同区",710103:"中山区",710104:"松山区",710105:"大安区",710106:"万华区",710107:"信义区",710108:"士林区",710109:"北投区",710110:"内湖区",710111:"南港区",710112:"文山区",710113:"其它区",710200:"高雄市",710201:"新兴区",710202:"前金区",710203:"芩雅区",710204:"盐埕区",710205:"鼓山区",710206:"旗津区",710207:"前镇区",710208:"三民区",710209:"左营区",710210:"楠梓区",710211:"小港区",710212:"其它区",710241:"苓雅区",710242:"仁武区",710243:"大社区",710244:"冈山区",710245:"路竹区",710246:"阿莲区",710247:"田寮区",710248:"燕巢区",710249:"桥头区",710250:"梓官区",710251:"弥陀区",710252:"永安区",710253:"湖内区",710254:"凤山区",710255:"大寮区",710256:"林园区",710257:"鸟松区",710258:"大树区",710259:"旗山区",710260:"美浓区",710261:"六龟区",710262:"内门区",710263:"杉林区",710264:"甲仙区",710265:"桃源区",710266:"那玛夏区",710267:"茂林区",710268:"茄萣区",710300:"台南市",710301:"中西区",710302:"东区",710303:"南区",710304:"北区",710305:"安平区",710306:"安南区",710307:"其它区",710339:"永康区",710340:"归仁区",710341:"新化区",710342:"左镇区",710343:"玉井区",710344:"楠西区",710345:"南化区",710346:"仁德区",710347:"关庙区",710348:"龙崎区",710349:"官田区",710350:"麻豆区",710351:"佳里区",710352:"西港区",710353:"七股区",710354:"将军区",710355:"学甲区",710356:"北门区",710357:"新营区",710358:"后壁区",710359:"白河区",710360:"东山区",710361:"六甲区",710362:"下营区",710363:"柳营区",710364:"盐水区",710365:"善化区",710366:"大内区",710367:"山上区",710368:"新市区",710369:"安定区",710400:"台中市",710401:"中区",710402:"东区",710403:"南区",710404:"西区",710405:"北区",710406:"北屯区",710407:"西屯区",710408:"南屯区",710409:"其它区",710431:"太平区",710432:"大里区",710433:"雾峰区",710434:"乌日区",710435:"丰原区",710436:"后里区",710437:"石冈区",710438:"东势区",710439:"和平区",710440:"新社区",710441:"潭子区",710442:"大雅区",710443:"神冈区",710444:"大肚区",710445:"沙鹿区",710446:"龙井区",710447:"梧栖区",710448:"清水区",710449:"大甲区",710450:"外埔区",710451:"大安区",710500:"金门县",710507:"金沙镇",710508:"金湖镇",710509:"金宁乡",710510:"金城镇",710511:"烈屿乡",710512:"乌坵乡",710600:"南投县",710614:"南投市",710615:"中寮乡",710616:"草屯镇",710617:"国姓乡",710618:"埔里镇",710619:"仁爱乡",710620:"名间乡",710621:"集集镇",710622:"水里乡",710623:"鱼池乡",710624:"信义乡",710625:"竹山镇",710626:"鹿谷乡",710700:"基隆市",710701:"仁爱区",710702:"信义区",710703:"中正区",710704:"中山区",710705:"安乐区",710706:"暖暖区",710707:"七堵区",710708:"其它区",710800:"新竹市",710801:"东区",710802:"北区",710803:"香山区",710804:"其它区",710900:"嘉义市",710901:"东区",710902:"西区",710903:"其它区",711100:"新北市",711130:"万里区",711131:"金山区",711132:"板桥区",711133:"汐止区",711134:"深坑区",711135:"石碇区",711136:"瑞芳区",711137:"平溪区",711138:"双溪区",711139:"贡寮区",711140:"新店区",711141:"坪林区",711142:"乌来区",711143:"永和区",711144:"中和区",711145:"土城区",711146:"三峡区",711147:"树林区",711148:"莺歌区",711149:"三重区",711150:"新庄区",711151:"泰山区",711152:"林口区",711153:"芦洲区",711154:"五股区",711155:"八里区",711156:"淡水区",711157:"三芝区",711158:"石门区",711200:"宜兰县",711214:"宜兰市",711215:"头城镇",711216:"礁溪乡",711217:"壮围乡",711218:"员山乡",711219:"罗东镇",711220:"三星乡",711221:"大同乡",711222:"五结乡",711223:"冬山乡",711224:"苏澳镇",711225:"南澳乡",711226:"钓鱼台",711300:"新竹县",711314:"竹北市",711315:"湖口乡",711316:"新丰乡",711317:"新埔镇",711318:"关西镇",711319:"芎林乡",711320:"宝山乡",711321:"竹东镇",711322:"五峰乡",711323:"横山乡",711324:"尖石乡",711325:"北埔乡",711326:"峨眉乡",711400:"桃园县",711414:"中坜市",711415:"平镇市",711416:"龙潭乡",711417:"杨梅市",711418:"新屋乡",711419:"观音乡",711420:"桃园市",711421:"龟山乡",711422:"八德市",711423:"大溪镇",711424:"复兴乡",711425:"大园乡",711426:"芦竹乡",711500:"苗栗县",711519:"竹南镇",711520:"头份镇",711521:"三湾乡",711522:"南庄乡",711523:"狮潭乡",711524:"后龙镇",711525:"通霄镇",711526:"苑里镇",711527:"苗栗市",711528:"造桥乡",711529:"头屋乡",711530:"公馆乡",711531:"大湖乡",711532:"泰安乡",711533:"铜锣乡",711534:"三义乡",711535:"西湖乡",711536:"卓兰镇",711700:"彰化县",711727:"彰化市",711728:"芬园乡",711729:"花坛乡",711730:"秀水乡",711731:"鹿港镇",711732:"福兴乡",711733:"线西乡",711734:"和美镇",711735:"伸港乡",711736:"员林镇",711737:"社头乡",711738:"永靖乡",711739:"埔心乡",711740:"溪湖镇",711741:"大村乡",711742:"埔盐乡",711743:"田中镇",711744:"北斗镇",711745:"田尾乡",711746:"埤头乡",711747:"溪州乡",711748:"竹塘乡",711749:"二林镇",711750:"大城乡",711751:"芳苑乡",711752:"二水乡",711900:"嘉义县",711919:"番路乡",711920:"梅山乡",711921:"竹崎乡",711922:"阿里山乡",711923:"中埔乡",711924:"大埔乡",711925:"水上乡",711926:"鹿草乡",711927:"太保市",711928:"朴子市",711929:"东石乡",711930:"六脚乡",711931:"新港乡",711932:"民雄乡",711933:"大林镇",711934:"溪口乡",711935:"义竹乡",711936:"布袋镇",712100:"云林县",712121:"斗南镇",712122:"大埤乡",712123:"虎尾镇",712124:"土库镇",712125:"褒忠乡",712126:"东势乡",712127:"台西乡",712128:"仑背乡",712129:"麦寮乡",712130:"斗六市",712131:"林内乡",712132:"古坑乡",712133:"莿桐乡",712134:"西螺镇",712135:"二仑乡",712136:"北港镇",712137:"水林乡",712138:"口湖乡",712139:"四湖乡",712140:"元长乡",712400:"屏东县",712434:"屏东市",712435:"三地门乡",712436:"雾台乡",712437:"玛家乡",712438:"九如乡",712439:"里港乡",712440:"高树乡",712441:"盐埔乡",712442:"长治乡",712443:"麟洛乡",712444:"竹田乡",712445:"内埔乡",712446:"万丹乡",712447:"潮州镇",712448:"泰武乡",712449:"来义乡",712450:"万峦乡",712451:"崁顶乡",712452:"新埤乡",712453:"南州乡",712454:"林边乡",712455:"东港镇",712456:"琉球乡",712457:"佳冬乡",712458:"新园乡",712459:"枋寮乡",712460:"枋山乡",712461:"春日乡",712462:"狮子乡",712463:"车城乡",712464:"牡丹乡",712465:"恒春镇",712466:"满州乡",712500:"台东县",712517:"台东市",712518:"绿岛乡",712519:"兰屿乡",712520:"延平乡",712521:"卑南乡",712522:"鹿野乡",712523:"关山镇",712524:"海端乡",712525:"池上乡",712526:"东河乡",712527:"成功镇",712528:"长滨乡",712529:"金峰乡",712530:"大武乡",712531:"达仁乡",712532:"太麻里乡",712600:"花莲县",712615:"花莲市",712616:"新城乡",712617:"太鲁阁",712618:"秀林乡",712619:"吉安乡",712620:"寿丰乡",712621:"凤林镇",712622:"光复乡",712623:"丰滨乡",712624:"瑞穗乡",712625:"万荣乡",712626:"玉里镇",712627:"卓溪乡",712628:"富里乡",712700:"澎湖县",712707:"马公市",712708:"西屿乡",712709:"望安乡",712710:"七美乡",712711:"白沙乡",712712:"湖西乡",712800:"连江县",712805:"南竿乡",712806:"北竿乡",712807:"莒光乡",712808:"东引乡",81e4:"香港特别行政区",810100:"香港岛",810101:"中西区",810102:"湾仔",810103:"东区",810104:"南区",810200:"九龙",810201:"九龙城区",810202:"油尖旺区",810203:"深水埗区",810204:"黄大仙区",810205:"观塘区",810300:"新界",810301:"北区",810302:"大埔区",810303:"沙田区",810304:"西贡区",810305:"元朗区",810306:"屯门区",810307:"荃湾区",810308:"葵青区",810309:"离岛区",82e4:"澳门特别行政区",820100:"澳门半岛",820200:"离岛",99e4:"海外",990100:"海外"},r=function(){var e=[];for(var t in n){var r="0000"===t.slice(2,6)?void 0:"00"==t.slice(4,6)?t.slice(0,2)+"0000":t.slice(0,4)+"00";e.push({id:t,pid:r,name:n[t]})}return function(e){for(var t,n={},r=0;r<e.length;r++)(t=e[r])&&t.id&&(n[t.id]=t);for(var o=[],a=0;a<e.length;a++)if(t=e[a])if(null!=t.pid||null!=t.parentId){var i=n[t.pid]||n[t.parentId];i&&(i.children||(i.children=[]),i.children.push(t))}else o.push(t);return o}(e)}();e.exports=r},function(e,t,n){var r,o=n(18);e.exports={d4:function(){return this.natural(1,4)},d6:function(){return this.natural(1,6)},d8:function(){return this.natural(1,8)},d12:function(){return this.natural(1,12)},d20:function(){return this.natural(1,20)},d100:function(){return this.natural(1,100)},guid:function(){var e="abcdefABCDEF1234567890";return this.string(e,8)+"-"+this.string(e,4)+"-"+this.string(e,4)+"-"+this.string(e,4)+"-"+this.string(e,12)},uuid:function(){return this.guid()},id:function(){var e,t=0,n=["7","9","10","5","8","4","2","1","6","3","7","9","10","5","8","4","2"];e=this.pick(o).id+this.date("yyyyMMdd")+this.string("number",3);for(var r=0;r<e.length;r++)t+=e[r]*n[r];return e+=["1","0","X","9","8","7","6","5","4","3","2"][t%11]},increment:(r=0,function(e){return r+=+e||1}),inc:function(e){return this.increment(e)}}},function(e,t,n){var r=n(21),o=n(22);e.exports={Parser:r,Handler:o}},function(e,t){function n(e){this.type=e,this.offset=n.offset(),this.text=n.text()}function r(e,t){n.call(this,"alternate"),this.left=e,this.right=t}function o(e){n.call(this,"match"),this.body=e.filter(Boolean)}function a(e,t){n.call(this,e),this.body=t}function i(e){a.call(this,"capture-group"),this.index=b[this.offset]||(b[this.offset]=y++),this.body=e}function s(e,t){n.call(this,"quantified"),this.body=e,this.quantifier=t}function l(e,t){n.call(this,"quantifier"),this.min=e,this.max=t,this.greedy=!0}function u(e,t){n.call(this,"charset"),this.invert=e,this.body=t}function c(e,t){n.call(this,"range"),this.start=e,this.end=t}function f(e){n.call(this,"literal"),this.body=e,this.escaped=this.body!=this.text}function p(e){n.call(this,"unicode"),this.code=e.toUpperCase()}function d(e){n.call(this,"hex"),this.code=e.toUpperCase()}function h(e){n.call(this,"octal"),this.code=e.toUpperCase()}function m(e){n.call(this,"back-reference"),this.code=e.toUpperCase()}function g(e){n.call(this,"control-character"),this.code=e.toUpperCase()}var v=function(){function e(e,t,n,r,o){this.expected=e,this.found=t,this.offset=n,this.line=r,this.column=o,this.name="SyntaxError",this.message=function(e,t){var n;switch(e.length){case 0:n="end of input";break;case 1:n=e[0];break;default:n=e.slice(0,-1).join(", ")+" or "+e[e.length-1]}return"Expected "+n+" but "+(t?'"'+function(e){function t(e){return e.charCodeAt(0).toString(16).toUpperCase()}return e.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\x08/g,"\\b").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\f/g,"\\f").replace(/\r/g,"\\r").replace(/[\x00-\x07\x0B\x0E\x0F]/g,function(e){return"\\x0"+t(e)}).replace(/[\x10-\x1F\x80-\xFF]/g,function(e){return"\\x"+t(e)}).replace(/[\u0180-\u0FFF]/g,function(e){return"\\u0"+t(e)}).replace(/[\u1080-\uFFFF]/g,function(e){return"\\u"+t(e)})}(t)+'"':"end of input")+" found."}(e,t)}return function(e,t){function n(){this.constructor=e}n.prototype=t.prototype,e.prototype=new n}(e,Error),{SyntaxError:e,parse:function(t){function v(e){return On!==e&&(On>e&&(On=0,Cn={line:1,column:1,seenCR:!1}),function(e,n,r){var o,a;for(o=n;r>o;o++)"\n"===(a=t.charAt(o))?(e.seenCR||e.line++,e.column=1,e.seenCR=!1):"\r"===a||"\u2028"===a||"\u2029"===a?(e.line++,e.column=1,e.seenCR=!0):(e.column++,e.seenCR=!1)}(Cn,On,e),On=e),Cn}function y(e){Rn>En||(En>Rn&&(Rn=En,Sn=[]),Sn.push(e))}function b(){var e,n,r,o,a;return e=En,n=function(){var e,n,r,o,a;if(e=En,n=function(){var e,n;return e=En,94===t.charCodeAt(En)?(n=Q,En++):(n=null,0===_n&&y(ee)),null!==n&&(An=e,n=te()),null===n?(En=e,e=n):e=n,e}(),null===n&&(n=G),null!==n)if(r=En,_n++,o=E(),_n--,null===o?r=G:(En=r,r=X),null!==r){for(o=[],null===(a=w())&&(a=x());null!==a;)o.push(a),null===(a=w())&&(a=x());null!==o?(a=function(){var e,n;return e=En,36===t.charCodeAt(En)?(n=ne,En++):(n=null,0===_n&&y(re)),null!==n&&(An=e,n=oe()),null===n?(En=e,e=n):e=n,e}(),null===a&&(a=G),null!==a?(An=e,null===(n=Z(n,o,a))?(En=e,e=n):e=n):(En=e,e=X)):(En=e,e=X)}else En=e,e=X;else En=e,e=X;return e}(),null!==n?(r=En,124===t.charCodeAt(En)?(o=Y,En++):(o=null,0===_n&&y(K)),null!==o&&null!==(a=b())?r=o=[o,a]:(En=r,r=X),null===r&&(r=G),null!==r?(An=e,null===(n=J(n,r))?(En=e,e=n):e=n):(En=e,e=X)):(En=e,e=X),e}function x(){var e,n,r,o,a;return n=En,40===t.charCodeAt(En)?(r=Me,En++):(r=null,0===_n&&y(Te)),null!==r?(null===(o=function(){var e,n,r;return e=En,t.substr(En,2)===Ie?(n=Ie,En+=2):(n=null,0===_n&&y($e)),null!==n&&null!==(r=b())?(An=e,null===(n=Ve(r))?(En=e,e=n):e=n):(En=e,e=X),e}())&&null===(o=function(){var e,n,r;return e=En,t.substr(En,2)===Be?(n=Be,En+=2):(n=null,0===_n&&y(We)),null!==n&&null!==(r=b())?(An=e,null===(n=Ue(r))?(En=e,e=n):e=n):(En=e,e=X),e}())&&null===(o=function(){var e,n,r;return e=En,t.substr(En,2)===qe?(n=qe,En+=2):(n=null,0===_n&&y(Le)),null!==n&&null!==(r=b())?(An=e,null===(n=je(r))?(En=e,e=n):e=n):(En=e,e=X),e}())&&(o=function(){var e,t;return e=En,null!==(t=b())&&(An=e,t=Ne(t)),null===t?(En=e,e=t):e=t,e}()),null!==o?(41===t.charCodeAt(En)?(a=Fe,En++):(a=null,0===_n&&y(He)),null!==a?(An=n,null===(r=De(o))?(En=n,n=r):n=r):(En=n,n=X)):(En=n,n=X)):(En=n,n=X),null===(e=n)&&(e=function(){var e,n,r,o,a;if(_n++,e=En,91===t.charCodeAt(En)?(n=Xe,En++):(n=null,0===_n&&y(Ge)),null!==n)if(94===t.charCodeAt(En)?(r=Q,En++):(r=null,0===_n&&y(ee)),null===r&&(r=G),null!==r){for(o=[],null===(a=O())&&(a=C());null!==a;)o.push(a),null===(a=O())&&(a=C());null!==o?(93===t.charCodeAt(En)?(a=Ye,En++):(a=null,0===_n&&y(Ke)),null!==a?(An=e,null===(n=Je(r,o))?(En=e,e=n):e=n):(En=e,e=X)):(En=e,e=X)}else En=e,e=X;else En=e,e=X;return _n--,null===e&&(n=null,0===_n&&y(ze)),e}(),null===e&&(e=function(){var e;return e=function(){var e,n;return e=En,46===t.charCodeAt(En)?(n=it,En++):(n=null,0===_n&&y(st)),null!==n&&(An=e,n=lt()),null===n?(En=e,e=n):e=n,e}(),null===e&&(e=function(){var e;return e=function(){var e,n;return e=En,t.substr(En,2)===pt?(n=pt,En+=2):(n=null,0===_n&&y(dt)),null!==n&&(An=e,n=mt()),null===n?(En=e,e=n):e=n,e}(),null===e&&(e=function(){var e,n;return e=En,t.substr(En,2)===gt?(n=gt,En+=2):(n=null,0===_n&&y(vt)),null!==n&&(An=e,n=yt()),null===n?(En=e,e=n):e=n,e}(),null===e&&(null===(e=q())&&(null===(e=R())&&(null===(e=S())&&(null===(e=_())&&(null===(e=P())&&(null===(e=k())&&(null===(e=M())&&(null===(e=T())&&(null===(e=F())&&(null===(e=H())&&(null===(e=D())&&(null===(e=N())&&(e=function(){var e,n,r;return e=En,92===t.charCodeAt(En)?(n=nn,En++):(n=null,0===_n&&y(rn)),null!==n?(on.test(t.charAt(En))?(r=t.charAt(En),En++):(r=null,0===_n&&y(an)),null!==r?(An=e,null===(n=sn(r))?(En=e,e=n):e=n):(En=e,e=X)):(En=e,e=X),e}(),null===e&&(null===(e=L())&&(null===(e=j())&&(null===(e=I())&&(null===(e=$())&&(e=V()))))))))))))))))))),e}(),null===e&&(e=function(){var e,n;return _n++,e=En,ct.test(t.charAt(En))?(n=t.charAt(En),En++):(n=null,0===_n&&y(ft)),null!==n&&(An=e,n=at(n)),null===n?(En=e,e=n):e=n,_n--,null===e&&(n=null,0===_n&&y(ut)),e}())),e}())),e}function w(){var e,t,n;return e=En,null!==(t=x())&&null!==(n=E())?(An=e,null===(t=ae(t,n))?(En=e,e=t):e=t):(En=e,e=X),e}function E(){var e,n,r;return _n++,e=En,n=function(){var e;return e=function(){var e,n,r,o,a,i;return e=En,123===t.charCodeAt(En)?(n=le,En++):(n=null,0===_n&&y(ue)),null!==n&&null!==(r=A())?(44===t.charCodeAt(En)?(o=ce,En++):(o=null,0===_n&&y(fe)),null!==o&&null!==(a=A())?(125===t.charCodeAt(En)?(i=pe,En++):(i=null,0===_n&&y(de)),null!==i?(An=e,null===(n=he(r,a))?(En=e,e=n):e=n):(En=e,e=X)):(En=e,e=X)):(En=e,e=X),e}(),null===e&&(e=function(){var e,n,r,o;return e=En,123===t.charCodeAt(En)?(n=le,En++):(n=null,0===_n&&y(ue)),null!==n&&null!==(r=A())?(t.substr(En,2)===me?(o=me,En+=2):(o=null,0===_n&&y(ge)),null!==o?(An=e,null===(n=ve(r))?(En=e,e=n):e=n):(En=e,e=X)):(En=e,e=X),e}(),null===e&&(e=function(){var e,n,r,o;return e=En,123===t.charCodeAt(En)?(n=le,En++):(n=null,0===_n&&y(ue)),null!==n&&null!==(r=A())?(125===t.charCodeAt(En)?(o=pe,En++):(o=null,0===_n&&y(de)),null!==o?(An=e,null===(n=ye(r))?(En=e,e=n):e=n):(En=e,e=X)):(En=e,e=X),e}(),null===e&&(e=function(){var e,n;return e=En,43===t.charCodeAt(En)?(n=be,En++):(n=null,0===_n&&y(xe)),null!==n&&(An=e,n=we()),null===n?(En=e,e=n):e=n,e}(),null===e&&(e=function(){var e,n;return e=En,42===t.charCodeAt(En)?(n=Ee,En++):(n=null,0===_n&&y(Ae)),null!==n&&(An=e,n=Oe()),null===n?(En=e,e=n):e=n,e}(),null===e&&(e=function(){var e,n;return e=En,63===t.charCodeAt(En)?(n=Ce,En++):(n=null,0===_n&&y(Re)),null!==n&&(An=e,n=Se()),null===n?(En=e,e=n):e=n,e}()))))),e}(),null!==n?(r=function(){var e;return 63===t.charCodeAt(En)?(e=Ce,En++):(e=null,0===_n&&y(Re)),e}(),null===r&&(r=G),null!==r?(An=e,null===(n=se(n,r))?(En=e,e=n):e=n):(En=e,e=X)):(En=e,e=X),_n--,null===e&&(n=null,0===_n&&y(ie)),e}function A(){var e,n,r;if(e=En,n=[],_e.test(t.charAt(En))?(r=t.charAt(En),En++):(r=null,0===_n&&y(Pe)),null!==r)for(;null!==r;)n.push(r),_e.test(t.charAt(En))?(r=t.charAt(En),En++):(r=null,0===_n&&y(Pe));else n=X;return null!==n&&(An=e,n=ke(n)),null===n?(En=e,e=n):e=n,e}function O(){var e,n,r,o;return _n++,e=En,null!==(n=C())?(45===t.charCodeAt(En)?(r=Qe,En++):(r=null,0===_n&&y(et)),null!==r&&null!==(o=C())?(An=e,null===(n=tt(n,o))?(En=e,e=n):e=n):(En=e,e=X)):(En=e,e=X),_n--,null===e&&(n=null,0===_n&&y(Ze)),e}function C(){var e,n,r;return _n++,e=function(){var e,n,r;return n=En,t.substr(En,2)===pt?(r=pt,En+=2):(r=null,0===_n&&y(dt)),null!==r&&(An=n,r=ht()),null===r?(En=n,n=r):n=r,null===(e=n)&&(null===(e=q())&&(null===(e=R())&&(null===(e=S())&&(null===(e=_())&&(null===(e=P())&&(null===(e=k())&&(null===(e=M())&&(null===(e=T())&&(null===(e=F())&&(null===(e=H())&&(null===(e=D())&&(null===(e=N())&&(null===(e=L())&&(null===(e=j())&&(null===(e=I())&&(null===(e=$())&&(e=V()))))))))))))))))),e}(),null===e&&(n=En,rt.test(t.charAt(En))?(r=t.charAt(En),En++):(r=null,0===_n&&y(ot)),null!==r&&(An=n,r=at(r)),null===r?(En=n,n=r):n=r,e=n),_n--,null===e&&0===_n&&y(nt),e}function R(){var e,n;return e=En,t.substr(En,2)===bt?(n=bt,En+=2):(n=null,0===_n&&y(xt)),null!==n&&(An=e,n=wt()),null===n?(En=e,e=n):e=n,e}function S(){var e,n;return e=En,t.substr(En,2)===Et?(n=Et,En+=2):(n=null,0===_n&&y(At)),null!==n&&(An=e,n=Ot()),null===n?(En=e,e=n):e=n,e}function _(){var e,n;return e=En,t.substr(En,2)===Ct?(n=Ct,En+=2):(n=null,0===_n&&y(Rt)),null!==n&&(An=e,n=St()),null===n?(En=e,e=n):e=n,e}function P(){var e,n;return e=En,t.substr(En,2)===_t?(n=_t,En+=2):(n=null,0===_n&&y(Pt)),null!==n&&(An=e,n=kt()),null===n?(En=e,e=n):e=n,e}function k(){var e,n;return e=En,t.substr(En,2)===Mt?(n=Mt,En+=2):(n=null,0===_n&&y(Tt)),null!==n&&(An=e,n=Ft()),null===n?(En=e,e=n):e=n,e}function M(){var e,n;return e=En,t.substr(En,2)===Ht?(n=Ht,En+=2):(n=null,0===_n&&y(Dt)),null!==n&&(An=e,n=Nt()),null===n?(En=e,e=n):e=n,e}function T(){var e,n;return e=En,t.substr(En,2)===qt?(n=qt,En+=2):(n=null,0===_n&&y(Lt)),null!==n&&(An=e,n=jt()),null===n?(En=e,e=n):e=n,e}function F(){var e,n;return e=En,t.substr(En,2)===It?(n=It,En+=2):(n=null,0===_n&&y($t)),null!==n&&(An=e,n=Vt()),null===n?(En=e,e=n):e=n,e}function H(){var e,n;return e=En,t.substr(En,2)===Bt?(n=Bt,En+=2):(n=null,0===_n&&y(Wt)),null!==n&&(An=e,n=Ut()),null===n?(En=e,e=n):e=n,e}function D(){var e,n;return e=En,t.substr(En,2)===zt?(n=zt,En+=2):(n=null,0===_n&&y(Xt)),null!==n&&(An=e,n=Gt()),null===n?(En=e,e=n):e=n,e}function N(){var e,n;return e=En,t.substr(En,2)===Yt?(n=Yt,En+=2):(n=null,0===_n&&y(Kt)),null!==n&&(An=e,n=Jt()),null===n?(En=e,e=n):e=n,e}function q(){var e,n,r;return e=En,t.substr(En,2)===Zt?(n=Zt,En+=2):(n=null,0===_n&&y(Qt)),null!==n?(t.length>En?(r=t.charAt(En),En++):(r=null,0===_n&&y(en)),null!==r?(An=e,null===(n=tn(r))?(En=e,e=n):e=n):(En=e,e=X)):(En=e,e=X),e}function L(){var e,n,r,o;if(e=En,t.substr(En,2)===ln?(n=ln,En+=2):(n=null,0===_n&&y(un)),null!==n){if(r=[],cn.test(t.charAt(En))?(o=t.charAt(En),En++):(o=null,0===_n&&y(fn)),null!==o)for(;null!==o;)r.push(o),cn.test(t.charAt(En))?(o=t.charAt(En),En++):(o=null,0===_n&&y(fn));else r=X;null!==r?(An=e,null===(n=pn(r))?(En=e,e=n):e=n):(En=e,e=X)}else En=e,e=X;return e}function j(){var e,n,r,o;if(e=En,t.substr(En,2)===dn?(n=dn,En+=2):(n=null,0===_n&&y(hn)),null!==n){if(r=[],mn.test(t.charAt(En))?(o=t.charAt(En),En++):(o=null,0===_n&&y(gn)),null!==o)for(;null!==o;)r.push(o),mn.test(t.charAt(En))?(o=t.charAt(En),En++):(o=null,0===_n&&y(gn));else r=X;null!==r?(An=e,null===(n=vn(r))?(En=e,e=n):e=n):(En=e,e=X)}else En=e,e=X;return e}function I(){var e,n,r,o;if(e=En,t.substr(En,2)===yn?(n=yn,En+=2):(n=null,0===_n&&y(bn)),null!==n){if(r=[],mn.test(t.charAt(En))?(o=t.charAt(En),En++):(o=null,0===_n&&y(gn)),null!==o)for(;null!==o;)r.push(o),mn.test(t.charAt(En))?(o=t.charAt(En),En++):(o=null,0===_n&&y(gn));else r=X;null!==r?(An=e,null===(n=xn(r))?(En=e,e=n):e=n):(En=e,e=X)}else En=e,e=X;return e}function $(){var e,n;return e=En,t.substr(En,2)===ln?(n=ln,En+=2):(n=null,0===_n&&y(un)),null!==n&&(An=e,n=wn()),null===n?(En=e,e=n):e=n,e}function V(){var e,n,r;return e=En,92===t.charCodeAt(En)?(n=nn,En++):(n=null,0===_n&&y(rn)),null!==n?(t.length>En?(r=t.charAt(En),En++):(r=null,0===_n&&y(en)),null!==r?(An=e,null===(n=at(r))?(En=e,e=n):e=n):(En=e,e=X)):(En=e,e=X),e}var B,W=arguments.length>1?arguments[1]:{},U={regexp:b},z=b,X=null,G="",Y="|",K='"|"',J=function(e,t){return t?new r(e,t[1]):e},Z=function(e,t,n){return new o([e].concat(t).concat([n]))},Q="^",ee='"^"',te=function(){return new n("start")},ne="$",re='"$"',oe=function(){return new n("end")},ae=function(e,t){return new s(e,t)},ie="Quantifier",se=function(e,t){return t&&(e.greedy=!1),e},le="{",ue='"{"',ce=",",fe='","',pe="}",de='"}"',he=function(e,t){return new l(e,t)},me=",}",ge='",}"',ve=function(e){return new l(e,1/0)},ye=function(e){return new l(e,e)},be="+",xe='"+"',we=function(){return new l(1,1/0)},Ee="*",Ae='"*"',Oe=function(){return new l(0,1/0)},Ce="?",Re='"?"',Se=function(){return new l(0,1)},_e=/^[0-9]/,Pe="[0-9]",ke=function(e){return+e.join("")},Me="(",Te='"("',Fe=")",He='")"',De=function(e){return e},Ne=function(e){return new i(e)},qe="?:",Le='"?:"',je=function(e){return new a("non-capture-group",e)},Ie="?=",$e='"?="',Ve=function(e){return new a("positive-lookahead",e)},Be="?!",We='"?!"',Ue=function(e){return new a("negative-lookahead",e)},ze="CharacterSet",Xe="[",Ge='"["',Ye="]",Ke='"]"',Je=function(e,t){return new u(!!e,t)},Ze="CharacterRange",Qe="-",et='"-"',tt=function(e,t){return new c(e,t)},nt="Character",rt=/^[^\\\]]/,ot="[^\\\\\\]]",at=function(e){return new f(e)},it=".",st='"."',lt=function(){return new n("any-character")},ut="Literal",ct=/^[^|\\\/.[()?+*$\^]/,ft="[^|\\\\\\/.[()?+*$\\^]",pt="\\b",dt='"\\\\b"',ht=function(){return new n("backspace")},mt=function(){return new n("word-boundary")},gt="\\B",vt='"\\\\B"',yt=function(){return new n("non-word-boundary")},bt="\\d",xt='"\\\\d"',wt=function(){return new n("digit")},Et="\\D",At='"\\\\D"',Ot=function(){return new n("non-digit")},Ct="\\f",Rt='"\\\\f"',St=function(){return new n("form-feed")},_t="\\n",Pt='"\\\\n"',kt=function(){return new n("line-feed")},Mt="\\r",Tt='"\\\\r"',Ft=function(){return new n("carriage-return")},Ht="\\s",Dt='"\\\\s"',Nt=function(){return new n("white-space")},qt="\\S",Lt='"\\\\S"',jt=function(){return new n("non-white-space")},It="\\t",$t='"\\\\t"',Vt=function(){return new n("tab")},Bt="\\v",Wt='"\\\\v"',Ut=function(){return new n("vertical-tab")},zt="\\w",Xt='"\\\\w"',Gt=function(){return new n("word")},Yt="\\W",Kt='"\\\\W"',Jt=function(){return new n("non-word")},Zt="\\c",Qt='"\\\\c"',en="any character",tn=function(e){return new g(e)},nn="\\",rn='"\\\\"',on=/^[1-9]/,an="[1-9]",sn=function(e){return new m(e)},ln="\\0",un='"\\\\0"',cn=/^[0-7]/,fn="[0-7]",pn=function(e){return new h(e.join(""))},dn="\\x",hn='"\\\\x"',mn=/^[0-9a-fA-F]/,gn="[0-9a-fA-F]",vn=function(e){return new d(e.join(""))},yn="\\u",bn='"\\\\u"',xn=function(e){return new p(e.join(""))},wn=function(){return new n("null-character")},En=0,An=0,On=0,Cn={line:1,column:1,seenCR:!1},Rn=0,Sn=[],_n=0;if("startRule"in W){if(!(W.startRule in U))throw new Error("Can't start parsing from rule \""+W.startRule+'".');z=U[W.startRule]}if(n.offset=function(){return An},n.text=function(){return t.substring(An,En)},null!==(B=z())&&En===t.length)return B;throw function(e){var t=0;for(e.sort();t<e.length;)e[t-1]===e[t]?e.splice(t,1):t++}(Sn),An=Math.max(En,Rn),new e(Sn,An<t.length?t.charAt(An):null,An,v(An).line,v(An).column)}}}(),y=1,b={};e.exports=v},function(e,t,n){var r=n(3),o=n(5),a={extend:r.extend},i=d(97,122),s=d(65,90),l=d(48,57),u=d(32,47)+d(58,64)+d(91,96)+d(123,126),c=d(32,126),f=" \f\n\r\t\v \u2028\u2029",p={"\\w":i+s+l+"_","\\W":u.replace("_",""),"\\s":f,"\\S":function(){for(var e=c,t=0;t<9;t++)e=e.replace(f[t],"");return e}(),"\\d":l,"\\D":i+s+u};function d(e,t){for(var n="",r=e;r<=t;r++)n+=String.fromCharCode(r);return n}a.gen=function(e,t,n){return n=n||{guid:1},a[e.type]?a[e.type](e,t,n):a.token(e,t,n)},a.extend({token:function(e,t,n){switch(e.type){case"start":case"end":case"backspace":case"word-boundary":return"";case"any-character":return o.character();case"non-word-boundary":case"form-feed":case"carriage-return":case"tab":case"vertical-tab":break;case"digit":return o.pick(l.split(""));case"non-digit":return o.pick((i+s+u).split(""));case"line-feed":return e.body||e.text;case"white-space":return o.pick(f.split(""));case"non-white-space":case"word":return o.pick((i+s+l).split(""));case"non-word":return o.pick(u.replace("_","").split(""))}return e.body||e.text},alternate:function(e,t,n){return this.gen(o.boolean()?e.left:e.right,t,n)},match:function(e,t,n){t="";for(var r=0;r<e.body.length;r++)t+=this.gen(e.body[r],t,n);return t},"capture-group":function(e,t,n){return t=this.gen(e.body,t,n),n[n.guid++]=t,t},"non-capture-group":function(e,t,n){return this.gen(e.body,t,n)},"positive-lookahead":function(e,t,n){return this.gen(e.body,t,n)},"negative-lookahead":function(e,t,n){return""},quantified:function(e,t,n){t="";for(var r=this.quantifier(e.quantifier),o=0;o<r;o++)t+=this.gen(e.body,t,n);return t},quantifier:function(e,t,n){var r=Math.max(e.min,0),a=isFinite(e.max)?e.max:r+o.integer(3,7);return o.integer(r,a)},charset:function(e,t,n){if(e.invert)return this["invert-charset"](e,t,n);var r=o.pick(e.body);return this.gen(r,t,n)},"invert-charset":function(e,t,n){for(var r,a=c,i=0;i<e.body.length;i++)switch((r=e.body[i]).type){case"literal":a=a.replace(r.body,"");break;case"range":for(var s=this.gen(r.start,t,n).charCodeAt(),l=this.gen(r.end,t,n).charCodeAt(),u=s;u<=l;u++)a=a.replace(String.fromCharCode(u),"");default:var f=p[r.text];if(f)for(var d=0;d<=f.length;d++)a=a.replace(f[d],"")}return o.pick(a.split(""))},range:function(e,t,n){var r=this.gen(e.start,t,n).charCodeAt(),a=this.gen(e.end,t,n).charCodeAt();return String.fromCharCode(o.integer(r,a))},literal:function(e,t,n){return e.escaped?e.body:e.text},unicode:function(e,t,n){return String.fromCharCode(parseInt(e.code,16))},hex:function(e,t,n){return String.fromCharCode(parseInt(e.code,16))},octal:function(e,t,n){return String.fromCharCode(parseInt(e.code,8))},"back-reference":function(e,t,n){return n[e.code]||""},CONTROL_CHARACTER_MAP:function(){for(var e="@ A B C D E F G H I J K L M N O P Q R S T U V W X Y Z [ \\ ] ^ _".split(" "),t="\0        \b \t \n \v \f \r                  ".split(" "),n={},r=0;r<e.length;r++)n[e[r]]=t[r];return n}(),"control-character":function(e,t,n){return this.CONTROL_CHARACTER_MAP[e.code]}}),e.exports=a},function(e,t,n){e.exports=n(24)},function(e,t,n){var r=n(2),o=n(3),a=n(4);e.exports=function e(t,n,i){i=i||[];var s={name:"string"==typeof n?n.replace(r.RE_KEY,"$1"):n,template:t,type:o.type(t),rule:a.parse(n)};switch(s.path=i.slice(0),s.path.push(void 0===n?"ROOT":s.name),s.type){case"array":s.items=[],o.each(t,function(t,n){s.items.push(e(t,n,s.path))});break;case"object":s.properties=[],o.each(t,function(t,n){s.properties.push(e(t,n,s.path))})}return s}},function(e,t,n){e.exports=n(26)},function(e,t,n){var r=n(2),o=n(3),a=n(23);function i(e,t){for(var n=a(e),r=s.diff(n,t),o=0;o<r.length;o++);return r}var s={diff:function(e,t,n){var r=[];return this.name(e,t,n,r)&&this.type(e,t,n,r)&&(this.value(e,t,n,r),this.properties(e,t,n,r),this.items(e,t,n,r)),r},name:function(e,t,n,r){var o=r.length;return l.equal("name",e.path,n+"",e.name+"",r),r.length===o},type:function(e,t,n,a){var i=a.length;switch(e.type){case"string":if(e.template.match(r.RE_PLACEHOLDER))return!0;break;case"array":if(e.rule.parameters){if(void 0!==e.rule.min&&void 0===e.rule.max&&1===e.rule.count)return!0;if(e.rule.parameters[2])return!0}break;case"function":return!0}return l.equal("type",e.path,o.type(t),e.type,a),a.length===i},value:function(e,t,n,o){var a,i=o.length,s=e.rule,u=e.type;if("object"===u||"array"===u||"function"===u)return!0;if(!s.parameters){switch(u){case"regexp":return l.match("value",e.path,t,e.template,o),o.length===i;case"string":if(e.template.match(r.RE_PLACEHOLDER))return o.length===i}return l.equal("value",e.path,t,e.template,o),o.length===i}switch(u){case"number":var c=(t+"").split(".");c[0]=+c[0],void 0!==s.min&&void 0!==s.max&&(l.greaterThanOrEqualTo("value",e.path,c[0],Math.min(s.min,s.max),o),l.lessThanOrEqualTo("value",e.path,c[0],Math.max(s.min,s.max),o)),void 0!==s.min&&void 0===s.max&&l.equal("value",e.path,c[0],s.min,o,"[value] "+n),s.decimal&&(void 0!==s.dmin&&void 0!==s.dmax&&(l.greaterThanOrEqualTo("value",e.path,c[1].length,s.dmin,o),l.lessThanOrEqualTo("value",e.path,c[1].length,s.dmax,o)),void 0!==s.dmin&&void 0===s.dmax&&l.equal("value",e.path,c[1].length,s.dmin,o));break;case"boolean":break;case"string":a=(a=t.match(new RegExp(e.template,"g")))?a.length:0,void 0!==s.min&&void 0!==s.max&&(l.greaterThanOrEqualTo("repeat count",e.path,a,s.min,o),l.lessThanOrEqualTo("repeat count",e.path,a,s.max,o)),void 0!==s.min&&void 0===s.max&&l.equal("repeat count",e.path,a,s.min,o);break;case"regexp":a=(a=t.match(new RegExp(e.template.source.replace(/^\^|\$$/g,""),"g")))?a.length:0,void 0!==s.min&&void 0!==s.max&&(l.greaterThanOrEqualTo("repeat count",e.path,a,s.min,o),l.lessThanOrEqualTo("repeat count",e.path,a,s.max,o)),void 0!==s.min&&void 0===s.max&&l.equal("repeat count",e.path,a,s.min,o)}return o.length===i},properties:function(e,t,n,r){var a=r.length,i=e.rule,s=o.keys(t);if(e.properties){if(e.rule.parameters?(void 0!==i.min&&void 0!==i.max&&(l.greaterThanOrEqualTo("properties length",e.path,s.length,Math.min(i.min,i.max),r),l.lessThanOrEqualTo("properties length",e.path,s.length,Math.max(i.min,i.max),r)),void 0!==i.min&&void 0===i.max&&1!==i.count&&l.equal("properties length",e.path,s.length,i.min,r)):l.equal("properties length",e.path,s.length,e.properties.length,r),r.length!==a)return!1;for(var u=0;u<s.length;u++)r.push.apply(r,this.diff(function(){var t;return o.each(e.properties,function(e){e.name===s[u]&&(t=e)}),t||e.properties[u]}(),t[s[u]],s[u]));return r.length===a}},items:function(e,t,n,r){var o=r.length;if(e.items){var a=e.rule;if(e.rule.parameters){if(void 0!==a.min&&void 0!==a.max&&(l.greaterThanOrEqualTo("items",e.path,t.length,Math.min(a.min,a.max)*e.items.length,r,"[{utype}] array is too short: {path} must have at least {expected} elements but instance has {actual} elements"),l.lessThanOrEqualTo("items",e.path,t.length,Math.max(a.min,a.max)*e.items.length,r,"[{utype}] array is too long: {path} must have at most {expected} elements but instance has {actual} elements")),void 0!==a.min&&void 0===a.max){if(1===a.count)return r.length===o;l.equal("items length",e.path,t.length,a.min*e.items.length,r)}if(a.parameters[2])return r.length===o}else l.equal("items length",e.path,t.length,e.items.length,r);if(r.length!==o)return!1;for(var i=0;i<t.length;i++)r.push.apply(r,this.diff(e.items[i%e.items.length],t[i],i%e.items.length));return r.length===o}}},l={message:function(e){return(e.message||"[{utype}] Expect {path}'{ltype} {action} {expected}, but is {actual}").replace("{utype}",e.type.toUpperCase()).replace("{ltype}",e.type.toLowerCase()).replace("{path}",o.isArray(e.path)&&e.path.join(".")||e.path).replace("{action}",e.action).replace("{expected}",e.expected).replace("{actual}",e.actual)},equal:function(e,t,n,r,o,a){if(n===r)return!0;if("type"===e&&"regexp"===r&&"string"===n)return!0;var i={path:t,type:e,actual:n,expected:r,action:"is equal to",message:a};return i.message=l.message(i),o.push(i),!1},match:function(e,t,n,r,o,a){if(r.test(n))return!0;var i={path:t,type:e,actual:n,expected:r,action:"matches",message:a};return i.message=l.message(i),o.push(i),!1},notEqual:function(e,t,n,r,o,a){if(n!==r)return!0;var i={path:t,type:e,actual:n,expected:r,action:"is not equal to",message:a};return i.message=l.message(i),o.push(i),!1},greaterThan:function(e,t,n,r,o,a){if(n>r)return!0;var i={path:t,type:e,actual:n,expected:r,action:"is greater than",message:a};return i.message=l.message(i),o.push(i),!1},lessThan:function(e,t,n,r,o,a){if(n<r)return!0;var i={path:t,type:e,actual:n,expected:r,action:"is less to",message:a};return i.message=l.message(i),o.push(i),!1},greaterThanOrEqualTo:function(e,t,n,r,o,a){if(n>=r)return!0;var i={path:t,type:e,actual:n,expected:r,action:"is greater than or equal to",message:a};return i.message=l.message(i),o.push(i),!1},lessThanOrEqualTo:function(e,t,n,r,o,a){if(n<=r)return!0;var i={path:t,type:e,actual:n,expected:r,action:"is less than or equal to",message:a};return i.message=l.message(i),o.push(i),!1}};i.Diff=s,i.Assert=l,e.exports=i},function(e,t,n){e.exports=n(28)},function(e,t,n){var r=n(3);window._XMLHttpRequest=window.XMLHttpRequest,window._ActiveXObject=window.ActiveXObject;try{new window.Event("custom")}catch(c){window.Event=function(e,t,n,r){var o=document.createEvent("CustomEvent");return o.initCustomEvent(e,t,n,r),o}}var o={UNSENT:0,OPENED:1,HEADERS_RECEIVED:2,LOADING:3,DONE:4},a="readystatechange loadstart progress abort error load timeout loadend".split(" "),i="timeout withCredentials".split(" "),s="readyState responseURL status statusText responseType response responseText responseXML".split(" "),l="OK";function u(){this.custom={events:{},requestHeaders:{},responseHeaders:{}}}u._settings={timeout:"10-100"},u.setup=function(e){return r.extend(u._settings,e),u._settings},r.extend(u,o),r.extend(u.prototype,o),u.prototype.mock=!0,u.prototype.match=!1,r.extend(u.prototype,{open:function(e,t,n,o,l){var c=this;r.extend(this.custom,{method:e,url:t,async:"boolean"!=typeof n||n,username:o,password:l,options:{url:t,type:e}}),this.custom.timeout=function(e){if("number"==typeof e)return e;if("string"==typeof e&&!~e.indexOf("-"))return parseInt(e,10);if("string"==typeof e&&~e.indexOf("-")){var t=e.split("-"),n=parseInt(t[0],10),r=parseInt(t[1],10);return Math.round(Math.random()*(r-n))+n}}(u._settings.timeout);var f=function(e){for(var t in u.Mock._mocked){var n=u.Mock._mocked[t];if((!n.rurl||o(n.rurl,e.url))&&(!n.rtype||o(n.rtype,e.type.toLowerCase())))return n}function o(e,t){return"string"===r.type(e)?e===t:"regexp"===r.type(e)?e.test(t):void 0}}(this.custom.options);function p(e){for(var t=0;t<s.length;t++)try{c[s[t]]=d[s[t]]}catch(n){}c.dispatchEvent(new Event(e.type))}if(f)this.match=!0,this.custom.template=f,this.readyState=u.OPENED,this.dispatchEvent(new Event("readystatechange"));else{var d=function(){var e,t,n,r,o=(e=/^(?:about|app|app-storage|.+-extension|file|res|widget):$/,t=/^([\w.+-]+:)(?:\/\/([^\/?#:]*)(?::(\d+)|)|)/,n=location.href,r=t.exec(n.toLowerCase())||[],e.test(r[1]));return window.ActiveXObject?!o&&a()||i():a();function a(){try{return new window._XMLHttpRequest}catch(e){}}function i(){try{return new window._ActiveXObject("Microsoft.XMLHTTP")}catch(e){}}}();this.custom.xhr=d;for(var h=0;h<a.length;h++)d.addEventListener(a[h],p);o?d.open(e,t,n,o,l):d.open(e,t,n);for(var m=0;m<i.length;m++)try{d[i[m]]=c[i[m]]}catch(g){}}},setRequestHeader:function(e,t){if(this.match){var n=this.custom.requestHeaders;n[e]?n[e]+=","+t:n[e]=t}else this.custom.xhr.setRequestHeader(e,t)},timeout:0,withCredentials:!1,upload:{},send:function(e){var t=this;function n(){var e,n;t.readyState=u.HEADERS_RECEIVED,t.dispatchEvent(new Event("readystatechange")),t.readyState=u.LOADING,t.dispatchEvent(new Event("readystatechange")),t.status=200,t.statusText=l,t.response=t.responseText=JSON.stringify((e=t.custom.template,n=t.custom.options,r.isFunction(e.template)?e.template(n):u.Mock.mock(e.template)),null,4),t.readyState=u.DONE,t.dispatchEvent(new Event("readystatechange")),t.dispatchEvent(new Event("load")),t.dispatchEvent(new Event("loadend"))}this.custom.options.body=e,this.match?(this.setRequestHeader("X-Requested-With","MockXMLHttpRequest"),this.dispatchEvent(new Event("loadstart")),this.custom.async?setTimeout(n,this.custom.timeout):n()):this.custom.xhr.send(e)},abort:function(){this.match?(this.readyState=u.UNSENT,this.dispatchEvent(new Event("abort",!1,!1,this)),this.dispatchEvent(new Event("error",!1,!1,this))):this.custom.xhr.abort()}}),r.extend(u.prototype,{responseURL:"",status:u.UNSENT,statusText:"",getResponseHeader:function(e){return this.match?this.custom.responseHeaders[e.toLowerCase()]:this.custom.xhr.getResponseHeader(e)},getAllResponseHeaders:function(){if(!this.match)return this.custom.xhr.getAllResponseHeaders();var e=this.custom.responseHeaders,t="";for(var n in e)e.hasOwnProperty(n)&&(t+=n+": "+e[n]+"\r\n");return t},overrideMimeType:function(){},responseType:"",response:null,responseText:"",responseXML:null}),r.extend(u.prototype,{addEventListener:function(e,t){var n=this.custom.events;n[e]||(n[e]=[]),n[e].push(t)},removeEventListener:function(e,t){for(var n=this.custom.events[e]||[],r=0;r<n.length;r++)n[r]===t&&n.splice(r--,1)},dispatchEvent:function(e){for(var t=this.custom.events[e.type]||[],n=0;n<t.length;n++)t[n].call(this,e);var r="on"+e.type;this[r]&&this[r](e)}}),e.exports=u}])},module.exports=factory()})(mock);var mockExports=mock.exports;const Mock=getDefaultExportFromCjs(mockExports);export{Ee as E,Mock as M,Schema as S,TinyColor as T,Y,arrow as a,autoUpdate as b,computePosition as c,detectOverflow as d,flip as f,memoizeOne as m,offset as o,shift as s,yn as y};
