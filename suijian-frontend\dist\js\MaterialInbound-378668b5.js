import{E as e}from"./element-plus-7917fd46.js";import{l as a,r as l,_ as t,y as r,R as u,J as o,av as d,x as i,O as n,z as s}from"./vue-vendor-fc5a6493.js";import{_ as m}from"./_plugin-vue_export-helper-1b428a4d.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const p={class:"material-inbound"},c={class:"form-actions"},_=m(a({__name:"MaterialInbound",setup(a){const m=l(),_=t({inboundDate:(new Date).toISOString().split("T")[0],operator:"",materialCode:"",materialName:"",specification:"",unit:"",quantity:0,unitPrice:0,remarks:""}),f={inboundDate:[{required:!0,message:"请选择入库日期",trigger:"change"}],operator:[{required:!0,message:"请输入操作员",trigger:"blur"}],materialCode:[{required:!0,message:"请输入物料编码",trigger:"blur"}],materialName:[{required:!0,message:"请输入物料名称",trigger:"blur"}],specification:[{required:!0,message:"请输入规格",trigger:"blur"}],unit:[{required:!0,message:"请输入单位",trigger:"blur"}],quantity:[{required:!0,message:"请输入入库数量",trigger:"blur"}],unitPrice:[{required:!0,message:"请输入单价",trigger:"blur"}]},b=()=>{return a=this,l=null,t=function*(){if(m.value)try{yield m.value.validate(),e.success("入库成功")}catch(a){e.error("请检查表单填写是否正确")}},new Promise((e,r)=>{var u=e=>{try{d(t.next(e))}catch(a){r(a)}},o=e=>{try{d(t.throw(e))}catch(a){r(a)}},d=a=>a.done?e(a.value):Promise.resolve(a.value).then(u,o);d((t=t.apply(a,l)).next())});var a,l,t},g=()=>{var e;null==(e=m.value)||e.resetFields()},V=()=>{e.info("已取消")};return(e,a)=>{const l=d("el-breadcrumb-item"),t=d("el-breadcrumb"),h=d("el-col"),v=d("el-date-picker"),y=d("el-form-item"),q=d("el-input"),w=d("el-row"),D=d("el-input-number"),U=d("el-button"),k=d("el-form"),C=d("el-card");return i(),r("div",p,[u(t,{class:"breadcrumb",separator:">"},{default:o(()=>[u(l,{to:{path:"/dashboard"}},{default:o(()=>a[9]||(a[9]=[n("首页",-1)])),_:1,__:[9]}),u(l,{to:{path:"/warehouse/material-list"}},{default:o(()=>a[10]||(a[10]=[n("仓库管理",-1)])),_:1,__:[10]}),u(l,null,{default:o(()=>a[11]||(a[11]=[n("甲料入库",-1)])),_:1,__:[11]})]),_:1}),u(C,{class:"main-card"},{header:o(()=>a[12]||(a[12]=[s("div",{class:"card-header"},[s("span",null,"📦 甲料入库")],-1)])),default:o(()=>[u(k,{model:_,rules:f,ref_key:"formRef",ref:m,"label-width":"120px"},{default:o(()=>[u(w,{gutter:20,class:"form-section"},{default:o(()=>[u(h,{span:24},{default:o(()=>a[13]||(a[13]=[s("div",{class:"section-title"},"基本信息",-1)])),_:1,__:[13]}),u(h,{span:12},{default:o(()=>[u(y,{label:"入库日期:",prop:"inboundDate"},{default:o(()=>[u(v,{modelValue:_.inboundDate,"onUpdate:modelValue":a[0]||(a[0]=e=>_.inboundDate=e),type:"date",placeholder:"请选择入库日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),u(h,{span:12},{default:o(()=>[u(y,{label:"操作员:",prop:"operator"},{default:o(()=>[u(q,{modelValue:_.operator,"onUpdate:modelValue":a[1]||(a[1]=e=>_.operator=e),placeholder:"请输入操作员"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),u(w,{gutter:20,class:"form-section"},{default:o(()=>[u(h,{span:24},{default:o(()=>a[14]||(a[14]=[s("div",{class:"section-title"},"物料信息",-1)])),_:1,__:[14]}),u(h,{span:12},{default:o(()=>[u(y,{label:"物料编码:",prop:"materialCode"},{default:o(()=>[u(q,{modelValue:_.materialCode,"onUpdate:modelValue":a[2]||(a[2]=e=>_.materialCode=e),placeholder:"请输入物料编码"},null,8,["modelValue"])]),_:1})]),_:1}),u(h,{span:12},{default:o(()=>[u(y,{label:"物料名称:",prop:"materialName"},{default:o(()=>[u(q,{modelValue:_.materialName,"onUpdate:modelValue":a[3]||(a[3]=e=>_.materialName=e),placeholder:"请输入物料名称"},null,8,["modelValue"])]),_:1})]),_:1}),u(h,{span:12},{default:o(()=>[u(y,{label:"规格:",prop:"specification"},{default:o(()=>[u(q,{modelValue:_.specification,"onUpdate:modelValue":a[4]||(a[4]=e=>_.specification=e),placeholder:"请输入规格"},null,8,["modelValue"])]),_:1})]),_:1}),u(h,{span:12},{default:o(()=>[u(y,{label:"单位:",prop:"unit"},{default:o(()=>[u(q,{modelValue:_.unit,"onUpdate:modelValue":a[5]||(a[5]=e=>_.unit=e),placeholder:"请输入单位"},null,8,["modelValue"])]),_:1})]),_:1}),u(h,{span:12},{default:o(()=>[u(y,{label:"入库数量:",prop:"quantity"},{default:o(()=>[u(D,{modelValue:_.quantity,"onUpdate:modelValue":a[6]||(a[6]=e=>_.quantity=e),min:0,step:1,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),u(h,{span:12},{default:o(()=>[u(y,{label:"单价:",prop:"unitPrice"},{default:o(()=>[u(D,{modelValue:_.unitPrice,"onUpdate:modelValue":a[7]||(a[7]=e=>_.unitPrice=e),min:0,step:.01,precision:2,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),u(h,{span:24},{default:o(()=>[u(y,{label:"备注:"},{default:o(()=>[u(q,{modelValue:_.remarks,"onUpdate:modelValue":a[8]||(a[8]=e=>_.remarks=e),type:"textarea",rows:3,placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),s("div",c,[u(U,{type:"primary",onClick:b},{default:o(()=>a[15]||(a[15]=[n("提交",-1)])),_:1,__:[15]}),u(U,{onClick:g},{default:o(()=>a[16]||(a[16]=[n("重置",-1)])),_:1,__:[16]}),u(U,{onClick:V},{default:o(()=>a[17]||(a[17]=[n("取消",-1)])),_:1,__:[17]})])]),_:1},8,["model"])]),_:1})])}}}),[["__scopeId","data-v-bc53c77d"]]);export{_ as default};
