var e=Object.defineProperty,a=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty,t=Object.prototype.propertyIsEnumerable,u=(a,l,t)=>l in a?e(a,l,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[l]=t,o=(e,o)=>{for(var r in o||(o={}))l.call(o,r)&&u(e,r,o[r]);if(a)for(var r of a(o))t.call(o,r)&&u(e,r,o[r]);return e};import{E as r,e as d}from"./element-plus-7917fd46.js";import{l as i,_ as n,r as p,c as m,y as s,R as c,J as _,av as f,x as v,z as y,O as C,P as b,Q as g,aa as V,M as h}from"./vue-vendor-fc5a6493.js";import{_ as k}from"./_plugin-vue_export-helper-1b428a4d.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const w={class:"material-base-container"},x={class:"search-buttons"},N={class:"form-actions"},U={key:0},T=k(i({__name:"MaterialBase",setup(e){const a=n({materialCode:"",materialName:"",category:""}),l=n({currentPage:1,pageSize:10,total:236}),t=p([{id:1,materialCode:"WL001",partyCode:["JD001","JD002"],materialCategory:"甲料",materialName:"电缆线",model:"YJV-3*4",specification:"3*4mm²",unit:"米",createTime:"2023-01-15"},{id:2,materialCode:"WL002",partyCode:["JD003"],materialCategory:"商品",materialName:"开关面板",model:"KP-86",specification:"86型",unit:"个",createTime:"2023-01-15"},{id:3,materialCode:"WL003",partyCode:["JD004"],materialCategory:"辅料",materialName:"电线",model:"BV-2.5",specification:"2.5mm²",unit:"米",createTime:"2023-01-14"},{id:4,materialCode:"WL004",partyCode:["JD005"],materialCategory:"甲料",materialName:"水管",model:"PPR-20",specification:"20mm",unit:"米",createTime:"2023-01-14"},{id:5,materialCode:"WL005",partyCode:["JD006"],materialCategory:"商品",materialName:"灯具",model:"LED-12W",specification:"12W",unit:"个",createTime:"2023-01-13"}]),u=p({id:0,materialCode:"",partyCode:[],partyCodeInput:"",materialCategory:"",materialName:"",model:"",specification:"",unit:"",createTime:"",updateTime:"",remarks:""}),i=p([]),k=p(""),T=p(!1),P=p(!1),D=p(!1),j=p(!1),z=p(),J=m(()=>u.value.id?"修改物料":"新增物料"),S={materialCode:[{required:!0,message:"请输入公司物料编码",trigger:"blur"}],materialCategory:[{required:!0,message:"请选择物料分类",trigger:"blur"}],materialName:[{required:!0,message:"请输入物料名称",trigger:"blur"}],unit:[{required:!0,message:"请输入单位",trigger:"blur"}]},I=()=>{r.success("搜索成功")},O=()=>{a.materialCode="",a.materialName="",a.category=""},W=e=>{l.pageSize=e},L=e=>{l.currentPage=e},E=()=>{u.value={id:0,materialCode:"",partyCode:[],partyCodeInput:"",materialCategory:"",materialName:"",model:"",specification:"",unit:"",createTime:"",updateTime:"",remarks:""},T.value=!0},q=()=>{z.value&&z.value.validate(e=>{e&&(r.success("保存成功"),T.value=!1)})},B=()=>{z.value&&z.value.resetFields()},R=()=>{j.value=!0},M=e=>{k.value=e.name},F=()=>{r.success("下载模板")},A=()=>{r.success("导入成功"),j.value=!1},K=()=>{r.success("导出Excel")},Q=()=>{r.success("显示统计分析")};return(e,n)=>{const p=f("el-input"),m=f("el-col"),Y=f("el-option"),$=f("el-select"),G=f("el-button"),H=f("el-row"),X=f("el-table-column"),Z=f("el-table"),ee=f("el-pagination"),ae=f("el-form-item"),le=f("el-form"),te=f("el-dialog"),ue=f("el-upload"),oe=f("el-card");return v(),s("div",w,[c(oe,{class:"main-card"},{header:_(()=>n[21]||(n[21]=[y("div",{class:"card-header"},[y("span",null,"物料基础库")],-1)])),default:_(()=>[c(H,{gutter:20,class:"search-section"},{default:_(()=>[c(m,{span:6},{default:_(()=>[c(p,{modelValue:a.materialCode,"onUpdate:modelValue":n[0]||(n[0]=e=>a.materialCode=e),placeholder:"公司物料编码",clearable:""},null,8,["modelValue"])]),_:1}),c(m,{span:6},{default:_(()=>[c(p,{modelValue:a.materialName,"onUpdate:modelValue":n[1]||(n[1]=e=>a.materialName=e),placeholder:"物料名称",clearable:""},null,8,["modelValue"])]),_:1}),c(m,{span:6},{default:_(()=>[c($,{modelValue:a.category,"onUpdate:modelValue":n[2]||(n[2]=e=>a.category=e),placeholder:"分类",clearable:"",style:{width:"100%"}},{default:_(()=>[c(Y,{label:"甲料",value:"material"}),c(Y,{label:"商品",value:"product"}),c(Y,{label:"辅料",value:"auxiliary"})]),_:1},8,["modelValue"])]),_:1}),c(m,{span:6},{default:_(()=>[y("div",x,[c(G,{type:"primary",icon:"Search",onClick:I},{default:_(()=>n[22]||(n[22]=[C("搜索",-1)])),_:1,__:[22]}),c(G,{icon:"Refresh",onClick:O},{default:_(()=>n[23]||(n[23]=[C("重置",-1)])),_:1,__:[23]})])]),_:1})]),_:1}),c(Z,{data:t.value,border:"",class:"material-table",style:{width:"100%","margin-top":"20px"}},{default:_(()=>[c(X,{prop:"materialCode",label:"公司物料编码","min-width":"120"}),c(X,{prop:"partyCode",label:"甲方编码","min-width":"100"},{default:_(e=>[c(G,{type:"primary",link:"",onClick:a=>{return l=e.row,i.value=l.partyCode,void(D.value=!0);var l}},{default:_(()=>n[24]||(n[24]=[C("显示",-1)])),_:2,__:[24]},1032,["onClick"])]),_:1}),c(X,{prop:"materialCategory",label:"物料分类","min-width":"100"}),c(X,{prop:"materialName",label:"物料名称","min-width":"120"}),c(X,{prop:"model",label:"型号","min-width":"100"}),c(X,{prop:"specification",label:"规格","min-width":"100"}),c(X,{prop:"unit",label:"单位","min-width":"80"}),c(X,{prop:"createTime",label:"创建时间","min-width":"120"}),c(X,{label:"操作","min-width":"150",fixed:"right"},{default:_(e=>[c(G,{type:"primary",link:"",onClick:a=>{return l=e.row,u.value=o({},l),u.value.partyCodeInput=l.partyCode.join(","),void(T.value=!0);var l}},{default:_(()=>n[25]||(n[25]=[C("修改",-1)])),_:2,__:[25]},1032,["onClick"]),c(G,{type:"primary",link:"",onClick:a=>{return l=e.row,u.value=o({},l),u.value.createTime="2023-01-15",u.value.updateTime="2024-01-15",void(P.value=!0);var l}},{default:_(()=>n[26]||(n[26]=[C("详情",-1)])),_:2,__:[26]},1032,["onClick"]),c(G,{type:"danger",link:"",onClick:a=>{return l=e.row,void d.confirm(`确定要删除物料 "${l.materialName}" 吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{r.success("删除成功")}).catch(()=>{});var l}},{default:_(()=>n[27]||(n[27]=[C("删除",-1)])),_:2,__:[27]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),c(ee,{"current-page":l.currentPage,"onUpdate:currentPage":n[3]||(n[3]=e=>l.currentPage=e),"page-size":l.pageSize,"onUpdate:pageSize":n[4]||(n[4]=e=>l.pageSize=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:l.total,onSizeChange:W,onCurrentChange:L,class:"pagination"},null,8,["current-page","page-size","total"]),y("div",N,[c(G,{type:"primary",icon:"Plus",onClick:E},{default:_(()=>n[28]||(n[28]=[C("新增物料",-1)])),_:1,__:[28]}),c(G,{icon:"Upload",onClick:R},{default:_(()=>n[29]||(n[29]=[C("批量导入",-1)])),_:1,__:[29]}),c(G,{icon:"Download",onClick:K},{default:_(()=>n[30]||(n[30]=[C("导出Excel",-1)])),_:1,__:[30]}),c(G,{icon:"DataAnalysis",onClick:Q},{default:_(()=>n[31]||(n[31]=[C("统计分析",-1)])),_:1,__:[31]})]),c(te,{modelValue:T.value,"onUpdate:modelValue":n[14]||(n[14]=e=>T.value=e),title:J.value,width:"600",onClose:B},{footer:_(()=>[c(G,{onClick:n[13]||(n[13]=e=>T.value=!1)},{default:_(()=>n[33]||(n[33]=[C("取消",-1)])),_:1,__:[33]}),c(G,{type:"primary",onClick:q},{default:_(()=>n[34]||(n[34]=[C("保存",-1)])),_:1,__:[34]})]),default:_(()=>[c(le,{ref_key:"materialFormRef",ref:z,model:u.value,rules:S,"label-width":"120px"},{default:_(()=>[c(H,{gutter:20},{default:_(()=>[c(m,{span:12},{default:_(()=>[c(ae,{label:"公司物料编码:",prop:"materialCode"},{default:_(()=>[c(p,{modelValue:u.value.materialCode,"onUpdate:modelValue":n[5]||(n[5]=e=>u.value.materialCode=e),placeholder:"请输入公司物料编码"},null,8,["modelValue"])]),_:1})]),_:1}),c(m,{span:12},{default:_(()=>[c(ae,{label:"物料分类:",prop:"materialCategory"},{default:_(()=>[c($,{modelValue:u.value.materialCategory,"onUpdate:modelValue":n[6]||(n[6]=e=>u.value.materialCategory=e),placeholder:"请选择物料分类",style:{width:"100%"}},{default:_(()=>[c(Y,{label:"甲料",value:"甲料"}),c(Y,{label:"商品",value:"商品"}),c(Y,{label:"辅料",value:"辅料"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),c(m,{span:12},{default:_(()=>[c(ae,{label:"物料名称:",prop:"materialName"},{default:_(()=>[c(p,{modelValue:u.value.materialName,"onUpdate:modelValue":n[7]||(n[7]=e=>u.value.materialName=e),placeholder:"请输入物料名称"},null,8,["modelValue"])]),_:1})]),_:1}),c(m,{span:12},{default:_(()=>[c(ae,{label:"型号:",prop:"model"},{default:_(()=>[c(p,{modelValue:u.value.model,"onUpdate:modelValue":n[8]||(n[8]=e=>u.value.model=e),placeholder:"请输入型号"},null,8,["modelValue"])]),_:1})]),_:1}),c(m,{span:12},{default:_(()=>[c(ae,{label:"规格:",prop:"specification"},{default:_(()=>[c(p,{modelValue:u.value.specification,"onUpdate:modelValue":n[9]||(n[9]=e=>u.value.specification=e),placeholder:"请输入规格"},null,8,["modelValue"])]),_:1})]),_:1}),c(m,{span:12},{default:_(()=>[c(ae,{label:"单位:",prop:"unit"},{default:_(()=>[c(p,{modelValue:u.value.unit,"onUpdate:modelValue":n[10]||(n[10]=e=>u.value.unit=e),placeholder:"请输入单位"},null,8,["modelValue"])]),_:1})]),_:1}),c(m,{span:24},{default:_(()=>[c(ae,{label:"甲方编码:",prop:"partyCode"},{default:_(()=>[c(p,{modelValue:u.value.partyCodeInput,"onUpdate:modelValue":n[11]||(n[11]=e=>u.value.partyCodeInput=e),placeholder:"请输入甲方编码，多个编码用逗号分隔"},null,8,["modelValue"]),n[32]||(n[32]=y("div",{class:"party-code-explanation"}," 示例: JD001,JD002,JD003 (多个编码用逗号分隔) ",-1))]),_:1,__:[32]})]),_:1}),c(m,{span:24},{default:_(()=>[c(ae,{label:"备注:"},{default:_(()=>[c(p,{modelValue:u.value.remarks,"onUpdate:modelValue":n[12]||(n[12]=e=>u.value.remarks=e),type:"textarea",rows:2,placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),c(te,{modelValue:P.value,"onUpdate:modelValue":n[16]||(n[16]=e=>P.value=e),title:"物料详情",width:"600"},{footer:_(()=>[c(G,{onClick:n[15]||(n[15]=e=>P.value=!1)},{default:_(()=>n[35]||(n[35]=[C("关闭",-1)])),_:1,__:[35]})]),default:_(()=>[c(le,{model:u.value,"label-width":"120px"},{default:_(()=>[c(H,{gutter:20},{default:_(()=>[c(m,{span:12},{default:_(()=>[c(ae,{label:"公司物料编码:"},{default:_(()=>[y("span",null,b(u.value.materialCode),1)]),_:1})]),_:1}),c(m,{span:12},{default:_(()=>[c(ae,{label:"物料分类:"},{default:_(()=>[y("span",null,b(u.value.materialCategory),1)]),_:1})]),_:1}),c(m,{span:12},{default:_(()=>[c(ae,{label:"物料名称:"},{default:_(()=>[y("span",null,b(u.value.materialName),1)]),_:1})]),_:1}),c(m,{span:12},{default:_(()=>[c(ae,{label:"型号:"},{default:_(()=>[y("span",null,b(u.value.model),1)]),_:1})]),_:1}),c(m,{span:12},{default:_(()=>[c(ae,{label:"规格:"},{default:_(()=>[y("span",null,b(u.value.specification),1)]),_:1})]),_:1}),c(m,{span:12},{default:_(()=>[c(ae,{label:"单位:"},{default:_(()=>[y("span",null,b(u.value.unit),1)]),_:1})]),_:1}),c(m,{span:24},{default:_(()=>[c(ae,{label:"甲方编码:"},{default:_(()=>[(v(!0),s(g,null,V(u.value.partyCode,(e,a)=>(v(),s("div",{key:a,class:"party-code-item"}," 编码"+b(a+1)+": "+b(e),1))),128))]),_:1})]),_:1}),c(m,{span:12},{default:_(()=>[c(ae,{label:"创建时间:"},{default:_(()=>[y("span",null,b(u.value.createTime),1)]),_:1})]),_:1}),c(m,{span:12},{default:_(()=>[c(ae,{label:"更新时间:"},{default:_(()=>[y("span",null,b(u.value.updateTime),1)]),_:1})]),_:1}),c(m,{span:24},{default:_(()=>[c(ae,{label:"备注:"},{default:_(()=>[y("span",null,b(u.value.remarks),1)]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),c(te,{modelValue:D.value,"onUpdate:modelValue":n[18]||(n[18]=e=>D.value=e),title:"甲方编码列表",width:"500"},{footer:_(()=>[c(G,{onClick:n[17]||(n[17]=e=>D.value=!1)},{default:_(()=>n[36]||(n[36]=[C("关闭",-1)])),_:1,__:[36]})]),default:_(()=>[(v(!0),s(g,null,V(i.value,(e,a)=>(v(),s("div",{key:a,class:"party-code-item"}," 编码"+b(a+1)+": "+b(e),1))),128))]),_:1},8,["modelValue"]),c(te,{modelValue:j.value,"onUpdate:modelValue":n[20]||(n[20]=e=>j.value=e),title:"批量导入",width:"500"},{footer:_(()=>[c(G,{onClick:n[19]||(n[19]=e=>j.value=!1)},{default:_(()=>n[41]||(n[41]=[C("取消",-1)])),_:1,__:[41]}),c(G,{type:"primary",onClick:A},{default:_(()=>n[42]||(n[42]=[C("导入",-1)])),_:1,__:[42]})]),default:_(()=>[c(le,{"label-width":"100px"},{default:_(()=>[c(ae,{label:"导入类型:"},{default:_(()=>n[37]||(n[37]=[y("span",null,"物料基础库",-1)])),_:1,__:[37]}),c(ae,{label:"选择文件:"},{default:_(()=>[c(ue,{class:"upload-demo",action:"","auto-upload":!1,"on-change":M},{default:_(()=>[c(G,{type:"primary"},{default:_(()=>n[38]||(n[38]=[C("选择文件",-1)])),_:1,__:[38]})]),_:1}),k.value?(v(),s("div",U,"文件名: "+b(k.value),1)):h("",!0)]),_:1}),c(ae,{label:"模板下载:"},{default:_(()=>[c(G,{type:"primary",link:"",onClick:F},{default:_(()=>n[39]||(n[39]=[C("下载模板",-1)])),_:1,__:[39]})]),_:1}),c(ae,{label:"导入说明:"},{default:_(()=>n[40]||(n[40]=[y("div",{class:"import-instructions"},[y("p",null,"1. 请按照模板格式填写物料信息"),y("p",null,"2. 公司物料编码为必填项且不能重复"),y("p",null,"3. 物料分类: 甲料、商品、辅料"),y("p",null,"4. 一个公司物料编码可对应多个甲方编码(用逗号分隔)"),y("p",null,"5. 支持.xls、.xlsx格式文件")],-1)])),_:1,__:[40]})]),_:1})]),_:1},8,["modelValue"])]),_:1})])}}}),[["__scopeId","data-v-c175e4fb"]]);export{T as default};
