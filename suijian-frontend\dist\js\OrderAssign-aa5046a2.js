import{E as e}from"./element-plus-7917fd46.js";import{l,_ as a,y as o,R as d,J as t,av as u,x as s,z as r,I as n,M as m,O as p}from"./vue-vendor-fc5a6493.js";import{_ as c}from"./_plugin-vue_export-helper-1b428a4d.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const _={class:"order-assign-container"},f={class:"form-actions"},i=c(l({__name:"OrderAssign",setup(l){const c=a({dispatchDate:"",partyOrderNo:"",customerName:"",userNumber:"",communityName:"",building:"",roomNumber:"",phone:"",contactPerson:"",address:"",orderCategory:"",projectName:"",remarks:""}),i=()=>{e.success("保存成功")},V=()=>{e.success("提交成功")},b=()=>{e.success("开始打印")},h=()=>{e.info("已取消"),Object.keys(c).forEach(e=>{c[e]=""})};return(e,l)=>{const a=u("el-col"),v=u("el-date-picker"),y=u("el-form-item"),N=u("el-row"),g=u("el-input"),U=u("el-option"),j=u("el-select"),k=u("el-card"),C=u("el-button");return s(),o("div",_,[d(k,{class:"main-card"},{header:t(()=>l[13]||(l[13]=[r("div",{class:"card-header"},[r("span",null,"甲方派单")],-1)])),default:t(()=>[d(N,{gutter:20,class:"form-section"},{default:t(()=>[d(a,{span:24},{default:t(()=>l[14]||(l[14]=[r("div",{class:"section-title"},"派单信息",-1)])),_:1,__:[14]}),d(a,{span:12},{default:t(()=>[d(y,{label:"派单日期:"},{default:t(()=>[d(v,{modelValue:c.dispatchDate,"onUpdate:modelValue":l[0]||(l[0]=e=>c.dispatchDate=e),type:"date",placeholder:"请选择派单日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),d(N,{gutter:20,class:"form-section"},{default:t(()=>[d(a,{span:24},{default:t(()=>l[15]||(l[15]=[r("div",{class:"section-title"},"订单信息",-1)])),_:1,__:[15]}),d(a,{span:12},{default:t(()=>[d(y,{label:"甲方订单号:"},{default:t(()=>[d(g,{modelValue:c.partyOrderNo,"onUpdate:modelValue":l[1]||(l[1]=e=>c.partyOrderNo=e),placeholder:"请输入甲方订单号"},null,8,["modelValue"])]),_:1})]),_:1}),d(a,{span:12},{default:t(()=>[d(y,{label:"户名:"},{default:t(()=>[d(g,{modelValue:c.customerName,"onUpdate:modelValue":l[2]||(l[2]=e=>c.customerName=e),placeholder:"请输入户名"},null,8,["modelValue"])]),_:1})]),_:1}),d(a,{span:12},{default:t(()=>[d(y,{label:"用户编号:"},{default:t(()=>[d(g,{modelValue:c.userNumber,"onUpdate:modelValue":l[3]||(l[3]=e=>c.userNumber=e),placeholder:"请输入用户编号"},null,8,["modelValue"])]),_:1})]),_:1}),d(a,{span:12},{default:t(()=>[d(y,{label:"小区名称:"},{default:t(()=>[d(g,{modelValue:c.communityName,"onUpdate:modelValue":l[4]||(l[4]=e=>c.communityName=e),placeholder:"请输入小区名称"},null,8,["modelValue"])]),_:1})]),_:1}),d(a,{span:12},{default:t(()=>[d(y,{label:"楼橦:"},{default:t(()=>[d(g,{modelValue:c.building,"onUpdate:modelValue":l[5]||(l[5]=e=>c.building=e),placeholder:"请输入楼橦"},null,8,["modelValue"])]),_:1})]),_:1}),d(a,{span:12},{default:t(()=>[d(y,{label:"房号:"},{default:t(()=>[d(g,{modelValue:c.roomNumber,"onUpdate:modelValue":l[6]||(l[6]=e=>c.roomNumber=e),placeholder:"请输入房号"},null,8,["modelValue"])]),_:1})]),_:1}),d(a,{span:12},{default:t(()=>[d(y,{label:"电话:"},{default:t(()=>[d(g,{modelValue:c.phone,"onUpdate:modelValue":l[7]||(l[7]=e=>c.phone=e),placeholder:"请输入电话"},null,8,["modelValue"])]),_:1})]),_:1}),d(a,{span:12},{default:t(()=>[d(y,{label:"联系人:"},{default:t(()=>[d(g,{modelValue:c.contactPerson,"onUpdate:modelValue":l[8]||(l[8]=e=>c.contactPerson=e),placeholder:"请输入联系人"},null,8,["modelValue"])]),_:1})]),_:1}),d(a,{span:24},{default:t(()=>[d(y,{label:"地址:"},{default:t(()=>[d(g,{modelValue:c.address,"onUpdate:modelValue":l[9]||(l[9]=e=>c.address=e),placeholder:"请输入地址"},null,8,["modelValue"])]),_:1})]),_:1}),d(a,{span:12},{default:t(()=>[d(y,{label:"订单分类:"},{default:t(()=>[d(j,{modelValue:c.orderCategory,"onUpdate:modelValue":l[10]||(l[10]=e=>c.orderCategory=e),placeholder:"请选择订单分类",style:{width:"100%"}},{default:t(()=>[d(U,{label:"一次挂表",value:"firstMeter"}),d(U,{label:"二次挂表",value:"secondMeter"}),d(U,{label:"一次安装",value:"firstInstall"}),d(U,{label:"二次安装",value:"secondInstall"}),d(U,{label:"售后",value:"afterSales"}),d(U,{label:"单项工程",value:"singleProject"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),"singleProject"===c.orderCategory?(s(),n(a,{key:0,span:12},{default:t(()=>[d(y,{label:"单项工程名称:"},{default:t(()=>[d(g,{modelValue:c.projectName,"onUpdate:modelValue":l[11]||(l[11]=e=>c.projectName=e),placeholder:"请输入单项工程名称"},null,8,["modelValue"])]),_:1})]),_:1})):m("",!0),d(a,{span:24},{default:t(()=>[d(y,{label:"备注:"},{default:t(()=>[d(g,{modelValue:c.remarks,"onUpdate:modelValue":l[12]||(l[12]=e=>c.remarks=e),type:"textarea",rows:3,placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),d(N,{gutter:20,class:"form-section"},{default:t(()=>[d(a,{span:24},{default:t(()=>[d(k,{class:"info-card"},{default:t(()=>l[16]||(l[16]=[r("div",{class:"info-title"},"订单分类说明:",-1),r("div",{class:"info-content"},[r("p",null,"1. 一次挂表: 首次水表/电表安装"),r("p",null,"2. 二次挂表: 补充或更换水表/电表"),r("p",null,"3. 一次安装: 首次水电安装"),r("p",null,"4. 二次安装: 补充或改造水电设施"),r("p",null,"5. 售后: 已完成订单的维修服务"),r("p",null,"6. 单项工程: 特定项目的独立工程")],-1)])),_:1,__:[16]})]),_:1})]),_:1}),r("div",f,[d(C,{type:"primary",onClick:i},{default:t(()=>l[17]||(l[17]=[p("保存",-1)])),_:1,__:[17]}),d(C,{type:"success",onClick:V},{default:t(()=>l[18]||(l[18]=[p("提交",-1)])),_:1,__:[18]}),d(C,{onClick:b},{default:t(()=>l[19]||(l[19]=[p("打印",-1)])),_:1,__:[19]}),d(C,{onClick:h},{default:t(()=>l[20]||(l[20]=[p("取消",-1)])),_:1,__:[20]})])]),_:1})])}}}),[["__scopeId","data-v-02d7989b"]]);export{i as default};
