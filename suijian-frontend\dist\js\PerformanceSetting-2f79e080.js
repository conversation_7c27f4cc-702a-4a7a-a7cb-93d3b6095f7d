var e=Object.defineProperty,a=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty,t=Object.prototype.propertyIsEnumerable,o=(a,l,t)=>l in a?e(a,l,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[l]=t,u=(e,u)=>{for(var d in u||(u={}))l.call(u,d)&&o(e,d,u[d]);if(a)for(var d of a(u))t.call(u,d)&&o(e,d,u[d]);return e};import{E as d}from"./element-plus-7917fd46.js";import{l as r,r as s,_ as n,c as i,y as c,R as p,J as m,av as f,x as _,z as v,O as b,I as y,P as g,M as h}from"./vue-vendor-fc5a6493.js";import{_ as C}from"./_plugin-vue_export-helper-1b428a4d.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const V={class:"performance-setting-container"},w={style:{"margin-top":"15px"}},A={class:"dialog-footer"},k={style:{"margin-top":"15px"}},x={class:"dialog-footer"},M={class:"form-actions"},R=C(r({__name:"PerformanceSetting",setup(e){const a=s("product"),l=s([{id:1,productCategory:"智能产品",calculationMethod:"salesAmountRatio",performanceRatio:8,status:"enabled"},{id:2,productCategory:"照明产品",calculationMethod:"salesAmountRatio",performanceRatio:5,status:"enabled"},{id:3,productCategory:"开关插座",calculationMethod:"salesAmountRatio",performanceRatio:6,status:"enabled"},{id:4,productCategory:"电线电缆",calculationMethod:"salesAmountRatio",performanceRatio:4,status:"enabled"}]),t=s([{id:1,orderCategory:"一次挂表",calculationMethod:"fixedAmount",performanceAmount:"50",status:"enabled"},{id:2,orderCategory:"二次挂表",calculationMethod:"fixedAmount",performanceAmount:"40",status:"enabled"},{id:3,orderCategory:"一次安装",calculationMethod:"fixedAmount",performanceAmount:"100",status:"enabled"},{id:4,orderCategory:"二次安装",calculationMethod:"fixedAmount",performanceAmount:"80",status:"enabled"},{id:5,orderCategory:"售后服务",calculationMethod:"fixedAmount",performanceAmount:"60",status:"enabled"},{id:6,orderCategory:"单项工程",calculationMethod:"mixedCalculation",performanceAmount:"基础50+超额10%",status:"enabled"}]),o=s({id:0,productCategory:"",calculationMethod:"salesAmountRatio",performanceRatio:0,status:"enabled"}),r=s({id:0,orderCategory:"",calculationMethod:"fixedAmount",performanceAmount:"",status:"enabled"}),C=n({performanceExplanation:""}),R=s(!1),U=s(!1),j=s(),O=s(),P=i(()=>o.value.id?"修改商品分类绩效":"新增商品分类绩效"),q=i(()=>r.value.id?"修改订单分类绩效":"新增订单分类绩效"),E={productCategory:[{required:!0,message:"请输入商品分类",trigger:"blur"}],calculationMethod:[{required:!0,message:"请选择计算方式",trigger:"blur"}],performanceRatio:[{required:!0,message:"请输入绩效比例",trigger:"blur"}]},$={orderCategory:[{required:!0,message:"请输入订单分类",trigger:"blur"}],calculationMethod:[{required:!0,message:"请选择计算方式",trigger:"blur"}],performanceAmount:[{required:!0,message:"请输入绩效金额",trigger:"blur"}]},F=()=>{d.success(`切换到${"product"===a.value?"商品售卖":"散户订单"}绩效`)},I=()=>{o.value={id:0,productCategory:"",calculationMethod:"salesAmountRatio",performanceRatio:0,status:"enabled"},R.value=!0},S=()=>{j.value&&j.value.validate(e=>{e&&(d.success("保存成功"),R.value=!1)})},z=()=>{j.value&&j.value.resetFields()},J=()=>{d.success("保存商品绩效设置成功")},B=()=>{r.value={id:0,orderCategory:"",calculationMethod:"fixedAmount",performanceAmount:"",status:"enabled"},U.value=!0},D=()=>{O.value&&O.value.validate(e=>{e&&(d.success("保存成功"),U.value=!1)})},G=()=>{O.value&&O.value.resetFields()},H=()=>{d.success("保存订单绩效设置成功")},K=()=>{d.success("保存所有设置成功")},L=()=>{d.success("恢复默认设置")},N=()=>{d.success("导出设置")};return(e,s)=>{const n=f("el-col"),i=f("el-radio-button"),Q=f("el-radio-group"),T=f("el-row"),W=f("el-table-column"),X=f("el-tag"),Y=f("el-button"),Z=f("el-table"),ee=f("el-input"),ae=f("el-form-item"),le=f("el-option"),te=f("el-select"),oe=f("el-input-number"),ue=f("el-radio"),de=f("el-form"),re=f("el-dialog"),se=f("el-card");return _(),c("div",V,[p(se,{class:"main-card"},{header:m(()=>s[14]||(s[14]=[v("div",{class:"card-header"},[v("span",null,"绩效设置")],-1)])),default:m(()=>[p(T,{gutter:20,class:"form-section"},{default:m(()=>[p(n,{span:24},{default:m(()=>s[15]||(s[15]=[v("div",{class:"section-title"},"绩效参数设置",-1)])),_:1,__:[15]}),p(n,{span:24},{default:m(()=>[p(Q,{modelValue:a.value,"onUpdate:modelValue":s[0]||(s[0]=e=>a.value=e),onChange:F},{default:m(()=>[p(i,{label:"product"},{default:m(()=>s[16]||(s[16]=[b("商品售卖绩效",-1)])),_:1,__:[16]}),p(i,{label:"order"},{default:m(()=>s[17]||(s[17]=[b("散户订单绩效",-1)])),_:1,__:[17]})]),_:1},8,["modelValue"])]),_:1})]),_:1}),"product"===a.value?(_(),y(T,{key:0,gutter:20,class:"form-section"},{default:m(()=>[p(n,{span:24},{default:m(()=>s[18]||(s[18]=[v("div",{class:"section-subtitle"},"商品售卖绩效参数",-1)])),_:1,__:[18]}),p(n,{span:24},{default:m(()=>[p(Z,{data:l.value,border:"",class:"performance-table"},{default:m(()=>[p(W,{prop:"productCategory",label:"商品分类","min-width":"120"}),p(W,{prop:"calculationMethod",label:"计算方式","min-width":"120"}),p(W,{prop:"performanceRatio",label:"绩效比例","min-width":"100"},{default:m(e=>[b(g(e.row.performanceRatio)+"% ",1)]),_:1}),p(W,{prop:"status",label:"状态","min-width":"80"},{default:m(e=>[p(X,{type:"enabled"===e.row.status?"success":"info"},{default:m(()=>[b(g("enabled"===e.row.status?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),p(W,{label:"操作","min-width":"120",fixed:"right"},{default:m(e=>[p(Y,{type:"primary",link:"",onClick:a=>{return l=e.row,o.value=u({},l),void(R.value=!0);var l}},{default:m(()=>s[19]||(s[19]=[b("修改",-1)])),_:2,__:[19]},1032,["onClick"]),p(Y,{type:"primary",link:"",onClick:a=>(e=>{const a="enabled"===e.status?"disabled":"enabled";d.success(`已${"enabled"===a?"启用":"禁用"}分类: ${e.productCategory}`),e.status=a})(e.row)},{default:m(()=>[b(g("enabled"===e.row.status?"禁用":"启用"),1)]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),v("div",w,[p(Y,{type:"primary",icon:"Plus",onClick:I},{default:m(()=>s[20]||(s[20]=[b("新增分类",-1)])),_:1,__:[20]}),p(Y,{type:"success",onClick:J},{default:m(()=>s[21]||(s[21]=[b("保存设置",-1)])),_:1,__:[21]})])]),_:1})]),_:1})):h("",!0),p(re,{modelValue:R.value,"onUpdate:modelValue":s[6]||(s[6]=e=>R.value=e),title:P.value,width:"500",onClose:z},{footer:m(()=>[v("div",A,[s[27]||(s[27]=v("p",{class:"explanation"},"说明: 绩效金额 = 销售金额 × 绩效比例",-1)),v("div",null,[p(Y,{onClick:s[5]||(s[5]=e=>R.value=!1)},{default:m(()=>s[25]||(s[25]=[b("取消",-1)])),_:1,__:[25]}),p(Y,{type:"primary",onClick:S},{default:m(()=>s[26]||(s[26]=[b("保存",-1)])),_:1,__:[26]})])])]),default:m(()=>[p(de,{ref_key:"productFormRef",ref:j,model:o.value,rules:E,"label-width":"120px"},{default:m(()=>[p(ae,{label:"商品分类:",prop:"productCategory"},{default:m(()=>[p(ee,{modelValue:o.value.productCategory,"onUpdate:modelValue":s[1]||(s[1]=e=>o.value.productCategory=e),placeholder:"请输入商品分类"},null,8,["modelValue"])]),_:1}),p(ae,{label:"计算方式:",prop:"calculationMethod"},{default:m(()=>[p(te,{modelValue:o.value.calculationMethod,"onUpdate:modelValue":s[2]||(s[2]=e=>o.value.calculationMethod=e),placeholder:"请选择计算方式",style:{width:"100%"}},{default:m(()=>[p(le,{label:"销售金额比例",value:"salesAmountRatio"})]),_:1},8,["modelValue"])]),_:1}),p(ae,{label:"绩效比例:",prop:"performanceRatio"},{default:m(()=>[p(oe,{modelValue:o.value.performanceRatio,"onUpdate:modelValue":s[3]||(s[3]=e=>o.value.performanceRatio=e),min:0,max:100,"controls-position":"right",style:{width:"100%"}},{append:m(()=>s[22]||(s[22]=[b("%",-1)])),_:1},8,["modelValue"])]),_:1}),p(ae,{label:"状态:",prop:"status"},{default:m(()=>[p(Q,{modelValue:o.value.status,"onUpdate:modelValue":s[4]||(s[4]=e=>o.value.status=e)},{default:m(()=>[p(ue,{label:"enabled"},{default:m(()=>s[23]||(s[23]=[b("启用",-1)])),_:1,__:[23]}),p(ue,{label:"disabled"},{default:m(()=>s[24]||(s[24]=[b("禁用",-1)])),_:1,__:[24]})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),"order"===a.value?(_(),y(T,{key:1,gutter:20,class:"form-section"},{default:m(()=>[p(n,{span:24},{default:m(()=>s[28]||(s[28]=[v("div",{class:"section-subtitle"},"散户订单绩效参数",-1)])),_:1,__:[28]}),p(n,{span:24},{default:m(()=>[p(Z,{data:t.value,border:"",class:"performance-table"},{default:m(()=>[p(W,{prop:"orderCategory",label:"订单分类","min-width":"120"}),p(W,{prop:"calculationMethod",label:"计算方式","min-width":"120"}),p(W,{prop:"performanceAmount",label:"绩效金额(元/单)","min-width":"120"}),p(W,{prop:"status",label:"状态","min-width":"80"},{default:m(e=>[p(X,{type:"enabled"===e.row.status?"success":"info"},{default:m(()=>[b(g("enabled"===e.row.status?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),p(W,{label:"操作","min-width":"120",fixed:"right"},{default:m(e=>[p(Y,{type:"primary",link:"",onClick:a=>{return l=e.row,r.value=u({},l),void(U.value=!0);var l}},{default:m(()=>s[29]||(s[29]=[b("修改",-1)])),_:2,__:[29]},1032,["onClick"]),p(Y,{type:"primary",link:"",onClick:a=>(e=>{const a="enabled"===e.status?"disabled":"enabled";d.success(`已${"enabled"===a?"启用":"禁用"}分类: ${e.orderCategory}`),e.status=a})(e.row)},{default:m(()=>[b(g("enabled"===e.row.status?"禁用":"启用"),1)]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),v("div",k,[p(Y,{type:"primary",icon:"Plus",onClick:B},{default:m(()=>s[30]||(s[30]=[b("新增分类",-1)])),_:1,__:[30]}),p(Y,{type:"success",onClick:H},{default:m(()=>s[31]||(s[31]=[b("保存设置",-1)])),_:1,__:[31]})])]),_:1})]),_:1})):h("",!0),p(re,{modelValue:U.value,"onUpdate:modelValue":s[12]||(s[12]=e=>U.value=e),title:q.value,width:"500",onClose:G},{footer:m(()=>[v("div",x,[s[37]||(s[37]=v("div",{class:"explanation"},[v("p",null,"计算方式说明:"),v("p",null,"1. 固定金额: 每单固定绩效金额"),v("p",null,"2. 混合计算: 基础金额+超额部分按比例计算"),v("p",null,"3. 比例计算: 按订单金额的一定比例计算")],-1)),v("div",null,[p(Y,{onClick:s[11]||(s[11]=e=>U.value=!1)},{default:m(()=>s[35]||(s[35]=[b("取消",-1)])),_:1,__:[35]}),p(Y,{type:"primary",onClick:D},{default:m(()=>s[36]||(s[36]=[b("保存",-1)])),_:1,__:[36]})])])]),default:m(()=>[p(de,{ref_key:"orderFormRef",ref:O,model:r.value,rules:$,"label-width":"120px"},{default:m(()=>[p(ae,{label:"订单分类:",prop:"orderCategory"},{default:m(()=>[p(ee,{modelValue:r.value.orderCategory,"onUpdate:modelValue":s[7]||(s[7]=e=>r.value.orderCategory=e),placeholder:"请输入订单分类"},null,8,["modelValue"])]),_:1}),p(ae,{label:"计算方式:",prop:"calculationMethod"},{default:m(()=>[p(te,{modelValue:r.value.calculationMethod,"onUpdate:modelValue":s[8]||(s[8]=e=>r.value.calculationMethod=e),placeholder:"请选择计算方式",style:{width:"100%"}},{default:m(()=>[p(le,{label:"固定金额",value:"fixedAmount"}),p(le,{label:"混合计算",value:"mixedCalculation"}),p(le,{label:"比例计算",value:"ratioCalculation"})]),_:1},8,["modelValue"])]),_:1}),p(ae,{label:"绩效金额:",prop:"performanceAmount"},{default:m(()=>[p(ee,{modelValue:r.value.performanceAmount,"onUpdate:modelValue":s[9]||(s[9]=e=>r.value.performanceAmount=e),placeholder:"请输入绩效金额"},{append:m(()=>s[32]||(s[32]=[b("元/单",-1)])),_:1},8,["modelValue"])]),_:1}),p(ae,{label:"状态:",prop:"status"},{default:m(()=>[p(Q,{modelValue:r.value.status,"onUpdate:modelValue":s[10]||(s[10]=e=>r.value.status=e)},{default:m(()=>[p(ue,{label:"enabled"},{default:m(()=>s[33]||(s[33]=[b("启用",-1)])),_:1,__:[33]}),p(ue,{label:"disabled"},{default:m(()=>s[34]||(s[34]=[b("禁用",-1)])),_:1,__:[34]})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),p(T,{gutter:20,class:"form-section"},{default:m(()=>[p(n,{span:24},{default:m(()=>s[38]||(s[38]=[v("div",{class:"section-title"},"绩效统计",-1)])),_:1,__:[38]}),p(n,{span:24},{default:m(()=>[p(se,{class:"statistics-card"},{default:m(()=>[p(T,{gutter:20},{default:m(()=>[p(n,{span:12},{default:m(()=>s[39]||(s[39]=[v("div",{class:"statistic-item"},[v("span",{class:"label"},"本月商品销售绩效总额:"),v("span",null,"¥12,580")],-1)])),_:1,__:[39]}),p(n,{span:12},{default:m(()=>s[40]||(s[40]=[v("div",{class:"statistic-item"},[v("span",{class:"label"},"本月订单完成绩效总额:"),v("span",null,"¥8,450")],-1)])),_:1,__:[40]}),p(n,{span:12},{default:m(()=>s[41]||(s[41]=[v("div",{class:"statistic-item"},[v("span",{class:"label"},"绩效支出合计:"),v("span",null,"¥21,030")],-1)])),_:1,__:[41]}),p(n,{span:12},{default:m(()=>s[42]||(s[42]=[v("div",{class:"statistic-item"},[v("span",{class:"label"},"占总人工成本比例:"),v("span",null,"15.2%")],-1)])),_:1,__:[42]})]),_:1})]),_:1})]),_:1})]),_:1}),p(T,{gutter:20,class:"form-section"},{default:m(()=>[p(n,{span:24},{default:m(()=>s[43]||(s[43]=[v("div",{class:"section-title"},"备注信息",-1)])),_:1,__:[43]}),p(n,{span:24},{default:m(()=>[p(ae,{label:"绩效说明:"},{default:m(()=>[p(ee,{modelValue:C.performanceExplanation,"onUpdate:modelValue":s[13]||(s[13]=e=>C.performanceExplanation=e),type:"textarea",rows:3,placeholder:"请输入绩效说明"},null,8,["modelValue"])]),_:1})]),_:1}),p(n,{span:24},{default:m(()=>[p(ae,{label:"调整记录:"},{default:m(()=>[p(se,{class:"record-card"},{default:m(()=>s[44]||(s[44]=[v("div",{class:"record-item"},"2024-01-01 调整智能产品绩效比例从7%至8%",-1),v("div",{class:"record-item"},"2023-12-15 新增单项工程混合计算方式",-1),v("div",{class:"record-item"},"2023-11-20 调整一次安装绩效从90元至100元",-1)])),_:1,__:[44]})]),_:1})]),_:1})]),_:1}),v("div",M,[p(Y,{type:"success",onClick:K},{default:m(()=>s[45]||(s[45]=[b("保存设置",-1)])),_:1,__:[45]}),p(Y,{onClick:L},{default:m(()=>s[46]||(s[46]=[b("恢复默认",-1)])),_:1,__:[46]}),p(Y,{onClick:N},{default:m(()=>s[47]||(s[47]=[b("导出设置",-1)])),_:1,__:[47]})])]),_:1})])}}}),[["__scopeId","data-v-0f5e6c8c"]]);export{R as default};
