<template>
  <div class="material-inbound">
    <!-- 面包屑导航 -->
    <el-breadcrumb class="breadcrumb" separator=">">
      <el-breadcrumb-item :to="{ path: '/dashboard' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item :to="{ path: '/warehouse/material-list' }">仓库管理</el-breadcrumb-item>
      <el-breadcrumb-item>甲料入库</el-breadcrumb-item>
    </el-breadcrumb>

    <el-card class="main-card">
      <template #header>
        <div class="card-header">
          <span>📦 甲料入库</span>
        </div>
      </template>

      <el-form :model="formData" :rules="formRules" ref="formRef" label-width="120px">
        <!-- 基本信息 -->
        <el-row :gutter="20" class="form-section">
          <el-col :span="24">
            <div class="section-title">基本信息</div>
          </el-col>
          <el-col :span="12">
            <el-form-item label="入库日期:" prop="inboundDate">
              <el-date-picker
                v-model="formData.inboundDate"
                type="date"
                placeholder="请选择入库日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="操作员:" prop="operator">
              <el-input v-model="formData.operator" placeholder="请输入操作员" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 物料信息 -->
        <el-row :gutter="20" class="form-section">
          <el-col :span="24">
            <div class="section-title">物料信息</div>
          </el-col>
          <el-col :span="12">
            <el-form-item label="物料编码:" prop="materialCode">
              <el-input v-model="formData.materialCode" placeholder="请输入物料编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="物料名称:" prop="materialName">
              <el-input v-model="formData.materialName" placeholder="请输入物料名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="规格:" prop="specification">
              <el-input v-model="formData.specification" placeholder="请输入规格" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单位:" prop="unit">
              <el-input v-model="formData.unit" placeholder="请输入单位" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="入库数量:" prop="quantity">
              <el-input-number
                v-model="formData.quantity"
                :min="0"
                :step="1"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单价:" prop="unitPrice">
              <el-input-number
                v-model="formData.unitPrice"
                :min="0"
                :step="0.01"
                :precision="2"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注:">
              <el-input
                v-model="formData.remarks"
                type="textarea"
                :rows="3"
                placeholder="请输入备注"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <el-button type="primary" @click="handleSubmit">提交</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button @click="handleCancel">取消</el-button>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'

// 表单引用
const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive({
  inboundDate: new Date().toISOString().split('T')[0],
  operator: '',
  materialCode: '',
  materialName: '',
  specification: '',
  unit: '',
  quantity: 0,
  unitPrice: 0,
  remarks: ''
})

// 表单验证规则
const formRules: FormRules = {
  inboundDate: [
    { required: true, message: '请选择入库日期', trigger: 'change' }
  ],
  operator: [
    { required: true, message: '请输入操作员', trigger: 'blur' }
  ],
  materialCode: [
    { required: true, message: '请输入物料编码', trigger: 'blur' }
  ],
  materialName: [
    { required: true, message: '请输入物料名称', trigger: 'blur' }
  ],
  specification: [
    { required: true, message: '请输入规格', trigger: 'blur' }
  ],
  unit: [
    { required: true, message: '请输入单位', trigger: 'blur' }
  ],
  quantity: [
    { required: true, message: '请输入入库数量', trigger: 'blur' }
  ],
  unitPrice: [
    { required: true, message: '请输入单价', trigger: 'blur' }
  ]
}

// 提交
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    ElMessage.success('入库成功')
    console.log('入库数据:', formData)
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单填写是否正确')
  }
}

// 重置
const handleReset = () => {
  formRef.value?.resetFields()
}

// 取消
const handleCancel = () => {
  ElMessage.info('已取消')
}
</script>

<style scoped lang="scss">
.material-inbound {
  padding: 20px;
  
  .breadcrumb {
    margin-bottom: 20px;
  }
  
  .main-card {
    .card-header {
      font-size: 18px;
      font-weight: bold;
      color: #303133;
    }
  }
  
  .form-section {
    margin-bottom: 20px;
    
    .section-title {
      font-size: 16px;
      font-weight: bold;
      color: #606266;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ebeef5;
    }
  }
  
  .form-actions {
    text-align: center;
    padding: 20px 0;
    
    .el-button {
      margin: 0 10px;
      padding: 12px 30px;
    }
  }
}
</style>
