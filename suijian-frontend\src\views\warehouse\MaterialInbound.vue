<template>
  <div class="material-inbound">
    <el-card class="main-card">
      <el-form :model="formData" :rules="formRules" ref="formRef" label-width="120px">
        <!-- 物料信息 -->
        <el-row :gutter="20" class="form-section">
          <el-col :span="24">
            <div class="section-title">物料信息</div>
          </el-col>
          <el-col :span="12">
            <el-form-item label="物料编码:" prop="materialCode">
              <el-input
                v-model="formData.materialCode"
                placeholder="请点击选择物料"
                readonly
                @click="openMaterialDialog"
                style="cursor: pointer;"
              >
                <template #suffix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="物料名称:" prop="materialName">
              <el-input v-model="formData.materialName" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="甲料编码:" prop="materialCodeA">
              <el-input v-model="formData.materialCodeA" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="型号:" prop="model">
              <el-input v-model="formData.model" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="规格:" prop="specification">
              <el-input v-model="formData.specification" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单位:" prop="unit">
              <el-input v-model="formData.unit" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="入库数量:" prop="quantity">
              <el-input-number
                v-model="formData.quantity"
                :min="0"
                :step="1"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注:">
              <el-input
                v-model="formData.remarks"
                type="textarea"
                :rows="3"
                placeholder="请输入备注"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <el-button type="primary" @click="handleSubmit">提交</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button @click="handleCancel">取消</el-button>
        </div>
      </el-form>
    </el-card>

    <!-- 物料选择弹窗 -->
    <el-dialog v-model="materialDialogVisible" title="选择物料" width="800px">
      <div class="material-search">
        <el-input
          v-model="materialSearch"
          placeholder="请输入物料名称或编码搜索"
          style="width: 300px; margin-bottom: 20px;"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>

      <el-table
        :data="filteredMaterialList"
        border
        style="width: 100%"
        @row-click="selectMaterial"
        highlight-current-row
      >
        <el-table-column prop="materialCode" label="物料编码" width="120" />
        <el-table-column prop="materialName" label="物料名称" width="150" />
        <el-table-column prop="materialCodeA" label="甲料编码" width="120" />
        <el-table-column prop="model" label="型号" width="100" />
        <el-table-column prop="specification" label="规格" width="120" />
        <el-table-column prop="unit" label="单位" width="80" />
        <el-table-column label="操作" width="80">
          <template #default="scope">
            <el-button type="primary" link @click="selectMaterial(scope.row)">选择</el-button>
          </template>
        </el-table-column>
      </el-table>

      <template #footer>
        <el-button @click="materialDialogVisible = false">取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { Search } from '@element-plus/icons-vue'

// 表单引用
const formRef = ref<FormInstance>()

// 物料弹窗控制
const materialDialogVisible = ref(false)
const materialSearch = ref('')

// 表单数据
const formData = reactive({
  materialCode: '',
  materialName: '',
  materialCodeA: '',
  model: '',
  specification: '',
  unit: '',
  quantity: 0,
  remarks: ''
})

// 物料列表数据
const materialList = ref([
  {
    id: 1,
    materialCode: 'WL001',
    materialName: '电缆线',
    materialCodeA: 'JL001',
    model: 'YJV-3*4',
    specification: '3*4mm²',
    unit: '米'
  },
  {
    id: 2,
    materialCode: 'WL002',
    materialName: '开关面板',
    materialCodeA: 'JL002',
    model: 'KP-86',
    specification: '86型',
    unit: '个'
  },
  {
    id: 3,
    materialCode: 'WL003',
    materialName: '插座',
    materialCodeA: 'JL003',
    model: 'ZP-86',
    specification: '86型',
    unit: '个'
  },
  {
    id: 4,
    materialCode: 'WL004',
    materialName: '灯具',
    materialCodeA: 'JL004',
    model: 'LED-12W',
    specification: '12W',
    unit: '个'
  },
  {
    id: 5,
    materialCode: 'WL005',
    materialName: '配电箱',
    materialCodeA: 'JL005',
    model: 'PX-400',
    specification: '400A',
    unit: '台'
  }
])

// 过滤后的物料列表
const filteredMaterialList = computed(() => {
  if (!materialSearch.value) {
    return materialList.value
  }
  return materialList.value.filter(item =>
    item.materialName.includes(materialSearch.value) ||
    item.materialCode.includes(materialSearch.value) ||
    item.materialCodeA.includes(materialSearch.value)
  )
})

// 表单验证规则
const formRules: FormRules = {
  materialCode: [
    { required: true, message: '请选择物料', trigger: 'blur' }
  ],
  quantity: [
    { required: true, message: '请输入入库数量', trigger: 'blur' }
  ]
}

// 打开物料选择弹窗
const openMaterialDialog = () => {
  materialDialogVisible.value = true
  materialSearch.value = ''
}

// 选择物料
const selectMaterial = (material: any) => {
  formData.materialCode = material.materialCode
  formData.materialName = material.materialName
  formData.materialCodeA = material.materialCodeA
  formData.model = material.model
  formData.specification = material.specification
  formData.unit = material.unit
  materialDialogVisible.value = false
  ElMessage.success('物料选择成功')
}

// 提交
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    ElMessage.success('入库成功')
    console.log('入库数据:', formData)
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单填写是否正确')
  }
}

// 重置
const handleReset = () => {
  formRef.value?.resetFields()
  // 清空物料相关字段
  formData.materialCode = ''
  formData.materialName = ''
  formData.materialCodeA = ''
  formData.model = ''
  formData.specification = ''
  formData.unit = ''
}

// 取消
const handleCancel = () => {
  ElMessage.info('已取消')
}
</script>

<style scoped lang="scss">
.material-inbound {
  padding: 20px;
  
  .breadcrumb {
    margin-bottom: 20px;
  }
  
  .main-card {
    .card-header {
      font-size: 18px;
      font-weight: bold;
      color: #303133;
    }
  }
  
  .form-section {
    margin-bottom: 20px;
    
    .section-title {
      font-size: 16px;
      font-weight: bold;
      color: #606266;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ebeef5;
    }
  }
  
  .form-actions {
    text-align: center;
    padding: 20px 0;
    
    .el-button {
      margin: 0 10px;
      padding: 12px 30px;
    }
  }
}
</style>
