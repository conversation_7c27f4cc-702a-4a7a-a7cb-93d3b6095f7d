<template>
  <div class="stock-warning-container">
    <el-card class="main-card">
      <template #header>
        <div class="card-header">
          <span>库存预警</span>
        </div>
      </template>
      
      <!-- 预警设置 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">预警设置</div>
        </el-col>
        <el-col :span="12">
          <el-form-item label="预警方式:">
            <el-checkbox-group v-model="warningSettings.warningMethods">
              <el-checkbox label="system">系统消息</el-checkbox>
              <el-checkbox label="email">邮件通知</el-checkbox>
              <el-checkbox label="sms">短信通知</el-checkbox>
              <el-checkbox label="wechat">微信通知</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="预警频率:">
            <el-select v-model="warningSettings.warningFrequency" placeholder="请选择预警频率" style="width: 100%">
              <el-option label="实时预警" value="realtime" />
              <el-option label="每日汇总" value="daily" />
              <el-option label="每周汇总" value="weekly" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="库存不足预警:">
            <el-input v-model="warningSettings.lowStockThreshold" placeholder="请输入库存不足预警阈值">
              <template #append>件</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="库存积压预警:">
            <el-input v-model="warningSettings.overstockThreshold" placeholder="请输入库存积压预警阈值">
              <template #append>件</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="接收人员:">
            <el-select 
              v-model="warningSettings.recipients" 
              multiple 
              placeholder="请选择接收人员" 
              style="width: 100%"
            >
              <el-option
                v-for="user in userList"
                :key="user.id"
                :label="user.name"
                :value="user.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 预警规则 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">预警规则</div>
        </el-col>
        <el-col :span="24">
          <el-table :data="warningRules" border class="warning-rules-table">
            <el-table-column prop="materialCode" label="公司物料编码" min-width="120" />
            <el-table-column prop="materialName" label="物料名称" min-width="120" />
            <el-table-column prop="model" label="型号" min-width="100" />
            <el-table-column prop="specification" label="规格" min-width="100" />
            <el-table-column prop="unit" label="单位" min-width="80" />
            <el-table-column prop="currentStock" label="当前库存" min-width="100" />
            <el-table-column prop="warningType" label="预警类型" min-width="100">
              <template #default="scope">
                <el-tag :type="getWarningTypeTag(scope.row.warningType)">
                  {{ getWarningTypeText(scope.row.warningType) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="warningThreshold" label="预警阈值" min-width="100" />
            <el-table-column label="操作" min-width="150" fixed="right">
              <template #default="scope">
                <el-button type="primary" link @click="modifyRule(scope.row)">修改</el-button>
                <el-button type="primary" link @click="disableRule(scope.row)">
                  {{ scope.row.status === 'enabled' ? '禁用' : '启用' }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      
      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        class="pagination"
      />
      
      <!-- 操作按钮 -->
      <div class="form-actions">
        <el-button type="primary" icon="Plus" @click="addRule">新增规则</el-button>
        <el-button icon="Setting" @click="batchSetting">批量设置</el-button>
        <el-button icon="Refresh" @click="refreshWarning">刷新预警</el-button>
        <el-button icon="DataAnalysis" @click="showWarningHistory">预警历史</el-button>
      </div>
      
      <!-- 新增/修改规则弹窗 -->
      <el-dialog 
        v-model="ruleDialogVisible" 
        :title="ruleDialogTitle" 
        width="600"
        @close="handleRuleDialogClose"
      >
        <el-form 
          ref="ruleFormRef" 
          :model="currentRule" 
          :rules="ruleRules" 
          label-width="120px"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="公司物料编码:" prop="materialCode">
                <el-input v-model="currentRule.materialCode" placeholder="请输入公司物料编码" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="物料名称:" prop="materialName">
                <el-input v-model="currentRule.materialName" placeholder="请输入物料名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="型号:" prop="model">
                <el-input v-model="currentRule.model" placeholder="请输入型号" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="规格:" prop="specification">
                <el-input v-model="currentRule.specification" placeholder="请输入规格" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="单位:" prop="unit">
                <el-input v-model="currentRule.unit" placeholder="请输入单位" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="当前库存:" prop="currentStock">
                <el-input-number 
                  v-model="currentRule.currentStock" 
                  :min="0" 
                  controls-position="right" 
                  style="width: 100%" 
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="预警类型:" prop="warningType">
                <el-select v-model="currentRule.warningType" placeholder="请选择预警类型" style="width: 100%">
                  <el-option label="库存不足" value="lowStock" />
                  <el-option label="库存积压" value="overstock" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="预警阈值:" prop="warningThreshold">
                <el-input v-model="currentRule.warningThreshold" placeholder="请输入预警阈值">
                  <template #append>件</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="状态:" prop="status">
                <el-radio-group v-model="currentRule.status">
                  <el-radio label="enabled">启用</el-radio>
                  <el-radio label="disabled">禁用</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="备注:">
                <el-input
                  v-model="currentRule.remarks"
                  type="textarea"
                  :rows="2"
                  placeholder="请输入备注"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template #footer>
          <el-button @click="ruleDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveRule">保存</el-button>
        </template>
      </el-dialog>
      
      <!-- 预警历史弹窗 -->
      <el-dialog v-model="historyDialogVisible" title="预警历史" width="800">
        <el-row :gutter="20" class="history-search">
          <el-col :span="8">
            <el-date-picker
              v-model="historySearch.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </el-col>
          <el-col :span="8">
            <el-select v-model="historySearch.warningType" placeholder="预警类型" clearable style="width: 100%">
              <el-option label="库存不足" value="lowStock" />
              <el-option label="库存积压" value="overstock" />
            </el-select>
          </el-col>
          <el-col :span="8">
            <div class="search-buttons">
              <el-button type="primary" icon="Search" @click="searchHistory">搜索</el-button>
              <el-button icon="Refresh" @click="resetHistorySearch">重置</el-button>
            </div>
          </el-col>
        </el-row>
        
        <el-table :data="warningHistory" border class="history-table" style="margin-top: 20px;">
          <el-table-column prop="warningTime" label="预警时间" min-width="150" />
          <el-table-column prop="materialCode" label="公司物料编码" min-width="120" />
          <el-table-column prop="materialName" label="物料名称" min-width="120" />
          <el-table-column prop="warningType" label="预警类型" min-width="100">
            <template #default="scope">
              <el-tag :type="getWarningTypeTag(scope.row.warningType)">
                {{ getWarningTypeText(scope.row.warningType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="currentStock" label="当前库存" min-width="100" />
          <el-table-column prop="warningThreshold" label="预警阈值" min-width="100" />
          <el-table-column prop="operator" label="处理人" min-width="100" />
          <el-table-column prop="status" label="处理状态" min-width="100">
            <template #default="scope">
              <el-tag :type="scope.row.status === 'handled' ? 'success' : 'warning'">
                {{ scope.row.status === 'handled' ? '已处理' : '未处理' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
        
        <el-pagination
          v-model:current-page="historyPagination.currentPage"
          v-model:page-size="historyPagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="historyPagination.total"
          @size-change="handleHistorySizeChange"
          @current-change="handleHistoryCurrentChange"
          class="pagination"
          style="margin-top: 20px; text-align: right;"
        />
        
        <template #footer>
          <el-button @click="historyDialogVisible = false">关闭</el-button>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'

// 预警设置
const warningSettings = reactive({
  warningMethods: ['system', 'email'],
  warningFrequency: 'realtime',
  lowStockThreshold: '10',
  overstockThreshold: '1000',
  recipients: [1, 2]
})

// 用户列表
const userList = ref([
  { id: 1, name: '张三' },
  { id: 2, name: '李四' },
  { id: 3, name: '王五' },
  { id: 4, name: '赵六' }
])

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 25
})

// 预警规则列表
const warningRules = ref([
  {
    id: 1,
    materialCode: 'WL001',
    materialName: '电缆线',
    model: 'YJV-3*4',
    specification: '3*4mm²',
    unit: '米',
    currentStock: 5,
    warningType: 'lowStock',
    warningThreshold: '10',
    status: 'enabled'
  },
  {
    id: 2,
    materialCode: 'WL002',
    materialName: '开关面板',
    model: 'KP-86',
    specification: '86型',
    unit: '个',
    currentStock: 1200,
    warningType: 'overstock',
    warningThreshold: '1000',
    status: 'enabled'
  },
  {
    id: 3,
    materialCode: 'WL003',
    materialName: '电线',
    model: 'BV-2.5',
    specification: '2.5mm²',
    unit: '米',
    currentStock: 8,
    warningType: 'lowStock',
    warningThreshold: '10',
    status: 'enabled'
  },
  {
    id: 4,
    materialCode: 'WL004',
    materialName: '水管',
    model: 'PPR-20',
    specification: '20mm',
    unit: '米',
    currentStock: 1500,
    warningType: 'overstock',
    warningThreshold: '1000',
    status: 'disabled'
  },
  {
    id: 5,
    materialCode: 'WL005',
    materialName: '灯具',
    model: 'LED-12W',
    specification: '12W',
    unit: '个',
    currentStock: 3,
    warningType: 'lowStock',
    warningThreshold: '10',
    status: 'enabled'
  }
])

// 当前规则
const currentRule = ref({
  id: 0,
  materialCode: '',
  materialName: '',
  model: '',
  specification: '',
  unit: '',
  currentStock: 0,
  warningType: 'lowStock',
  warningThreshold: '',
  status: 'enabled',
  remarks: ''
})

// 历史搜索条件
const historySearch = reactive({
  dateRange: ['', ''],
  warningType: ''
})

// 历史分页
const historyPagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 126
})

// 预警历史列表
const warningHistory = ref([
  {
    id: 1,
    warningTime: '2024-01-15 10:30:25',
    materialCode: 'WL001',
    materialName: '电缆线',
    warningType: 'lowStock',
    currentStock: 5,
    warningThreshold: '10',
    operator: '系统',
    status: 'handled'
  },
  {
    id: 2,
    warningTime: '2024-01-15 09:15:42',
    materialCode: 'WL003',
    materialName: '电线',
    warningType: 'lowStock',
    currentStock: 8,
    warningThreshold: '10',
    operator: '系统',
    status: 'handled'
  },
  {
    id: 3,
    warningTime: '2024-01-14 16:22:18',
    materialCode: 'WL002',
    materialName: '开关面板',
    warningType: 'overstock',
    currentStock: 1200,
    warningThreshold: '1000',
    operator: '系统',
    status: 'handled'
  },
  {
    id: 4,
    warningTime: '2024-01-14 14:05:33',
    materialCode: 'WL005',
    materialName: '灯具',
    warningType: 'lowStock',
    currentStock: 3,
    warningThreshold: '10',
    operator: '系统',
    status: 'unhandled'
  }
])

// 弹窗控制
const ruleDialogVisible = ref(false)
const historyDialogVisible = ref(false)

// 表单引用
const ruleFormRef = ref()

// 对话框标题
const ruleDialogTitle = computed(() => {
  return currentRule.value.id ? '修改预警规则' : '新增预警规则'
})

// 表单验证规则
const ruleRules = {
  materialCode: [
    { required: true, message: '请输入公司物料编码', trigger: 'blur' }
  ],
  materialName: [
    { required: true, message: '请输入物料名称', trigger: 'blur' }
  ],
  unit: [
    { required: true, message: '请输入单位', trigger: 'blur' }
  ],
  warningType: [
    { required: true, message: '请选择预警类型', trigger: 'blur' }
  ],
  warningThreshold: [
    { required: true, message: '请输入预警阈值', trigger: 'blur' }
  ]
}

// 获取预警类型标签
const getWarningTypeTag = (type: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  switch (type) {
    case 'lowStock':
      return 'warning'
    case 'overstock':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取预警类型文本
const getWarningTypeText = (type: string) => {
  switch (type) {
    case 'lowStock':
      return '库存不足'
    case 'overstock':
      return '库存积压'
    default:
      return ''
  }
}

// 分页变化
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  console.log('每页条数:', val)
}

const handleCurrentChange = (val: number) => {
  pagination.currentPage = val
  console.log('当前页:', val)
}

// 新增规则
const addRule = () => {
  // 重置表单
  currentRule.value = {
    id: 0,
    materialCode: '',
    materialName: '',
    model: '',
    specification: '',
    unit: '',
    currentStock: 0,
    warningType: 'lowStock',
    warningThreshold: '',
    status: 'enabled',
    remarks: ''
  }
  ruleDialogVisible.value = true
}

// 修改规则
const modifyRule = (row: any) => {
  currentRule.value = { ...row }
  ruleDialogVisible.value = true
}

// 禁用/启用规则
const disableRule = (row: any) => {
  const newStatus = row.status === 'enabled' ? 'disabled' : 'enabled'
  ElMessage.success(`已${newStatus === 'enabled' ? '启用' : '禁用'}规则`)
  row.status = newStatus
}

// 保存规则
const saveRule = () => {
  if (!ruleFormRef.value) return
  
  ruleFormRef.value.validate((valid: boolean) => {
    if (valid) {
      ElMessage.success('保存成功')
      ruleDialogVisible.value = false
      console.log('保存规则:', currentRule.value)
    }
  })
}

// 对话框关闭
const handleRuleDialogClose = () => {
  if (ruleFormRef.value) {
    ruleFormRef.value.resetFields()
  }
}

// 批量设置
const batchSetting = () => {
  ElMessage.success('批量设置')
}

// 刷新预警
const refreshWarning = () => {
  ElMessage.success('刷新预警')
}

// 显示预警历史
const showWarningHistory = () => {
  historyDialogVisible.value = true
}

// 搜索历史
const searchHistory = () => {
  ElMessage.success('搜索历史')
  console.log('搜索条件:', historySearch)
}

// 重置历史搜索
const resetHistorySearch = () => {
  historySearch.dateRange = ['', '']
  historySearch.warningType = ''
}

// 历史分页变化
const handleHistorySizeChange = (val: number) => {
  historyPagination.pageSize = val
  console.log('历史每页条数:', val)
}

const handleHistoryCurrentChange = (val: number) => {
  historyPagination.currentPage = val
  console.log('历史当前页:', val)
}
</script>

<style lang="scss" scoped>
.stock-warning-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
  
  .main-card {
    margin-bottom: 20px;
    
    .card-header {
      font-size: 18px;
      font-weight: bold;
      color: #303133;
    }
  }
  
  .form-section {
    margin-bottom: 20px;
    
    .section-title {
      font-size: 16px;
      font-weight: bold;
      color: #606266;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ebeef5;
    }
    
    .el-form-item {
      margin-bottom: 18px;
    }
  }
  
  .warning-rules-table {
    margin-top: 10px;
  }
  
  .pagination {
    margin-top: 20px;
    text-align: right;
  }
  
  .form-actions {
    margin-top: 20px;
    text-align: left;
    
    .el-button {
      margin-right: 10px;
    }
  }
  
  .history-search {
    .search-buttons {
      display: flex;
      
      .el-button {
        margin-left: 10px;
      }
    }
  }
  
  .history-table {
    margin-top: 20px;
  }
}
</style>