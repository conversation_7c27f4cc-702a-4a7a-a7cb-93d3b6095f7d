<template>
  <div class="page-container">
    <div class="search-form">
      <el-form :model="searchForm" inline>
        <el-form-item label="关键字">
          <el-input
            v-model="searchForm.keyword"
            placeholder="请输入物料编码、名称、型号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="分类">
          <el-select v-model="searchForm.category" placeholder="请选择分类" clearable>
            <el-option label="甲料" value="甲料" />
            <el-option label="商品" value="商品" />
            <el-option label="辅料" value="辅料" />
          </el-select>
        </el-form-item>
        <el-form-item label="标签">
          <el-select v-model="searchForm.tags" placeholder="请选择标签" clearable multiple>
            <el-option label="管材" value="管材" />
            <el-option label="赠品" value="赠品" />
            <el-option label="工具" value="工具" />
            <el-option label="耗材" value="耗材" />
            <el-option label="设备" value="设备" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="正常" value="正常" />
            <el-option label="需进仓" value="需进仓" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="action-buttons">
      <el-button type="primary" @click="handleAdd">新增物料</el-button>
      <el-button type="warning" @click="handleExport">导出Excel</el-button>
    </div>

    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        stripe
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="companyCode" label="公司物料编码" width="150" />
        <el-table-column label="甲方编码" width="120">
          <template #default="{ row }">
            <el-button type="text" @click="showClientCodes(row)">
              {{ getDisplayClientCode(row.clientCodes) }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="标签" width="150">
          <template #default="{ row }">
            <el-tag
              v-for="tag in row.tags"
              :key="tag"
              size="small"
              style="margin-right: 4px; margin-bottom: 4px;"
            >
              {{ tag }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="category" label="分类" width="100">
          <template #default="{ row }">
            <el-tag :type="getCategoryType(row.category)">{{ row.category }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="名称" width="150" />
        <el-table-column prop="model" label="型号" width="120" />
        <el-table-column prop="specification" label="规格" width="150" />
        <el-table-column prop="unit" label="单位" width="80" />
        <el-table-column prop="quantity" label="数量" width="100">
          <template #default="{ row }">
            <span :class="{ 'text-danger': row.quantity <= 10 }">
              {{ row.quantity }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.status === '正常' ? 'success' : 'warning'">
              {{ row.status === '正常' ? '正常' : '需进仓' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
            <el-button type="success" size="small" @click="handleAddSpecification(row)">新增物料</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="700px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="公司物料编码" prop="companyCode">
              <el-input 
                v-model="form.companyCode" 
                :placeholder="form.id ? '请输入公司物料编码' : '后台自动生成'"
                :disabled="!form.id"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="物料名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入物料名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="甲方编码" prop="clientCodes">
          <el-input
            v-model="form.clientCodes"
            placeholder="请输入甲方编码"
            style="width: 100%"
          />
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="型号" prop="model">
              <el-input v-model="form.model" placeholder="请输入型号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="规格" prop="specification">
              <el-input v-model="form.specification" placeholder="请输入规格" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="单位" prop="unit">
              <el-input v-model="form.unit" placeholder="请输入单位" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="分类" prop="category">
              <el-select v-model="form.category" placeholder="请选择分类">
                <el-option label="甲料" value="甲料" />
                <el-option label="商品" value="商品" />
                <el-option label="辅料" value="辅料" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="标签" prop="tags">
          <el-select v-model="form.tags" placeholder="请选择标签" multiple>
            <el-option label="管材" value="管材" />
            <el-option label="赠品" value="赠品" />
            <el-option label="工具" value="工具" />
            <el-option label="耗材" value="耗材" />
            <el-option label="设备" value="设备" />
          </el-select>
        </el-form-item>

      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>

    <!-- 甲方编码详情对话框 -->
    <el-dialog v-model="clientCodesVisible" title="甲方编码列表" width="600px">
      <el-table :data="currentClientCodes" border>
        <el-table-column prop="code" label="甲方编码" width="150" />
        <el-table-column prop="name" label="甲方名称" width="200" />
        <el-table-column prop="quantity" label="数量" width="100">
          <template #default="{ row }">
            <span :class="{ 'text-danger': row.quantity <= 10 }">
              {{ row.quantity }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="unit" label="单位" width="80" />
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import request from '@/utils/request'

// 搜索表单
const searchForm = reactive({
  keyword: '',
  category: '',
  tags: [],
  status: ''
})

// 表格数据
const tableData = ref([])
const loading = ref(false)

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 对话框
const dialogVisible = ref(false)
const dialogTitle = ref('')
const clientCodesVisible = ref(false)
const currentClientCodes = ref([])
const formRef = ref()

// 表单数据
const form = reactive({
  id: '',
  companyCode: '',
  clientCodes: '',
  name: '',
  model: '',
  specification: '',
  unit: '',
  category: '',
  tags: []
})

// 表单验证规则
const rules = {
  companyCode: [{ required: false, message: '公司物料编码由后台自动生成', trigger: 'blur' }],
  name: [{ required: true, message: '请输入物料名称', trigger: 'blur' }],
  category: [{ required: true, message: '请选择分类', trigger: 'change' }],
  unit: [{ required: true, message: '请输入单位', trigger: 'blur' }],
  clientCodes: [{ required: false, message: '请输入甲方编码', trigger: 'blur' }]
}

// 获取分类标签类型
const getCategoryType = (category: string) => {
  const types = {
    '甲料': 'primary',
    '商品': 'success',
    '辅料': 'warning'
  }
  return types[category] || 'info'
}

// 获取显示的甲方编码
const getDisplayClientCode = (clientCodes: any[]) => {
  if (!clientCodes || clientCodes.length === 0) {
    return '无'
  }
  if (clientCodes.length === 1) {
    return clientCodes[0].code
  }
  return `${clientCodes[0].code}...`
}

// 显示甲方编码
const showClientCodes = (row: any) => {
  currentClientCodes.value = row.clientCodes.map((item: any) => ({
    code: item.code,
    name: item.name,
    quantity: item.quantity,
    unit: item.unit
  }))
  clientCodesVisible.value = true
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  loadData()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    category: '',
    tags: [],
    status: ''
  })
  handleSearch()
}

// 新增
const handleAdd = () => {
  dialogTitle.value = '新增物料'
  Object.assign(form, {
    id: '',
    companyCode: '',
    clientCodes: '',
    name: '',
    model: '',
    specification: '',
    unit: '',
    category: '',
    tags: []
  })
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row: any) => {
  dialogTitle.value = '编辑物料'
  // 处理甲方编码数据，将对象数组转换为字符串
  const clientCodesStr = row.clientCodes && row.clientCodes.length > 0 
    ? row.clientCodes.map((item: any) => item.code).join(',')
    : ''
  
  Object.assign(form, {
    ...row,
    clientCodes: clientCodesStr
  })
  dialogVisible.value = true
}

// 增加不同型号规格
const handleAddSpecification = (row: any) => {
  dialogTitle.value = '新增物料'
  Object.assign(form, {
    id: '',
    companyCode: '',
    clientCodes: '',
    name: row.name, // 自动填写相同的名称
    model: '',
    specification: '',
    unit: row.unit, // 自动填写相同的单位
    category: row.category, // 自动填写相同的分类
    tags: [...row.tags] // 自动填写相同的标签
  })
  dialogVisible.value = true
}



// 导出
const handleExport = () => {
  ElMessage.info('导出功能待实现')
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    // 实现提交逻辑
    ElMessage.success(form.id ? '更新成功' : '新增成功')
    dialogVisible.value = false
    loadData()
  } catch (error) {
    ElMessage.error('表单验证失败')
  }
}

// 对话框关闭
const handleDialogClose = () => {
  formRef.value?.resetFields()
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  loadData()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadData()
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const response = await request.get('/api/materials')
    tableData.value = response.data.list
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.text-danger {
  color: #f56c6c;
  font-weight: bold;
}
</style> 