import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'

const isMock = import.meta.env.VITE_APP_MOCK_ENABLED === 'true'

// 调试信息
console.log('Mock enabled:', isMock)
console.log('Environment variables:', {
  VITE_APP_MOCK_ENABLED: import.meta.env.VITE_APP_MOCK_ENABLED,
  VITE_APP_API_BASE_URL: import.meta.env.VITE_APP_API_BASE_URL,
  MODE: import.meta.env.MODE
})

function toMockUrl(url: string, method: string) {
  if (isMock && url.startsWith('/api/')) {
    // 将 /api/xxx/yyy 映射到 /mock/xxx_yyy.json
    // 例如 /api/auth/login → /mock/auth_login.json
    //      /api/materials/apply → /mock/materials_apply.json
    //      /api/dashboard/materials/unused → /mock/dashboard_materials_unused.json
    const path = url.replace(/^\/api\//, '').replace(/\?.*$/, '') // 只移除查询参数，保留路径
    console.log(`Path after removing /api/: ${path}`)
    // 将路径中的 / 替换为 _
    let file = path.replace(/\//g, '_')
    console.log(`File after replacing / with _: ${file}`)
    if (!file.endsWith('.json')) file += '.json'
    const mockUrl = `/mock/${file}`
    console.log(`Mock URL mapping: ${url} -> ${mockUrl}`)
    return mockUrl
  }
  return url
}

class Request {
  private instance: AxiosInstance

  constructor() {
    this.instance = axios.create({
      // 在mock模式下，baseURL设为空，让请求直接访问本地文件
      baseURL: isMock ? '' : import.meta.env.VITE_APP_API_BASE_URL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    })

    this.setupInterceptors()
  }

  private setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        const authStore = useAuthStore()
        if (authStore.token) {
          config.headers.Authorization = `Bearer ${authStore.token}`
        }
        // mock适配
        const originalUrl = config.url || ''
        config.url = toMockUrl(originalUrl, config.method?.toUpperCase() || 'GET')
        console.log(`Request interceptor: ${originalUrl} -> ${config.url}`)
        // post/put/delete 也走 get 方式读取本地json
        if (isMock && ['POST', 'PUT', 'DELETE'].includes((config.method || '').toUpperCase())) {
          config.method = 'get'
          console.log(`Mock mode: changing method to GET`)
        }
        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response: AxiosResponse) => {
        return response.data
      },
      (error) => {
        const authStore = useAuthStore()
        
        if (error.response?.status === 401) {
          authStore.logout()
          window.location.href = '/login'
        }
        
        ElMessage.error(error.response?.data?.message || '请求失败')
        return Promise.reject(error)
      }
    )
  }

  public get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.get(toMockUrl(url, 'GET'), config)
  }

  public post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.post(toMockUrl(url, 'POST'), data, config)
  }

  public put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.put(toMockUrl(url, 'PUT'), data, config)
  }

  public delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.delete(toMockUrl(url, 'DELETE'), config)
  }
}

export default new Request() 