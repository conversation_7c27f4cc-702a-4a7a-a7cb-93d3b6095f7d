import{l as e,r as a,_ as l,q as t,y as o,z as d,R as s,J as r,aU as u,av as n,x as i,O as p,P as c}from"./vue-vendor-fc5a6493.js";import{E as m}from"./element-plus-7917fd46.js";import{_ as f}from"./_plugin-vue_export-helper-1b428a4d.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const v={class:"order-execute-container"},_={class:"header"},g={key:0},h={class:"info-item"},y={class:"value"},b={class:"info-item"},w={class:"value"},V={class:"info-item"},N={class:"value"},C={class:"info-item"},U={class:"value"},k={class:"info-item"},x={class:"value"},D={class:"info-item"},z={class:"value"},q={class:"info-item"},S={class:"value"},P={class:"info-item"},W={class:"value"},j={class:"info-item"},J={class:"value"},T={class:"info-item"},$={class:"value"},A={class:"info-item"},O={class:"value"},Y={class:"info-item"},E={class:"value"},H={class:"info-item"},I={class:"value"},L={class:"info-item"},R={class:"value"},M={class:"info-item"},B={class:"value"},F={key:1,class:"no-order-info"},G={class:"card-header"},K={class:"pagination-container"},Q={class:"search-form"},X={class:"pagination-container"},Z=f(e({__name:"OrderExecute",setup(e){const f=u(),Z=a(),ee=l({orderNo:"",completionDate:"",daysUsed:1,remark:"",materials:[]}),ae={orderNo:[{required:!0,message:"请选择订单",trigger:"change"}],completionDate:[{required:!0,message:"请选择完成时间",trigger:"change"}],daysUsed:[{required:!0,message:"请输入用时天数",trigger:"change"}]},le=a(null),te=a(!1),oe=a([{orderNo:"JD202401001",customerName:"张先生",userNo:"YH001234",communityName:"阳光小区",building:"1栋",roomNo:"101",address:"阳光小区1栋101",phone:"138****1234",contactPerson:"张先生",orderType:"水电安装",status:"待执行",assignedWorker:"张三",totalAmount:1500,estimatedDays:3,startDate:"2024-01-15",endDate:""},{orderNo:"JD202401002",customerName:"李女士",userNo:"YH001235",communityName:"花园小区",building:"2栋",roomNo:"202",address:"花园小区2栋202",phone:"139****5678",contactPerson:"李女士",orderType:"一次挂表",status:"执行中",assignedWorker:"李四",totalAmount:800,estimatedDays:2,startDate:"2024-01-16",endDate:""}]),de=l({page:1,pageSize:10,total:2}),se=a(!1),re=a(-1),ue=a([{code:"WL001",name:"电缆线",model:"YJV",specification:"3*4mm²",unit:"米",category:"甲料",quantity:1e3},{code:"WL002",name:"开关面板",model:"86型",specification:"单控",unit:"个",category:"辅料",quantity:500}]),ne=l({code:"",name:""}),ie=l({page:1,pageSize:10,total:2}),pe=()=>{te.value=!0},ce=a(null),me=e=>{ce.value=e},fe=()=>{ce.value?(le.value=ce.value,ee.orderNo=ce.value.orderNo,te.value=!1,m.success(`已选择订单: ${ce.value.orderNo}`)):m.warning("请先选择订单")},ve=()=>{ce.value=null},_e=e=>{de.page=e},ge=a(null),he=e=>{ge.value=e},ye=()=>{if(ge.value){if(re.value>=0){const e=ee.materials[re.value];e.materialCode=ge.value.code,e.materialName=ge.value.name,e.specification=ge.value.specification,e.unit=ge.value.unit}se.value=!1}else m.warning("请先选择物料")},be=()=>{ge.value=null,re.value=-1},we=e=>{ie.page=e},Ve=()=>{m.info("执行物料搜索")},Ne=()=>{ne.code="",ne.name=""},Ce=()=>{ee.materials.push({materialCode:"",materialName:"",specification:"",unit:"",quantity:1})},Ue=e=>{switch(e){case"甲料":return"primary";case"辅料":return"success";case"商品":return"warning";default:return"info"}},ke=e=>{switch(e){case"待执行":default:return"info";case"执行中":case"已完成":return"success";case"已取消":return"danger"}},xe=()=>{return e=this,a=null,l=function*(){if(Z.value)try{if(!(yield Z.value.validate()))return void m.error("请填写必填项");ee.orderNo,ee.daysUsed,ee.materials.map(e=>({materialCode:e.materialCode,quantity:e.quantity})),ee.remark,m.success("订单执行信息保存成功")}catch(e){m.error("保存失败: "+e.message)}},new Promise((t,o)=>{var d=e=>{try{r(l.next(e))}catch(a){o(a)}},s=e=>{try{r(l.throw(e))}catch(a){o(a)}},r=e=>e.done?t(e.value):Promise.resolve(e.value).then(d,s);r((l=l.apply(e,a)).next())});var e,a,l};return t(()=>{(()=>{const e=f.query.orderInfo;if(e)try{const a=JSON.parse(e);le.value=a,ee.orderNo=a.orderNo,m.success(`已加载订单信息: ${a.orderNo}`)}catch(a){m.error("订单信息解析失败"),le.value=null}else le.value=null})()}),(e,a)=>{const l=n("el-button"),t=n("el-col"),u=n("el-row"),m=n("el-tag"),f=n("el-empty"),ce=n("el-card"),ge=n("Search"),De=n("el-icon"),ze=n("el-input"),qe=n("el-form-item"),Se=n("el-date-picker"),Pe=n("el-input-number"),We=n("el-form"),je=n("el-table-column"),Je=n("el-table"),Te=n("el-pagination"),$e=n("el-dialog");return i(),o("div",v,[d("div",_,[a[15]||(a[15]=d("h2",null,"订单执行",-1)),s(l,{type:"primary",onClick:xe},{default:r(()=>a[14]||(a[14]=[p("保存",-1)])),_:1,__:[14]})]),s(ce,{class:"order-info-card"},{header:r(()=>a[16]||(a[16]=[d("div",{class:"card-header"},[d("span",null,"订单详细信息")],-1)])),default:r(()=>[le.value?(i(),o("div",g,[s(u,{gutter:20},{default:r(()=>[s(t,{span:8},{default:r(()=>[d("div",h,[a[17]||(a[17]=d("span",{class:"label"},"甲方订单号:",-1)),d("span",y,c(le.value.orderNo),1)])]),_:1}),s(t,{span:8},{default:r(()=>[d("div",b,[a[18]||(a[18]=d("span",{class:"label"},"户名:",-1)),d("span",w,c(le.value.customerName),1)])]),_:1}),s(t,{span:8},{default:r(()=>[d("div",V,[a[19]||(a[19]=d("span",{class:"label"},"用户编号:",-1)),d("span",N,c(le.value.userNo),1)])]),_:1})]),_:1}),s(u,{gutter:20},{default:r(()=>[s(t,{span:8},{default:r(()=>[d("div",C,[a[20]||(a[20]=d("span",{class:"label"},"小区名称:",-1)),d("span",U,c(le.value.communityName),1)])]),_:1}),s(t,{span:8},{default:r(()=>[d("div",k,[a[21]||(a[21]=d("span",{class:"label"},"楼橦:",-1)),d("span",x,c(le.value.building),1)])]),_:1}),s(t,{span:8},{default:r(()=>[d("div",D,[a[22]||(a[22]=d("span",{class:"label"},"房号:",-1)),d("span",z,c(le.value.roomNo),1)])]),_:1})]),_:1}),s(u,{gutter:20},{default:r(()=>[s(t,{span:8},{default:r(()=>[d("div",q,[a[23]||(a[23]=d("span",{class:"label"},"电话:",-1)),d("span",S,c(le.value.phone),1)])]),_:1}),s(t,{span:8},{default:r(()=>[d("div",P,[a[24]||(a[24]=d("span",{class:"label"},"联系人:",-1)),d("span",W,c(le.value.contactPerson),1)])]),_:1}),s(t,{span:8},{default:r(()=>[d("div",j,[a[25]||(a[25]=d("span",{class:"label"},"订单分类:",-1)),d("span",J,c(le.value.orderType),1)])]),_:1})]),_:1}),s(u,{gutter:20},{default:r(()=>[s(t,{span:8},{default:r(()=>[d("div",T,[a[26]||(a[26]=d("span",{class:"label"},"订单状态:",-1)),d("span",$,[s(m,{type:ke(le.value.status)},{default:r(()=>[p(c(le.value.status),1)]),_:1},8,["type"])])])]),_:1}),s(t,{span:8},{default:r(()=>[d("div",A,[a[27]||(a[27]=d("span",{class:"label"},"跟进师傅:",-1)),d("span",O,c(le.value.assignedWorker||"未分派"),1)])]),_:1}),s(t,{span:8},{default:r(()=>[d("div",Y,[a[28]||(a[28]=d("span",{class:"label"},"总金额:",-1)),d("span",E,"¥"+c(le.value.totalAmount),1)])]),_:1})]),_:1}),s(u,{gutter:20},{default:r(()=>[s(t,{span:8},{default:r(()=>[d("div",H,[a[29]||(a[29]=d("span",{class:"label"},"预计用时:",-1)),d("span",I,c(le.value.estimatedDays)+"天",1)])]),_:1}),s(t,{span:8},{default:r(()=>[d("div",L,[a[30]||(a[30]=d("span",{class:"label"},"开始时间:",-1)),d("span",R,c(le.value.startDate||"未开始"),1)])]),_:1}),s(t,{span:8},{default:r(()=>[d("div",M,[a[31]||(a[31]=d("span",{class:"label"},"完成时间:",-1)),d("span",B,c(le.value.endDate||"未完成"),1)])]),_:1})]),_:1})])):(i(),o("div",F,[s(f,{description:"请选择订单以查看详细信息"},{default:r(()=>[s(l,{type:"primary",onClick:pe},{default:r(()=>a[32]||(a[32]=[p("选择订单",-1)])),_:1,__:[32]})]),_:1})]))]),_:1}),s(ce,{class:"form-card"},{header:r(()=>a[33]||(a[33]=[d("div",{class:"card-header"},[d("span",null,"执行信息")],-1)])),default:r(()=>[s(We,{ref_key:"formRef",ref:Z,model:ee,rules:ae,"label-width":"120px"},{default:r(()=>[s(u,{gutter:20},{default:r(()=>[s(t,{span:12},{default:r(()=>[s(qe,{label:"订单号",prop:"orderNo"},{default:r(()=>[s(ze,{modelValue:ee.orderNo,"onUpdate:modelValue":a[0]||(a[0]=e=>ee.orderNo=e),placeholder:"请选择订单",readonly:""},{append:r(()=>[s(l,{onClick:pe},{default:r(()=>[s(De,null,{default:r(()=>[s(ge)]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),s(u,{gutter:20},{default:r(()=>[s(t,{span:12},{default:r(()=>[s(qe,{label:"完成时间",prop:"completionDate"},{default:r(()=>[s(Se,{modelValue:ee.completionDate,"onUpdate:modelValue":a[1]||(a[1]=e=>ee.completionDate=e),type:"date",placeholder:"请选择完成时间",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),s(t,{span:12},{default:r(()=>[s(qe,{label:"用时(天)",prop:"daysUsed"},{default:r(()=>[s(Pe,{modelValue:ee.daysUsed,"onUpdate:modelValue":a[2]||(a[2]=e=>ee.daysUsed=e),min:0,step:.5,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),s(u,{gutter:20},{default:r(()=>[s(t,{span:24},{default:r(()=>[s(qe,{label:"备注"},{default:r(()=>[s(ze,{modelValue:ee.remark,"onUpdate:modelValue":a[3]||(a[3]=e=>ee.remark=e),type:"textarea",rows:2,placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),s(ce,{class:"material-card"},{header:r(()=>[d("div",G,[a[35]||(a[35]=d("span",null,"物料使用列表",-1)),s(l,{type:"primary",onClick:Ce},{default:r(()=>a[34]||(a[34]=[p("添加物料",-1)])),_:1,__:[34]})])]),default:r(()=>[s(Je,{data:ee.materials,border:""},{default:r(()=>[s(je,{type:"index",label:"#",width:"60"}),s(je,{label:"公司物料编码",width:"180"},{default:r(({row:e,$index:a})=>[s(ze,{modelValue:e.materialCode,"onUpdate:modelValue":a=>e.materialCode=a,placeholder:"请选择物料"},{append:r(()=>[s(l,{onClick:e=>{return l=a,re.value=l,void(se.value=!0);var l}},{default:r(()=>[s(De,null,{default:r(()=>[s(ge)]),_:1})]),_:2},1032,["onClick"])]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),s(je,{label:"物料名称",width:"150"},{default:r(({row:e})=>[s(ze,{modelValue:e.materialName,"onUpdate:modelValue":a=>e.materialName=a,disabled:""},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),s(je,{label:"规格型号",width:"150"},{default:r(({row:e})=>[s(ze,{modelValue:e.specification,"onUpdate:modelValue":a=>e.specification=a,disabled:""},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),s(je,{label:"单位",width:"80"},{default:r(({row:e})=>[s(ze,{modelValue:e.unit,"onUpdate:modelValue":a=>e.unit=a,disabled:""},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),s(je,{label:"数量",width:"120"},{default:r(({row:e})=>[s(Pe,{modelValue:e.quantity,"onUpdate:modelValue":a=>e.quantity=a,min:1,style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),s(je,{label:"操作",width:"80"},{default:r(({$index:e})=>[s(l,{type:"danger",size:"small",onClick:a=>{return l=e,void ee.materials.splice(l,1);var l}},{default:r(()=>a[36]||(a[36]=[p(" 删除 ",-1)])),_:2,__:[36]},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1}),s($e,{modelValue:te.value,"onUpdate:modelValue":a[7]||(a[7]=e=>te.value=e),title:"选择订单",width:"1000px",onClose:ve},{footer:r(()=>[s(l,{onClick:a[6]||(a[6]=e=>te.value=!1)},{default:r(()=>a[37]||(a[37]=[p("取消",-1)])),_:1,__:[37]}),s(l,{type:"primary",onClick:fe},{default:r(()=>a[38]||(a[38]=[p("确定",-1)])),_:1,__:[38]})]),default:r(()=>[s(Je,{data:oe.value,border:"","highlight-current-row":"",onCurrentChange:me},{default:r(()=>[s(je,{prop:"orderNo",label:"甲方订单号",width:"150"}),s(je,{prop:"customerName",label:"户名",width:"100"}),s(je,{prop:"communityName",label:"小区名称",width:"120"}),s(je,{prop:"building",label:"楼橦",width:"80"}),s(je,{prop:"roomNo",label:"房号",width:"80"}),s(je,{prop:"phone",label:"电话",width:"120"}),s(je,{prop:"orderType",label:"订单分类",width:"100"}),s(je,{prop:"status",label:"订单状态",width:"100"},{default:r(({row:e})=>[s(m,{type:ke(e.status)},{default:r(()=>[p(c(e.status),1)]),_:2},1032,["type"])]),_:1}),s(je,{prop:"assignedWorker",label:"跟进师傅",width:"100"})]),_:1},8,["data"]),d("div",K,[s(Te,{"current-page":de.page,"onUpdate:currentPage":a[4]||(a[4]=e=>de.page=e),"page-size":de.pageSize,"onUpdate:pageSize":a[5]||(a[5]=e=>de.pageSize=e),total:de.total,layout:"total, prev, pager, next",onCurrentChange:_e},null,8,["current-page","page-size","total"])])]),_:1},8,["modelValue"]),s($e,{modelValue:se.value,"onUpdate:modelValue":a[13]||(a[13]=e=>se.value=e),title:"选择物料",width:"900px",onClose:be},{footer:r(()=>[s(l,{onClick:a[12]||(a[12]=e=>se.value=!1)},{default:r(()=>a[41]||(a[41]=[p("取消",-1)])),_:1,__:[41]}),s(l,{type:"primary",onClick:ye},{default:r(()=>a[42]||(a[42]=[p("确定",-1)])),_:1,__:[42]})]),default:r(()=>[d("div",Q,[s(We,{model:ne,inline:""},{default:r(()=>[s(qe,{label:"物料编码"},{default:r(()=>[s(ze,{modelValue:ne.code,"onUpdate:modelValue":a[8]||(a[8]=e=>ne.code=e),placeholder:"请输入物料编码",clearable:""},null,8,["modelValue"])]),_:1}),s(qe,{label:"物料名称"},{default:r(()=>[s(ze,{modelValue:ne.name,"onUpdate:modelValue":a[9]||(a[9]=e=>ne.name=e),placeholder:"请输入物料名称",clearable:""},null,8,["modelValue"])]),_:1}),s(qe,null,{default:r(()=>[s(l,{type:"primary",onClick:Ve},{default:r(()=>a[39]||(a[39]=[p("搜索",-1)])),_:1,__:[39]}),s(l,{onClick:Ne},{default:r(()=>a[40]||(a[40]=[p("重置",-1)])),_:1,__:[40]})]),_:1})]),_:1},8,["model"])]),s(Je,{data:ue.value,border:"","highlight-current-row":"",onCurrentChange:he},{default:r(()=>[s(je,{prop:"code",label:"公司物料编码",width:"150"}),s(je,{prop:"name",label:"物料名称",width:"120"}),s(je,{prop:"model",label:"型号",width:"120"}),s(je,{prop:"specification",label:"规格",width:"120"}),s(je,{prop:"unit",label:"单位",width:"80"}),s(je,{prop:"category",label:"分类",width:"100"},{default:r(({row:e})=>[s(m,{type:Ue(e.category)},{default:r(()=>[p(c(e.category),1)]),_:2},1032,["type"])]),_:1}),s(je,{prop:"quantity",label:"库存数量",width:"100"})]),_:1},8,["data"]),d("div",X,[s(Te,{"current-page":ie.page,"onUpdate:currentPage":a[10]||(a[10]=e=>ie.page=e),"page-size":ie.pageSize,"onUpdate:pageSize":a[11]||(a[11]=e=>ie.pageSize=e),total:ie.total,layout:"total, prev, pager, next",onCurrentChange:we},null,8,["current-page","page-size","total"])])]),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-149a3e79"]]);export{Z as default};
