<template>
  <div class="party-dispatch-container">
    <el-card class="main-card">

      <el-form :model="formData" :rules="formRules" ref="formRef" label-width="120px">
      
        <!-- 派单信息 -->
        <el-row :gutter="20" class="form-section">
          <el-col :span="24">
            <div class="section-title">
              <el-icon><Calendar /></el-icon>
              派单信息
            </div>
          </el-col>
          <el-col :span="12">
            <el-form-item label="派单日期:" prop="dispatchDate">
              <el-date-picker
                v-model="formData.dispatchDate"
                type="date"
                placeholder="请选择派单日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
                :disabled-date="disabledDate"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="操作员:" prop="operator">
              <el-select v-model="formData.operator" placeholder="请选择操作员" style="width: 100%">
                <el-option
                  v-for="operator in operatorList"
                  :key="operator.id"
                  :label="operator.name"
                  :value="operator.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      
        <!-- 订单信息 -->
        <el-row :gutter="20" class="form-section">
          <el-col :span="24">
            <div class="section-title">
              <el-icon><Document /></el-icon>
              订单信息
            </div>
          </el-col>
          <el-col :span="12">
            <el-form-item label="甲方订单号:" prop="partyOrderNo">
              <el-input
                v-model="formData.partyOrderNo"
                placeholder="请输入甲方订单号"
                maxlength="50"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工程名称:" prop="projectName">
              <el-input
                v-model="formData.projectName"
                placeholder="请输入工程名称"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="工程地址:" prop="projectAddress">
              <el-input
                v-model="formData.projectAddress"
                placeholder="请输入工程地址"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预估开始时间:" prop="estimatedStartTime">
              <el-date-picker
                v-model="formData.estimatedStartTime"
                type="date"
                placeholder="请选择预估开始时间"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
                :disabled-date="disabledStartDate"
                @change="onStartTimeChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预估结束时间:" prop="estimatedEndTime">
              <el-date-picker
                v-model="formData.estimatedEndTime"
                type="date"
                placeholder="请选择预估结束时间"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
                :disabled-date="disabledEndDate"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工程负责人:" prop="projectManager">
              <el-select
                v-model="formData.projectManager"
                placeholder="请选择工程负责人"
                style="width: 100%"
                filterable
                allow-create
              >
                <el-option
                  v-for="manager in managerList"
                  :key="manager.id"
                  :label="manager.name"
                  :value="manager.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话:" prop="contactPhone">
              <el-input
                v-model="formData.contactPhone"
                placeholder="请输入联系电话"
                maxlength="20"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="工程描述:" prop="projectDescription">
              <el-input
                v-model="formData.projectDescription"
                type="textarea"
                :rows="4"
                placeholder="请详细描述工程内容、要求、注意事项等"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注:">
              <el-input
                v-model="formData.remarks"
                type="textarea"
                :rows="2"
                placeholder="请输入备注信息"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>
      
        <!-- 时间选择说明 -->
        <el-row :gutter="20" class="form-section">
          <el-col :span="24">
            <el-alert
              title="时间选择说明"
              type="info"
              :closable="false"
              show-icon
            >
              <template #default>
                <div class="info-content">
                  <p><strong>1. 预估开始时间:</strong> 工程计划开始日期，不能早于当前日期</p>
                  <p><strong>2. 预估结束时间:</strong> 工程计划完成日期，必须晚于开始时间</p>
                  <p><strong>3. 时间范围:</strong> 应根据工程规模合理设定，建议预留适当缓冲时间</p>
                  <p><strong>4. 工期计算:</strong> 当前设定工期为 <span class="duration-text">{{ projectDuration }}</span> 天</p>
                </div>
              </template>
            </el-alert>
          </el-col>
        </el-row>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <el-button
            type="primary"
            size="large"
            :loading="saveLoading"
            @click="handleSave"
          >
            <el-icon><DocumentAdd /></el-icon>
            保存
          </el-button>
          <el-button
            type="success"
            size="large"
            :loading="submitLoading"
            @click="handleSubmit"
          >
            <el-icon><Check /></el-icon>
            提交
          </el-button>
          <el-button
            type="info"
            size="large"
            @click="handlePrint"
          >
            <el-icon><Printer /></el-icon>
            打印
          </el-button>
          <el-button
            size="large"
            @click="handleCancel"
          >
            <el-icon><Close /></el-icon>
            取消
          </el-button>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import {
  Calendar,
  DocumentAdd,
  Check,
  Printer,
  Close
} from '@element-plus/icons-vue'

// 表单引用
const formRef = ref<FormInstance>()

// 加载状态
const saveLoading = ref(false)
const submitLoading = ref(false)

// 表单数据
const formData = reactive({
  dispatchDate: new Date().toISOString().split('T')[0], // 派单日期，默认今天
  operator: '', // 操作员
  partyOrderNo: '', // 甲方订单号
  projectName: '', // 工程名称
  projectAddress: '', // 工程地址
  estimatedStartTime: '', // 预估开始时间
  estimatedEndTime: '', // 预估结束时间
  projectManager: '', // 工程负责人
  contactPhone: '', // 联系电话
  projectDescription: '', // 工程描述
  remarks: '' // 备注
})

// 本地模拟数据
const operatorList = ref([
  { id: 1, name: '张三' },
  { id: 2, name: '李四' },
  { id: 3, name: '王五' },
  { id: 4, name: '赵六' }
])

const managerList = ref([
  { id: 1, name: '项目经理A' },
  { id: 2, name: '项目经理B' },
  { id: 3, name: '项目经理C' },
  { id: 4, name: '高级工程师' },
  { id: 5, name: '技术负责人' }
])

// 表单验证规则
const formRules: FormRules = {
  dispatchDate: [
    { required: true, message: '请选择派单日期', trigger: 'change' }
  ],
  operator: [
    { required: true, message: '请选择操作员', trigger: 'change' }
  ],
  partyOrderNo: [
    { required: true, message: '请输入甲方订单号', trigger: 'blur' },
    { min: 3, max: 50, message: '订单号长度在 3 到 50 个字符', trigger: 'blur' }
  ],
  projectName: [
    { required: true, message: '请输入工程名称', trigger: 'blur' },
    { min: 2, max: 100, message: '工程名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  projectAddress: [
    { required: true, message: '请输入工程地址', trigger: 'blur' },
    { min: 5, max: 200, message: '工程地址长度在 5 到 200 个字符', trigger: 'blur' }
  ],
  estimatedStartTime: [
    { required: true, message: '请选择预估开始时间', trigger: 'change' }
  ],
  estimatedEndTime: [
    { required: true, message: '请选择预估结束时间', trigger: 'change' }
  ],
  projectManager: [
    { required: true, message: '请选择工程负责人', trigger: 'change' }
  ],
  contactPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  projectDescription: [
    { required: true, message: '请输入工程描述', trigger: 'blur' },
    { min: 10, max: 500, message: '工程描述长度在 10 到 500 个字符', trigger: 'blur' }
  ]
}

// 计算工期
const projectDuration = computed(() => {
  if (formData.estimatedStartTime && formData.estimatedEndTime) {
    const start = new Date(formData.estimatedStartTime)
    const end = new Date(formData.estimatedEndTime)
    const diffTime = end.getTime() - start.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays > 0 ? diffDays : 0
  }
  return 0
})

// 禁用日期函数
const disabledDate = (time: Date) => {
  return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
}

const disabledStartDate = (time: Date) => {
  return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
}

const disabledEndDate = (time: Date) => {
  if (!formData.estimatedStartTime) return false
  const startTime = new Date(formData.estimatedStartTime).getTime()
  return time.getTime() <= startTime
}

// 开始时间变化时的处理
const onStartTimeChange = (value: string) => {
  if (value && formData.estimatedEndTime) {
    const startTime = new Date(value).getTime()
    const endTime = new Date(formData.estimatedEndTime).getTime()
    if (endTime <= startTime) {
      formData.estimatedEndTime = ''
      ElMessage.warning('结束时间必须晚于开始时间，请重新选择')
    }
  }
}




// 保存
const handleSave = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    saveLoading.value = true

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    const saveData = {
      ...formData,
      id: Date.now(),
      status: 'draft',
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    }

    console.log('保存数据:', saveData)
    ElMessage.success('保存成功')
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单填写是否正确')
  } finally {
    saveLoading.value = false
  }
}

// 提交
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    await ElMessageBox.confirm('确定要提交此派单吗？提交后将无法修改。', '确认提交', {
      confirmButtonText: '确定提交',
      cancelButtonText: '取消',
      type: 'warning'
    })

    submitLoading.value = true

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1500))

    const submitData = {
      ...formData,
      id: Date.now(),
      status: 'submitted',
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString(),
      submitTime: new Date().toISOString()
    }

    console.log('提交数据:', submitData)
    ElMessage.success('提交成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('表单验证失败:', error)
      ElMessage.error('请检查表单填写是否正确')
    }
  } finally {
    submitLoading.value = false
  }
}

// 打印
const handlePrint = () => {
  if (!formData.partyOrderNo) {
    ElMessage.warning('请先填写订单信息')
    return
  }

  ElMessage.success('正在准备打印...')

  // 模拟打印功能
  setTimeout(() => {
    const printData = {
      ...formData,
      printTime: new Date().toLocaleString('zh-CN')
    }
    console.log('打印数据:', printData)

    // 这里可以调用实际的打印功能
    window.print()
  }, 500)
}

// 取消
const handleCancel = async () => {
  const hasData = Object.values(formData).some(value =>
    value && value !== new Date().toISOString().split('T')[0]
  )

  if (hasData) {
    try {
      await ElMessageBox.confirm('确定要取消吗？未保存的数据将丢失。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '继续编辑',
        type: 'warning'
      })
    } catch {
      return
    }
  }

  // 重置表单
  formRef.value?.resetFields()
  Object.keys(formData).forEach(key => {
    if (key === 'dispatchDate') {
      (formData as any)[key] = new Date().toISOString().split('T')[0]
    } else {
      (formData as any)[key] = ''
    }
  })

  ElMessage.info('已取消')
}

// 初始化数据
onMounted(() => {
  // 设置默认操作员（模拟当前登录用户）
  formData.operator = '张三'
})
</script>

<style lang="scss" scoped>
.party-dispatch-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);



  .main-card {
    margin-bottom: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 18px;
      font-weight: bold;
      color: #303133;

      .header-actions {
        display: flex;
        gap: 10px;
      }
    }
  }

  .form-section {
    margin-bottom: 30px;

    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: bold;
      color: #409eff;
      margin-bottom: 20px;
      padding-bottom: 12px;
      border-bottom: 2px solid #e4e7ed;

      .el-icon {
        font-size: 18px;
      }
    }

    .el-form-item {
      margin-bottom: 22px;

      :deep(.el-form-item__label) {
        font-weight: 500;
        color: #606266;
      }

      :deep(.el-input__wrapper) {
        box-shadow: 0 0 0 1px #dcdfe6 inset;
        transition: box-shadow 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

        &:hover {
          box-shadow: 0 0 0 1px #c0c4cc inset;
        }

        &.is-focus {
          box-shadow: 0 0 0 1px #409eff inset;
        }
      }

      :deep(.el-textarea__inner) {
        box-shadow: 0 0 0 1px #dcdfe6 inset;
        transition: box-shadow 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

        &:hover {
          box-shadow: 0 0 0 1px #c0c4cc inset;
        }

        &:focus {
          box-shadow: 0 0 0 1px #409eff inset;
        }
      }
    }
  }

  .info-content {
    p {
      margin: 8px 0;
      color: #606266;
      line-height: 1.6;

      strong {
        color: #303133;
      }
    }

    .duration-text {
      color: #409eff;
      font-weight: bold;
      font-size: 16px;
    }
  }

  .form-actions {
    text-align: center;
    padding: 30px 0;
    border-top: 1px solid #ebeef5;
    margin-top: 20px;

    .el-button {
      margin: 0 12px;
      padding: 12px 24px;
      font-size: 14px;
      border-radius: 6px;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      }

      .el-icon {
        margin-right: 6px;
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: 15px;

    .form-section {
      .el-col {
        margin-bottom: 15px;
      }
    }

    .form-actions {
      .el-button {
        margin: 5px;
        width: calc(50% - 10px);
      }
    }
  }
}

// 打印样式
@media print {
  .party-dispatch-container {
    background-color: white;

    .header-actions,
    .form-actions {
      display: none;
    }

    .main-card {
      box-shadow: none;
      border: 1px solid #000;
    }

    .section-title {
      color: #000;
      border-bottom-color: #000;
    }
  }
}
</style>