import{t as e,d as a,E as l}from"./element-plus-ad78a7bf.js";import{l as t,r as o,_ as s,y as r,R as d,J as u,av as i,x as n,z as p,u as m,O as c,Q as f,aa as _,I as b,P as y,M as w}from"./vue-vendor-fc5a6493.js";import{_ as g}from"./_plugin-vue_export-helper-1b428a4d.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const k={class:"project-finish-container"},h={class:"section-title"},V={class:"section-title"},v={class:"form-actions"},j=g(t({__name:"ProjectFinish",setup(t){const g=o(),j=o(null),C=o([{id:1,name:"阳光小区A栋",status:"在建",progress:95,startDate:"2024-01-12"},{id:2,name:"花园广场项目",status:"在建",progress:88,startDate:"2024-01-15"},{id:3,name:"商业中心B区",status:"在建",progress:92,startDate:"2024-01-20"}]),D=s({projectId:"",projectSummary:"",maintenanceSuggestions:""}),U={projectId:[{required:!0,message:"请选择工程",trigger:"change"}]},W=e=>{j.value=C.value.find(a=>a.id===Number(e))},L=s([{id:1,workType:"电工",name:"李师傅",dailyWage:300,workDays:25,totalLaborCost:"¥7,500",remarks:""},{id:2,workType:"水工",name:"王师傅",dailyWage:280,workDays:20,totalLaborCost:"¥5,600",remarks:""},{id:3,workType:"安装工",name:"张师傅",dailyWage:320,workDays:22,totalLaborCost:"¥7,040",remarks:""},{id:4,workType:"维修工",name:"赵师傅",dailyWage:250,workDays:15,totalLaborCost:"¥3,750",remarks:""}]),P=s([{id:1,materialCode:"WL001",materialName:"电缆线",model:"YJV-3*4",specification:"3*4mm²",unit:"米",usageQuantity:300,unitPrice:"¥30.00",subtotal:"¥9,000",remarks:""},{id:2,materialCode:"WL002",materialName:"开关面板",model:"KP-86",specification:"86型",unit:"个",usageQuantity:40,unitPrice:"¥250.00",subtotal:"¥10,000",remarks:""},{id:3,materialCode:"WL003",materialName:"插座",model:"ZP-86",specification:"86型",unit:"个",usageQuantity:60,unitPrice:"¥60.00",subtotal:"¥3,600",remarks:""},{id:4,materialCode:"WL004",materialName:"灯具",model:"LED-12W",specification:"12W",unit:"个",usageQuantity:30,unitPrice:"¥130.00",subtotal:"¥3,900",remarks:""}]),x=()=>{l.success("保存成功")},I=()=>{l.success("提交成功")},N=()=>{l.success("开始打印报告")},Q=()=>{l.info("已取消")};return(l,t)=>{const o=i("el-icon"),s=i("el-col"),S=i("el-option"),T=i("el-select"),E=i("el-form-item"),J=i("el-row"),M=i("el-input"),R=i("el-tag"),$=i("el-progress"),q=i("el-table-column"),z=i("el-input-number"),A=i("el-table"),B=i("el-button"),F=i("el-form"),K=i("el-card");return n(),r("div",k,[d(K,{class:"main-card"},{default:u(()=>[d(F,{model:D,rules:U,ref_key:"formRef",ref:g,"label-width":"120px"},{default:u(()=>[d(J,{gutter:20,class:"form-section"},{default:u(()=>[d(s,{span:24},{default:u(()=>[p("div",h,[d(o,null,{default:u(()=>[d(m(e))]),_:1}),t[5]||(t[5]=c(" 工程选择 ",-1))])]),_:1}),d(s,{span:24},{default:u(()=>[d(E,{label:"选择工程:",prop:"projectId"},{default:u(()=>[d(T,{modelValue:D.projectId,"onUpdate:modelValue":t[0]||(t[0]=e=>D.projectId=e),placeholder:"请选择要完成的工程",style:{width:"100%"},onChange:W},{default:u(()=>[(n(!0),r(f,null,_(C.value,e=>(n(),b(S,{key:e.id,label:`${e.name} (${e.status})`,value:e.id,disabled:"在建"!==e.status},null,8,["label","value","disabled"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),j.value?(n(),b(J,{key:0,gutter:20,class:"form-section"},{default:u(()=>[d(s,{span:24},{default:u(()=>[p("div",V,[d(o,null,{default:u(()=>[d(m(a))]),_:1}),t[6]||(t[6]=c(" 当前工程信息 ",-1))])]),_:1}),d(s,{span:12},{default:u(()=>[d(E,{label:"工程名称:"},{default:u(()=>[d(M,{modelValue:j.value.name,"onUpdate:modelValue":t[1]||(t[1]=e=>j.value.name=e),readonly:""},null,8,["modelValue"])]),_:1})]),_:1}),d(s,{span:12},{default:u(()=>[d(E,{label:"当前状态:"},{default:u(()=>{return[d(R,{type:(e=j.value.status,{"未开始":"info","在建":"warning","暂停":"danger","完成":"success"}[e]||"info")},{default:u(()=>[c(y(j.value.status),1)]),_:1},8,["type"])];var e}),_:1})]),_:1}),d(s,{span:12},{default:u(()=>[d(E,{label:"当前进度:"},{default:u(()=>[d($,{percentage:j.value.progress},null,8,["percentage"])]),_:1})]),_:1}),d(s,{span:12},{default:u(()=>[d(E,{label:"开始时间:"},{default:u(()=>[d(M,{modelValue:j.value.startDate,"onUpdate:modelValue":t[2]||(t[2]=e=>j.value.startDate=e),readonly:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})):w("",!0),d(J,{gutter:20,class:"form-section"},{default:u(()=>[d(s,{span:24},{default:u(()=>t[7]||(t[7]=[p("div",{class:"section-title"},"人员工时统计",-1)])),_:1,__:[7]}),d(s,{span:24},{default:u(()=>[d(A,{data:L,border:"",class:"staff-table"},{default:u(()=>[d(q,{type:"index",label:"序号",width:"60"}),d(q,{prop:"workType",label:"工种",width:"120"}),d(q,{prop:"name",label:"人员姓名",width:"120"}),d(q,{prop:"dailyWage",label:"工价(元/天)",width:"120"}),d(q,{prop:"workDays",label:"工作天数",width:"100"},{default:u(e=>[d(z,{modelValue:e.row.workDays,"onUpdate:modelValue":a=>e.row.workDays=a,min:0,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),d(q,{prop:"totalLaborCost",label:"总工时费",width:"120"}),d(q,{prop:"remarks",label:"备注"},{default:u(e=>[d(M,{modelValue:e.row.remarks,"onUpdate:modelValue":a=>e.row.remarks=a,placeholder:"请输入备注"},null,8,["modelValue","onUpdate:modelValue"])]),_:1})]),_:1},8,["data"])]),_:1})]),_:1}),d(J,{gutter:20,class:"form-section"},{default:u(()=>[d(s,{span:24},{default:u(()=>t[8]||(t[8]=[p("div",{class:"section-title"},"物料使用汇总",-1)])),_:1,__:[8]}),d(s,{span:24},{default:u(()=>[d(A,{data:P,border:"",class:"material-table"},{default:u(()=>[d(q,{type:"index",label:"序号",width:"60"}),d(q,{prop:"materialCode",label:"公司物料编码",width:"120"}),d(q,{prop:"materialName",label:"物料名称",width:"120"}),d(q,{prop:"model",label:"型号",width:"100"}),d(q,{prop:"specification",label:"规格",width:"100"}),d(q,{prop:"unit",label:"单位",width:"80"}),d(q,{prop:"usageQuantity",label:"使用数量",width:"100"}),d(q,{prop:"unitPrice",label:"单价",width:"100"}),d(q,{prop:"subtotal",label:"小计",width:"100"}),d(q,{prop:"remarks",label:"备注"},{default:u(e=>[d(M,{modelValue:e.row.remarks,"onUpdate:modelValue":a=>e.row.remarks=a,placeholder:"请输入备注"},null,8,["modelValue","onUpdate:modelValue"])]),_:1})]),_:1},8,["data"])]),_:1})]),_:1}),d(J,{gutter:20,class:"form-section"},{default:u(()=>[d(s,{span:24},{default:u(()=>t[9]||(t[9]=[p("div",{class:"section-title"},"备注信息",-1)])),_:1,__:[9]}),d(s,{span:24},{default:u(()=>[d(E,{label:"工程总结:"},{default:u(()=>[d(M,{modelValue:D.projectSummary,"onUpdate:modelValue":t[3]||(t[3]=e=>D.projectSummary=e),type:"textarea",rows:3,placeholder:"请输入工程总结"},null,8,["modelValue"])]),_:1})]),_:1}),d(s,{span:24},{default:u(()=>[d(E,{label:"后续维护建议:"},{default:u(()=>[d(M,{modelValue:D.maintenanceSuggestions,"onUpdate:modelValue":t[4]||(t[4]=e=>D.maintenanceSuggestions=e),type:"textarea",rows:3,placeholder:"请输入后续维护建议"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),p("div",v,[d(B,{type:"primary",onClick:x},{default:u(()=>t[10]||(t[10]=[c("保存",-1)])),_:1,__:[10]}),d(B,{type:"success",onClick:I},{default:u(()=>t[11]||(t[11]=[c("提交",-1)])),_:1,__:[11]}),d(B,{onClick:N},{default:u(()=>t[12]||(t[12]=[c("打印报告",-1)])),_:1,__:[12]}),d(B,{onClick:Q},{default:u(()=>t[13]||(t[13]=[c("取消",-1)])),_:1,__:[13]})])]),_:1},8,["model"])]),_:1})])}}}),[["__scopeId","data-v-401792f4"]]);export{j as default};
