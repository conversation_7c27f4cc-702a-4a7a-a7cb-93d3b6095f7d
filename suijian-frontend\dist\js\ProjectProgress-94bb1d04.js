var e=Object.defineProperty,l=Object.defineProperties,a=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,i=Object.prototype.propertyIsEnumerable,r=(l,a,t)=>a in l?e(l,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):l[a]=t,d=(e,l,a)=>new Promise((t,o)=>{var i=e=>{try{d(a.next(e))}catch(l){o(l)}},r=e=>{try{d(a.throw(e))}catch(l){o(l)}},d=e=>e.done?t(e.value):Promise.resolve(e.value).then(i,r);d((a=a.apply(e,l)).next())});import{t as u,d as s,q as n,u as p,b as m,v as c,m as f,l as _,o as y,E as v}from"./element-plus-7917fd46.js";import{l as g,r as b,_ as w,q as h,y as V,R as C,J as k,av as P,x,z as U,u as q,O as j,Q as D,aa as L,I as M,P as N,M as I}from"./vue-vendor-fc5a6493.js";import{_ as O}from"./_plugin-vue_export-helper-1b428a4d.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const z={class:"project-progress-container"},$={class:"section-title"},F={class:"section-title"},T={class:"section-title"},W={class:"section-title"},J={style:{"margin-top":"10px"}},R={class:"section-title"},S={style:{"margin-top":"10px"}},A={class:"section-title"},B={style:{"margin-top":"10px"}},E={class:"form-actions"},G={class:"dialog-footer"},Q={class:"dialog-footer"},Z=O(g({__name:"ProjectProgress",setup(e){const g=b(),O=b(!1),Z=b([{id:1,name:"阳光小区A栋",status:"在建",progress:65,startDate:"2024-01-01"},{id:2,name:"花园广场项目",status:"在建",progress:45,startDate:"2024-01-05"},{id:3,name:"绿城花园D区",status:"在建",progress:75,startDate:"2023-12-01"},{id:4,name:"商业中心B区",status:"暂停",progress:30,startDate:"2023-12-15"},{id:5,name:"住宅楼C座",status:"完成",progress:100,startDate:"2023-11-01"}]),H=b(null),K=w({projectId:"",completionProgress:0,completionDescription:"",existingIssues:"",personnelList:[],materialList:[],otherCostList:[]}),X={projectId:[{required:!0,message:"请选择工程",trigger:"change"}],completionProgress:[{required:!0,message:"请输入完成进度",trigger:"blur"}],completionDescription:[{required:!0,message:"请输入完成情况描述",trigger:"blur"},{min:10,max:500,message:"描述长度在 10 到 500 个字符",trigger:"blur"}]},Y=e=>{H.value=Z.value.find(l=>l.id===Number(e)),H.value&&(K.completionProgress=H.value.progress)},ee=()=>{K.personnelList.push({workType:"",dailyWage:0,days:0})},le=b(!1),ae=b(!1),te=b(),oe=b([{companyMaterialCode:"CM001",materialCode:"JL001",materialName:"燃气表",model:"G2.5",specification:"DN20",unit:"个"},{companyMaterialCode:"CM002",materialCode:"JL002",materialName:"镀锌管件",model:"ZG-001",specification:"20*1.5",unit:"个"},{companyMaterialCode:"CM003",materialCode:"JL003",materialName:"波纹管",model:"BW-15",specification:"DN15*500",unit:"m"},{companyMaterialCode:"CM004",materialCode:"JL004",materialName:"阀门",model:"FM-25",specification:"DN25",unit:"个"}]),ie=w({name:"",quantity:1,unitPrice:0,remarks:""}),re={name:[{required:!0,message:"请输入费用名称",trigger:"blur"}],quantity:[{required:!0,message:"请输入数量",trigger:"blur"}],unitPrice:[{required:!0,message:"请输入单价",trigger:"blur"}]},de=()=>{le.value=!0},ue=e=>{K.materialList.push({companyMaterialCode:e.companyMaterialCode,materialCode:e.materialCode,materialName:e.materialName,model:e.model,specification:e.specification,unit:e.unit,quantity:1,laborPrice:0}),le.value=!1,v.success("物料添加成功")},se=()=>{Object.assign(ie,{name:"",quantity:1,unitPrice:0,remarks:""}),ae.value=!0},ne=()=>d(this,null,function*(){if(te.value)try{yield te.value.validate(),K.otherCostList.push({name:ie.name,quantity:ie.quantity,unitPrice:ie.unitPrice,remarks:ie.remarks}),ae.value=!1,v.success("费用添加成功")}catch(e){}}),pe=()=>d(this,null,function*(){var e,d;if(g.value)try{yield g.value.validate(),O.value=!0,yield new Promise(e=>setTimeout(e,1500));e=((e,l)=>{for(var a in l||(l={}))o.call(l,a)&&r(e,a,l[a]);if(t)for(var a of t(l))i.call(l,a)&&r(e,a,l[a]);return e})({},K),d={id:Date.now(),submitTime:(new Date).toISOString()},l(e,a(d));v.success("工程推进提交成功"),H.value&&(H.value.progress=K.completionProgress)}catch(u){v.error("请检查表单填写是否正确")}finally{O.value=!1}}),me=()=>{v.success("草稿保存成功")},ce=()=>{var e;null==(e=g.value)||e.resetFields(),H.value=null,v.info("已取消")};return h(()=>{}),(e,l)=>{const a=P("el-icon"),t=P("el-col"),o=P("el-option"),i=P("el-select"),r=P("el-form-item"),d=P("el-row"),v=P("el-input"),b=P("el-tag"),w=P("el-progress"),h=P("el-input-number"),fe=P("el-table-column"),_e=P("el-button"),ye=P("el-table"),ve=P("el-form"),ge=P("el-card"),be=P("el-dialog");return x(),V("div",z,[C(ge,{class:"main-card"},{default:k(()=>[C(ve,{model:K,rules:X,ref_key:"formRef",ref:g,"label-width":"120px"},{default:k(()=>[C(d,{gutter:20,class:"form-section"},{default:k(()=>[C(t,{span:24},{default:k(()=>[U("div",$,[C(a,null,{default:k(()=>[C(q(u))]),_:1}),l[14]||(l[14]=j(" 工程选择 ",-1))])]),_:1}),C(t,{span:24},{default:k(()=>[C(r,{label:"选择工程:",prop:"projectId"},{default:k(()=>[C(i,{modelValue:K.projectId,"onUpdate:modelValue":l[0]||(l[0]=e=>K.projectId=e),placeholder:"请选择要推进的工程",style:{width:"100%"},onChange:Y},{default:k(()=>[(x(!0),V(D,null,L(Z.value,e=>(x(),M(o,{key:e.id,label:`${e.name} (${e.status})`,value:e.id,disabled:"在建"!==e.status},null,8,["label","value","disabled"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),H.value?(x(),M(d,{key:0,gutter:20,class:"form-section"},{default:k(()=>[C(t,{span:24},{default:k(()=>[U("div",F,[C(a,null,{default:k(()=>[C(q(s))]),_:1}),l[15]||(l[15]=j(" 当前工程信息 ",-1))])]),_:1}),C(t,{span:12},{default:k(()=>[C(r,{label:"工程名称:"},{default:k(()=>[C(v,{modelValue:H.value.name,"onUpdate:modelValue":l[1]||(l[1]=e=>H.value.name=e),readonly:""},null,8,["modelValue"])]),_:1})]),_:1}),C(t,{span:12},{default:k(()=>[C(r,{label:"当前状态:"},{default:k(()=>{return[C(b,{type:(e=H.value.status,{"未开始":"info","在建":"warning","暂停":"danger","完成":"success"}[e]||"info")},{default:k(()=>[j(N(H.value.status),1)]),_:1},8,["type"])];var e}),_:1})]),_:1}),C(t,{span:12},{default:k(()=>[C(r,{label:"当前进度:"},{default:k(()=>[C(w,{percentage:H.value.progress},null,8,["percentage"])]),_:1})]),_:1}),C(t,{span:12},{default:k(()=>[C(r,{label:"开始时间:"},{default:k(()=>[C(v,{modelValue:H.value.startDate,"onUpdate:modelValue":l[2]||(l[2]=e=>H.value.startDate=e),readonly:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})):I("",!0),H.value?(x(),M(d,{key:1,gutter:20,class:"form-section"},{default:k(()=>[C(t,{span:24},{default:k(()=>[U("div",T,[C(a,null,{default:k(()=>[C(q(n))]),_:1}),l[16]||(l[16]=j(" 推进信息 ",-1))])]),_:1}),C(t,{span:12},{default:k(()=>[C(r,{label:"完成进度:",prop:"completionProgress"},{default:k(()=>[C(h,{modelValue:K.completionProgress,"onUpdate:modelValue":l[3]||(l[3]=e=>K.completionProgress=e),min:H.value.progress,max:100,step:5,style:{width:"100%"}},null,8,["modelValue","min"]),l[17]||(l[17]=U("span",{style:{"margin-left":"10px"}},"%",-1))]),_:1,__:[17]})]),_:1}),C(t,{span:24},{default:k(()=>[C(r,{label:"完成情况:",prop:"completionDescription"},{default:k(()=>[C(v,{modelValue:K.completionDescription,"onUpdate:modelValue":l[4]||(l[4]=e=>K.completionDescription=e),type:"textarea",rows:4,placeholder:"请详细描述本次推进完成的工作内容、质量情况等",maxlength:"500","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1}),C(t,{span:24},{default:k(()=>[C(r,{label:"存在问题:",prop:"existingIssues"},{default:k(()=>[C(v,{modelValue:K.existingIssues,"onUpdate:modelValue":l[5]||(l[5]=e=>K.existingIssues=e),type:"textarea",rows:3,placeholder:"请描述当前存在的问题、困难或需要协调解决的事项（如无问题可留空）",maxlength:"300","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})):I("",!0),H.value?(x(),M(d,{key:2,gutter:20,class:"form-section"},{default:k(()=>[C(t,{span:24},{default:k(()=>[U("div",W,[C(a,null,{default:k(()=>[C(q(p))]),_:1}),l[18]||(l[18]=j(" 人员列表 ",-1))])]),_:1}),C(t,{span:24},{default:k(()=>[C(ye,{data:K.personnelList,border:"",style:{width:"100%"}},{default:k(()=>[C(fe,{prop:"workType",label:"工种",width:"150"},{default:k(({row:e,$index:l})=>[C(i,{modelValue:e.workType,"onUpdate:modelValue":l=>e.workType=l,placeholder:"选择工种"},{default:k(()=>[C(o,{label:"项目经理",value:"项目经理"}),C(o,{label:"技术员",value:"技术员"}),C(o,{label:"施工员",value:"施工员"}),C(o,{label:"安全员",value:"安全员"}),C(o,{label:"质检员",value:"质检员"})]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),C(fe,{prop:"dailyWage",label:"工价(元/天)",width:"150"},{default:k(({row:e,$index:l})=>[C(h,{modelValue:e.dailyWage,"onUpdate:modelValue":l=>e.dailyWage=l,min:0,step:50},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),C(fe,{prop:"days",label:"天数",width:"120"},{default:k(({row:e,$index:l})=>[C(h,{modelValue:e.days,"onUpdate:modelValue":l=>e.days=l,min:0,step:.5},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),C(fe,{prop:"totalAmount",label:"总金额",width:"150"},{default:k(({row:e})=>[j(" ¥"+N((e.dailyWage*e.days).toFixed(2)),1)]),_:1}),C(fe,{label:"操作",width:"100"},{default:k(({$index:e})=>[C(_e,{type:"danger",size:"small",onClick:l=>{return a=e,void K.personnelList.splice(a,1);var a}},{default:k(()=>l[19]||(l[19]=[j(" 删除 ",-1)])),_:2,__:[19]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),U("div",J,[C(_e,{type:"primary",onClick:ee},{default:k(()=>l[20]||(l[20]=[j("添加人员",-1)])),_:1,__:[20]})])]),_:1})]),_:1})):I("",!0),H.value?(x(),M(d,{key:3,gutter:20,class:"form-section"},{default:k(()=>[C(t,{span:24},{default:k(()=>[U("div",R,[C(a,null,{default:k(()=>[C(q(m))]),_:1}),l[21]||(l[21]=j(" 物料使用列表 ",-1))])]),_:1}),C(t,{span:24},{default:k(()=>[C(ye,{data:K.materialList,border:"",style:{width:"100%"}},{default:k(()=>[C(fe,{prop:"companyMaterialCode",label:"公司物料编码",width:"120"},{default:k(({row:e})=>[U("span",null,N(e.companyMaterialCode),1)]),_:1}),C(fe,{prop:"materialCode",label:"甲料编码",width:"120"},{default:k(({row:e})=>[U("span",null,N(e.materialCode),1)]),_:1}),C(fe,{prop:"materialName",label:"物料名称",width:"150"},{default:k(({row:e})=>[U("span",null,N(e.materialName),1)]),_:1}),C(fe,{prop:"model",label:"型号",width:"120"},{default:k(({row:e})=>[U("span",null,N(e.model),1)]),_:1}),C(fe,{prop:"specification",label:"规格",width:"120"},{default:k(({row:e})=>[U("span",null,N(e.specification),1)]),_:1}),C(fe,{prop:"unit",label:"单位",width:"80"},{default:k(({row:e})=>[U("span",null,N(e.unit),1)]),_:1}),C(fe,{prop:"quantity",label:"数量",width:"120"},{default:k(({row:e})=>[C(h,{modelValue:e.quantity,"onUpdate:modelValue":l=>e.quantity=l,min:0,step:1,style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),C(fe,{prop:"laborPrice",label:"人工单价",width:"120"},{default:k(({row:e})=>[C(h,{modelValue:e.laborPrice,"onUpdate:modelValue":l=>e.laborPrice=l,min:0,step:.01,precision:2,style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),C(fe,{label:"操作",width:"100"},{default:k(({$index:e})=>[C(_e,{type:"danger",size:"small",onClick:l=>{return a=e,void K.materialList.splice(a,1);var a}},{default:k(()=>l[22]||(l[22]=[j(" 删除 ",-1)])),_:2,__:[22]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),U("div",S,[C(_e,{type:"primary",onClick:de},{default:k(()=>l[23]||(l[23]=[j("添加物料",-1)])),_:1,__:[23]})])]),_:1})]),_:1})):I("",!0),H.value?(x(),M(d,{key:4,gutter:20,class:"form-section"},{default:k(()=>[C(t,{span:24},{default:k(()=>[U("div",A,[C(a,null,{default:k(()=>[C(q(c))]),_:1}),l[24]||(l[24]=j(" 其它费用列表 ",-1))])]),_:1}),C(t,{span:24},{default:k(()=>[C(ye,{data:K.otherCostList,border:"",style:{width:"100%"}},{default:k(()=>[C(fe,{prop:"name",label:"名称",width:"200"},{default:k(({row:e})=>[U("span",null,N(e.name),1)]),_:1}),C(fe,{prop:"quantity",label:"数量",width:"120"},{default:k(({row:e})=>[C(h,{modelValue:e.quantity,"onUpdate:modelValue":l=>e.quantity=l,min:0,step:1,style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),C(fe,{prop:"unitPrice",label:"单价",width:"120"},{default:k(({row:e})=>[C(h,{modelValue:e.unitPrice,"onUpdate:modelValue":l=>e.unitPrice=l,min:0,step:.01,precision:2,style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),C(fe,{prop:"totalPrice",label:"总价",width:"120"},{default:k(({row:e})=>[U("span",null,"¥"+N((e.quantity*e.unitPrice).toFixed(2)),1)]),_:1}),C(fe,{prop:"remarks",label:"备注","min-width":"150"},{default:k(({row:e})=>[U("span",null,N(e.remarks),1)]),_:1}),C(fe,{label:"操作",width:"100"},{default:k(({$index:e})=>[C(_e,{type:"danger",size:"small",onClick:l=>{return a=e,void K.otherCostList.splice(a,1);var a}},{default:k(()=>l[25]||(l[25]=[j(" 删除 ",-1)])),_:2,__:[25]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),U("div",B,[C(_e,{type:"primary",onClick:se},{default:k(()=>l[26]||(l[26]=[j("添加费用",-1)])),_:1,__:[26]})])]),_:1})]),_:1})):I("",!0),U("div",E,[C(_e,{type:"primary",size:"large",loading:O.value,onClick:pe},{default:k(()=>[C(a,null,{default:k(()=>[C(q(f))]),_:1}),l[27]||(l[27]=j(" 提交推进 ",-1))]),_:1,__:[27]},8,["loading"]),C(_e,{type:"success",size:"large",onClick:me},{default:k(()=>[C(a,null,{default:k(()=>[C(q(_))]),_:1}),l[28]||(l[28]=j(" 保存草稿 ",-1))]),_:1,__:[28]}),C(_e,{size:"large",onClick:ce},{default:k(()=>[C(a,null,{default:k(()=>[C(q(y))]),_:1}),l[29]||(l[29]=j(" 取消 ",-1))]),_:1,__:[29]})])]),_:1},8,["model"])]),_:1}),C(be,{modelValue:le.value,"onUpdate:modelValue":l[7]||(l[7]=e=>le.value=e),title:"选择物料",width:"800px"},{footer:k(()=>[U("div",G,[C(_e,{onClick:l[6]||(l[6]=e=>le.value=!1)},{default:k(()=>l[31]||(l[31]=[j("取消",-1)])),_:1,__:[31]})])]),default:k(()=>[C(ye,{data:oe.value,border:"",style:{width:"100%"},onRowClick:ue},{default:k(()=>[C(fe,{prop:"companyMaterialCode",label:"公司物料编码",width:"120"}),C(fe,{prop:"materialCode",label:"甲料编码",width:"120"}),C(fe,{prop:"materialName",label:"物料名称",width:"150"}),C(fe,{prop:"model",label:"型号",width:"120"}),C(fe,{prop:"specification",label:"规格",width:"120"}),C(fe,{prop:"unit",label:"单位",width:"80"}),C(fe,{label:"操作",width:"100"},{default:k(({row:e})=>[C(_e,{type:"primary",size:"small",onClick:l=>ue(e)},{default:k(()=>l[30]||(l[30]=[j(" 选择 ",-1)])),_:2,__:[30]},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1},8,["modelValue"]),C(be,{modelValue:ae.value,"onUpdate:modelValue":l[13]||(l[13]=e=>ae.value=e),title:"添加其它费用",width:"500px"},{footer:k(()=>[U("div",Q,[C(_e,{onClick:l[12]||(l[12]=e=>ae.value=!1)},{default:k(()=>l[32]||(l[32]=[j("取消",-1)])),_:1,__:[32]}),C(_e,{type:"primary",onClick:ne},{default:k(()=>l[33]||(l[33]=[j("确定",-1)])),_:1,__:[33]})])]),default:k(()=>[C(ve,{model:ie,rules:re,ref_key:"otherCostFormRef",ref:te,"label-width":"80px"},{default:k(()=>[C(r,{label:"名称:",prop:"name"},{default:k(()=>[C(v,{modelValue:ie.name,"onUpdate:modelValue":l[8]||(l[8]=e=>ie.name=e),placeholder:"请输入费用名称"},null,8,["modelValue"])]),_:1}),C(r,{label:"数量:",prop:"quantity"},{default:k(()=>[C(h,{modelValue:ie.quantity,"onUpdate:modelValue":l[9]||(l[9]=e=>ie.quantity=e),min:0,step:1,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),C(r,{label:"单价:",prop:"unitPrice"},{default:k(()=>[C(h,{modelValue:ie.unitPrice,"onUpdate:modelValue":l[10]||(l[10]=e=>ie.unitPrice=e),min:0,step:.01,precision:2,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),C(r,{label:"总价:"},{default:k(()=>[U("span",null,"¥"+N((ie.quantity*ie.unitPrice).toFixed(2)),1)]),_:1}),C(r,{label:"备注:",prop:"remarks"},{default:k(()=>[C(v,{modelValue:ie.remarks,"onUpdate:modelValue":l[11]||(l[11]=e=>ie.remarks=e),type:"textarea",rows:3,placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-f4f9197c"]]);export{Z as default};
