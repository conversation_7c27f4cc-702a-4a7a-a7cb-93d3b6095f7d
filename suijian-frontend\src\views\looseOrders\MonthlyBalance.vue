<template>
  <div class="monthly-balance">
    <el-card class="balance-info-card">
      <template #header>
        <div class="card-header">
          <span>平账信息</span>
        </div>
      </template>
      
      <el-descriptions :column="2" border class="balance-info">
        <el-descriptions-item label="月份">
          {{ balanceInfo.month }}
        </el-descriptions-item>
        <el-descriptions-item label="人工费">
          ¥{{ formatNumber(balanceInfo.laborCost) }}
        </el-descriptions-item>
        <el-descriptions-item label="超领材料">
          {{ balanceInfo.overIssuedItems }}项，{{ balanceInfo.overIssuedQuantity }}件
        </el-descriptions-item>
        <el-descriptions-item label="师傅手中未退回材料">
          {{ balanceInfo.unreturnedItems }}项，{{ balanceInfo.unreturnedQuantity }}件
        </el-descriptions-item>
        <el-descriptions-item label="平账时间">
          {{ formatDate(balanceInfo.balanceDate) }}
        </el-descriptions-item>
      </el-descriptions>
        </el-card>

    <!-- 选项卡 -->
    <el-card class="tabs-card">
      <template #header>
        <div class="card-header">
          <span>工程费用汇总</span>
        </div>
      </template>
      
      <el-tabs v-model="activeTab" type="border-card" class="project-tabs" @tab-change="handleTabChange">
        <!-- 户内安装工程费用汇总 -->
        <el-tab-pane label="户内安装工程费用汇总" name="project-cost-summary">
          <div v-loading="loadingStates.projectCost" element-loading-text="加载中...">
            <el-table :data="projectCostData" style="width: 100%" border show-summary :summary-method="getProjectCostSummary">
              <el-table-column prop="serialNo" label="序号" width="60" align="center" />
              <el-table-column prop="projectName" label="单项工程名称" width="200" />
              
              <!-- 工程安装人工费 -->
              <el-table-column label="工程安装人工费(元)" align="center">
                <el-table-column prop="preMeterLaborCost" label="表前安装人工费" width="120" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.preMeterLaborCost) }}
                  </template>
                </el-table-column>
                <el-table-column prop="indoorLaborCost" label="户内安装人工费" width="120" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.indoorLaborCost) }}
                  </template>
                </el-table-column>
                <el-table-column prop="laborSubtotal" label="小计(元)" width="120" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.laborSubtotal) }}
                  </template>
                </el-table-column>
              </el-table-column>
              
              <el-table-column prop="installedHouseholds" label="安装户数" width="100" align="center" />
              
              <!-- 实际耗用材料金额 -->
              <el-table-column label="实际耗用材料金额" align="center">
                <el-table-column prop="gasMeterCost" label="气表" width="100" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.gasMeterCost) }}
                  </template>
                </el-table-column>
                <el-table-column prop="postMeterCost" label="表后" width="100" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.postMeterCost) }}
                  </template>
                </el-table-column>
                <el-table-column prop="fittingsCost" label="管件" width="100" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.fittingsCost) }}
                  </template>
                </el-table-column>
                <el-table-column prop="materialSubtotal" label="小计(元)" width="120" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.materialSubtotal) }}
                  </template>
                </el-table-column>
              </el-table-column>
              
              <el-table-column prop="actualReceivedAmount" label="实际领用材料金额(元)" width="150" align="center">
                <template #default="{ row }">
                  ¥{{ formatNumber(row.actualReceivedAmount) }}
                </template>
              </el-table-column>
              
              <el-table-column prop="overReceivedAmount" label="超领甲供材料金额" width="150" align="center">
                <template #default="{ row }">
                  ¥{{ formatNumber(row.overReceivedAmount) }}
                </template>
              </el-table-column>
              
              <el-table-column prop="totalProjectCost" label="工程总造价(元)" width="150" align="center">
                <template #header>
                  <el-tooltip content="计算公式：工程总造价 = 人工费小计 + 实际领用材料金额" placement="top">
                    <span>工程总造价(元)</span>
                  </el-tooltip>
                </template>
                <template #default="{ row }">
                  ¥{{ formatNumber(row.totalProjectCost) }}
                </template>
              </el-table-column>
              
              <el-table-column prop="payableAmount" label="应付施工单位金额" width="150" align="center">
                <template #header>
                  <el-tooltip content="计算公式：应付施工单位金额 = 人工费小计 - 超领甲供材料金额" placement="top">
                    <span>应付施工单位金额</span>
                  </el-tooltip>
                </template>
                <template #default="{ row }">
                  ¥{{ formatNumber(row.payableAmount) }}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <!-- 户内表前甲供材清单表 -->
        <el-tab-pane label="户内表前甲供材清单表" name="pre-meter-materials">
          <el-table :data="preMeterMaterialsData" style="width: 100%" border show-summary :summary-method="getPreMeterMaterialsSummary">
            <el-table-column prop="serialNo" label="序号" width="60" align="center" />
            <el-table-column prop="projectName" label="单项工程名称" width="200" />
            
            <!-- 甲供材料 -->
            <el-table-column label="甲供材料" align="center">
              <!-- 气表 -->
              <el-table-column label="气表" align="center">
                <el-table-column label="机械表接头(个)" align="center">
                  <el-table-column prop="mechanicalConnectorReceived" label="实领" width="80" align="center" />
                  <el-table-column prop="mechanicalConnectorConsumed" label="实耗" width="80" align="center" />
                  <el-table-column prop="mechanicalConnectorPrice" label="单价(元/个)" width="100" align="center">
                    <template #default="{ row }">
                      ¥{{ formatNumber(row.mechanicalConnectorPrice) }}
                    </template>
                  </el-table-column>
                </el-table-column>
                <el-table-column label="千嘉表(块)" align="center">
                  <el-table-column prop="qianjiaMeterReceived" label="实领" width="80" align="center" />
                  <el-table-column prop="qianjiaMeterConsumed" label="实耗" width="80" align="center" />
                  <el-table-column prop="qianjiaMeterPrice" label="单价(元/块)" width="100" align="center">
                    <template #default="{ row }">
                      ¥{{ formatNumber(row.qianjiaMeterPrice) }}
                    </template>
                  </el-table-column>
                </el-table-column>
              </el-table-column>
              
              <!-- 表后 -->
              <el-table-column label="表后" align="center">
                <el-table-column label="低低压调压器(个)" align="center">
                  <el-table-column prop="lowPressureRegulatorReceived" label="实领" width="80" align="center" />
                  <el-table-column prop="lowPressureRegulatorConsumed" label="实耗" width="80" align="center" />
                  <el-table-column prop="lowPressureRegulatorPrice" label="单价(元/个)" width="100" align="center">
                    <template #default="{ row }">
                      ¥{{ formatNumber(row.lowPressureRegulatorPrice) }}
                    </template>
                  </el-table-column>
                </el-table-column>
                <el-table-column label="表箱(个)" align="center">
                  <el-table-column prop="meterBoxReceived" label="实领" width="80" align="center" />
                  <el-table-column prop="meterBoxConsumed" label="实耗" width="80" align="center" />
                  <el-table-column prop="meterBoxPrice" label="单价(元/个)" width="100" align="center">
                    <template #default="{ row }">
                      ¥{{ formatNumber(row.meterBoxPrice) }}
                    </template>
                  </el-table-column>
                </el-table-column>
                <el-table-column label="表前阀(个)" align="center">
                  <el-table-column prop="preMeterValveReceived" label="实领" width="80" align="center" />
                  <el-table-column prop="preMeterValveConsumed" label="实耗" width="80" align="center" />
                  <el-table-column prop="preMeterValvePrice" label="单价(元/个)" width="100" align="center">
                    <template #default="{ row }">
                      ¥{{ formatNumber(row.preMeterValvePrice) }}
                    </template>
                  </el-table-column>
                </el-table-column>
                <el-table-column label="预制短管(米)" align="center">
                  <el-table-column prop="prefabricatedPipeReceived" label="实领" width="80" align="center" />
                  <el-table-column prop="prefabricatedPipeConsumed" label="实耗" width="80" align="center" />
                  <el-table-column prop="prefabricatedPipePrice" label="单价(元/米)" width="100" align="center">
                    <template #default="{ row }">
                      ¥{{ formatNumber(row.prefabricatedPipePrice) }}
                    </template>
                  </el-table-column>
                </el-table-column>
              </el-table-column>
            </el-table-column>
            
            <!-- 表前安装甲供材料金额 -->
            <el-table-column label="表前安装甲供材料金额(元)" align="center">
              <el-table-column prop="preMeterMaterialsReceivedAmount" label="实领" width="120" align="center">
                <template #default="{ row }">
                  ¥{{ formatNumber(row.preMeterMaterialsReceivedAmount) }}
                </template>
              </el-table-column>
              <el-table-column prop="preMeterMaterialsConsumedAmount" label="实耗" width="120" align="center">
                <template #default="{ row }">
                  ¥{{ formatNumber(row.preMeterMaterialsConsumedAmount) }}
                </template>
              </el-table-column>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <!-- 户内甲供材料清单表 -->
        <el-tab-pane label="户内甲供材料清单表" name="indoor-materials">
          <el-table :data="indoorMaterialsData" style="width: 100%" border show-summary :summary-method="getIndoorMaterialsSummary">
            <el-table-column prop="serialNo" label="序号" width="60" align="center" />
            <el-table-column prop="projectName" label="单项工程名称" width="200" />
            
            <!-- 甲供材料-户内 -->
            <el-table-column label="甲供材料-户内" align="center">
              <el-table-column label="灶前阀(个)" align="center">
                <el-table-column prop="stoveFrontValveReceived" label="实领" width="80" align="center" />
                <el-table-column prop="stoveFrontValveConsumed" label="实耗" width="80" align="center" />
                <el-table-column prop="stoveFrontValvePrice" label="单价(元/个)" width="100" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.stoveFrontValvePrice) }}
                  </template>
                </el-table-column>
              </el-table-column>
              
              <el-table-column label="涂覆钢管(米)" align="center">
                <el-table-column prop="coatedSteelPipeReceived" label="实领" width="80" align="center" />
                <el-table-column prop="coatedSteelPipeConsumed" label="实耗" width="80" align="center" />
                <el-table-column prop="coatedSteelPipePrice" label="单价(元/米)" width="100" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.coatedSteelPipePrice) }}
                  </template>
                </el-table-column>
              </el-table-column>
              
              <el-table-column label="波纹管(米)" align="center">
                <el-table-column prop="corrugatedPipeReceived" label="实领" width="80" align="center" />
                <el-table-column prop="corrugatedPipeConsumed" label="实耗" width="80" align="center" />
                <el-table-column prop="corrugatedPipePrice" label="单价(元/米)" width="100" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.corrugatedPipePrice) }}
                  </template>
                </el-table-column>
              </el-table-column>
              
              <el-table-column label="输送波纹管防护钢板(直板)" align="center">
                <el-table-column prop="protectionPlateStraightReceived" label="实领" width="80" align="center" />
                <el-table-column prop="protectionPlateStraightConsumed" label="实耗" width="80" align="center" />
                <el-table-column prop="protectionPlateStraightPrice" label="单价(元/个)" width="100" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.protectionPlateStraightPrice) }}
                  </template>
                </el-table-column>
              </el-table-column>
              
              <el-table-column label="输送波纹管防护钢板(外、侧弯)" align="center">
                <el-table-column prop="protectionPlateBendReceived" label="实领" width="80" align="center" />
                <el-table-column prop="protectionPlateBendConsumed" label="实耗" width="80" align="center" />
                <el-table-column prop="protectionPlateBendPrice" label="单价(元/个)" width="100" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.protectionPlateBendPrice) }}
                  </template>
                </el-table-column>
              </el-table-column>
              
              <el-table-column label="波纹软管(条)(1200mm、1000mm)" align="center">
                <el-table-column prop="corrugatedHoseLongReceived" label="实领" width="80" align="center" />
                <el-table-column prop="corrugatedHoseLongConsumed" label="实耗" width="80" align="center" />
                <el-table-column prop="corrugatedHoseLongPrice" label="单价(元/条)" width="100" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.corrugatedHoseLongPrice) }}
                  </template>
                </el-table-column>
              </el-table-column>
              
              <el-table-column label="波纹软管(条)(800、500mm)" align="center">
                <el-table-column prop="corrugatedHoseShortReceived" label="实领" width="80" align="center" />
                <el-table-column prop="corrugatedHoseShortConsumed" label="实耗" width="80" align="center" />
                <el-table-column prop="corrugatedHoseShortPrice" label="单价(元/条)" width="100" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.corrugatedHoseShortPrice) }}
                  </template>
                </el-table-column>
              </el-table-column>
            </el-table-column>
            
            <!-- 户内安装甲供材料金额 -->
            <el-table-column label="户内安装甲供材料金额(元)" align="center">
              <el-table-column prop="indoorMaterialsReceivedAmount" label="实领-户内" width="120" align="center">
                <template #default="{ row }">
                  ¥{{ formatNumber(row.indoorMaterialsReceivedAmount) }}
                </template>
              </el-table-column>
              <el-table-column prop="indoorMaterialsConsumedAmount" label="实耗-户内" width="120" align="center">
                <template #default="{ row }">
                  ¥{{ formatNumber(row.indoorMaterialsConsumedAmount) }}
                </template>
              </el-table-column>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <!-- 户内结算做销售处理超领材料费用表 -->
        <el-tab-pane label="户内结算做销售处理超领材料费用表" name="over-received-materials">
          <el-table :data="overReceivedMaterialsData" style="width: 100%" border show-summary :summary-method="getOverReceivedMaterialsSummary">
            <el-table-column prop="serialNo" label="序号" width="60" align="center" />
            <el-table-column prop="materialName" label="材料名称" width="200" />
            <el-table-column prop="specification" label="规格" width="150" />
            <el-table-column prop="unit" label="单位" width="80" align="center" />
            
            <!-- 损耗率以内 -->
            <el-table-column prop="overQuantityWithinRate" label="超领量(损耗率以内)" width="150" align="center" />
            <el-table-column prop="unitPriceWithinRate" label="含税材料单价(元)(损耗率以内)" width="180" align="center">
              <template #default="{ row }">
                ¥{{ formatNumber(row.unitPriceWithinRate) }}
              </template>
            </el-table-column>
            
            <!-- 损耗率以外 -->
            <el-table-column prop="overQuantityBeyondRate" label="超领量(损耗率以外)" width="150" align="center" />
            <el-table-column prop="unitPriceBeyondRate" label="含税材料单价(元)(损耗率以外)" width="180" align="center">
              <template #default="{ row }">
                ¥{{ formatNumber(row.unitPriceBeyondRate) }}
              </template>
            </el-table-column>
            
            <el-table-column prop="totalPrice" label="合价(元)" width="120" align="center">
              <template #default="{ row }">
                ¥{{ formatNumber(row.totalPrice) }}
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <!-- 户内管件甲供材料费用表 -->
        <el-tab-pane label="户内管件甲供材料费用表" name="fittings-materials">
          <el-table :data="fittingsMaterialsData" style="width: 100%" border show-summary :summary-method="getFittingsMaterialsSummary">
            <el-table-column prop="serialNo" label="序号" width="60" align="center" />
            <el-table-column prop="projectName" label="单项工程名称" width="150" />
            
            <!-- 甲供材料-户内 -->
            <el-table-column label="甲供材料-户内" align="center">
              <el-table-column label="波纹管快速外螺纹接头(个)" align="center">
                <el-table-column prop="corrugatedConnectorReceived" label="实领" width="80" align="center" />
                <el-table-column prop="corrugatedConnectorConsumed" label="实耗" width="80" align="center" />
                <el-table-column prop="corrugatedConnectorPrice" label="单价(元/个)" width="100" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.corrugatedConnectorPrice) }}
                  </template>
                </el-table-column>
              </el-table-column>
              
              <el-table-column label="镀锌90°弯头(个)" align="center">
                <el-table-column prop="galvanizedElbow90Received" label="实领" width="80" align="center" />
                <el-table-column prop="galvanizedElbow90Consumed" label="实耗" width="80" align="center" />
                <el-table-column prop="galvanizedElbow90Price" label="单价(元/个)" width="100" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.galvanizedElbow90Price) }}
                  </template>
                </el-table-column>
              </el-table-column>
              
              <el-table-column label="镀锌六角外丝(个)" align="center">
                <el-table-column prop="galvanizedHexExternalReceived" label="实领" width="80" align="center" />
                <el-table-column prop="galvanizedHexExternalConsumed" label="实耗" width="80" align="center" />
                <el-table-column prop="galvanizedHexExternalPrice" label="单价(元/个)" width="100" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.galvanizedHexExternalPrice) }}
                  </template>
                </el-table-column>
              </el-table-column>
              
              <el-table-column label="镀锌内丝(个)" align="center">
                <el-table-column prop="galvanizedInternalReceived" label="实领" width="80" align="center" />
                <el-table-column prop="galvanizedInternalConsumed" label="实耗" width="80" align="center" />
                <el-table-column prop="galvanizedInternalPrice" label="单价(元/个)" width="100" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.galvanizedInternalPrice) }}
                  </template>
                </el-table-column>
              </el-table-column>
              
              <el-table-column label="镀锌内外丝弯头(个)" align="center">
                <el-table-column prop="galvanizedInternalExternalElbowReceived" label="实领" width="80" align="center" />
                <el-table-column prop="galvanizedInternalExternalElbowConsumed" label="实耗" width="80" align="center" />
                <el-table-column prop="galvanizedInternalExternalElbowPrice" label="单价(元/个)" width="100" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.galvanizedInternalExternalElbowPrice) }}
                  </template>
                </el-table-column>
              </el-table-column>
              
              <el-table-column label="镀锌三通(个)" align="center">
                <el-table-column prop="galvanizedTeeReceived" label="实领" width="80" align="center" />
                <el-table-column prop="galvanizedTeeConsumed" label="实耗" width="80" align="center" />
                <el-table-column prop="galvanizedTeePrice" label="单价(元/个)" width="100" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.galvanizedTeePrice) }}
                  </template>
                </el-table-column>
              </el-table-column>
              
              <el-table-column label="不锈钢管卡架(钢管用)(个)" align="center">
                <el-table-column prop="stainlessSteelClampReceived" label="实领" width="80" align="center" />
                <el-table-column prop="stainlessSteelClampConsumed" label="实耗" width="80" align="center" />
                <el-table-column prop="stainlessSteelClampPrice" label="单价(元/个)" width="100" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.stainlessSteelClampPrice) }}
                  </template>
                </el-table-column>
              </el-table-column>
              
              <el-table-column label="定制镀锌补芯(个)" align="center">
                <el-table-column prop="customGalvanizedBushingReceived" label="实领" width="80" align="center" />
                <el-table-column prop="customGalvanizedBushingConsumed" label="实耗" width="80" align="center" />
                <el-table-column prop="customGalvanizedBushingPrice" label="单价(元/个)" width="100" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.customGalvanizedBushingPrice) }}
                  </template>
                </el-table-column>
              </el-table-column>
              
              <el-table-column label="表具波纹管(个)" align="center">
                <el-table-column prop="meterCorrugatedPipeReceived" label="实领" width="80" align="center" />
                <el-table-column prop="meterCorrugatedPipeConsumed" label="实耗" width="80" align="center" />
                <el-table-column prop="meterCorrugatedPipePrice" label="单价(元/个)" width="100" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.meterCorrugatedPipePrice) }}
                  </template>
                </el-table-column>
              </el-table-column>
              
              <el-table-column label="燃气表托架(个)" align="center">
                <el-table-column prop="gasMeterBracketReceived" label="实领" width="80" align="center" />
                <el-table-column prop="gasMeterBracketConsumed" label="实耗" width="80" align="center" />
                <el-table-column prop="gasMeterBracketPrice" label="单价(元/个)" width="100" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.gasMeterBracketPrice) }}
                  </template>
                </el-table-column>
              </el-table-column>
              
              <el-table-column label="防盗气锁卡(个)" align="center">
                <el-table-column prop="antiTheftLockClipReceived" label="实领" width="80" align="center" />
                <el-table-column prop="antiTheftLockClipConsumed" label="实耗" width="80" align="center" />
                <el-table-column prop="antiTheftLockClipPrice" label="单价(元/个)" width="100" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.antiTheftLockClipPrice) }}
                  </template>
                </el-table-column>
              </el-table-column>
              
              <el-table-column label="镀锌管堵(个)" align="center">
                <el-table-column prop="galvanizedPipePlugReceived" label="实领" width="80" align="center" />
                <el-table-column prop="galvanizedPipePlugConsumed" label="实耗" width="80" align="center" />
                <el-table-column prop="galvanizedPipePlugPrice" label="单价(元/个)" width="100" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.galvanizedPipePlugPrice) }}
                  </template>
                </el-table-column>
              </el-table-column>
              
              <el-table-column label="定中装饰盖(个)" align="center">
                <el-table-column prop="centeringDecorativeCoverReceived" label="实领" width="80" align="center" />
                <el-table-column prop="centeringDecorativeCoverConsumed" label="实耗" width="80" align="center" />
                <el-table-column prop="centeringDecorativeCoverPrice" label="单价(元/个)" width="100" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.centeringDecorativeCoverPrice) }}
                  </template>
                </el-table-column>
              </el-table-column>
            </el-table-column>
            
            <!-- 户内管件甲供材料金额 -->
            <el-table-column label="户内管件甲供材料金额(元)" align="center">
              <el-table-column prop="totalReceivedAmount" label="实领" width="100" align="center">
                <template #default="{ row }">
                  ¥{{ formatNumber(row.totalReceivedAmount) }}
                </template>
              </el-table-column>
              <el-table-column prop="totalConsumedAmount" label="实耗" width="100" align="center">
                <template #default="{ row }">
                  ¥{{ formatNumber(row.totalConsumedAmount) }}
                </template>
              </el-table-column>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <!-- 户内挂表安装工程管件统计 -->
        <el-tab-pane label="户内挂表安装工程管件统计" name="meter-installation-fittings">
          <el-table :data="meterInstallationFittingsData" style="width: 100%" border show-summary :summary-method="getMeterInstallationSummary">
            <el-table-column prop="serialNo" label="序号" width="60" align="center" />

            <!-- 户主信息 -->
            <el-table-column label="户主信息" align="center">
              <el-table-column prop="customerName" label="姓名" width="100" align="center" />
              <el-table-column prop="customerCode" label="用户编号" width="120" align="center" />
              <el-table-column prop="customerAddress" label="住址" width="200" align="center" />
            </el-table-column>

            <el-table-column prop="dispatchTime" label="派单时间" width="120" align="center" />
            <el-table-column prop="installTime" label="安装时间" width="120" align="center" />

            <!-- 管件统计 -->
            <el-table-column prop="corrugatedQuickConnector" label="波纹管快速外螺纹接头" width="120" align="center" />
            <el-table-column prop="galvanized90Elbow" label="镀锌90°弯头" width="100" align="center" />
            <el-table-column prop="galvanizedHexMaleThread" label="镀锌六角外丝" width="80" align="center" />
            <el-table-column prop="galvanizedFemaleThread" label="镀锌内丝" width="80" align="center" />
            <el-table-column prop="galvanizedMaleFemaleElbow" label="镀锌内外丝弯头" width="100" align="center" />
            <el-table-column prop="galvanizedTee" label="镀锌三通" width="80" align="center" />
            <el-table-column prop="stainlessSteelClamp" label="不锈钢管卡架(钢管用)" width="140" align="center" />
            <el-table-column prop="customGalvanizedBushing" label="定制镀锌补芯" width="100" align="center" />
            <el-table-column prop="meterCorrugatedPipe" label="表具波纹管" width="100" align="center" />
            <el-table-column prop="gasMeterBracket" label="燃气表托架" width="100" align="center" />
            <el-table-column prop="antiTheftGasLock" label="防盗气锁卡" width="100" align="center" />
            <el-table-column prop="galvanizedPlug" label="镀锌管堵" width="100" align="center" />
            <el-table-column prop="protectionPlateStraight" label="输送波纹管防护钢板（直板）" width="180" align="center" />
            <el-table-column prop="protectionPlateBend" label="输送波纹管防护钢板（外、侧弯）" width="200" align="center" />
            <el-table-column prop="decorativeCover" label="定中装饰盖" width="100" align="center" />
            <el-table-column prop="actualMaterialCost" label="实际耗用甲供材料金额-户内（元）" width="200" align="center">
              <template #default="{ row }">
                ¥{{ formatNumber(row.actualMaterialCost) }}
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <!-- 户内零星安装工程管件统计 -->
        <el-tab-pane label="户内零星安装工程管件统计" name="minor-installation-fittings">
          <el-table :data="minorInstallationFittingsData" style="width: 100%" border show-summary :summary-method="getMinorInstallationSummary">
            <el-table-column prop="serialNo" label="序号" width="60" align="center" />

            <!-- 户主信息 -->
            <el-table-column label="户主信息" align="center">
              <el-table-column prop="customerName" label="姓名" width="100" align="center" />
              <el-table-column prop="customerCode" label="编号" width="120" align="center" />
              <el-table-column label="详细地址" align="center">
                <el-table-column prop="fullAddress" label="详细地址 汇总" width="180" align="center" />
                <el-table-column prop="communityName" label="小区名称" width="120" align="center" />
                <el-table-column prop="buildingNo" label="楼橦" width="80" align="center" />
                <el-table-column prop="roomNo" label="房号" width="80" align="center" />
              </el-table-column>
            </el-table-column>

            <el-table-column prop="dispatchDate" label="派单日期" width="120" align="center" />
            <el-table-column prop="constructionDate" label="施工日期" width="120" align="center" />

            <!-- 管件统计 -->
            <el-table-column prop="corrugatedQuickConnector" label="波纹管快速外螺纹接头" width="120" align="center" />
            <el-table-column prop="galvanized90Elbow" label="镀锌90°弯头" width="100" align="center" />
            <el-table-column prop="galvanizedHexMaleThread" label="镀锌六角外丝" width="80" align="center" />
            <el-table-column prop="galvanizedFemaleThread" label="镀锌内丝" width="80" align="center" />
            <el-table-column prop="galvanizedMaleFemaleElbow" label="镀锌内外丝弯头" width="100" align="center" />
            <el-table-column prop="galvanizedTee" label="镀锌三通" width="80" align="center" />
            <el-table-column prop="stainlessSteelClamp" label="不锈钢管卡架(钢管用)" width="140" align="center" />
            <el-table-column prop="customGalvanizedBushing" label="定制镀锌补芯" width="100" align="center" />
            <el-table-column prop="meterCorrugatedPipe" label="表具波纹管" width="100" align="center" />
            <el-table-column prop="gasMeterBracket" label="燃气表托架" width="100" align="center" />
            <el-table-column prop="antiTheftGasLock" label="防盗气锁卡" width="100" align="center" />
            <el-table-column prop="galvanizedPlug" label="镀锌管堵" width="100" align="center" />
            <el-table-column prop="protectionPlateStraight" label="输送波纹管防护钢板（直板）" width="180" align="center" />
            <el-table-column prop="protectionPlateBend" label="输送波纹管防护钢板（外、侧弯）" width="200" align="center" />
            <el-table-column prop="decorativeCover" label="定中装饰盖" width="100" align="center" />
            <el-table-column prop="actualMaterialCost" label="实际耗用甲供材料金额-户内（元）" width="200" align="center">
              <template #default="{ row }">
                ¥{{ formatNumber(row.actualMaterialCost) }}
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <!-- 户内二次安装工程管件统计 -->
        <el-tab-pane label="户内二次安装工程管件统计" name="secondary-installation-fittings">
          <el-table :data="secondaryInstallationFittingsData" style="width: 100%" border show-summary :summary-method="getSecondaryInstallationSummary">
            <el-table-column prop="serialNo" label="序号" width="60" align="center" />

            <!-- 户主信息 -->
            <el-table-column label="户主信息" align="center">
              <el-table-column prop="customerName" label="姓名" width="100" align="center" />
              <el-table-column prop="customerCode" label="编号" width="120" align="center" />
              <el-table-column label="详细地址" align="center">
                <el-table-column prop="communityName" label="小区名称" width="120" align="center" />
                <el-table-column prop="buildingNo" label="楼橦" width="80" align="center" />
                <el-table-column prop="roomNo" label="房号" width="80" align="center" />
              </el-table-column>
            </el-table-column>

            <el-table-column prop="dispatchDate" label="派单日期" width="120" align="center" />
            <el-table-column prop="constructionDate" label="施工日期" width="120" align="center" />

            <!-- 管件统计 -->
            <el-table-column prop="customGalvanizedBushing" label="定制镀锌补芯" width="100" align="center" />
            <el-table-column prop="meterCorrugatedPipe" label="表具波纹管" width="100" align="center" />
            <el-table-column prop="gasMeterBracket" label="燃气表托架" width="100" align="center" />
            <el-table-column prop="antiTheftGasLock" label="防盗气锁卡" width="100" align="center" />
            <el-table-column prop="actualMaterialCost" label="实际耗用甲供材料金额-户内（元）" width="200" align="center">
              <template #default="{ row }">
                ¥{{ formatNumber(row.actualMaterialCost) }}
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <!-- 户内安装管件统计表 -->
        <el-tab-pane label="户内安装管件统计表" name="indoor-installation-fittings">
          <el-table :data="indoorInstallationFittingsData" style="width: 100%" border show-summary :summary-method="getIndoorFittingsSummary">
            <el-table-column prop="serialNo" label="序号" width="60" align="center" />
            <el-table-column prop="projectName" label="单项工程名称" width="200" />
            <el-table-column prop="corrugatedConnector" label="波纹管接头（个）" width="100" align="center" />
            <el-table-column prop="galvanized90Elbow" label="镀锌90°弯头（个）" width="120" align="center" />
            <el-table-column prop="galvanizedHexMaleThread" label="镀锌六角外丝（个）" width="120" align="center" />
            <el-table-column prop="galvanizedFemaleThread" label="镀锌内丝（个）" width="100" align="center" />
            <el-table-column prop="galvanizedMaleFemaleElbow" label="镀锌内外丝弯头（个）" width="140" align="center" />
            <el-table-column prop="galvanizedTee" label="镀锌三通（个）" width="100" align="center" />
            <el-table-column prop="stainlessSteelClamp" label="不锈钢管卡架(钢管用)（个）" width="160" align="center" />
            <el-table-column prop="customGalvanizedBushing" label="定制镀锌补芯（个）" width="120" align="center" />
            <el-table-column prop="corrugatedShortPipe" label="波纹管短管（个）" width="120" align="center" />
            <el-table-column prop="gasMeterBracket" label="燃气表托架（个）" width="120" align="center" />
            <el-table-column prop="antiTheftGasLock" label="防盗气锁卡（个）" width="120" align="center" />
            <el-table-column prop="galvanizedPlug" label="镀锌管堵（个）" width="120" align="center" />
            <el-table-column prop="protectionPlateStraight" label="输送波纹管防护钢板（直板）" width="180" align="center" />
            <el-table-column prop="protectionPlateBend" label="输送波纹管防护钢板（外、侧弯）" width="200" align="center" />
            <el-table-column prop="decorativeCover" label="定中装饰盖（个）" width="120" align="center" />
            <el-table-column prop="actualMaterialCost" label="实际耗用甲供材料金额-户内（元）" width="200" align="center">
              <template #default="{ row }">
                ¥{{ formatNumber(row.actualMaterialCost) }}
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <!-- 户内挂表安装工程决算（半月板） -->
        <el-tab-pane label="户内挂表安装工程决算（半月板）" name="meter-installation-settlement-half">
          <el-table :data="meterInstallationSettlementHalfData" style="width: 100%" border show-summary :summary-method="getMeterSettlementHalfSummary">
            <el-table-column prop="serialNo" label="序号" width="60" align="center" />

            <!-- 户主信息 -->
            <el-table-column label="户主信息" align="center">
              <el-table-column prop="customerName" label="姓名" width="100" align="center" />
              <el-table-column prop="customerCode" label="用户编号" width="120" align="center" />
              <el-table-column label="住址" align="center">
                <el-table-column prop="communityName" label="小区名称" width="120" align="center" />
                <el-table-column prop="buildingNo" label="楼橦" width="80" align="center" />
                <el-table-column prop="roomNo" label="房号" width="80" align="center" />
              </el-table-column>
            </el-table-column>

            <el-table-column prop="dispatchTime" label="派单时间" width="120" align="center" />
            <el-table-column prop="installTime" label="安装时间" width="120" align="center" />

            <!-- 煤气表信息 -->
            <el-table-column label="煤气表信息" align="center">
              <el-table-column prop="hasMeterBox" label="有/无 表箱" width="100" align="center" />
              <el-table-column prop="preMeterValve" label="表前阀" width="80" align="center" />
            </el-table-column>

            <!-- 包干价 -->
            <el-table-column label="包干价（元）" align="center">
              <el-table-column prop="preMeterPrice" label="表前" width="80" align="center">
                <template #default="{ row }">
                  ¥{{ formatNumber(row.preMeterPrice) }}
                </template>
              </el-table-column>
              <el-table-column prop="indoorPrice" label="户内" width="80" align="center">
                <template #default="{ row }">
                  ¥{{ formatNumber(row.indoorPrice) }}
                </template>
              </el-table-column>
            </el-table-column>

            <el-table-column prop="stoveFrontValveQty" label="灶前阀 数量（个）" width="120" align="center" />

            <!-- 表前管道耗材 -->
            <el-table-column label="表前管道耗材" align="center">
              <el-table-column prop="preSteelPipe" label="钢管（米）" width="100" align="center" />
              <el-table-column prop="preCoatedSteelPipe" label="涂覆钢管（米）" width="120" align="center" />
              <el-table-column prop="preCorrugatedPipe" label="波纹管（米）" width="120" align="center" />
            </el-table-column>

            <!-- 户内管道耗材 -->
            <el-table-column label="户内管道耗材" align="center">
              <el-table-column prop="indoorSteelPipe" label="钢管（米）" width="100" align="center" />
              <el-table-column prop="indoorCoatedSteelPipe" label="涂覆钢管（米）" width="120" align="center" />
              <el-table-column prop="indoorCorrugatedPipe" label="波纹管（米）" width="120" align="center" />
            </el-table-column>

            <el-table-column prop="mechanicalMeter" label="机械表" width="80" align="center" />

            <!-- 表前阀 -->
            <el-table-column label="表前阀" align="center">
              <el-table-column prop="lowPressureRegulator" label="低低压调压器（个）" width="140" align="center" />
              <el-table-column prop="corrugatedConnectLong" label="波纹连接管（1.2m,1m）" width="160" align="center" />
              <el-table-column prop="corrugatedConnectShort" label="波纹连接管（0.8m,0.5m）" width="160" align="center" />
              <el-table-column prop="prefabShort12" label="预制短管12cm" width="120" align="center" />
              <el-table-column prop="prefabShort18" label="预制短管18cm" width="120" align="center" />
            </el-table-column>

            <el-table-column prop="preMeterInstallCost" label="表前安装成本（元）" width="140" align="center">
              <template #default="{ row }">
                ¥{{ formatNumber(row.preMeterInstallCost) }}
              </template>
            </el-table-column>
            <el-table-column prop="indoorInstallCost" label="户内安装成本（元）" width="140" align="center">
              <template #default="{ row }">
                ¥{{ formatNumber(row.indoorInstallCost) }}
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <!-- 户内挂表安装工程决算（未半月板） -->
        <el-tab-pane label="户内挂表安装工程决算（未半月板）" name="meter-installation-settlement-no-half">
          <el-table :data="meterInstallationSettlementNoHalfData" style="width: 100%" border show-summary :summary-method="getMeterSettlementNoHalfSummary">
            <el-table-column prop="serialNo" label="序号" width="60" align="center" />

            <!-- 户主信息 -->
            <el-table-column label="户主信息" align="center">
              <el-table-column label="住址 汇总" align="center">
                <el-table-column prop="communityName" label="小区名称" width="120" align="center" />
                <el-table-column prop="buildingNo" label="楼橦" width="80" align="center" />
                <el-table-column prop="roomNo" label="房号" width="80" align="center" />
              </el-table-column>
            </el-table-column>

            <el-table-column prop="dispatchTime" label="派单时间" width="120" align="center" />
            <el-table-column prop="installTime" label="安装时间" width="120" align="center" />

            <!-- 煤气表信息 -->
            <el-table-column label="煤气表信息" align="center">
              <el-table-column prop="hasMeterBox" label="有/无 表箱" width="100" align="center" />
              <el-table-column prop="preMeterValve" label="表前阀" width="80" align="center" />
            </el-table-column>

            <!-- 包干价 -->
            <el-table-column label="包干价（元）" align="center">
              <el-table-column prop="preMeterPrice" label="表前" width="80" align="center">
                <template #default="{ row }">
                  ¥{{ formatNumber(row.preMeterPrice) }}
                </template>
              </el-table-column>
              <el-table-column prop="indoorPrice" label="户内" width="80" align="center">
                <template #default="{ row }">
                  ¥{{ formatNumber(row.indoorPrice) }}
                </template>
              </el-table-column>
            </el-table-column>

            <el-table-column prop="stoveFrontValveQty" label="灶前阀 数量（个）" width="120" align="center" />

            <!-- 表前管道耗材 -->
            <el-table-column label="表前管道耗材" align="center">
              <el-table-column prop="preSteelPipe" label="钢管（米）" width="100" align="center" />
              <el-table-column prop="preCoatedSteelPipe" label="涂覆钢管（米）" width="120" align="center" />
              <el-table-column prop="preCorrugatedPipe" label="波纹管（米）" width="120" align="center" />
            </el-table-column>

            <!-- 户内管道耗材 -->
            <el-table-column label="户内管道耗材" align="center">
              <el-table-column prop="indoorSteelPipe" label="钢管（米）" width="100" align="center" />
              <el-table-column prop="indoorCoatedSteelPipe" label="涂覆钢管（米）" width="120" align="center" />
              <el-table-column prop="indoorCorrugatedPipe" label="波纹管（米）" width="120" align="center" />
            </el-table-column>

            <el-table-column prop="mechanicalMeter" label="机械表" width="80" align="center" />

            <!-- 表前阀 -->
            <el-table-column label="表前阀" align="center">
              <el-table-column prop="lowPressureRegulator" label="低低压调压器（个）" width="140" align="center" />
              <el-table-column prop="corrugatedConnectLong" label="波纹连接管（1.2m,1m）" width="160" align="center" />
              <el-table-column prop="corrugatedConnectShort" label="波纹连接管（0.8m,0.5m）" width="160" align="center" />
              <el-table-column prop="prefabShort12" label="预制短管12cm" width="120" align="center" />
              <el-table-column prop="prefabShort18" label="预制短管18cm" width="120" align="center" />
            </el-table-column>

            <el-table-column prop="preMeterInstallCost" label="表前安装成本（元）" width="140" align="center">
              <template #default="{ row }">
                ¥{{ formatNumber(row.preMeterInstallCost) }}
              </template>
            </el-table-column>
            <el-table-column prop="indoorInstallCost" label="户内安装成本（元）" width="140" align="center">
              <template #default="{ row }">
                ¥{{ formatNumber(row.indoorInstallCost) }}
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <!-- 管道燃气户内零星安装工程决算（半月板） -->
        <el-tab-pane label="管道燃气户内零星安装工程决算（半月板）" name="gas-minor-settlement-half">
          <el-table :data="gasMinorSettlementHalfData" style="width: 100%" border show-summary :summary-method="getGasMinorSettlementHalfSummary">
            <!-- 户主信息 -->
            <el-table-column label="户主信息" align="center">
              <el-table-column prop="customerName" label="姓名" width="100" align="center" />
              <el-table-column prop="customerCode" label="编号" width="120" align="center" />
              <el-table-column label="详细地址" align="center">
                <el-table-column prop="communityName" label="小区名称" width="120" align="center" />
                <el-table-column prop="buildingNo" label="楼橦" width="80" align="center" />
                <el-table-column prop="roomNo" label="房号" width="80" align="center" />
              </el-table-column>
            </el-table-column>

            <el-table-column prop="dispatchDate" label="派单日期" width="120" align="center" />
            <el-table-column prop="constructionDate" label="施工日期" width="120" align="center" />
            <el-table-column prop="firePointQty" label="火点数量（个）" width="120" align="center" />
            <el-table-column prop="stoveFrontValveQty" label="灶前阀数量（个）" width="130" align="center" />

            <!-- 安装项目 -->
            <el-table-column label="安装项目" align="center">
              <el-table-column prop="gasMeterBox" label="燃气表箱(个)" width="120" align="center" />
              <el-table-column prop="preMeterValve" label="表前阀(个)" width="100" align="center" />
              <el-table-column prop="lowPressureRegulator" label="低低压调压器" width="120" align="center" />
              <el-table-column prop="gasMeter" label="燃气表（个）" width="120" align="center" />
            </el-table-column>

            <el-table-column prop="packagePrice" label="包干价（元）" width="120" align="center">
              <template #default="{ row }">
                ¥{{ formatNumber(row.packagePrice) }}
              </template>
            </el-table-column>

            <!-- 管道材料耗用统计 -->
            <el-table-column label="管道材料耗用统计（米）" align="center">
              <el-table-column prop="dn15SteelPipe" label="DN15钢管" width="100" align="center" />
              <el-table-column prop="dn15CoatedSteelPipe" label="DN15涂覆钢管" width="130" align="center" />
              <el-table-column prop="corrugatedPipe" label="波纹管" width="100" align="center" />
            </el-table-column>

            <el-table-column prop="corrugatedConnectStove" label="波纹管连接（灶）" width="140" align="center" />
            <el-table-column prop="corrugatedConnectHeat" label="波纹管连接（热）" width="140" align="center" />
            <el-table-column prop="prefabShort12" label="预制短管12cm" width="120" align="center" />
            <el-table-column prop="prefabShort18" label="预制短管18cm" width="120" align="center" />
            <el-table-column prop="singleHouseholdCost" label="单户户内零星安装工程造价（元）" width="200" align="center">
              <template #default="{ row }">
                ¥{{ formatNumber(row.singleHouseholdCost) }}
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <!-- 管道燃气户内零星安装工程决算（未半月板） -->
        <el-tab-pane label="管道燃气户内零星安装工程决算（未半月板）" name="gas-minor-settlement-no-half">
          <el-table :data="gasMinorSettlementNoHalfData" style="width: 100%" border show-summary :summary-method="getGasMinorSettlementNoHalfSummary">
            <el-table-column prop="serialNo" label="序号" width="60" align="center" />

            <!-- 户主信息 -->
            <el-table-column label="户主信息" align="center">
              <el-table-column prop="customerName" label="姓名" width="100" align="center" />
              <el-table-column prop="customerCode" label="编号" width="120" align="center" />
              <el-table-column label="详细地址" align="center">
                <el-table-column prop="communityName" label="小区名称" width="120" align="center" />
                <el-table-column prop="buildingNo" label="楼橦" width="80" align="center" />
                <el-table-column prop="roomNo" label="房号" width="80" align="center" />
              </el-table-column>
            </el-table-column>

            <el-table-column prop="dispatchDate" label="派单日期" width="120" align="center" />
            <el-table-column prop="constructionDate" label="施工日期" width="120" align="center" />
            <el-table-column prop="firePointQty" label="火点数量（个）" width="120" align="center" />
            <el-table-column prop="stoveFrontValveQty" label="灶前阀数量（个）" width="130" align="center" />

            <!-- 安装项目 -->
            <el-table-column label="安装项目" align="center">
              <el-table-column prop="gasMeterBox" label="燃气表箱(个)" width="120" align="center" />
              <el-table-column prop="preMeterValve" label="表前阀(个)" width="100" align="center" />
              <el-table-column prop="lowPressureRegulator" label="低低压调压器" width="120" align="center" />
              <el-table-column prop="gasMeter" label="燃气表（个）" width="120" align="center" />
            </el-table-column>

            <el-table-column prop="packagePrice" label="包干价（元）" width="120" align="center">
              <template #default="{ row }">
                ¥{{ formatNumber(row.packagePrice) }}
              </template>
            </el-table-column>

            <!-- 管道材料耗用统计 -->
            <el-table-column label="管道材料耗用统计（米）" align="center">
              <el-table-column prop="dn15SteelPipe" label="DN15钢管" width="100" align="center" />
              <el-table-column prop="dn15CoatedSteelPipe" label="DN15涂覆钢管" width="130" align="center" />
              <el-table-column prop="corrugatedPipe" label="波纹管" width="100" align="center" />
            </el-table-column>

            <el-table-column prop="corrugatedConnectStove" label="波纹管连接（灶）" width="140" align="center" />
            <el-table-column prop="corrugatedConnectHeat" label="波纹管连接（热）" width="140" align="center" />
            <el-table-column prop="prefabShort12" label="预制短管12cm" width="120" align="center" />
            <el-table-column prop="prefabShort18" label="预制短管18cm" width="120" align="center" />
            <el-table-column prop="singleHouseholdCost" label="单户户内零星安装工程造价（元）" width="200" align="center">
              <template #default="{ row }">
                ¥{{ formatNumber(row.singleHouseholdCost) }}
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <!-- 管道燃气户内零星安装工程决算（不可用燃气表） -->
        <el-tab-pane label="管道燃气户内零星安装工程决算（不可用燃气表）" name="gas-minor-settlement-no-meter">
          <el-table :data="gasMinorSettlementNoMeterData" style="width: 100%" border show-summary :summary-method="getGasMinorSettlementNoMeterSummary">
            <el-table-column prop="serialNo" label="序号" width="60" align="center" />

            <!-- 户主信息 -->
            <el-table-column label="户主信息" align="center">
              <el-table-column prop="customerName" label="姓名" width="100" align="center" />
              <el-table-column prop="customerCode" label="编号" width="120" align="center" />
              <el-table-column label="详细地址" align="center">
                <el-table-column prop="communityName" label="小区名称" width="120" align="center" />
                <el-table-column prop="buildingNo" label="楼橦" width="80" align="center" />
                <el-table-column prop="roomNo" label="房号" width="80" align="center" />
              </el-table-column>
            </el-table-column>

            <el-table-column prop="dispatchDate" label="派单日期" width="120" align="center" />
            <el-table-column prop="constructionDate" label="施工日期" width="120" align="center" />
            <el-table-column prop="firePointQty" label="火点数量（个）" width="120" align="center" />
            <el-table-column prop="stoveFrontValveQty" label="灶前阀数量（个）" width="130" align="center" />

            <!-- 安装项目 -->
            <el-table-column label="安装项目" align="center">
              <el-table-column prop="gasMeterBox" label="燃气表箱(个)" width="120" align="center" />
              <el-table-column prop="preMeterValve" label="表前阀(个)" width="100" align="center" />
              <el-table-column prop="lowPressureRegulator" label="低低压调压器" width="120" align="center" />
              <el-table-column prop="gasMeter" label="燃气表（个）" width="120" align="center" />
            </el-table-column>

            <el-table-column prop="packagePrice" label="包干价（元）" width="120" align="center">
              <template #default="{ row }">
                ¥{{ formatNumber(row.packagePrice) }}
              </template>
            </el-table-column>

            <!-- 管道材料耗用统计 -->
            <el-table-column label="管道材料耗用统计（米）" align="center">
              <el-table-column prop="dn15SteelPipe" label="DN15钢管" width="100" align="center" />
              <el-table-column prop="dn15CoatedSteelPipe" label="DN15涂覆钢管" width="130" align="center" />
              <el-table-column prop="corrugatedPipe" label="波纹管" width="100" align="center" />
            </el-table-column>

            <el-table-column prop="corrugatedConnectStove" label="波纹管连接（灶）" width="140" align="center" />
            <el-table-column prop="corrugatedConnectHeat" label="波纹管连接（热）" width="140" align="center" />
            <el-table-column prop="prefabShort12" label="预制短管12cm" width="120" align="center" />
            <el-table-column prop="prefabShort18" label="预制短管18cm" width="120" align="center" />
            <el-table-column prop="singleHouseholdCost" label="单户户内零星安装工程造价（元）" width="200" align="center">
              <template #default="{ row }">
                ¥{{ formatNumber(row.singleHouseholdCost) }}
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>


        <!-- 甲供材料领用表 -->
        <el-tab-pane label="甲供材料领用表" name="supplied-materials-receipt">
          <el-table :data="suppliedMaterialsReceiptData" style="width: 100%" border show-summary :summary-method="getSuppliedMaterialsReceiptSummary">
            <el-table-column prop="receiptDate" label="领料日期" width="120" align="center" />
            <el-table-column prop="materialCode" label="物料编码" width="120" align="center" />
            <el-table-column prop="materialName" label="材料名称" width="200" />
            <el-table-column prop="specification" label="型号规格" width="150" />
            <el-table-column prop="unit" label="单位" width="80" align="center" />
            <el-table-column prop="receivedQuantity" label="领料数" width="100" align="center" />
            <el-table-column prop="unitPriceExcludingTax" label="不含税单价" width="120" align="center">
              <template #default="{ row }">
                ¥{{ formatNumber(row.unitPriceExcludingTax) }}
              </template>
            </el-table-column>
            <el-table-column prop="unitPriceIncludingTax" label="含税单价" width="120" align="center">
              <template #default="{ row }">
                ¥{{ formatNumber(row.unitPriceIncludingTax) }}
              </template>
            </el-table-column>
            <el-table-column prop="amountExcludingTax" label="不含税金额" width="120" align="center">
              <template #default="{ row }">
                ¥{{ formatNumber(row.amountExcludingTax) }}
              </template>
            </el-table-column>
            <el-table-column prop="amountIncludingTax" label="含税金额" width="120" align="center">
              <template #default="{ row }">
                ¥{{ formatNumber(row.amountIncludingTax) }}
              </template>
            </el-table-column>
            <el-table-column prop="totalDeductibleAmount" label="总应扣材料金额" width="140" align="center">
              <template #default="{ row }">
                ¥{{ formatNumber(row.totalDeductibleAmount) }}
              </template>
            </el-table-column>
            <el-table-column prop="remarks" label="备注" width="200" />
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <el-button type="primary" @click="handleConfirmBalance">确认平账</el-button>
      <el-button type="success" @click="handleExportReport">导出报表</el-button>
      <el-button type="info" @click="handlePrint">打印</el-button>
      <el-button @click="handleCancel">取消</el-button>
    </div>

    <!-- 平账确认弹窗 -->
    <el-dialog
      v-model="confirmDialogVisible"
      title="确认平账"
      width="500px"
      :before-close="handleCloseConfirmDialog"
    >
      <div class="confirm-content">
        <div class="confirm-item">
          <span class="label">平账月份:</span>
          <span class="value">{{ balanceInfo.month }}</span>
        </div>
        <div class="confirm-item">
          <span class="label">平账时间:</span>
          <span class="value">{{ formatDate(balanceInfo.balanceDate) }}</span>
        </div>
        
        <div class="confirm-section">
          <div class="section-title">平账内容:</div>
          <div class="confirm-item">
            <span class="label">人工费:</span>
            <span class="value">¥{{ formatNumber(balanceInfo.laborCost) }}</span>
          </div>
          <div class="confirm-item">
            <span class="label">超领材料:</span>
            <span class="value">{{ balanceInfo.overIssuedItems }}项，{{ balanceInfo.overIssuedQuantity }}件</span>
          </div>
          <div class="confirm-item">
            <span class="label">师傅手中未退回材料:</span>
            <span class="value">{{ balanceInfo.unreturnedItems }}项，{{ balanceInfo.unreturnedQuantity }}件</span>
          </div>
        </div>
        
        <div class="confirm-warning">
          确认平账后将无法修改，是否继续？
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="confirmDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirmBalanceSubmit">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import request from '../../utils/request'
import { ElMessage, ElMessageBox, ElTooltip } from 'element-plus'

// 平账信息数据
const balanceInfo = reactive({
  month: '2024年1月',
  balanceDate: new Date(),
  laborCost: 54336,
  overIssuedItems: 12,
  overIssuedQuantity: 156,
  unreturnedItems: 8,
  unreturnedQuantity: 89
})

// 选项卡控制
const activeTab = ref('project-cost-summary')

// 数据加载状态
const loadingStates = reactive({
  projectCost: false,
  preMeterMaterials: false,
  indoorMaterials: false,
  overReceivedMaterials: false,
  fittingsMaterials: false,
  meterInstallationFittings: false,
  minorInstallationFittings: false,
  secondaryInstallationFittings: false,
  indoorInstallationFittings: false,
  meterSettlementHalf: false,
  meterSettlementNoHalf: false,
  gasMinorSettlementHalf: false,
  gasMinorSettlementNoHalf: false,
  gasMinorSettlementNoMeter: false,
  suppliedMaterialsReceipt: false
})

// 数据缓存
const dataCache = reactive({
  projectCost: null,
  preMeterMaterials: null,
  indoorMaterials: null,
  overReceivedMaterials: null,
  fittingsMaterials: null,
  meterInstallationFittings: null,
  minorInstallationFittings: null,
  secondaryInstallationFittings: null,
  indoorInstallationFittings: null,
  meterSettlementHalf: null,
  meterSettlementNoHalf: null,
  gasMinorSettlementHalf: null,
  gasMinorSettlementNoHalf: null,
  gasMinorSettlementNoMeter: null,
  suppliedMaterialsReceipt: null
})

// 所有数据变量初始化为空数组
const projectCostData = ref([])
const preMeterMaterialsData = ref([])
const indoorMaterialsData = ref([])
const overReceivedMaterialsData = ref([])
const fittingsMaterialsData = ref([])
const meterInstallationFittingsData = ref([])
const minorInstallationFittingsData = ref([])
const secondaryInstallationFittingsData = ref([])
const indoorInstallationFittingsData = ref([])
const meterInstallationSettlementHalfData = ref([])
const meterInstallationSettlementNoHalfData = ref([])
const gasMinorSettlementHalfData = ref([])
const gasMinorSettlementNoHalfData = ref([])
const gasMinorSettlementNoMeterData = ref([])
const suppliedMaterialsReceiptData = ref([])

// 弹窗控制
const confirmDialogVisible = ref(false)

// 按需加载数据的函数
const loadTableData = async (tableType: string) => {
  // 如果数据已经加载过，直接返回
  if (dataCache[tableType]) {
    return
  }

  // 设置加载状态
  loadingStates[tableType] = true

  try {
    let url = ''
    switch (tableType) {
      case 'projectCost':
        url = '/api/loose-orders/balance/project-cost'
        break
      case 'preMeterMaterials':
        url = '/api/loose-orders/balance/pre-meter-materials'
        break
      case 'indoorMaterials':
        url = '/api/loose-orders/balance/indoor-materials'
        break
      case 'overReceivedMaterials':
        url = '/api/loose-orders/balance/over-received-materials'
        break
      case 'fittingsMaterials':
        url = '/api/loose-orders/balance/fittings-materials'
        break
      case 'meterInstallationFittings':
        url = '/api/loose-orders/balance/meter-installation-fittings'
        break
      case 'minorInstallationFittings':
        url = '/api/loose-orders/balance/minor-installation-fittings'
        break
      case 'secondaryInstallationFittings':
        url = '/api/loose-orders/balance/secondary-installation-fittings'
        break
      case 'indoorInstallationFittings':
        url = '/api/loose-orders/balance/indoor-installation-fittings'
        break
      case 'meterSettlementHalf':
        url = '/api/loose-orders/balance/meter-settlement-half'
        break
      case 'meterSettlementNoHalf':
        url = '/api/loose-orders/balance/meter-settlement-no-half'
        break
      case 'gasMinorSettlementHalf':
        url = '/api/loose-orders/balance/gas-minor-settlement-half'
        break
      case 'gasMinorSettlementNoHalf':
        url = '/api/loose-orders/balance/gas-minor-settlement-no-half'
        break
      case 'gasMinorSettlementNoMeter':
        url = '/api/loose-orders/balance/gas-minor-settlement-no-meter'
        break
      case 'suppliedMaterialsReceipt':
        url = '/api/loose-orders/balance/supplied-materials-receipt'
        break
      default:
        throw new Error(`未知的表格类型: ${tableType}`)
    }

    const result = await request.get(url)
    
    if (result.code === 200) {
      // 缓存数据
      dataCache[tableType] = result.data
      
      // 更新对应的数据变量
      switch (tableType) {
        case 'projectCost':
          projectCostData.value = result.data.list || []
          break
        case 'preMeterMaterials':
          preMeterMaterialsData.value = result.data.list || []
          break
        case 'indoorMaterials':
          indoorMaterialsData.value = result.data.list || []
          break
        case 'overReceivedMaterials':
          overReceivedMaterialsData.value = result.data.list || []
          break
        case 'fittingsMaterials':
          fittingsMaterialsData.value = result.data.list || []
          break
        case 'meterInstallationFittings':
          meterInstallationFittingsData.value = result.data.list || []
          break
        case 'minorInstallationFittings':
          minorInstallationFittingsData.value = result.data.list || []
          break
        case 'secondaryInstallationFittings':
          secondaryInstallationFittingsData.value = result.data.list || []
          break
        case 'indoorInstallationFittings':
          indoorInstallationFittingsData.value = result.data.list || []
          break
        case 'meterSettlementHalf':
          meterInstallationSettlementHalfData.value = result.data.list || []
          break
        case 'meterSettlementNoHalf':
          meterInstallationSettlementNoHalfData.value = result.data.list || []
          break
        case 'gasMinorSettlementHalf':
          gasMinorSettlementHalfData.value = result.data.list || []
          break
        case 'gasMinorSettlementNoHalf':
          gasMinorSettlementNoHalfData.value = result.data.list || []
          break
        case 'gasMinorSettlementNoMeter':
          gasMinorSettlementNoMeterData.value = result.data.list || []
          break
        case 'suppliedMaterialsReceipt':
          suppliedMaterialsReceiptData.value = result.data.list || []
          break
      }
      
      ElMessage.success(`${tableType} 数据加载成功`)
    } else {
      ElMessage.error(`${tableType} 数据加载失败`)
    }
  } catch (error) {
    console.error(`加载 ${tableType} 数据失败:`, error)
    ElMessage.error(`${tableType} 数据加载失败`)
  } finally {
    loadingStates[tableType] = false
  }
}

// 选项卡切换处理函数
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName
  
  // 根据选项卡类型加载对应的数据
  switch (tabName) {
    case 'project-cost-summary':
      loadTableData('projectCost')
      break
    case 'pre-meter-materials':
      loadTableData('preMeterMaterials')
      break
    case 'indoor-materials':
      loadTableData('indoorMaterials')
      break
    case 'over-received-materials':
      loadTableData('overReceivedMaterials')
      break
    case 'fittings-materials':
      loadTableData('fittingsMaterials')
      break
    case 'meter-installation-fittings':
      loadTableData('meterInstallationFittings')
      break
    case 'minor-installation-fittings':
      loadTableData('minorInstallationFittings')
      break
    case 'secondary-installation-fittings':
      loadTableData('secondaryInstallationFittings')
      break
    case 'indoor-installation-fittings':
      loadTableData('indoorInstallationFittings')
      break
    case 'meter-settlement-half':
      loadTableData('meterSettlementHalf')
      break
    case 'meter-settlement-no-half':
      loadTableData('meterSettlementNoHalf')
      break
    case 'gas-minor-settlement-half':
      loadTableData('gasMinorSettlementHalf')
      break
    case 'gas-minor-settlement-no-half':
      loadTableData('gasMinorSettlementNoHalf')
      break
    case 'gas-minor-settlement-no-meter':
      loadTableData('gasMinorSettlementNoMeter')
      break
    case 'supplied-materials-receipt':
      loadTableData('suppliedMaterialsReceipt')
      break
  }
}

// 加载月度平账基础数据
const loadMonthlyBalanceData = async () => {
  try {
    const result = await request.get('/api/loose-orders/balance/month')
    
    if (result.code === 200) {
      const data = result.data
      
      // 更新平账信息
      if (data.balanceInfo) {
        Object.assign(balanceInfo, data.balanceInfo)
        if (data.balanceInfo.balanceDate) {
          balanceInfo.balanceDate = new Date(data.balanceInfo.balanceDate)
        }
      }
      
      // 加载默认选项卡的数据（工程费用汇总）
      await loadTableData('projectCost')
      
      ElMessage.success('基础数据加载成功')
    } else {
      ElMessage.error('基础数据加载失败')
    }
  } catch (error) {
    console.error('加载月度平账基础数据失败:', error)
    ElMessage.error('基础数据加载失败')
  }
}

// 确认平账提交
const handleConfirmBalanceSubmit = async () => {
  try {
    const result = await request.post('/api/loose-orders/balance/confirm', {
      month: balanceInfo.month,
      balanceDate: balanceInfo.balanceDate,
      laborCost: balanceInfo.laborCost,
      overIssuedItems: balanceInfo.overIssuedItems,
      overIssuedQuantity: balanceInfo.overIssuedQuantity,
      unreturnedItems: balanceInfo.unreturnedItems,
      unreturnedQuantity: balanceInfo.unreturnedQuantity
    })
    
    if (result.code === 200) {
      ElMessage.success('平账确认成功')
      confirmDialogVisible.value = false
    } else {
      ElMessage.error('平账确认失败')
    }
  } catch (error) {
    console.error('平账确认失败:', error)
    ElMessage.error('平账确认失败')
  }
}

// 打开确认弹窗
const handleConfirmBalance = () => {
  confirmDialogVisible.value = true
}

// 关闭确认弹窗
const handleCloseConfirmDialog = () => {
  confirmDialogVisible.value = false
}

// 打印功能
const handlePrint = () => {
  window.print()
}

// 取消操作
const handleCancel = () => {
  ElMessageBox.confirm('确定要取消当前操作吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.info('已取消操作')
  }).catch(() => {
    // 用户取消
  })
}

// 数字格式化函数
const formatNumber = (num: any) => {
  if (num === undefined || num === null || isNaN(num) || num === '') {
    return '0'
  }
  return Number(num).toLocaleString()
}

// 日期格式化函数
const formatDate = (date: any) => {
  if (!date || date === undefined || date === null) return ''
  try {
    const dateObj = new Date(date)
    if (isNaN(dateObj.getTime())) return ''
    return dateObj.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  } catch (error) {
    return ''
  }
}

// 表格合计方法
const getProjectCostSummary = (param: any) => {
  const { columns } = param
  const sums: any = []
  
  // 如果有缓存的合计数据，使用服务器返回的合计
  if (dataCache.projectCost && dataCache.projectCost.summary) {
    const summary = dataCache.projectCost.summary
    columns.forEach((column: any, index: number) => {
      if (index === 0) {
        sums[index] = '合计'
        return
      }
      if (index === 1) {
        sums[index] = ''
        return
      }
      
      const property = column.property
      if (summary[property] !== undefined) {
        if (property === 'preMeterLaborCost' || property === 'indoorLaborCost' || 
            property === 'laborSubtotal' || property === 'gasMeterCost' || 
            property === 'postMeterCost' || property === 'fittingsCost' || 
            property === 'materialSubtotal' || property === 'actualReceivedAmount' || 
            property === 'overReceivedAmount' || property === 'totalProjectCost' || 
            property === 'payableAmount') {
          sums[index] = `¥${formatNumber(summary[property])}`
        } else {
          sums[index] = formatNumber(summary[property])
        }
      } else {
        sums[index] = ''
      }
    })
  } else {
    // 如果没有缓存数据，使用本地计算（兼容性）
    columns.forEach((column: any, index: number) => {
      if (index === 0) {
        sums[index] = '合计'
        return
      }
      if (index === 1) {
        sums[index] = ''
        return
      }
      const values = projectCostData.value.map((item: any) => {
        const value = item[column.property]
        return value !== undefined && value !== null ? Number(value) : 0
      })
      if (!values.every((value: number) => isNaN(value))) {
        const total = values.reduce((prev: number, curr: number) => {
          const value = Number(curr)
          if (!isNaN(value)) {
            return prev + curr
          } else {
            return prev
          }
        }, 0)
        
        if (column.property === 'preMeterLaborCost' || column.property === 'indoorLaborCost' || 
            column.property === 'laborSubtotal' || column.property === 'gasMeterCost' || 
            column.property === 'postMeterCost' || column.property === 'fittingsCost' || 
            column.property === 'materialSubtotal' || column.property === 'actualReceivedAmount' || 
            column.property === 'overReceivedAmount' || column.property === 'totalProjectCost' || 
            column.property === 'payableAmount') {
          sums[index] = `¥${formatNumber(total)}`
        } else {
          sums[index] = formatNumber(total)
        }
      } else {
        sums[index] = ''
      }
    })
  }
  
  return sums
}

// 其他表格的合计方法（简化版本，使用服务器返回的合计数据）
const getTableSummary = (tableType: string) => {
  return (param: any) => {
    const { columns } = param
    const sums: any = []
    
    // 如果有缓存的合计数据，使用服务器返回的合计
    if (dataCache[tableType] && dataCache[tableType].summary) {
      const summary = dataCache[tableType].summary
      columns.forEach((column: any, index: number) => {
        if (index === 0) {
          sums[index] = '合计'
          return
        }
        
        const property = column.property
        if (summary[property] !== undefined) {
          // 检查是否为金额字段
          if (property.includes('Amount') || property.includes('Cost') || property.includes('Price')) {
            sums[index] = `¥${formatNumber(summary[property])}`
          } else {
            sums[index] = formatNumber(summary[property])
          }
        } else {
          sums[index] = ''
        }
      })
    } else {
      // 如果没有缓存数据，显示空合计
      columns.forEach((column: any, index: number) => {
        if (index === 0) {
          sums[index] = '合计'
        } else {
          sums[index] = ''
        }
      })
    }
    
    return sums
  }
}

// 表格汇总方法
const getPreMeterMaterialsSummary = (param: any) => {
  const { columns, data } = param
  const sums: string[] = []
  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }
    const values = data.map((item: any) => Number(item[column.property]))
    if (!values.every((value: any) => Number.isNaN(value))) {
      sums[index] = values.reduce((prev: number, curr: number) => {
        const value = Number(curr)
        if (!Number.isNaN(value)) {
          return prev + curr
        } else {
          return prev
        }
      }, 0).toFixed(2)
    } else {
      sums[index] = ''
    }
  })
  return sums
}

const getIndoorMaterialsSummary = (param: any) => {
  return getPreMeterMaterialsSummary(param)
}

const getOverReceivedMaterialsSummary = (param: any) => {
  return getPreMeterMaterialsSummary(param)
}

const getFittingsMaterialsSummary = (param: any) => {
  return getPreMeterMaterialsSummary(param)
}

const getMeterInstallationSummary = (param: any) => {
  return getPreMeterMaterialsSummary(param)
}

const getMinorInstallationSummary = (param: any) => {
  return getPreMeterMaterialsSummary(param)
}

const getSecondaryInstallationSummary = (param: any) => {
  return getPreMeterMaterialsSummary(param)
}

const getIndoorFittingsSummary = (param: any) => {
  return getPreMeterMaterialsSummary(param)
}

const getMeterSettlementHalfSummary = (param: any) => {
  return getPreMeterMaterialsSummary(param)
}

const getMeterSettlementNoHalfSummary = (param: any) => {
  return getPreMeterMaterialsSummary(param)
}

const getGasMinorSettlementHalfSummary = (param: any) => {
  return getPreMeterMaterialsSummary(param)
}

const getGasMinorSettlementNoHalfSummary = (param: any) => {
  return getPreMeterMaterialsSummary(param)
}

const getGasMinorSettlementNoMeterSummary = (param: any) => {
  return getPreMeterMaterialsSummary(param)
}

const getSuppliedMaterialsReceiptSummary = (param: any) => {
  return getPreMeterMaterialsSummary(param)
}

// 导出报表
const handleExportReport = () => {
  ElMessage.success('报表导出功能开发中...')
}

// 页面初始化
onMounted(() => {
  loadMonthlyBalanceData()
})
</script>

<style scoped>
.monthly-balance {
  padding: 20px;
}

.balance-info-card,
.tabs-card {
  margin-bottom: 20px;
}

.project-tabs {
  margin-top: 10px;
}

.project-tabs .el-tabs__content {
  padding: 20px 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.balance-info {
  margin-top: 10px;
}



.action-buttons {
  text-align: center;
  margin-top: 20px;
  padding: 20px;
}

.action-buttons .el-button {
  margin: 0 10px;
}

.confirm-content {
  padding: 10px 0;
}

.confirm-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  padding: 5px 0;
}

.confirm-item .label {
  font-weight: bold;
  color: #606266;
}

.confirm-item .value {
  color: #303133;
}

.confirm-section {
  margin: 20px 0;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.section-title {
  font-weight: bold;
  margin-bottom: 10px;
  color: #303133;
}

.confirm-warning {
  margin-top: 20px;
  padding: 10px;
  background-color: #fdf6ec;
  border: 1px solid #f5dab1;
  border-radius: 4px;
  color: #e6a23c;
  text-align: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style> 