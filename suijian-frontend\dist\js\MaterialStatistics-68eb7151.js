import{E as e,r as a,g as t}from"./element-plus-ad78a7bf.js";import{l as i,_ as l,r as n,c,q as r,y as o,R as s,J as u,z as p,av as d,x as g,P as m,u as v,O as y}from"./vue-vendor-fc5a6493.js";import{_ as f}from"./_plugin-vue_export-helper-1b428a4d.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const P={class:"material-statistics"},h={class:"summary-content"},_={class:"summary-item"},z={class:"value"},w={class:"amount"},b={class:"card-header"},S={class:"header-actions"},q={class:"statistics-summary"},C={class:"material-icon"},k={class:"pagination-section"},D={class:"statistics-summary"},N={class:"material-icon"},j={class:"pagination-section"},x={class:"action-buttons"},L=f(i({__name:"MaterialStatistics",setup(i){const f=l({currentPage:1,pageSize:10}),L=l({currentPage:1,pageSize:10}),F=n([{id:1,name:"燃气表",icon:"🔧",model:"G2.5",specification:"DN20",quantity:50,unit:"个",unitPrice:300,totalPrice:15e3,category:"meter"},{id:2,name:"镀锌管件",icon:"🔩",model:"ZG-001",specification:"20*1.5",quantity:200,unit:"个",unitPrice:62.5,totalPrice:12500,category:"fitting"},{id:3,name:"波纹管",icon:"⚡",model:"BW-15",specification:"DN15*500",quantity:150,unit:"m",unitPrice:69.33,totalPrice:10400,category:"pipe"},{id:4,name:"连接件",icon:"🔗",model:"LJ-20",specification:"20mm",quantity:80,unit:"个",unitPrice:102.5,totalPrice:8200,category:"connector"},{id:9,name:"阀门",icon:"🚰",model:"FM-25",specification:"DN25",quantity:30,unit:"个",unitPrice:150,totalPrice:4500,category:"valve"},{id:10,name:"法兰",icon:"⭕",model:"FL-32",specification:"DN32",quantity:25,unit:"个",unitPrice:80,totalPrice:2e3,category:"flange"}]),G=n([{id:5,name:"燃气表",icon:"🔧",model:"G2.5",specification:"DN20",quantity:30,unit:"个",unitPrice:300,totalPrice:9e3,category:"meter"},{id:6,name:"镀锌管件",icon:"🔩",model:"ZG-001",specification:"20*1.5",quantity:120,unit:"个",unitPrice:62.5,totalPrice:7500,category:"fitting"},{id:7,name:"波纹管",icon:"⚡",model:"BW-15",specification:"DN15*500",quantity:100,unit:"m",unitPrice:68,totalPrice:6800,category:"pipe"},{id:8,name:"连接件",icon:"🔗",model:"LJ-20",specification:"20mm",quantity:60,unit:"个",unitPrice:100,totalPrice:6e3,category:"connector"},{id:11,name:"阀门",icon:"🚰",model:"FM-25",specification:"DN25",quantity:20,unit:"个",unitPrice:150,totalPrice:3e3,category:"valve"},{id:12,name:"法兰",icon:"⭕",model:"FL-32",specification:"DN32",quantity:15,unit:"个",unitPrice:80,totalPrice:1200,category:"flange"}]),M=c(()=>F.value.length+G.value.length),U=c(()=>J.value+B.value),J=c(()=>F.value.reduce((e,a)=>e+a.totalPrice,0)),B=c(()=>G.value.reduce((e,a)=>e+a.totalPrice,0)),W=c(()=>F.value.reduce((e,a)=>e+a.quantity,0)),Z=c(()=>G.value.reduce((e,a)=>e+a.quantity,0)),E=c(()=>{const e=(f.currentPage-1)*f.pageSize,a=e+f.pageSize;return F.value.slice(e,a)}),I=c(()=>{const e=(L.currentPage-1)*L.pageSize,a=e+L.pageSize;return G.value.slice(e,a)}),O=e=>e.toLocaleString(),R=()=>{e.success("数据已刷新")},$=()=>{e.success("数据导出成功")},A=a=>{e.info(`查看 ${a.name} 的详细信息`)},H=()=>{e.info("跳转到详细报表页面")},K=e=>{f.pageSize=e,f.currentPage=1},Q=e=>{f.currentPage=e},T=e=>{L.pageSize=e,L.currentPage=1},V=e=>{L.currentPage=e};return r(()=>{}),(e,i)=>{const l=d("el-card"),n=d("el-icon"),c=d("el-button"),r=d("el-table-column"),X=d("el-table"),Y=d("el-pagination");return g(),o("div",P,[s(l,{class:"summary-card",shadow:"hover"},{header:u(()=>i[4]||(i[4]=[p("div",{class:"card-header"},[p("span",null,"📊 总体统计")],-1)])),default:u(()=>[p("div",h,[p("div",_,[i[5]||(i[5]=p("span",{class:"label"},"📦 已领出物料总计:",-1)),p("span",z,m(M.value)+"项",1),p("span",w,"总价: ¥"+m(O(U.value)),1)])])]),_:1}),s(l,{class:"statistics-card",shadow:"hover"},{header:u(()=>[p("div",b,[i[8]||(i[8]=p("span",null,"🔴 未使用物料统计",-1)),p("div",S,[s(c,{type:"primary",size:"small",onClick:R},{default:u(()=>[s(n,null,{default:u(()=>[s(v(a))]),_:1}),i[6]||(i[6]=y(" 刷新数据 ",-1))]),_:1,__:[6]}),s(c,{type:"success",size:"small",onClick:$},{default:u(()=>[s(n,null,{default:u(()=>[s(v(t))]),_:1}),i[7]||(i[7]=y(" 导出统计 ",-1))]),_:1,__:[7]})])])]),default:u(()=>[p("div",q,[p("span",null,m(F.value.length)+"项，数量: "+m(O(W.value)),1),p("span",null,"总价: ¥"+m(O(J.value)),1)]),s(X,{data:E.value,style:{width:"100%"},border:""},{default:u(()=>[s(r,{prop:"icon",label:"",width:"50",align:"center"},{default:u(({row:e})=>[p("span",C,m(e.icon),1)]),_:1}),s(r,{prop:"name",label:"名称分类",width:"150"}),s(r,{prop:"model",label:"型号",width:"120"}),s(r,{prop:"specification",label:"规格",width:"120"}),s(r,{prop:"unit",label:"单位",width:"80",align:"center"}),s(r,{prop:"quantity",label:"数量",width:"100",align:"center"}),s(r,{prop:"totalPrice",label:"总价",width:"120",align:"center"},{default:u(({row:e})=>[y(" ¥"+m(O(e.totalPrice)),1)]),_:1}),s(r,{label:"操作",width:"120",align:"center"},{default:u(({row:e})=>[s(c,{type:"primary",size:"small",onClick:a=>A(e)},{default:u(()=>i[9]||(i[9]=[y(" 详情 ",-1)])),_:2,__:[9]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),p("div",k,[s(Y,{"current-page":f.currentPage,"onUpdate:currentPage":i[0]||(i[0]=e=>f.currentPage=e),"page-size":f.pageSize,"onUpdate:pageSize":i[1]||(i[1]=e=>f.pageSize=e),"page-sizes":[10,20,50,100],total:F.value.length,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:K,onCurrentChange:Q},null,8,["current-page","page-size","total"])])]),_:1}),s(l,{class:"statistics-card",shadow:"hover"},{header:u(()=>i[10]||(i[10]=[p("div",{class:"card-header"},[p("span",null,"✅ 已使用物料统计")],-1)])),default:u(()=>[p("div",D,[p("span",null,m(G.value.length)+"项，数量: "+m(O(Z.value)),1),p("span",null,"总价: ¥"+m(O(B.value)),1)]),s(X,{data:I.value,style:{width:"100%"},border:""},{default:u(()=>[s(r,{prop:"icon",label:"",width:"50",align:"center"},{default:u(({row:e})=>[p("span",N,m(e.icon),1)]),_:1}),s(r,{prop:"name",label:"名称分类",width:"150"}),s(r,{prop:"model",label:"型号",width:"120"}),s(r,{prop:"specification",label:"规格",width:"120"}),s(r,{prop:"unit",label:"单位",width:"80",align:"center"}),s(r,{prop:"quantity",label:"数量",width:"100",align:"center"}),s(r,{prop:"totalPrice",label:"总价",width:"120",align:"center"},{default:u(({row:e})=>[y(" ¥"+m(O(e.totalPrice)),1)]),_:1}),s(r,{label:"操作",width:"120",align:"center"},{default:u(({row:e})=>[s(c,{type:"primary",size:"small",onClick:a=>A(e)},{default:u(()=>i[11]||(i[11]=[y(" 详情 ",-1)])),_:2,__:[11]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),p("div",j,[s(Y,{"current-page":L.currentPage,"onUpdate:currentPage":i[2]||(i[2]=e=>L.currentPage=e),"page-size":L.pageSize,"onUpdate:pageSize":i[3]||(i[3]=e=>L.pageSize=e),"page-sizes":[10,20,50,100],total:G.value.length,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:T,onCurrentChange:V},null,8,["current-page","page-size","total"])])]),_:1}),p("div",x,[s(c,{type:"primary",size:"large",onClick:H},{default:u(()=>i[12]||(i[12]=[y(" 📈 查看详细报表 ",-1)])),_:1,__:[12]}),s(c,{type:"default",size:"large",onClick:R},{default:u(()=>i[13]||(i[13]=[y(" 🔄 刷新数据 ",-1)])),_:1,__:[13]}),s(c,{type:"success",size:"large",onClick:$},{default:u(()=>i[14]||(i[14]=[y(" 📊 导出统计 ",-1)])),_:1,__:[14]})])])}}}),[["__scopeId","data-v-d4713fb3"]]);export{L as default};
