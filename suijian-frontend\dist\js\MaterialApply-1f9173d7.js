var e=Object.defineProperty,l=Object.defineProperties,a=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable,u=(l,a,t)=>a in l?e(l,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):l[a]=t,p=(e,l,a)=>new Promise((t,r)=>{var o=e=>{try{p(a.next(e))}catch(l){r(l)}},u=e=>{try{p(a.throw(e))}catch(l){r(l)}},p=e=>e.done?t(e.value):Promise.resolve(e.value).then(o,u);p((a=a.apply(e,l)).next())});import{E as d}from"./element-plus-7917fd46.js";import{l as i,_ as n,r as s,c,q as m,y as v,z as b,R as f,J as y,P as _,av as h,x as w,Q as g,aa as D,O as V,I as k}from"./vue-vendor-fc5a6493.js";import{_ as x}from"./_plugin-vue_export-helper-1b428a4d.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const j={class:"page-container"},N={class:"form-container"},O={class:"material-list"},q={class:"list-header"},P={class:"list-footer"},U={class:"total-info"},C={class:"total-amount"},S={class:"form-actions"},M={class:"material-search"},Q=x(i({__name:"MaterialApply",setup(e){const i=n({applyNo:"",applyDate:new Date,applicant:"",orderNo:"",receiver:"",remark:""}),x=s([{value:"张三",label:"张三 - 工程部"},{value:"李四",label:"李四 - 技术部"},{value:"王五",label:"王五 - 施工部"},{value:"赵六",label:"赵六 - 工程部"},{value:"钱七",label:"钱七 - 技术部"}]),Q=s([{value:"张三",label:"张三 - 工程部"},{value:"李四",label:"李四 - 技术部"},{value:"王五",label:"王五 - 施工部"},{value:"赵六",label:"赵六 - 工程部"},{value:"钱七",label:"钱七 - 技术部"}]),$=s([{value:"DD001",label:"DD001 - 某小区燃气安装工程"},{value:"DD002",label:"DD002 - 某商场燃气管道改造"},{value:"DD003",label:"DD003 - 某工厂燃气设备安装"},{value:"DD004",label:"DD004 - 某住宅楼燃气入户工程"},{value:"DD005",label:"DD005 - 某商业街燃气管道铺设"}]),z={applyDate:[{required:!0,message:"请选择申请日期",trigger:"change"}],applicant:[{required:!0,message:"请选择申请人",trigger:"change"}],receiver:[{required:!0,message:"请选择领料人",trigger:"change"}]},F=s([]),I=s(!1),L=s(""),R=s([]),Y=c(()=>F.value.reduce((e,l)=>e+l.price*l.quantity,0)),E=()=>{const e=new Date,l=e.getFullYear(),a=String(e.getMonth()+1).padStart(2,"0"),t=String(e.getDate()).padStart(2,"0"),r=Math.floor(1e3*Math.random()).toString().padStart(3,"0");i.applyNo=`LY${l}${a}${t}${r}`},J=()=>{I.value=!0,B()},W=()=>{B()},A=e=>{var p;F.value.find(l=>l.id===e.id)?d.warning("该物料已添加"):(F.value.push((p=((e,l)=>{for(var a in l||(l={}))r.call(l,a)&&u(e,a,l[a]);if(t)for(var a of t(l))o.call(l,a)&&u(e,a,l[a]);return e})({},e),l(p,a({quantity:1})))),I.value=!1)},B=()=>p(this,null,function*(){yield new Promise(e=>setTimeout(e,500)),R.value=[{id:1,code:"WL001",name:"电缆线",model:"YJV-3*4",specification:"3*4mm²",unit:"米",price:30,stockQuantity:500},{id:2,code:"WL002",name:"电线",model:"BV-2.5",specification:"2.5mm²",unit:"米",price:15,stockQuantity:800}]}),T=()=>p(this,null,function*(){0!==F.value.length?d.success("保存成功"):d.warning("请至少添加一个物料")}),G=()=>p(this,null,function*(){yield T(),d.success("打印功能待实现")}),H=()=>{Object.assign(i,{applyNo:"",applyDate:new Date,applicant:"",orderNo:"",receiver:"",remark:""}),F.value=[],E()};return m(()=>{E()}),(e,l)=>{const a=h("el-input"),t=h("el-form-item"),r=h("el-col"),o=h("el-date-picker"),u=h("el-row"),p=h("el-option"),d=h("el-select"),n=h("el-form"),s=h("el-button"),c=h("el-table-column"),m=h("el-input-number"),E=h("el-table"),B=h("el-dialog");return w(),v("div",j,[b("div",N,[f(n,{ref:"formRef",model:i,rules:z,"label-width":"120px"},{default:y(()=>[f(u,{gutter:20},{default:y(()=>[f(r,{span:12},{default:y(()=>[f(t,{label:"申请单号",prop:"applyNo"},{default:y(()=>[f(a,{modelValue:i.applyNo,"onUpdate:modelValue":l[0]||(l[0]=e=>i.applyNo=e),placeholder:"系统自动生成",disabled:""},null,8,["modelValue"])]),_:1})]),_:1}),f(r,{span:12},{default:y(()=>[f(t,{label:"申请日期",prop:"applyDate"},{default:y(()=>[f(o,{modelValue:i.applyDate,"onUpdate:modelValue":l[1]||(l[1]=e=>i.applyDate=e),type:"date",placeholder:"选择申请日期",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),f(u,{gutter:20},{default:y(()=>[f(r,{span:12},{default:y(()=>[f(t,{label:"申请人",prop:"applicant"},{default:y(()=>[f(d,{modelValue:i.applicant,"onUpdate:modelValue":l[2]||(l[2]=e=>i.applicant=e),placeholder:"请选择申请人",filterable:"",style:{width:"100%"}},{default:y(()=>[(w(!0),v(g,null,D(x.value,e=>(w(),k(p,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),f(r,{span:12},{default:y(()=>[f(t,{label:"领料人",prop:"receiver"},{default:y(()=>[f(d,{modelValue:i.receiver,"onUpdate:modelValue":l[3]||(l[3]=e=>i.receiver=e),placeholder:"请选择领料人",filterable:"",style:{width:"100%"}},{default:y(()=>[(w(!0),v(g,null,D(Q.value,e=>(w(),k(p,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),f(u,{gutter:20},{default:y(()=>[f(r,{span:12},{default:y(()=>[f(t,{label:"关联订单",prop:"orderNo"},{default:y(()=>[f(d,{modelValue:i.orderNo,"onUpdate:modelValue":l[4]||(l[4]=e=>i.orderNo=e),placeholder:"请选择关联订单",filterable:"",style:{width:"100%"}},{default:y(()=>[(w(!0),v(g,null,D($.value,e=>(w(),k(p,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),f(t,{label:"备注",prop:"remark"},{default:y(()=>[f(a,{modelValue:i.remark,"onUpdate:modelValue":l[5]||(l[5]=e=>i.remark=e),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"]),b("div",O,[b("div",q,[l[10]||(l[10]=b("h3",null,"物料清单",-1)),f(s,{type:"primary",onClick:J},{default:y(()=>l[9]||(l[9]=[V("添加物料",-1)])),_:1,__:[9]})]),f(E,{data:F.value,border:"",stripe:""},{default:y(()=>[f(c,{type:"index",label:"序号",width:"60"}),f(c,{prop:"code",label:"物料编码",width:"150"}),f(c,{prop:"name",label:"物料名称",width:"200"}),f(c,{prop:"model",label:"型号",width:"120"}),f(c,{prop:"specification",label:"规格",width:"150"}),f(c,{prop:"unit",label:"单位",width:"80"}),f(c,{prop:"price",label:"单价",width:"100"},{default:y(({row:e})=>[V(" ¥"+_(e.price),1)]),_:1}),f(c,{prop:"quantity",label:"申请数量",width:"120"},{default:y(({row:e})=>[f(m,{modelValue:e.quantity,"onUpdate:modelValue":l=>e.quantity=l,min:1,max:e.stockQuantity,size:"small",style:{width:"80px"}},null,8,["modelValue","onUpdate:modelValue","max"])]),_:1}),f(c,{prop:"totalPrice",label:"小计",width:"100"},{default:y(({row:e})=>[V(" ¥"+_((e.price*e.quantity).toFixed(2)),1)]),_:1}),f(c,{label:"操作",width:"100"},{default:y(({$index:e})=>[f(s,{type:"danger",size:"small",onClick:l=>{return a=e,void F.value.splice(a,1);var a}},{default:y(()=>l[11]||(l[11]=[V(" 删除 ",-1)])),_:2,__:[11]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),b("div",P,[b("div",U,[b("span",null,"物料总数："+_(F.value.length)+" 项",1),b("span",C,"总金额：¥"+_(Y.value.toFixed(2)),1)])])]),b("div",S,[f(s,{onClick:H},{default:y(()=>l[12]||(l[12]=[V("重置",-1)])),_:1,__:[12]}),f(s,{type:"primary",onClick:T},{default:y(()=>l[13]||(l[13]=[V("保存",-1)])),_:1,__:[13]}),f(s,{type:"success",onClick:G},{default:y(()=>l[14]||(l[14]=[V("保存并打印",-1)])),_:1,__:[14]})])]),f(B,{modelValue:I.value,"onUpdate:modelValue":l[8]||(l[8]=e=>I.value=e),title:"选择物料",width:"800px"},{footer:y(()=>[f(s,{onClick:l[7]||(l[7]=e=>I.value=!1)},{default:y(()=>l[16]||(l[16]=[V("取消",-1)])),_:1,__:[16]})]),default:y(()=>[b("div",M,[f(a,{modelValue:L.value,"onUpdate:modelValue":l[6]||(l[6]=e=>L.value=e),placeholder:"请输入物料编码、名称、型号",clearable:"",style:{width:"300px"}},{append:y(()=>[f(s,{onClick:W},{default:y(()=>l[15]||(l[15]=[V("搜索",-1)])),_:1,__:[15]})]),_:1},8,["modelValue"])]),f(E,{data:R.value,border:"",stripe:"",onRowClick:A,style:{"margin-top":"20px"}},{default:y(()=>[f(c,{prop:"code",label:"物料编码",width:"150"}),f(c,{prop:"name",label:"物料名称",width:"200"}),f(c,{prop:"model",label:"型号",width:"120"}),f(c,{prop:"specification",label:"规格",width:"150"}),f(c,{prop:"unit",label:"单位",width:"80"}),f(c,{prop:"price",label:"单价",width:"100"},{default:y(({row:e})=>[V(" ¥"+_(e.price),1)]),_:1}),f(c,{prop:"stockQuantity",label:"库存数量",width:"100"})]),_:1},8,["data"])]),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-19a93bd2"]]);export{Q as default};
