<template>
  <div class="dashboard">

    <!-- 数据概览卡片 -->
    <div class="overview-section">
      <h2 class="section-title">📊 数据概览</h2>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card class="overview-card" shadow="hover" @click="goToMaterialStatistics">
            <div class="card-content">
              <div class="card-icon">📦</div>
              <div class="card-info">
                <div class="card-title">工程物料</div>
                <div class="card-stats">
                  <span>总计: {{ materialStats.totalItems }}项</span>
                  <span>总价: ¥{{ formatNumber(materialStats.totalAmount) }}</span>
                </div>
                <el-button type="primary" size="small" class="card-button">
                  查看详情 →
                </el-button>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="overview-card" shadow="hover" @click="goToProjectProgress">
            <div class="card-content">
              <div class="card-icon">🏗️</div>
              <div class="card-info">
                <div class="card-title">工程进度</div>
                <div class="card-stats">
                  <span>在建: {{ projectStats.inProgress }}个</span>
                  <span>完成率: {{ projectStats.completionRate }}%</span>
                </div>
                <el-button type="primary" size="small" class="card-button">
                  查看详情 →
                </el-button>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="overview-card" shadow="hover" @click="goToLooseMaterialStatistics">
            <div class="card-content">
              <div class="card-icon">🏷️</div>
              <div class="card-info">
                <div class="card-title">散单物料</div>
                <div class="card-stats">
                  <span>甲料: {{ looseMaterialStats.totalQuantity }}件</span>
                  <span>总价: ¥{{ formatNumber(looseMaterialStats.totalAmount) }}</span>
                </div>
                <el-button type="primary" size="small" class="card-button">
                  查看详情 →
                </el-button>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="8">
          <el-card class="overview-card" shadow="hover" @click="goToSalesStatistics">
            <div class="card-content">
              <div class="card-icon">💰</div>
              <div class="card-info">
                <div class="card-title">商品售卖</div>
                <div class="card-stats">
                  <span>本月: ¥{{ formatNumber(salesStats.monthlyAmount) }}</span>
                  <span>完成度: {{ salesStats.completionRate }}%</span>
                </div>
                <el-button type="primary" size="small" class="card-button">
                  查看详情 →
                </el-button>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="overview-card" shadow="hover">
            <div class="card-content">
              <div class="card-icon">⚠️</div>
              <div class="card-info">
                <div class="card-title">系统提醒</div>
                <div class="card-stats">
                  <span>库存预警: {{ systemAlerts.stockWarning }}项</span>
                  <span>平账提醒: {{ systemAlerts.balanceReminder }}天</span>
                </div>
                <el-button type="warning" size="small" class="card-button">
                  查看详情 →
                </el-button>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="overview-card" shadow="hover">
            <div class="card-content">
              <div class="card-icon">📈</div>
              <div class="card-info">
                <div class="card-title">今日数据</div>
                <div class="card-stats">
                  <span>新订单: {{ todayData.newOrders }}个</span>
                  <span>完成工程: {{ todayData.completedProjects }}个</span>
                </div>
                <el-button type="success" size="small" class="card-button">
                  查看详情 →
                </el-button>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 快捷操作 -->
    <div class="quick-actions-section">
      <h2 class="section-title">🚀 快捷操作</h2>
      <el-card shadow="hover">
        <el-row :gutter="15">
          <el-col :span="6">
            <el-button type="primary" size="large" @click="$router.push('/warehouse/material-inbound')" block>
              📦 物料入库
            </el-button>
          </el-col>
          <el-col :span="6">
            <el-button type="success" size="large" @click="$router.push('/warehouse/material-apply')" block>
              📋 领料申请
            </el-button>
          </el-col>
          <el-col :span="6">
            <el-button type="warning" size="large" @click="$router.push('/projects/project-assign')" block>
              🏗️ 新建工程
            </el-button>
          </el-col>
          <el-col :span="6">
            <el-button type="info" size="large" @click="$router.push('/employees/employee-list')" block>
              👤 添加员工
            </el-button>
          </el-col>
        </el-row>
        <el-row :gutter="15" style="margin-top: 15px;">
          <el-col :span="6">
            <el-button type="danger" size="large" @click="$router.push('/warehouse/product-outbound')" block>
              💰 商品售卖
            </el-button>
          </el-col>
          <el-col :span="6">
            <el-button type="primary" size="large" @click="$router.push('/loose-orders/monthly-balance')" block>
              📊 月度平账
            </el-button>
          </el-col>
          <el-col :span="6">
            <el-button type="success" size="large" @click="viewReports" block>
              📈 查看报表
            </el-button>
          </el-col>
          <el-col :span="6">
            <el-button type="default" size="large" @click="$router.push('/system/user-management')" block>
              ⚙️ 系统设置
            </el-button>
          </el-col>
        </el-row>
      </el-card>
    </div>

    <!-- 统计模块入口 -->
    <div class="statistics-modules-section">
      <h2 class="section-title">📊 统计模块入口</h2>
      <el-card shadow="hover">
        <div class="module-list">
          <div class="module-item" @click="goToMaterialStatistics">
            <div class="module-icon">📦</div>
            <div class="module-info">
              <div class="module-title">工程物料统计</div>
              <div class="module-description">查看工程物料使用情况</div>
            </div>
            <el-icon class="module-arrow"><ArrowRight /></el-icon>
          </div>
          <div class="module-item" @click="goToProjectProgress">
            <div class="module-icon">🏗️</div>
            <div class="module-info">
              <div class="module-title">工程进度统计</div>
              <div class="module-description">查看工程进度和状态</div>
            </div>
            <el-icon class="module-arrow"><ArrowRight /></el-icon>
          </div>
          <div class="module-item" @click="goToLooseMaterialStatistics">
            <div class="module-icon">🏷️</div>
            <div class="module-info">
              <div class="module-title">散单物料统计</div>
              <div class="module-description">查看散单物料和平账信息</div>
            </div>
            <el-icon class="module-arrow"><ArrowRight /></el-icon>
          </div>
          <div class="module-item" @click="goToSalesStatistics">
            <div class="module-icon">💰</div>
            <div class="module-info">
              <div class="module-title">商品售卖统计</div>
              <div class="module-description">查看商品销售和排行榜</div>
            </div>
            <el-icon class="module-arrow"><ArrowRight /></el-icon>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 系统通知 -->
    <div class="notifications-section">
      <h2 class="section-title">🔔 系统通知</h2>
      <el-card shadow="hover">
        <div class="notification-list">
          <div class="notification-item warning">
            <span class="notification-icon">⚠️</span>
            <span class="notification-text">库存预警: 智能开关库存不足，建议及时补货</span>
          </div>
          <div class="notification-item info">
            <span class="notification-icon">📅</span>
            <span class="notification-text">提醒: 距离上次平账已15天，建议进行月度平账</span>
          </div>
          <div class="notification-item success">
            <span class="notification-icon">🎉</span>
            <span class="notification-text">恭喜: 张三本月销售业绩突破¥8,000</span>
          </div>
          <div class="notification-item default">
            <span class="notification-icon">🔧</span>
            <span class="notification-text">系统: 定时备份已完成 (2024-01-15 03:00)</span>
          </div>
        </div>
        <div class="notification-footer">
          <el-button type="primary" size="small" @click="viewAllNotifications">
            查看全部通知
          </el-button>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowRight } from '@element-plus/icons-vue'
import request from '@/utils/request'

const router = useRouter()

// 响应式数据
const materialStats = ref({
  totalItems: 43,
  totalAmount: 215000
})

const projectStats = ref({
  inProgress: 12,
  completionRate: 28.6
})

const looseMaterialStats = ref({
  totalQuantity: 430,
  totalAmount: 70000
})

const salesStats = ref({
  monthlyAmount: 35680,
  completionRate: 71.4
})

const systemAlerts = ref({
  stockWarning: 3,
  balanceReminder: 15
})

const todayData = ref({
  newOrders: 8,
  completedProjects: 2
})

// 方法
const formatNumber = (num: number): string => {
  return num.toLocaleString()
}

const goToMaterialStatistics = () => {
  router.push('/dashboard/material-statistics')
}

const goToProjectProgress = () => {
  router.push('/dashboard/project-progress')
}

const goToLooseMaterialStatistics = () => {
  router.push('/dashboard/loose-material-statistics')
}

const goToSalesStatistics = () => {
  router.push('/dashboard/sales-statistics')
}

const viewReports = () => {
  ElMessage.info('跳转到报表页面')
  // 这里可以跳转到报表页面
}

const viewAllNotifications = () => {
  ElMessage.info('查看全部通知')
  // 这里可以打开通知列表弹窗或跳转到通知页面
}

// 生命周期
onMounted(async () => {
  try {
    // 加载统计数据
    // 这里可以调用API获取实际数据
    console.log('Dashboard loaded')
  } catch (error) {
    console.error('Failed to load dashboard data:', error)
  }
})
</script>

<style scoped lang="scss">
.dashboard {
  padding: 20px;
  max-width: 100%;
  overflow-x: hidden;
}

.page-header {
  margin-bottom: 30px;
  text-align: center;
}

.page-title {
  font-size: 28px;
  font-weight: bold;
  margin: 0 0 10px 0;
  color: #303133;
}

.page-description {
  color: #606266;
  font-size: 16px;
  margin: 0;
}

.overview-section,
.quick-actions-section,
.statistics-modules-section,
.notifications-section {
  margin-bottom: 30px;
}

.section-title {
  font-size: 20px;
  font-weight: bold;
  margin: 0 0 15px 0;
  color: #303133;
}

.overview-card {
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

.card-content {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 10px;
}

.card-icon {
  font-size: 32px;
  min-width: 50px;
  text-align: center;
}

.card-info {
  flex: 1;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
}

.card-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 10px;

  span {
    font-size: 14px;
    color: #606266;
  }
}

.card-button {
  margin-top: 5px;
}

.module-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.module-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: #f5f7fa;
    border-color: #409eff;
  }
}

.module-icon {
  font-size: 24px;
  min-width: 40px;
  text-align: center;
}

.module-info {
  flex: 1;
}

.module-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.module-description {
  font-size: 14px;
  color: #606266;
}

.module-arrow {
  color: #c0c4cc;
  font-size: 16px;
}

.notification-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 15px;
}

.notification-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px;
  border-radius: 6px;

  &.warning {
    background-color: #fef0e6;
    border-left: 4px solid #e6a23c;
  }

  &.info {
    background-color: #ecf5ff;
    border-left: 4px solid #409eff;
  }

  &.success {
    background-color: #f0fff4;
    border-left: 4px solid #67c23a;
  }

  &.default {
    background-color: #f5f7fa;
    border-left: 4px solid #909399;
  }
}

.notification-icon {
  font-size: 16px;
  min-width: 20px;
}

.notification-text {
  flex: 1;
  font-size: 14px;
  color: #303133;
}

.notification-footer {
  text-align: center;
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
}

// 确保表格和卡片不会溢出
.el-card {
  overflow: hidden;
}

.el-table {
  overflow-x: auto;
  max-width: 100%;
}
</style>