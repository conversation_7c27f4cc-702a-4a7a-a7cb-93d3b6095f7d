<template>
  <div class="work-type-setting-container">
    <el-card class="main-card">
      <template #header>
        <div class="card-header">
          <span>工种设置</span>
        </div>
      </template>
      
      <!-- 搜索栏 -->
      <el-row :gutter="20" class="search-section">
        <el-col :span="6">
          <el-input v-model="searchForm.workTypeName" placeholder="工种名称" clearable />
        </el-col>
        <el-col :span="12">
          <div class="search-buttons">
            <el-button type="primary" icon="Search" @click="handleSearch">搜索</el-button>
            <el-button icon="Refresh" @click="handleReset">重置</el-button>
          </div>
        </el-col>
      </el-row>
      
      <!-- 工种列表 -->
      <el-table :data="workTypeList" border class="work-type-table" style="width: 100%; margin-top: 20px;">
        <el-table-column prop="workTypeName" label="工种名称" min-width="120" />
        <el-table-column prop="workTypeDesc" label="工种描述" min-width="150" />
        <el-table-column prop="standardDailyWage" label="标准工价(元/天)" min-width="120" />
        <el-table-column prop="employeeCount" label="员工数量" min-width="80" />
        <el-table-column prop="status" label="状态" min-width="80">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'enabled' ? 'success' : 'info'">
              {{ scope.row.status === 'enabled' ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" min-width="120" />
        <el-table-column label="操作" min-width="150" fixed="right">
          <template #default="scope">
            <el-button type="primary" link @click="editWorkType(scope.row)">修改</el-button>
            <el-button type="primary" link @click="viewDetail(scope.row)">详情</el-button>
            <el-button 
              type="primary" 
              link 
              @click="toggleStatus(scope.row)"
            >
              {{ scope.row.status === 'enabled' ? '禁用' : '启用' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        class="pagination"
      />
      
      <!-- 操作按钮 -->
      <div class="form-actions">
        <el-button type="primary" icon="Plus" @click="addWorkType">新增工种</el-button>
        <el-button icon="Upload" @click="batchImport">批量导入</el-button>
        <el-button icon="Download" @click="exportExcel">导出Excel</el-button>
      </div>
      
      <!-- 新增/修改工种弹窗 -->
      <el-dialog 
        v-model="workTypeDialogVisible" 
        :title="dialogTitle" 
        width="500"
        @close="handleDialogClose"
      >
        <el-form 
          ref="workTypeFormRef" 
          :model="currentWorkType" 
          :rules="workTypeRules" 
          label-width="120px"
        >
          <el-form-item label="工种名称:" prop="workTypeName">
            <el-input v-model="currentWorkType.workTypeName" placeholder="请输入工种名称" />
          </el-form-item>
          <el-form-item label="工种描述:" prop="workTypeDesc">
            <el-input 
              v-model="currentWorkType.workTypeDesc" 
              type="textarea" 
              placeholder="请输入工种描述" 
            />
          </el-form-item>
          <el-form-item label="标准工价(元/天):" prop="standardDailyWage">
            <el-input-number 
              v-model="currentWorkType.standardDailyWage" 
              :min="0" 
              controls-position="right" 
              style="width: 100%" 
            />
          </el-form-item>
          <el-form-item label="状态:" prop="status">
            <el-radio-group v-model="currentWorkType.status">
              <el-radio label="enabled">启用</el-radio>
              <el-radio label="disabled">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="workTypeDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveWorkType">保存</el-button>
        </template>
      </el-dialog>
      
      <!-- 工种详情弹窗 -->
      <el-dialog v-model="detailDialogVisible" title="工种详情" width="600">
        <el-form :model="currentWorkType" label-width="120px">
          <el-form-item label="工种名称:">
            <span>{{ currentWorkType.workTypeName }}</span>
          </el-form-item>
          <el-form-item label="工种描述:">
            <span>{{ currentWorkType.workTypeDesc }}</span>
          </el-form-item>
          <el-form-item label="标准工价:">
            <span>{{ currentWorkType.standardDailyWage }}元/天</span>
          </el-form-item>
          <el-form-item label="状态:">
            <el-tag :type="currentWorkType.status === 'enabled' ? 'success' : 'info'">
              {{ currentWorkType.status === 'enabled' ? '启用' : '禁用' }}
            </el-tag>
          </el-form-item>
          <el-form-item label="创建时间:">
            <span>{{ currentWorkType.createTime }}</span>
          </el-form-item>
          <el-form-item label="更新时间:">
            <span>{{ currentWorkType.updateTime }}</span>
          </el-form-item>
          
          <el-divider />
          
          <el-form-item label="关联员工列表:">
            <el-table :data="currentWorkType.employeeList" border style="width: 100%">
              <el-table-column prop="name" label="姓名" width="80" />
              <el-table-column prop="dailyWage" label="工价(元/天)" width="100" />
              <el-table-column prop="status" label="状态" width="80">
                <template #default="scope">
                  <el-tag :type="scope.row.status === 'active' ? 'success' : 'info'">
                    {{ scope.row.status === 'active' ? '在职' : '离职' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="hireDate" label="入职时间" width="120" />
              <el-table-column prop="remarks" label="备注" />
            </el-table>
          </el-form-item>
          
          <el-form-item label="工作统计:">
            <div>- 本月参与项目: {{ currentWorkType.projectCount }}个</div>
            <div>- 本月工作天数: {{ currentWorkType.workDays }}天</div>
            <div>- 本月总产值: ¥{{ currentWorkType.totalOutput }}</div>
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </template>
      </el-dialog>
      
      <!-- 批量导入弹窗 -->
      <el-dialog v-model="importDialogVisible" title="批量导入" width="500">
        <el-form label-width="100px">
          <el-form-item label="导入类型:">
            <span>工种设置</span>
          </el-form-item>
          <el-form-item label="选择文件:">
            <el-upload
              class="upload-demo"
              action=""
              :auto-upload="false"
              :on-change="handleFileChange"
            >
              <el-button type="primary">选择文件</el-button>
            </el-upload>
            <div v-if="fileName">文件名: {{ fileName }}</div>
          </el-form-item>
          <el-form-item label="模板下载:">
            <el-button type="primary" link @click="downloadTemplate">下载模板</el-button>
          </el-form-item>
          <el-form-item label="导入说明:">
            <div class="import-instructions">
              <p>1. 请按照模板格式填写工种信息</p>
              <p>2. 工种名称为必填项且不能重复</p>
              <p>3. 标准工价为数字，单位为元/天</p>
              <p>4. 状态只能填写: 启用、禁用</p>
              <p>5. 支持.xls、.xlsx格式文件</p>
            </div>
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="importWorkTypes">导入</el-button>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'

// 搜索表单
const searchForm = reactive({
  workTypeName: ''
})

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 15
})

// 工种列表数据
const workTypeList = ref([
  {
    id: 1,
    workTypeName: '电工',
    workTypeDesc: '电气安装维修',
    standardDailyWage: 300,
    employeeCount: 5,
    status: 'enabled',
    createTime: '2023-01-01'
  },
  {
    id: 2,
    workTypeName: '水工',
    workTypeDesc: '水管安装维修',
    standardDailyWage: 280,
    employeeCount: 4,
    status: 'enabled',
    createTime: '2023-01-01'
  },
  {
    id: 3,
    workTypeName: '安装工',
    workTypeDesc: '设备安装调试',
    standardDailyWage: 320,
    employeeCount: 6,
    status: 'enabled',
    createTime: '2023-01-01'
  },
  {
    id: 4,
    workTypeName: '维修工',
    workTypeDesc: '设备维修保养',
    standardDailyWage: 250,
    employeeCount: 3,
    status: 'enabled',
    createTime: '2023-01-01'
  },
  {
    id: 5,
    workTypeName: '油漆工',
    workTypeDesc: '墙面涂刷处理',
    standardDailyWage: 260,
    employeeCount: 2,
    status: 'enabled',
    createTime: '2023-01-01'
  }
])

// 当前工种
const currentWorkType = ref({
  id: 0,
  workTypeName: '',
  workTypeDesc: '',
  standardDailyWage: 0,
  status: 'enabled',
  createTime: '',
  updateTime: '',
  employeeList: [] as any[],
  projectCount: 0,
  workDays: 0,
  totalOutput: 0
})

// 弹窗控制
const workTypeDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const importDialogVisible = ref(false)

// 文件名
const fileName = ref('')

// 表单引用
const workTypeFormRef = ref()

// 对话框标题
const dialogTitle = computed(() => {
  return currentWorkType.value.id ? '修改工种' : '新增工种'
})

// 表单验证规则
const workTypeRules = {
  workTypeName: [
    { required: true, message: '请输入工种名称', trigger: 'blur' }
  ],
  workTypeDesc: [
    { required: true, message: '请输入工种描述', trigger: 'blur' }
  ],
  standardDailyWage: [
    { required: true, message: '请输入标准工价', trigger: 'blur' }
  ]
}

// 搜索
const handleSearch = () => {
  ElMessage.success('搜索成功')
  console.log('搜索条件:', searchForm)
}

// 重置
const handleReset = () => {
  searchForm.workTypeName = ''
}

// 分页变化
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  console.log('每页条数:', val)
}

const handleCurrentChange = (val: number) => {
  pagination.currentPage = val
  console.log('当前页:', val)
}

// 修改工种
const editWorkType = (row: any) => {
  currentWorkType.value = { ...row }
  // 添加详情数据
  currentWorkType.value.createTime = '2023-01-01'
  currentWorkType.value.updateTime = '2024-01-01'
  currentWorkType.value.employeeList = [
    { name: '李师傅', dailyWage: 300, status: 'active', hireDate: '2023-01-15', remarks: '技术熟练' },
    { name: '王师傅', dailyWage: 320, status: 'active', hireDate: '2023-03-20', remarks: '高级技工' },
    { name: '张师傅', dailyWage: 280, status: 'active', hireDate: '2023-05-10', remarks: '初级技工' }
  ]
  currentWorkType.value.projectCount = 5
  currentWorkType.value.workDays = 22
  currentWorkType.value.totalOutput = 6600
  workTypeDialogVisible.value = true
}

// 查看详情
const viewDetail = (row: any) => {
  currentWorkType.value = { ...row }
  // 添加详情数据
  currentWorkType.value.createTime = '2023-01-01'
  currentWorkType.value.updateTime = '2024-01-01'
  currentWorkType.value.employeeList = [
    { name: '李师傅', dailyWage: 300, status: 'active', hireDate: '2023-01-15', remarks: '技术熟练' },
    { name: '王师傅', dailyWage: 320, status: 'active', hireDate: '2023-03-20', remarks: '高级技工' },
    { name: '张师傅', dailyWage: 280, status: 'active', hireDate: '2023-05-10', remarks: '初级技工' }
  ]
  currentWorkType.value.projectCount = 5
  currentWorkType.value.workDays = 22
  currentWorkType.value.totalOutput = 6600
  detailDialogVisible.value = true
}

// 切换状态
const toggleStatus = (row: any) => {
  const newStatus = row.status === 'enabled' ? 'disabled' : 'enabled'
  ElMessage.success(`已${newStatus === 'enabled' ? '启用' : '禁用'}工种: ${row.workTypeName}`)
  row.status = newStatus
}

// 新增工种
const addWorkType = () => {
  // 重置表单
  currentWorkType.value = {
    id: 0,
    workTypeName: '',
    workTypeDesc: '',
    standardDailyWage: 0,
    status: 'enabled',
    createTime: '',
    updateTime: '',
    employeeList: [],
    projectCount: 0,
    workDays: 0,
    totalOutput: 0
  }
  workTypeDialogVisible.value = true
}

// 保存工种
const saveWorkType = () => {
  if (!workTypeFormRef.value) return
  
  workTypeFormRef.value.validate((valid: boolean) => {
    if (valid) {
      ElMessage.success('保存成功')
      workTypeDialogVisible.value = false
      console.log('保存工种:', currentWorkType.value)
    }
  })
}

// 对话框关闭
const handleDialogClose = () => {
  if (workTypeFormRef.value) {
    workTypeFormRef.value.resetFields()
  }
}

// 批量导入
const batchImport = () => {
  importDialogVisible.value = true
}

// 文件变更
const handleFileChange = (file: any) => {
  fileName.value = file.name
}

// 下载模板
const downloadTemplate = () => {
  ElMessage.success('下载模板')
}

// 导入工种
const importWorkTypes = () => {
  ElMessage.success('导入成功')
  importDialogVisible.value = false
}

// 导出Excel
const exportExcel = () => {
  ElMessage.success('导出Excel')
}
</script>

<style lang="scss" scoped>
.work-type-setting-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
  
  .main-card {
    margin-bottom: 20px;
    
    .card-header {
      font-size: 18px;
      font-weight: bold;
      color: #303133;
    }
  }
  
  .search-section {
    margin-bottom: 15px;
    
    .search-buttons {
      display: flex;
      
      .el-button {
        margin-right: 10px;
      }
    }
  }
  
  .work-type-table {
    margin-top: 20px;
  }
  
  .pagination {
    margin-top: 20px;
    text-align: right;
  }
  
  .form-actions {
    margin-top: 20px;
    text-align: left;
    
    .el-button {
      margin-right: 10px;
    }
  }
  
  .import-instructions {
    p {
      margin: 5px 0;
      color: #606266;
    }
  }
}
</style>