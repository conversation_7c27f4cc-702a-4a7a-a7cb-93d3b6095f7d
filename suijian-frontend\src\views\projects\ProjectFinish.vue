<template>
  <div class="project-finish-container">
    <el-card class="main-card">

      <el-form :model="formData" :rules="formRules" ref="formRef" label-width="120px">
        <!-- 工程选择 -->
        <el-row :gutter="20" class="form-section">
          <el-col :span="24">
            <div class="section-title">
              <el-icon><Tools /></el-icon>
              工程选择
            </div>
          </el-col>
          <el-col :span="24">
            <el-form-item label="选择工程:" prop="projectId">
              <el-select
                v-model="formData.projectId"
                placeholder="请选择要完成的工程"
                style="width: 100%"
                @change="onProjectChange"
              >
                <el-option
                  v-for="project in projectList"
                  :key="project.id"
                  :label="`${project.name} (${project.status})`"
                  :value="project.id"
                  :disabled="project.status !== '在建'"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 工程信息 -->
        <el-row :gutter="20" class="form-section" v-if="selectedProject">
          <el-col :span="24">
            <div class="section-title">
              <el-icon><Document /></el-icon>
              当前工程信息
            </div>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工程名称:">
              <el-input v-model="selectedProject.name" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="当前状态:">
              <el-tag :type="getStatusType(selectedProject.status)">
                {{ selectedProject.status }}
              </el-tag>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="当前进度:">
              <el-progress :percentage="selectedProject.progress" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开始时间:">
              <el-input v-model="selectedProject.startDate" readonly />
            </el-form-item>
          </el-col>
        </el-row>
      
      <!-- 人员工时统计 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">人员工时统计</div>
        </el-col>
        <el-col :span="24">
          <el-table :data="staffList" border class="staff-table">
            <el-table-column type="index" label="序号" width="60" />
            <el-table-column prop="workType" label="工种" width="120" />
            <el-table-column prop="name" label="人员姓名" width="120" />
            <el-table-column prop="dailyWage" label="工价(元/天)" width="120" />
            <el-table-column prop="workDays" label="工作天数" width="100">
              <template #default="scope">
                <el-input-number 
                  v-model="scope.row.workDays" 
                  :min="0" 
                  controls-position="right" 
                  style="width: 100%" 
                />
              </template>
            </el-table-column>
            <el-table-column prop="totalLaborCost" label="总工时费" width="120" />
            <el-table-column prop="remarks" label="备注">
              <template #default="scope">
                <el-input v-model="scope.row.remarks" placeholder="请输入备注" />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      
      <!-- 物料使用汇总 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">物料使用汇总</div>
        </el-col>
        <el-col :span="24">
          <el-table :data="materialList" border class="material-table">
            <el-table-column type="index" label="序号" width="60" />
            <el-table-column prop="materialCode" label="公司物料编码" width="120" />
            <el-table-column prop="materialName" label="物料名称" width="120" />
            <el-table-column prop="model" label="型号" width="100" />
            <el-table-column prop="specification" label="规格" width="100" />
            <el-table-column prop="unit" label="单位" width="80" />
            <el-table-column prop="usageQuantity" label="使用数量" width="100" />
            <el-table-column prop="unitPrice" label="单价" width="100" />
            <el-table-column prop="subtotal" label="小计" width="100" />
            <el-table-column prop="remarks" label="备注">
              <template #default="scope">
                <el-input v-model="scope.row.remarks" placeholder="请输入备注" />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      
      <!-- 成本汇总 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">成本汇总</div>
        </el-col>
        <el-col :span="24">
          <el-card class="cost-summary-card">
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="cost-item">
                  <span class="label">人工成本:</span>
                  <span>¥23,890</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="cost-item">
                  <span class="label">物料成本:</span>
                  <span>¥26,500</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="cost-item">
                  <span class="label">其他费用:</span>
                  <span>¥2,300</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="cost-item">
                  <span class="label">总成本:</span>
                  <span class="total-cost">¥52,690</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="cost-item">
                  <span class="label">预算成本:</span>
                  <span>¥55,000</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="cost-item">
                  <span class="label">成本差异:</span>
                  <span class="saving">¥2,310 (节约)</span>
                </div>
              </el-col>
            </el-row>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 验收信息 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">验收信息</div>
        </el-col>
        <el-col :span="12">
          <el-form-item label="验收结果:">
            <el-select v-model="formData.acceptanceResult" placeholder="请选择验收结果" style="width: 100%">
              <el-option label="合格" value="qualified" />
              <el-option label="不合格" value="unqualified" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="验收日期:">
            <el-date-picker
              v-model="formData.acceptanceDate"
              type="date"
              placeholder="请选择验收日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="验收人员:">
            <el-input v-model="formData.acceptancePerson" placeholder="请输入验收人员" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="客户签字:">
            <el-input v-model="formData.customerSignature" placeholder="请输入客户签字" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="验收说明:">
            <el-input
              v-model="formData.acceptanceDescription"
              type="textarea"
              :rows="3"
              placeholder="请输入验收说明"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 备注信息 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">备注信息</div>
        </el-col>
        <el-col :span="24">
          <el-form-item label="工程总结:">
            <el-input
              v-model="formData.projectSummary"
              type="textarea"
              :rows="3"
              placeholder="请输入工程总结"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="后续维护建议:">
            <el-input
              v-model="formData.maintenanceSuggestions"
              type="textarea"
              :rows="3"
              placeholder="请输入后续维护建议"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 操作按钮 -->
      <div class="form-actions">
        <el-button type="primary" @click="handleSave">保存</el-button>
        <el-button type="success" @click="handleSubmit">提交</el-button>
        <el-button @click="printReport">打印报告</el-button>
        <el-button @click="handleCancel">取消</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormRules } from 'element-plus'
import { Tools, Document } from '@element-plus/icons-vue'

// 表单引用
const formRef = ref()

// 选中的项目
const selectedProject = ref<any>(null)

// 工程列表
const projectList = ref([
  {
    id: 1,
    name: '阳光小区A栋',
    status: '在建',
    progress: 95,
    startDate: '2024-01-12'
  },
  {
    id: 2,
    name: '花园广场项目',
    status: '在建',
    progress: 88,
    startDate: '2024-01-15'
  },
  {
    id: 3,
    name: '商业中心B区',
    status: '在建',
    progress: 92,
    startDate: '2024-01-20'
  }
])

// 表单数据
const formData = reactive({
  projectId: '',
  acceptanceResult: '',
  acceptanceDate: '',
  acceptancePerson: '',
  customerSignature: '',
  acceptanceDescription: '',
  projectSummary: '',
  maintenanceSuggestions: ''
})

// 表单验证规则
const formRules: FormRules = {
  projectId: [
    { required: true, message: '请选择工程', trigger: 'change' }
  ]
}

// 获取状态类型
const getStatusType = (status: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const statusMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    '未开始': 'info',
    '在建': 'warning',
    '暂停': 'danger',
    '完成': 'success'
  }
  return statusMap[status] || 'info'
}

// 工程选择变更
const onProjectChange = (projectId: string) => {
  selectedProject.value = projectList.value.find(p => p.id === Number(projectId))
}

// 人员工时统计
const staffList = reactive([
  {
    id: 1,
    workType: '电工',
    name: '李师傅',
    dailyWage: 300,
    workDays: 25,
    totalLaborCost: '¥7,500',
    remarks: ''
  },
  {
    id: 2,
    workType: '水工',
    name: '王师傅',
    dailyWage: 280,
    workDays: 20,
    totalLaborCost: '¥5,600',
    remarks: ''
  },
  {
    id: 3,
    workType: '安装工',
    name: '张师傅',
    dailyWage: 320,
    workDays: 22,
    totalLaborCost: '¥7,040',
    remarks: ''
  },
  {
    id: 4,
    workType: '维修工',
    name: '赵师傅',
    dailyWage: 250,
    workDays: 15,
    totalLaborCost: '¥3,750',
    remarks: ''
  }
])

// 物料使用汇总
const materialList = reactive([
  {
    id: 1,
    materialCode: 'WL001',
    materialName: '电缆线',
    model: 'YJV-3*4',
    specification: '3*4mm²',
    unit: '米',
    usageQuantity: 300,
    unitPrice: '¥30.00',
    subtotal: '¥9,000',
    remarks: ''
  },
  {
    id: 2,
    materialCode: 'WL002',
    materialName: '开关面板',
    model: 'KP-86',
    specification: '86型',
    unit: '个',
    usageQuantity: 40,
    unitPrice: '¥250.00',
    subtotal: '¥10,000',
    remarks: ''
  },
  {
    id: 3,
    materialCode: 'WL003',
    materialName: '插座',
    model: 'ZP-86',
    specification: '86型',
    unit: '个',
    usageQuantity: 60,
    unitPrice: '¥60.00',
    subtotal: '¥3,600',
    remarks: ''
  },
  {
    id: 4,
    materialCode: 'WL004',
    materialName: '灯具',
    model: 'LED-12W',
    specification: '12W',
    unit: '个',
    usageQuantity: 30,
    unitPrice: '¥130.00',
    subtotal: '¥3,900',
    remarks: ''
  }
])

// 保存
const handleSave = () => {
  ElMessage.success('保存成功')
  console.log('保存数据:', formData, staffList, materialList)
}

// 提交
const handleSubmit = () => {
  ElMessage.success('提交成功')
  console.log('提交数据:', formData, staffList, materialList)
}

// 打印报告
const printReport = () => {
  ElMessage.success('开始打印报告')
}

// 取消
const handleCancel = () => {
  ElMessage.info('已取消')
}
</script>

<style lang="scss" scoped>
.project-finish-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
  
  .main-card {
    margin-bottom: 20px;
    
    .card-header {
      font-size: 18px;
      font-weight: bold;
      color: #303133;
    }
  }
  
  .form-section {
    margin-bottom: 20px;
    
    .section-title {
      font-size: 16px;
      font-weight: bold;
      color: #606266;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ebeef5;
    }
    
    .el-form-item {
      margin-bottom: 18px;
    }
  }
  
  .staff-table,
  .material-table {
    margin-top: 10px;
  }
  
  .cost-summary-card {
    background-color: #f0f9ff;
    border-color: #b3e0ff;
    
    .cost-item {
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;
      
      .label {
        font-weight: bold;
        color: #409eff;
      }
      
      .total-cost {
        font-weight: bold;
        color: #303133;
      }
      
      .saving {
        color: #67c23a;
        font-weight: bold;
      }
    }
  }
  
  .form-actions {
    text-align: center;
    padding: 20px 0;
    
    .el-button {
      margin: 0 10px;
      padding: 12px 30px;
    }
  }
}
</style>