var e=(e,a,t)=>new Promise((l,r)=>{var n=e=>{try{o(t.next(e))}catch(a){r(a)}},i=e=>{try{o(t.throw(e))}catch(a){r(a)}},o=e=>e.done?l(e.value):Promise.resolve(e.value).then(n,i);o((t=t.apply(e,a)).next())});import{r as a}from"./index-395c01fc.js";import{E as t,e as l,j as r}from"./element-plus-7917fd46.js";import{l as n,_ as i,r as o,q as d,y as c,R as p,J as s,z as g,av as u,aD as b,x as h,O as m,P as w,K as f,u as v}from"./vue-vendor-fc5a6493.js";import{_}from"./_plugin-vue_export-helper-1b428a4d.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const C={class:"monthly-balance"},P={"element-loading-text":"加载中..."},y={class:"action-buttons"},M={class:"confirm-content"},N={class:"confirm-item"},S={class:"value"},k={class:"confirm-item"},R={class:"value"},I={class:"confirm-section"},z={class:"confirm-item"},x={class:"value"},H={class:"confirm-item"},T={class:"value"},B={class:"confirm-item"},j={class:"value"},F={class:"dialog-footer"},D=_(n({__name:"MonthlyBalance",setup(n){const _=i({month:"2024年1月",balanceDate:new Date,laborCost:54336,overIssuedItems:12,overIssuedQuantity:156,unreturnedItems:8,unreturnedQuantity:89}),D=o("project-cost-summary"),E=i({projectCost:!1,preMeterMaterials:!1,indoorMaterials:!1,overReceivedMaterials:!1,fittingsMaterials:!1,meterInstallationFittings:!1,minorInstallationFittings:!1,secondaryInstallationFittings:!1,indoorInstallationFittings:!1,meterSettlementHalf:!1,meterSettlementNoHalf:!1,gasMinorSettlementHalf:!1,gasMinorSettlementNoHalf:!1,gasMinorSettlementNoMeter:!1,suppliedMaterialsReceipt:!1}),A=i({projectCost:null,preMeterMaterials:null,indoorMaterials:null,overReceivedMaterials:null,fittingsMaterials:null,meterInstallationFittings:null,minorInstallationFittings:null,secondaryInstallationFittings:null,indoorInstallationFittings:null,meterSettlementHalf:null,meterSettlementNoHalf:null,gasMinorSettlementHalf:null,gasMinorSettlementNoHalf:null,gasMinorSettlementNoMeter:null,suppliedMaterialsReceipt:null}),L=o([]),V=o([]),Q=o([]),G=o([]),$=o([]),q=o([]),W=o([]),O=o([]),U=o([]),J=o([]),K=o([]),X=o([]),Y=o([]),Z=o([]),ee=o([]),ae=o(!1),te=l=>e(this,null,function*(){if(!A[l]){E[l]=!0;try{let e="";switch(l){case"projectCost":e="/api/loose-orders/balance/project-cost";break;case"preMeterMaterials":e="/api/loose-orders/balance/pre-meter-materials";break;case"indoorMaterials":e="/api/loose-orders/balance/indoor-materials";break;case"overReceivedMaterials":e="/api/loose-orders/balance/over-received-materials";break;case"fittingsMaterials":e="/api/loose-orders/balance/fittings-materials";break;case"meterInstallationFittings":e="/api/loose-orders/balance/meter-installation-fittings";break;case"minorInstallationFittings":e="/api/loose-orders/balance/minor-installation-fittings";break;case"secondaryInstallationFittings":e="/api/loose-orders/balance/secondary-installation-fittings";break;case"indoorInstallationFittings":e="/api/loose-orders/balance/indoor-installation-fittings";break;case"meterSettlementHalf":e="/api/loose-orders/balance/meter-settlement-half";break;case"meterSettlementNoHalf":e="/api/loose-orders/balance/meter-settlement-no-half";break;case"gasMinorSettlementHalf":e="/api/loose-orders/balance/gas-minor-settlement-half";break;case"gasMinorSettlementNoHalf":e="/api/loose-orders/balance/gas-minor-settlement-no-half";break;case"gasMinorSettlementNoMeter":e="/api/loose-orders/balance/gas-minor-settlement-no-meter";break;case"suppliedMaterialsReceipt":e="/api/loose-orders/balance/supplied-materials-receipt";break;default:throw new Error(`未知的表格类型: ${l}`)}const r=yield a.get(e);if(200===r.code){switch(A[l]=r.data,l){case"projectCost":L.value=r.data.list||[];break;case"preMeterMaterials":V.value=r.data.list||[];break;case"indoorMaterials":Q.value=r.data.list||[];break;case"overReceivedMaterials":G.value=r.data.list||[];break;case"fittingsMaterials":$.value=r.data.list||[];break;case"meterInstallationFittings":q.value=r.data.list||[];break;case"minorInstallationFittings":W.value=r.data.list||[];break;case"secondaryInstallationFittings":O.value=r.data.list||[];break;case"indoorInstallationFittings":U.value=r.data.list||[];break;case"meterSettlementHalf":J.value=r.data.list||[];break;case"meterSettlementNoHalf":K.value=r.data.list||[];break;case"gasMinorSettlementHalf":X.value=r.data.list||[];break;case"gasMinorSettlementNoHalf":Y.value=r.data.list||[];break;case"gasMinorSettlementNoMeter":Z.value=r.data.list||[];break;case"suppliedMaterialsReceipt":ee.value=r.data.list||[]}t.success(`${l} 数据加载成功`)}else t.error(`${l} 数据加载失败`)}catch(e){t.error(`${l} 数据加载失败`)}finally{E[l]=!1}}}),le=e=>{switch(D.value=e,e){case"project-cost-summary":te("projectCost");break;case"pre-meter-materials":te("preMeterMaterials");break;case"indoor-materials":te("indoorMaterials");break;case"over-received-materials":te("overReceivedMaterials");break;case"fittings-materials":te("fittingsMaterials");break;case"meter-installation-fittings":te("meterInstallationFittings");break;case"minor-installation-fittings":te("minorInstallationFittings");break;case"secondary-installation-fittings":te("secondaryInstallationFittings");break;case"indoor-installation-fittings":te("indoorInstallationFittings");break;case"meter-settlement-half":te("meterSettlementHalf");break;case"meter-settlement-no-half":te("meterSettlementNoHalf");break;case"gas-minor-settlement-half":te("gasMinorSettlementHalf");break;case"gas-minor-settlement-no-half":te("gasMinorSettlementNoHalf");break;case"gas-minor-settlement-no-meter":te("gasMinorSettlementNoMeter");break;case"supplied-materials-receipt":te("suppliedMaterialsReceipt")}},re=()=>e(this,null,function*(){try{const e=yield a.get("/api/loose-orders/balance/month");if(200===e.code){const a=e.data;a.balanceInfo&&(Object.assign(_,a.balanceInfo),a.balanceInfo.balanceDate&&(_.balanceDate=new Date(a.balanceInfo.balanceDate))),yield te("projectCost"),t.success("基础数据加载成功")}else t.error("基础数据加载失败")}catch(e){t.error("基础数据加载失败")}}),ne=()=>e(this,null,function*(){try{200===(yield a.post("/api/loose-orders/balance/confirm",{month:_.month,balanceDate:_.balanceDate,laborCost:_.laborCost,overIssuedItems:_.overIssuedItems,overIssuedQuantity:_.overIssuedQuantity,unreturnedItems:_.unreturnedItems,unreturnedQuantity:_.unreturnedQuantity})).code?(t.success("平账确认成功"),ae.value=!1):t.error("平账确认失败")}catch(e){t.error("平账确认失败")}}),ie=()=>{ae.value=!0},oe=()=>{ae.value=!1},de=()=>{window.print()},ce=()=>{l.confirm("确定要取消当前操作吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{t.info("已取消操作")}).catch(()=>{})},pe=e=>null==e||isNaN(e)||""===e?"0":Number(e).toLocaleString(),se=e=>{if(!e||null==e)return"";try{const a=new Date(e);return isNaN(a.getTime())?"":a.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}catch(a){return""}},ge=e=>{const{columns:a}=e,t=[];if(A.projectCost&&A.projectCost.summary){const e=A.projectCost.summary;a.forEach((a,l)=>{if(0===l)return void(t[l]="合计");if(1===l)return void(t[l]="");const r=a.property;void 0!==e[r]?t[l]="preMeterLaborCost"===r||"indoorLaborCost"===r||"laborSubtotal"===r||"gasMeterCost"===r||"postMeterCost"===r||"fittingsCost"===r||"materialSubtotal"===r||"actualReceivedAmount"===r||"overReceivedAmount"===r||"totalProjectCost"===r||"payableAmount"===r?`¥${pe(e[r])}`:pe(e[r]):t[l]=""})}else a.forEach((e,a)=>{if(0===a)return void(t[a]="合计");if(1===a)return void(t[a]="");const l=L.value.map(a=>{const t=a[e.property];return null!=t?Number(t):0});if(l.every(e=>isNaN(e)))t[a]="";else{const r=l.reduce((e,a)=>{const t=Number(a);return isNaN(t)?e:e+a},0);"preMeterLaborCost"===e.property||"indoorLaborCost"===e.property||"laborSubtotal"===e.property||"gasMeterCost"===e.property||"postMeterCost"===e.property||"fittingsCost"===e.property||"materialSubtotal"===e.property||"actualReceivedAmount"===e.property||"overReceivedAmount"===e.property||"totalProjectCost"===e.property||"payableAmount"===e.property?t[a]=`¥${pe(r)}`:t[a]=pe(r)}});return t},ue=e=>{const{columns:a,data:t}=e,l=[];return a.forEach((e,a)=>{if(0===a)return void(l[a]="合计");const r=t.map(a=>Number(a[e.property]));r.every(e=>Number.isNaN(e))?l[a]="":l[a]=r.reduce((e,a)=>{const t=Number(a);return Number.isNaN(t)?e:e+a},0).toFixed(2)}),l},be=e=>ue(e),he=e=>ue(e),me=e=>ue(e),we=e=>ue(e),fe=e=>ue(e),ve=e=>ue(e),_e=e=>ue(e),Ce=e=>ue(e),Pe=e=>ue(e),ye=e=>ue(e),Me=e=>ue(e),Ne=e=>ue(e),Se=e=>ue(e),ke=()=>{t.success("报表导出功能开发中...")};return d(()=>{re()}),(e,a)=>{const t=u("el-descriptions-item"),l=u("el-descriptions"),n=u("el-card"),i=u("el-table-column"),o=u("el-table"),d=u("el-tab-pane"),A=u("el-tabs"),te=u("el-button"),re=u("el-dialog"),Re=b("loading");return h(),c("div",C,[p(n,{class:"balance-info-card"},{header:s(()=>a[3]||(a[3]=[g("div",{class:"card-header"},[g("span",null,"平账信息")],-1)])),default:s(()=>[p(l,{column:2,border:"",class:"balance-info"},{default:s(()=>[p(t,{label:"月份"},{default:s(()=>[m(w(_.month),1)]),_:1}),p(t,{label:"人工费"},{default:s(()=>[m(" ¥"+w(pe(_.laborCost)),1)]),_:1}),p(t,{label:"超领材料"},{default:s(()=>[m(w(_.overIssuedItems)+"项，"+w(_.overIssuedQuantity)+"件 ",1)]),_:1}),p(t,{label:"师傅手中未退回材料"},{default:s(()=>[m(w(_.unreturnedItems)+"项，"+w(_.unreturnedQuantity)+"件 ",1)]),_:1}),p(t,{label:"平账时间"},{default:s(()=>[m(w(se(_.balanceDate)),1)]),_:1})]),_:1})]),_:1}),p(n,{class:"tabs-card"},{header:s(()=>a[4]||(a[4]=[g("div",{class:"card-header"},[g("span",null,"工程费用汇总")],-1)])),default:s(()=>[p(A,{modelValue:D.value,"onUpdate:modelValue":a[0]||(a[0]=e=>D.value=e),type:"border-card",class:"project-tabs",onTabChange:le},{default:s(()=>[p(d,{label:"户内安装工程费用汇总",name:"project-cost-summary"},{default:s(()=>[f((h(),c("div",P,[p(o,{data:L.value,style:{width:"100%"},border:"","show-summary":"","summary-method":ge},{default:s(()=>[p(i,{prop:"serialNo",label:"序号",width:"60",align:"center"}),p(i,{prop:"projectName",label:"单项工程名称",width:"200"}),p(i,{label:"工程安装人工费(元)",align:"center"},{default:s(()=>[p(i,{prop:"preMeterLaborCost",label:"表前安装人工费",width:"120",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.preMeterLaborCost)),1)]),_:1}),p(i,{prop:"indoorLaborCost",label:"户内安装人工费",width:"120",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.indoorLaborCost)),1)]),_:1}),p(i,{prop:"laborSubtotal",label:"小计(元)",width:"120",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.laborSubtotal)),1)]),_:1})]),_:1}),p(i,{prop:"installedHouseholds",label:"安装户数",width:"100",align:"center"}),p(i,{label:"实际耗用材料金额",align:"center"},{default:s(()=>[p(i,{prop:"gasMeterCost",label:"气表",width:"100",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.gasMeterCost)),1)]),_:1}),p(i,{prop:"postMeterCost",label:"表后",width:"100",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.postMeterCost)),1)]),_:1}),p(i,{prop:"fittingsCost",label:"管件",width:"100",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.fittingsCost)),1)]),_:1}),p(i,{prop:"materialSubtotal",label:"小计(元)",width:"120",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.materialSubtotal)),1)]),_:1})]),_:1}),p(i,{prop:"actualReceivedAmount",label:"实际领用材料金额(元)",width:"150",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.actualReceivedAmount)),1)]),_:1}),p(i,{prop:"overReceivedAmount",label:"超领甲供材料金额",width:"150",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.overReceivedAmount)),1)]),_:1}),p(i,{prop:"totalProjectCost",label:"工程总造价(元)",width:"150",align:"center"},{header:s(()=>[p(v(r),{content:"计算公式：工程总造价 = 人工费小计 + 实际领用材料金额",placement:"top"},{default:s(()=>a[5]||(a[5]=[g("span",null,"工程总造价(元)",-1)])),_:1,__:[5]})]),default:s(({row:e})=>[m(" ¥"+w(pe(e.totalProjectCost)),1)]),_:1}),p(i,{prop:"payableAmount",label:"应付施工单位金额",width:"150",align:"center"},{header:s(()=>[p(v(r),{content:"计算公式：应付施工单位金额 = 人工费小计 - 超领甲供材料金额",placement:"top"},{default:s(()=>a[6]||(a[6]=[g("span",null,"应付施工单位金额",-1)])),_:1,__:[6]})]),default:s(({row:e})=>[m(" ¥"+w(pe(e.payableAmount)),1)]),_:1})]),_:1},8,["data"])])),[[Re,E.projectCost]])]),_:1}),p(d,{label:"户内表前甲供材清单表",name:"pre-meter-materials"},{default:s(()=>[p(o,{data:V.value,style:{width:"100%"},border:"","show-summary":"","summary-method":ue},{default:s(()=>[p(i,{prop:"serialNo",label:"序号",width:"60",align:"center"}),p(i,{prop:"projectName",label:"单项工程名称",width:"200"}),p(i,{label:"甲供材料",align:"center"},{default:s(()=>[p(i,{label:"气表",align:"center"},{default:s(()=>[p(i,{label:"机械表接头(个)",align:"center"},{default:s(()=>[p(i,{prop:"mechanicalConnectorReceived",label:"实领",width:"80",align:"center"}),p(i,{prop:"mechanicalConnectorConsumed",label:"实耗",width:"80",align:"center"}),p(i,{prop:"mechanicalConnectorPrice",label:"单价(元/个)",width:"100",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.mechanicalConnectorPrice)),1)]),_:1})]),_:1}),p(i,{label:"千嘉表(块)",align:"center"},{default:s(()=>[p(i,{prop:"qianjiaMeterReceived",label:"实领",width:"80",align:"center"}),p(i,{prop:"qianjiaMeterConsumed",label:"实耗",width:"80",align:"center"}),p(i,{prop:"qianjiaMeterPrice",label:"单价(元/块)",width:"100",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.qianjiaMeterPrice)),1)]),_:1})]),_:1})]),_:1}),p(i,{label:"表后",align:"center"},{default:s(()=>[p(i,{label:"低低压调压器(个)",align:"center"},{default:s(()=>[p(i,{prop:"lowPressureRegulatorReceived",label:"实领",width:"80",align:"center"}),p(i,{prop:"lowPressureRegulatorConsumed",label:"实耗",width:"80",align:"center"}),p(i,{prop:"lowPressureRegulatorPrice",label:"单价(元/个)",width:"100",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.lowPressureRegulatorPrice)),1)]),_:1})]),_:1}),p(i,{label:"表箱(个)",align:"center"},{default:s(()=>[p(i,{prop:"meterBoxReceived",label:"实领",width:"80",align:"center"}),p(i,{prop:"meterBoxConsumed",label:"实耗",width:"80",align:"center"}),p(i,{prop:"meterBoxPrice",label:"单价(元/个)",width:"100",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.meterBoxPrice)),1)]),_:1})]),_:1}),p(i,{label:"表前阀(个)",align:"center"},{default:s(()=>[p(i,{prop:"preMeterValveReceived",label:"实领",width:"80",align:"center"}),p(i,{prop:"preMeterValveConsumed",label:"实耗",width:"80",align:"center"}),p(i,{prop:"preMeterValvePrice",label:"单价(元/个)",width:"100",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.preMeterValvePrice)),1)]),_:1})]),_:1}),p(i,{label:"预制短管(米)",align:"center"},{default:s(()=>[p(i,{prop:"prefabricatedPipeReceived",label:"实领",width:"80",align:"center"}),p(i,{prop:"prefabricatedPipeConsumed",label:"实耗",width:"80",align:"center"}),p(i,{prop:"prefabricatedPipePrice",label:"单价(元/米)",width:"100",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.prefabricatedPipePrice)),1)]),_:1})]),_:1})]),_:1})]),_:1}),p(i,{label:"表前安装甲供材料金额(元)",align:"center"},{default:s(()=>[p(i,{prop:"preMeterMaterialsReceivedAmount",label:"实领",width:"120",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.preMeterMaterialsReceivedAmount)),1)]),_:1}),p(i,{prop:"preMeterMaterialsConsumedAmount",label:"实耗",width:"120",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.preMeterMaterialsConsumedAmount)),1)]),_:1})]),_:1})]),_:1},8,["data"])]),_:1}),p(d,{label:"户内甲供材料清单表",name:"indoor-materials"},{default:s(()=>[p(o,{data:Q.value,style:{width:"100%"},border:"","show-summary":"","summary-method":be},{default:s(()=>[p(i,{prop:"serialNo",label:"序号",width:"60",align:"center"}),p(i,{prop:"projectName",label:"单项工程名称",width:"200"}),p(i,{label:"甲供材料-户内",align:"center"},{default:s(()=>[p(i,{label:"灶前阀(个)",align:"center"},{default:s(()=>[p(i,{prop:"stoveFrontValveReceived",label:"实领",width:"80",align:"center"}),p(i,{prop:"stoveFrontValveConsumed",label:"实耗",width:"80",align:"center"}),p(i,{prop:"stoveFrontValvePrice",label:"单价(元/个)",width:"100",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.stoveFrontValvePrice)),1)]),_:1})]),_:1}),p(i,{label:"涂覆钢管(米)",align:"center"},{default:s(()=>[p(i,{prop:"coatedSteelPipeReceived",label:"实领",width:"80",align:"center"}),p(i,{prop:"coatedSteelPipeConsumed",label:"实耗",width:"80",align:"center"}),p(i,{prop:"coatedSteelPipePrice",label:"单价(元/米)",width:"100",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.coatedSteelPipePrice)),1)]),_:1})]),_:1}),p(i,{label:"波纹管(米)",align:"center"},{default:s(()=>[p(i,{prop:"corrugatedPipeReceived",label:"实领",width:"80",align:"center"}),p(i,{prop:"corrugatedPipeConsumed",label:"实耗",width:"80",align:"center"}),p(i,{prop:"corrugatedPipePrice",label:"单价(元/米)",width:"100",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.corrugatedPipePrice)),1)]),_:1})]),_:1}),p(i,{label:"输送波纹管防护钢板(直板)",align:"center"},{default:s(()=>[p(i,{prop:"protectionPlateStraightReceived",label:"实领",width:"80",align:"center"}),p(i,{prop:"protectionPlateStraightConsumed",label:"实耗",width:"80",align:"center"}),p(i,{prop:"protectionPlateStraightPrice",label:"单价(元/个)",width:"100",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.protectionPlateStraightPrice)),1)]),_:1})]),_:1}),p(i,{label:"输送波纹管防护钢板(外、侧弯)",align:"center"},{default:s(()=>[p(i,{prop:"protectionPlateBendReceived",label:"实领",width:"80",align:"center"}),p(i,{prop:"protectionPlateBendConsumed",label:"实耗",width:"80",align:"center"}),p(i,{prop:"protectionPlateBendPrice",label:"单价(元/个)",width:"100",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.protectionPlateBendPrice)),1)]),_:1})]),_:1}),p(i,{label:"波纹软管(条)(1200mm、1000mm)",align:"center"},{default:s(()=>[p(i,{prop:"corrugatedHoseLongReceived",label:"实领",width:"80",align:"center"}),p(i,{prop:"corrugatedHoseLongConsumed",label:"实耗",width:"80",align:"center"}),p(i,{prop:"corrugatedHoseLongPrice",label:"单价(元/条)",width:"100",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.corrugatedHoseLongPrice)),1)]),_:1})]),_:1}),p(i,{label:"波纹软管(条)(800、500mm)",align:"center"},{default:s(()=>[p(i,{prop:"corrugatedHoseShortReceived",label:"实领",width:"80",align:"center"}),p(i,{prop:"corrugatedHoseShortConsumed",label:"实耗",width:"80",align:"center"}),p(i,{prop:"corrugatedHoseShortPrice",label:"单价(元/条)",width:"100",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.corrugatedHoseShortPrice)),1)]),_:1})]),_:1})]),_:1}),p(i,{label:"户内安装甲供材料金额(元)",align:"center"},{default:s(()=>[p(i,{prop:"indoorMaterialsReceivedAmount",label:"实领-户内",width:"120",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.indoorMaterialsReceivedAmount)),1)]),_:1}),p(i,{prop:"indoorMaterialsConsumedAmount",label:"实耗-户内",width:"120",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.indoorMaterialsConsumedAmount)),1)]),_:1})]),_:1})]),_:1},8,["data"])]),_:1}),p(d,{label:"户内结算做销售处理超领材料费用表",name:"over-received-materials"},{default:s(()=>[p(o,{data:G.value,style:{width:"100%"},border:"","show-summary":"","summary-method":he},{default:s(()=>[p(i,{prop:"serialNo",label:"序号",width:"60",align:"center"}),p(i,{prop:"materialName",label:"材料名称",width:"200"}),p(i,{prop:"specification",label:"规格",width:"150"}),p(i,{prop:"unit",label:"单位",width:"80",align:"center"}),p(i,{prop:"overQuantityWithinRate",label:"超领量(损耗率以内)",width:"150",align:"center"}),p(i,{prop:"unitPriceWithinRate",label:"含税材料单价(元)(损耗率以内)",width:"180",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.unitPriceWithinRate)),1)]),_:1}),p(i,{prop:"overQuantityBeyondRate",label:"超领量(损耗率以外)",width:"150",align:"center"}),p(i,{prop:"unitPriceBeyondRate",label:"含税材料单价(元)(损耗率以外)",width:"180",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.unitPriceBeyondRate)),1)]),_:1}),p(i,{prop:"totalPrice",label:"合价(元)",width:"120",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.totalPrice)),1)]),_:1})]),_:1},8,["data"])]),_:1}),p(d,{label:"户内管件甲供材料费用表",name:"fittings-materials"},{default:s(()=>[p(o,{data:$.value,style:{width:"100%"},border:"","show-summary":"","summary-method":me},{default:s(()=>[p(i,{prop:"serialNo",label:"序号",width:"60",align:"center"}),p(i,{prop:"projectName",label:"单项工程名称",width:"150"}),p(i,{label:"甲供材料-户内",align:"center"},{default:s(()=>[p(i,{label:"波纹管快速外螺纹接头(个)",align:"center"},{default:s(()=>[p(i,{prop:"corrugatedConnectorReceived",label:"实领",width:"80",align:"center"}),p(i,{prop:"corrugatedConnectorConsumed",label:"实耗",width:"80",align:"center"}),p(i,{prop:"corrugatedConnectorPrice",label:"单价(元/个)",width:"100",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.corrugatedConnectorPrice)),1)]),_:1})]),_:1}),p(i,{label:"镀锌90°弯头(个)",align:"center"},{default:s(()=>[p(i,{prop:"galvanizedElbow90Received",label:"实领",width:"80",align:"center"}),p(i,{prop:"galvanizedElbow90Consumed",label:"实耗",width:"80",align:"center"}),p(i,{prop:"galvanizedElbow90Price",label:"单价(元/个)",width:"100",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.galvanizedElbow90Price)),1)]),_:1})]),_:1}),p(i,{label:"镀锌六角外丝(个)",align:"center"},{default:s(()=>[p(i,{prop:"galvanizedHexExternalReceived",label:"实领",width:"80",align:"center"}),p(i,{prop:"galvanizedHexExternalConsumed",label:"实耗",width:"80",align:"center"}),p(i,{prop:"galvanizedHexExternalPrice",label:"单价(元/个)",width:"100",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.galvanizedHexExternalPrice)),1)]),_:1})]),_:1}),p(i,{label:"镀锌内丝(个)",align:"center"},{default:s(()=>[p(i,{prop:"galvanizedInternalReceived",label:"实领",width:"80",align:"center"}),p(i,{prop:"galvanizedInternalConsumed",label:"实耗",width:"80",align:"center"}),p(i,{prop:"galvanizedInternalPrice",label:"单价(元/个)",width:"100",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.galvanizedInternalPrice)),1)]),_:1})]),_:1}),p(i,{label:"镀锌内外丝弯头(个)",align:"center"},{default:s(()=>[p(i,{prop:"galvanizedInternalExternalElbowReceived",label:"实领",width:"80",align:"center"}),p(i,{prop:"galvanizedInternalExternalElbowConsumed",label:"实耗",width:"80",align:"center"}),p(i,{prop:"galvanizedInternalExternalElbowPrice",label:"单价(元/个)",width:"100",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.galvanizedInternalExternalElbowPrice)),1)]),_:1})]),_:1}),p(i,{label:"镀锌三通(个)",align:"center"},{default:s(()=>[p(i,{prop:"galvanizedTeeReceived",label:"实领",width:"80",align:"center"}),p(i,{prop:"galvanizedTeeConsumed",label:"实耗",width:"80",align:"center"}),p(i,{prop:"galvanizedTeePrice",label:"单价(元/个)",width:"100",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.galvanizedTeePrice)),1)]),_:1})]),_:1}),p(i,{label:"不锈钢管卡架(钢管用)(个)",align:"center"},{default:s(()=>[p(i,{prop:"stainlessSteelClampReceived",label:"实领",width:"80",align:"center"}),p(i,{prop:"stainlessSteelClampConsumed",label:"实耗",width:"80",align:"center"}),p(i,{prop:"stainlessSteelClampPrice",label:"单价(元/个)",width:"100",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.stainlessSteelClampPrice)),1)]),_:1})]),_:1}),p(i,{label:"定制镀锌补芯(个)",align:"center"},{default:s(()=>[p(i,{prop:"customGalvanizedBushingReceived",label:"实领",width:"80",align:"center"}),p(i,{prop:"customGalvanizedBushingConsumed",label:"实耗",width:"80",align:"center"}),p(i,{prop:"customGalvanizedBushingPrice",label:"单价(元/个)",width:"100",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.customGalvanizedBushingPrice)),1)]),_:1})]),_:1}),p(i,{label:"表具波纹管(个)",align:"center"},{default:s(()=>[p(i,{prop:"meterCorrugatedPipeReceived",label:"实领",width:"80",align:"center"}),p(i,{prop:"meterCorrugatedPipeConsumed",label:"实耗",width:"80",align:"center"}),p(i,{prop:"meterCorrugatedPipePrice",label:"单价(元/个)",width:"100",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.meterCorrugatedPipePrice)),1)]),_:1})]),_:1}),p(i,{label:"燃气表托架(个)",align:"center"},{default:s(()=>[p(i,{prop:"gasMeterBracketReceived",label:"实领",width:"80",align:"center"}),p(i,{prop:"gasMeterBracketConsumed",label:"实耗",width:"80",align:"center"}),p(i,{prop:"gasMeterBracketPrice",label:"单价(元/个)",width:"100",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.gasMeterBracketPrice)),1)]),_:1})]),_:1}),p(i,{label:"防盗气锁卡(个)",align:"center"},{default:s(()=>[p(i,{prop:"antiTheftLockClipReceived",label:"实领",width:"80",align:"center"}),p(i,{prop:"antiTheftLockClipConsumed",label:"实耗",width:"80",align:"center"}),p(i,{prop:"antiTheftLockClipPrice",label:"单价(元/个)",width:"100",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.antiTheftLockClipPrice)),1)]),_:1})]),_:1}),p(i,{label:"镀锌管堵(个)",align:"center"},{default:s(()=>[p(i,{prop:"galvanizedPipePlugReceived",label:"实领",width:"80",align:"center"}),p(i,{prop:"galvanizedPipePlugConsumed",label:"实耗",width:"80",align:"center"}),p(i,{prop:"galvanizedPipePlugPrice",label:"单价(元/个)",width:"100",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.galvanizedPipePlugPrice)),1)]),_:1})]),_:1}),p(i,{label:"定中装饰盖(个)",align:"center"},{default:s(()=>[p(i,{prop:"centeringDecorativeCoverReceived",label:"实领",width:"80",align:"center"}),p(i,{prop:"centeringDecorativeCoverConsumed",label:"实耗",width:"80",align:"center"}),p(i,{prop:"centeringDecorativeCoverPrice",label:"单价(元/个)",width:"100",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.centeringDecorativeCoverPrice)),1)]),_:1})]),_:1})]),_:1}),p(i,{label:"户内管件甲供材料金额(元)",align:"center"},{default:s(()=>[p(i,{prop:"totalReceivedAmount",label:"实领",width:"100",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.totalReceivedAmount)),1)]),_:1}),p(i,{prop:"totalConsumedAmount",label:"实耗",width:"100",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.totalConsumedAmount)),1)]),_:1})]),_:1})]),_:1},8,["data"])]),_:1}),p(d,{label:"户内挂表安装工程管件统计",name:"meter-installation-fittings"},{default:s(()=>[p(o,{data:q.value,style:{width:"100%"},border:"","show-summary":"","summary-method":we},{default:s(()=>[p(i,{prop:"serialNo",label:"序号",width:"60",align:"center"}),p(i,{label:"户主信息",align:"center"},{default:s(()=>[p(i,{prop:"customerName",label:"姓名",width:"100",align:"center"}),p(i,{prop:"customerCode",label:"用户编号",width:"120",align:"center"}),p(i,{prop:"customerAddress",label:"住址",width:"200",align:"center"})]),_:1}),p(i,{prop:"dispatchTime",label:"派单时间",width:"120",align:"center"}),p(i,{prop:"installTime",label:"安装时间",width:"120",align:"center"}),p(i,{prop:"corrugatedQuickConnector",label:"波纹管快速外螺纹接头",width:"120",align:"center"}),p(i,{prop:"galvanized90Elbow",label:"镀锌90°弯头",width:"100",align:"center"}),p(i,{prop:"galvanizedHexMaleThread",label:"镀锌六角外丝",width:"80",align:"center"}),p(i,{prop:"galvanizedFemaleThread",label:"镀锌内丝",width:"80",align:"center"}),p(i,{prop:"galvanizedMaleFemaleElbow",label:"镀锌内外丝弯头",width:"100",align:"center"}),p(i,{prop:"galvanizedTee",label:"镀锌三通",width:"80",align:"center"}),p(i,{prop:"stainlessSteelClamp",label:"不锈钢管卡架(钢管用)",width:"140",align:"center"}),p(i,{prop:"customGalvanizedBushing",label:"定制镀锌补芯",width:"100",align:"center"}),p(i,{prop:"meterCorrugatedPipe",label:"表具波纹管",width:"100",align:"center"}),p(i,{prop:"gasMeterBracket",label:"燃气表托架",width:"100",align:"center"}),p(i,{prop:"antiTheftGasLock",label:"防盗气锁卡",width:"100",align:"center"}),p(i,{prop:"galvanizedPlug",label:"镀锌管堵",width:"100",align:"center"}),p(i,{prop:"protectionPlateStraight",label:"输送波纹管防护钢板（直板）",width:"180",align:"center"}),p(i,{prop:"protectionPlateBend",label:"输送波纹管防护钢板（外、侧弯）",width:"200",align:"center"}),p(i,{prop:"decorativeCover",label:"定中装饰盖",width:"100",align:"center"}),p(i,{prop:"actualMaterialCost",label:"实际耗用甲供材料金额-户内（元）",width:"200",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.actualMaterialCost)),1)]),_:1})]),_:1},8,["data"])]),_:1}),p(d,{label:"户内零星安装工程管件统计",name:"minor-installation-fittings"},{default:s(()=>[p(o,{data:W.value,style:{width:"100%"},border:"","show-summary":"","summary-method":fe},{default:s(()=>[p(i,{prop:"serialNo",label:"序号",width:"60",align:"center"}),p(i,{label:"户主信息",align:"center"},{default:s(()=>[p(i,{prop:"customerName",label:"姓名",width:"100",align:"center"}),p(i,{prop:"customerCode",label:"编号",width:"120",align:"center"}),p(i,{label:"详细地址",align:"center"},{default:s(()=>[p(i,{prop:"fullAddress",label:"详细地址 汇总",width:"180",align:"center"}),p(i,{prop:"communityName",label:"小区名称",width:"120",align:"center"}),p(i,{prop:"buildingNo",label:"楼橦",width:"80",align:"center"}),p(i,{prop:"roomNo",label:"房号",width:"80",align:"center"})]),_:1})]),_:1}),p(i,{prop:"dispatchDate",label:"派单日期",width:"120",align:"center"}),p(i,{prop:"constructionDate",label:"施工日期",width:"120",align:"center"}),p(i,{prop:"corrugatedQuickConnector",label:"波纹管快速外螺纹接头",width:"120",align:"center"}),p(i,{prop:"galvanized90Elbow",label:"镀锌90°弯头",width:"100",align:"center"}),p(i,{prop:"galvanizedHexMaleThread",label:"镀锌六角外丝",width:"80",align:"center"}),p(i,{prop:"galvanizedFemaleThread",label:"镀锌内丝",width:"80",align:"center"}),p(i,{prop:"galvanizedMaleFemaleElbow",label:"镀锌内外丝弯头",width:"100",align:"center"}),p(i,{prop:"galvanizedTee",label:"镀锌三通",width:"80",align:"center"}),p(i,{prop:"stainlessSteelClamp",label:"不锈钢管卡架(钢管用)",width:"140",align:"center"}),p(i,{prop:"customGalvanizedBushing",label:"定制镀锌补芯",width:"100",align:"center"}),p(i,{prop:"meterCorrugatedPipe",label:"表具波纹管",width:"100",align:"center"}),p(i,{prop:"gasMeterBracket",label:"燃气表托架",width:"100",align:"center"}),p(i,{prop:"antiTheftGasLock",label:"防盗气锁卡",width:"100",align:"center"}),p(i,{prop:"galvanizedPlug",label:"镀锌管堵",width:"100",align:"center"}),p(i,{prop:"protectionPlateStraight",label:"输送波纹管防护钢板（直板）",width:"180",align:"center"}),p(i,{prop:"protectionPlateBend",label:"输送波纹管防护钢板（外、侧弯）",width:"200",align:"center"}),p(i,{prop:"decorativeCover",label:"定中装饰盖",width:"100",align:"center"}),p(i,{prop:"actualMaterialCost",label:"实际耗用甲供材料金额-户内（元）",width:"200",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.actualMaterialCost)),1)]),_:1})]),_:1},8,["data"])]),_:1}),p(d,{label:"户内二次安装工程管件统计",name:"secondary-installation-fittings"},{default:s(()=>[p(o,{data:O.value,style:{width:"100%"},border:"","show-summary":"","summary-method":ve},{default:s(()=>[p(i,{prop:"serialNo",label:"序号",width:"60",align:"center"}),p(i,{label:"户主信息",align:"center"},{default:s(()=>[p(i,{prop:"customerName",label:"姓名",width:"100",align:"center"}),p(i,{prop:"customerCode",label:"编号",width:"120",align:"center"}),p(i,{label:"详细地址",align:"center"},{default:s(()=>[p(i,{prop:"communityName",label:"小区名称",width:"120",align:"center"}),p(i,{prop:"buildingNo",label:"楼橦",width:"80",align:"center"}),p(i,{prop:"roomNo",label:"房号",width:"80",align:"center"})]),_:1})]),_:1}),p(i,{prop:"dispatchDate",label:"派单日期",width:"120",align:"center"}),p(i,{prop:"constructionDate",label:"施工日期",width:"120",align:"center"}),p(i,{prop:"customGalvanizedBushing",label:"定制镀锌补芯",width:"100",align:"center"}),p(i,{prop:"meterCorrugatedPipe",label:"表具波纹管",width:"100",align:"center"}),p(i,{prop:"gasMeterBracket",label:"燃气表托架",width:"100",align:"center"}),p(i,{prop:"antiTheftGasLock",label:"防盗气锁卡",width:"100",align:"center"}),p(i,{prop:"actualMaterialCost",label:"实际耗用甲供材料金额-户内（元）",width:"200",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.actualMaterialCost)),1)]),_:1})]),_:1},8,["data"])]),_:1}),p(d,{label:"户内安装管件统计表",name:"indoor-installation-fittings"},{default:s(()=>[p(o,{data:U.value,style:{width:"100%"},border:"","show-summary":"","summary-method":_e},{default:s(()=>[p(i,{prop:"serialNo",label:"序号",width:"60",align:"center"}),p(i,{prop:"projectName",label:"单项工程名称",width:"200"}),p(i,{prop:"corrugatedConnector",label:"波纹管接头（个）",width:"100",align:"center"}),p(i,{prop:"galvanized90Elbow",label:"镀锌90°弯头（个）",width:"120",align:"center"}),p(i,{prop:"galvanizedHexMaleThread",label:"镀锌六角外丝（个）",width:"120",align:"center"}),p(i,{prop:"galvanizedFemaleThread",label:"镀锌内丝（个）",width:"100",align:"center"}),p(i,{prop:"galvanizedMaleFemaleElbow",label:"镀锌内外丝弯头（个）",width:"140",align:"center"}),p(i,{prop:"galvanizedTee",label:"镀锌三通（个）",width:"100",align:"center"}),p(i,{prop:"stainlessSteelClamp",label:"不锈钢管卡架(钢管用)（个）",width:"160",align:"center"}),p(i,{prop:"customGalvanizedBushing",label:"定制镀锌补芯（个）",width:"120",align:"center"}),p(i,{prop:"corrugatedShortPipe",label:"波纹管短管（个）",width:"120",align:"center"}),p(i,{prop:"gasMeterBracket",label:"燃气表托架（个）",width:"120",align:"center"}),p(i,{prop:"antiTheftGasLock",label:"防盗气锁卡（个）",width:"120",align:"center"}),p(i,{prop:"galvanizedPlug",label:"镀锌管堵（个）",width:"120",align:"center"}),p(i,{prop:"protectionPlateStraight",label:"输送波纹管防护钢板（直板）",width:"180",align:"center"}),p(i,{prop:"protectionPlateBend",label:"输送波纹管防护钢板（外、侧弯）",width:"200",align:"center"}),p(i,{prop:"decorativeCover",label:"定中装饰盖（个）",width:"120",align:"center"}),p(i,{prop:"actualMaterialCost",label:"实际耗用甲供材料金额-户内（元）",width:"200",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.actualMaterialCost)),1)]),_:1})]),_:1},8,["data"])]),_:1}),p(d,{label:"户内挂表安装工程决算（半月板）",name:"meter-installation-settlement-half"},{default:s(()=>[p(o,{data:J.value,style:{width:"100%"},border:"","show-summary":"","summary-method":Ce},{default:s(()=>[p(i,{prop:"serialNo",label:"序号",width:"60",align:"center"}),p(i,{label:"户主信息",align:"center"},{default:s(()=>[p(i,{prop:"customerName",label:"姓名",width:"100",align:"center"}),p(i,{prop:"customerCode",label:"用户编号",width:"120",align:"center"}),p(i,{label:"住址",align:"center"},{default:s(()=>[p(i,{prop:"communityName",label:"小区名称",width:"120",align:"center"}),p(i,{prop:"buildingNo",label:"楼橦",width:"80",align:"center"}),p(i,{prop:"roomNo",label:"房号",width:"80",align:"center"})]),_:1})]),_:1}),p(i,{prop:"dispatchTime",label:"派单时间",width:"120",align:"center"}),p(i,{prop:"installTime",label:"安装时间",width:"120",align:"center"}),p(i,{label:"煤气表信息",align:"center"},{default:s(()=>[p(i,{prop:"hasMeterBox",label:"有/无 表箱",width:"100",align:"center"}),p(i,{prop:"preMeterValve",label:"表前阀",width:"80",align:"center"})]),_:1}),p(i,{label:"包干价（元）",align:"center"},{default:s(()=>[p(i,{prop:"preMeterPrice",label:"表前",width:"80",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.preMeterPrice)),1)]),_:1}),p(i,{prop:"indoorPrice",label:"户内",width:"80",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.indoorPrice)),1)]),_:1})]),_:1}),p(i,{prop:"stoveFrontValveQty",label:"灶前阀 数量（个）",width:"120",align:"center"}),p(i,{label:"表前管道耗材",align:"center"},{default:s(()=>[p(i,{prop:"preSteelPipe",label:"钢管（米）",width:"100",align:"center"}),p(i,{prop:"preCoatedSteelPipe",label:"涂覆钢管（米）",width:"120",align:"center"}),p(i,{prop:"preCorrugatedPipe",label:"波纹管（米）",width:"120",align:"center"})]),_:1}),p(i,{label:"户内管道耗材",align:"center"},{default:s(()=>[p(i,{prop:"indoorSteelPipe",label:"钢管（米）",width:"100",align:"center"}),p(i,{prop:"indoorCoatedSteelPipe",label:"涂覆钢管（米）",width:"120",align:"center"}),p(i,{prop:"indoorCorrugatedPipe",label:"波纹管（米）",width:"120",align:"center"})]),_:1}),p(i,{prop:"mechanicalMeter",label:"机械表",width:"80",align:"center"}),p(i,{label:"表前阀",align:"center"},{default:s(()=>[p(i,{prop:"lowPressureRegulator",label:"低低压调压器（个）",width:"140",align:"center"}),p(i,{prop:"corrugatedConnectLong",label:"波纹连接管（1.2m,1m）",width:"160",align:"center"}),p(i,{prop:"corrugatedConnectShort",label:"波纹连接管（0.8m,0.5m）",width:"160",align:"center"}),p(i,{prop:"prefabShort12",label:"预制短管12cm",width:"120",align:"center"}),p(i,{prop:"prefabShort18",label:"预制短管18cm",width:"120",align:"center"})]),_:1}),p(i,{prop:"preMeterInstallCost",label:"表前安装成本（元）",width:"140",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.preMeterInstallCost)),1)]),_:1}),p(i,{prop:"indoorInstallCost",label:"户内安装成本（元）",width:"140",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.indoorInstallCost)),1)]),_:1})]),_:1},8,["data"])]),_:1}),p(d,{label:"户内挂表安装工程决算（未半月板）",name:"meter-installation-settlement-no-half"},{default:s(()=>[p(o,{data:K.value,style:{width:"100%"},border:"","show-summary":"","summary-method":Pe},{default:s(()=>[p(i,{prop:"serialNo",label:"序号",width:"60",align:"center"}),p(i,{label:"户主信息",align:"center"},{default:s(()=>[p(i,{label:"住址 汇总",align:"center"},{default:s(()=>[p(i,{prop:"communityName",label:"小区名称",width:"120",align:"center"}),p(i,{prop:"buildingNo",label:"楼橦",width:"80",align:"center"}),p(i,{prop:"roomNo",label:"房号",width:"80",align:"center"})]),_:1})]),_:1}),p(i,{prop:"dispatchTime",label:"派单时间",width:"120",align:"center"}),p(i,{prop:"installTime",label:"安装时间",width:"120",align:"center"}),p(i,{label:"煤气表信息",align:"center"},{default:s(()=>[p(i,{prop:"hasMeterBox",label:"有/无 表箱",width:"100",align:"center"}),p(i,{prop:"preMeterValve",label:"表前阀",width:"80",align:"center"})]),_:1}),p(i,{label:"包干价（元）",align:"center"},{default:s(()=>[p(i,{prop:"preMeterPrice",label:"表前",width:"80",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.preMeterPrice)),1)]),_:1}),p(i,{prop:"indoorPrice",label:"户内",width:"80",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.indoorPrice)),1)]),_:1})]),_:1}),p(i,{prop:"stoveFrontValveQty",label:"灶前阀 数量（个）",width:"120",align:"center"}),p(i,{label:"表前管道耗材",align:"center"},{default:s(()=>[p(i,{prop:"preSteelPipe",label:"钢管（米）",width:"100",align:"center"}),p(i,{prop:"preCoatedSteelPipe",label:"涂覆钢管（米）",width:"120",align:"center"}),p(i,{prop:"preCorrugatedPipe",label:"波纹管（米）",width:"120",align:"center"})]),_:1}),p(i,{label:"户内管道耗材",align:"center"},{default:s(()=>[p(i,{prop:"indoorSteelPipe",label:"钢管（米）",width:"100",align:"center"}),p(i,{prop:"indoorCoatedSteelPipe",label:"涂覆钢管（米）",width:"120",align:"center"}),p(i,{prop:"indoorCorrugatedPipe",label:"波纹管（米）",width:"120",align:"center"})]),_:1}),p(i,{prop:"mechanicalMeter",label:"机械表",width:"80",align:"center"}),p(i,{label:"表前阀",align:"center"},{default:s(()=>[p(i,{prop:"lowPressureRegulator",label:"低低压调压器（个）",width:"140",align:"center"}),p(i,{prop:"corrugatedConnectLong",label:"波纹连接管（1.2m,1m）",width:"160",align:"center"}),p(i,{prop:"corrugatedConnectShort",label:"波纹连接管（0.8m,0.5m）",width:"160",align:"center"}),p(i,{prop:"prefabShort12",label:"预制短管12cm",width:"120",align:"center"}),p(i,{prop:"prefabShort18",label:"预制短管18cm",width:"120",align:"center"})]),_:1}),p(i,{prop:"preMeterInstallCost",label:"表前安装成本（元）",width:"140",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.preMeterInstallCost)),1)]),_:1}),p(i,{prop:"indoorInstallCost",label:"户内安装成本（元）",width:"140",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.indoorInstallCost)),1)]),_:1})]),_:1},8,["data"])]),_:1}),p(d,{label:"管道燃气户内零星安装工程决算（半月板）",name:"gas-minor-settlement-half"},{default:s(()=>[p(o,{data:X.value,style:{width:"100%"},border:"","show-summary":"","summary-method":ye},{default:s(()=>[p(i,{label:"户主信息",align:"center"},{default:s(()=>[p(i,{prop:"customerName",label:"姓名",width:"100",align:"center"}),p(i,{prop:"customerCode",label:"编号",width:"120",align:"center"}),p(i,{label:"详细地址",align:"center"},{default:s(()=>[p(i,{prop:"communityName",label:"小区名称",width:"120",align:"center"}),p(i,{prop:"buildingNo",label:"楼橦",width:"80",align:"center"}),p(i,{prop:"roomNo",label:"房号",width:"80",align:"center"})]),_:1})]),_:1}),p(i,{prop:"dispatchDate",label:"派单日期",width:"120",align:"center"}),p(i,{prop:"constructionDate",label:"施工日期",width:"120",align:"center"}),p(i,{prop:"firePointQty",label:"火点数量（个）",width:"120",align:"center"}),p(i,{prop:"stoveFrontValveQty",label:"灶前阀数量（个）",width:"130",align:"center"}),p(i,{label:"安装项目",align:"center"},{default:s(()=>[p(i,{prop:"gasMeterBox",label:"燃气表箱(个)",width:"120",align:"center"}),p(i,{prop:"preMeterValve",label:"表前阀(个)",width:"100",align:"center"}),p(i,{prop:"lowPressureRegulator",label:"低低压调压器",width:"120",align:"center"}),p(i,{prop:"gasMeter",label:"燃气表（个）",width:"120",align:"center"})]),_:1}),p(i,{prop:"packagePrice",label:"包干价（元）",width:"120",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.packagePrice)),1)]),_:1}),p(i,{label:"管道材料耗用统计（米）",align:"center"},{default:s(()=>[p(i,{prop:"dn15SteelPipe",label:"DN15钢管",width:"100",align:"center"}),p(i,{prop:"dn15CoatedSteelPipe",label:"DN15涂覆钢管",width:"130",align:"center"}),p(i,{prop:"corrugatedPipe",label:"波纹管",width:"100",align:"center"})]),_:1}),p(i,{prop:"corrugatedConnectStove",label:"波纹管连接（灶）",width:"140",align:"center"}),p(i,{prop:"corrugatedConnectHeat",label:"波纹管连接（热）",width:"140",align:"center"}),p(i,{prop:"prefabShort12",label:"预制短管12cm",width:"120",align:"center"}),p(i,{prop:"prefabShort18",label:"预制短管18cm",width:"120",align:"center"}),p(i,{prop:"singleHouseholdCost",label:"单户户内零星安装工程造价（元）",width:"200",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.singleHouseholdCost)),1)]),_:1})]),_:1},8,["data"])]),_:1}),p(d,{label:"管道燃气户内零星安装工程决算（未半月板）",name:"gas-minor-settlement-no-half"},{default:s(()=>[p(o,{data:Y.value,style:{width:"100%"},border:"","show-summary":"","summary-method":Me},{default:s(()=>[p(i,{prop:"serialNo",label:"序号",width:"60",align:"center"}),p(i,{label:"户主信息",align:"center"},{default:s(()=>[p(i,{prop:"customerName",label:"姓名",width:"100",align:"center"}),p(i,{prop:"customerCode",label:"编号",width:"120",align:"center"}),p(i,{label:"详细地址",align:"center"},{default:s(()=>[p(i,{prop:"communityName",label:"小区名称",width:"120",align:"center"}),p(i,{prop:"buildingNo",label:"楼橦",width:"80",align:"center"}),p(i,{prop:"roomNo",label:"房号",width:"80",align:"center"})]),_:1})]),_:1}),p(i,{prop:"dispatchDate",label:"派单日期",width:"120",align:"center"}),p(i,{prop:"constructionDate",label:"施工日期",width:"120",align:"center"}),p(i,{prop:"firePointQty",label:"火点数量（个）",width:"120",align:"center"}),p(i,{prop:"stoveFrontValveQty",label:"灶前阀数量（个）",width:"130",align:"center"}),p(i,{label:"安装项目",align:"center"},{default:s(()=>[p(i,{prop:"gasMeterBox",label:"燃气表箱(个)",width:"120",align:"center"}),p(i,{prop:"preMeterValve",label:"表前阀(个)",width:"100",align:"center"}),p(i,{prop:"lowPressureRegulator",label:"低低压调压器",width:"120",align:"center"}),p(i,{prop:"gasMeter",label:"燃气表（个）",width:"120",align:"center"})]),_:1}),p(i,{prop:"packagePrice",label:"包干价（元）",width:"120",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.packagePrice)),1)]),_:1}),p(i,{label:"管道材料耗用统计（米）",align:"center"},{default:s(()=>[p(i,{prop:"dn15SteelPipe",label:"DN15钢管",width:"100",align:"center"}),p(i,{prop:"dn15CoatedSteelPipe",label:"DN15涂覆钢管",width:"130",align:"center"}),p(i,{prop:"corrugatedPipe",label:"波纹管",width:"100",align:"center"})]),_:1}),p(i,{prop:"corrugatedConnectStove",label:"波纹管连接（灶）",width:"140",align:"center"}),p(i,{prop:"corrugatedConnectHeat",label:"波纹管连接（热）",width:"140",align:"center"}),p(i,{prop:"prefabShort12",label:"预制短管12cm",width:"120",align:"center"}),p(i,{prop:"prefabShort18",label:"预制短管18cm",width:"120",align:"center"}),p(i,{prop:"singleHouseholdCost",label:"单户户内零星安装工程造价（元）",width:"200",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.singleHouseholdCost)),1)]),_:1})]),_:1},8,["data"])]),_:1}),p(d,{label:"管道燃气户内零星安装工程决算（不可用燃气表）",name:"gas-minor-settlement-no-meter"},{default:s(()=>[p(o,{data:Z.value,style:{width:"100%"},border:"","show-summary":"","summary-method":Ne},{default:s(()=>[p(i,{prop:"serialNo",label:"序号",width:"60",align:"center"}),p(i,{label:"户主信息",align:"center"},{default:s(()=>[p(i,{prop:"customerName",label:"姓名",width:"100",align:"center"}),p(i,{prop:"customerCode",label:"编号",width:"120",align:"center"}),p(i,{label:"详细地址",align:"center"},{default:s(()=>[p(i,{prop:"communityName",label:"小区名称",width:"120",align:"center"}),p(i,{prop:"buildingNo",label:"楼橦",width:"80",align:"center"}),p(i,{prop:"roomNo",label:"房号",width:"80",align:"center"})]),_:1})]),_:1}),p(i,{prop:"dispatchDate",label:"派单日期",width:"120",align:"center"}),p(i,{prop:"constructionDate",label:"施工日期",width:"120",align:"center"}),p(i,{prop:"firePointQty",label:"火点数量（个）",width:"120",align:"center"}),p(i,{prop:"stoveFrontValveQty",label:"灶前阀数量（个）",width:"130",align:"center"}),p(i,{label:"安装项目",align:"center"},{default:s(()=>[p(i,{prop:"gasMeterBox",label:"燃气表箱(个)",width:"120",align:"center"}),p(i,{prop:"preMeterValve",label:"表前阀(个)",width:"100",align:"center"}),p(i,{prop:"lowPressureRegulator",label:"低低压调压器",width:"120",align:"center"}),p(i,{prop:"gasMeter",label:"燃气表（个）",width:"120",align:"center"})]),_:1}),p(i,{prop:"packagePrice",label:"包干价（元）",width:"120",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.packagePrice)),1)]),_:1}),p(i,{label:"管道材料耗用统计（米）",align:"center"},{default:s(()=>[p(i,{prop:"dn15SteelPipe",label:"DN15钢管",width:"100",align:"center"}),p(i,{prop:"dn15CoatedSteelPipe",label:"DN15涂覆钢管",width:"130",align:"center"}),p(i,{prop:"corrugatedPipe",label:"波纹管",width:"100",align:"center"})]),_:1}),p(i,{prop:"corrugatedConnectStove",label:"波纹管连接（灶）",width:"140",align:"center"}),p(i,{prop:"corrugatedConnectHeat",label:"波纹管连接（热）",width:"140",align:"center"}),p(i,{prop:"prefabShort12",label:"预制短管12cm",width:"120",align:"center"}),p(i,{prop:"prefabShort18",label:"预制短管18cm",width:"120",align:"center"}),p(i,{prop:"singleHouseholdCost",label:"单户户内零星安装工程造价（元）",width:"200",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.singleHouseholdCost)),1)]),_:1})]),_:1},8,["data"])]),_:1}),p(d,{label:"甲供材料领用表",name:"supplied-materials-receipt"},{default:s(()=>[p(o,{data:ee.value,style:{width:"100%"},border:"","show-summary":"","summary-method":Se},{default:s(()=>[p(i,{prop:"receiptDate",label:"领料日期",width:"120",align:"center"}),p(i,{prop:"materialCode",label:"物料编码",width:"120",align:"center"}),p(i,{prop:"materialName",label:"材料名称",width:"200"}),p(i,{prop:"specification",label:"型号规格",width:"150"}),p(i,{prop:"unit",label:"单位",width:"80",align:"center"}),p(i,{prop:"receivedQuantity",label:"领料数",width:"100",align:"center"}),p(i,{prop:"unitPriceExcludingTax",label:"不含税单价",width:"120",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.unitPriceExcludingTax)),1)]),_:1}),p(i,{prop:"unitPriceIncludingTax",label:"含税单价",width:"120",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.unitPriceIncludingTax)),1)]),_:1}),p(i,{prop:"amountExcludingTax",label:"不含税金额",width:"120",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.amountExcludingTax)),1)]),_:1}),p(i,{prop:"amountIncludingTax",label:"含税金额",width:"120",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.amountIncludingTax)),1)]),_:1}),p(i,{prop:"totalDeductibleAmount",label:"总应扣材料金额",width:"140",align:"center"},{default:s(({row:e})=>[m(" ¥"+w(pe(e.totalDeductibleAmount)),1)]),_:1}),p(i,{prop:"remarks",label:"备注",width:"200"})]),_:1},8,["data"])]),_:1})]),_:1},8,["modelValue"])]),_:1}),g("div",y,[p(te,{type:"primary",onClick:ie},{default:s(()=>a[7]||(a[7]=[m("确认平账",-1)])),_:1,__:[7]}),p(te,{type:"success",onClick:ke},{default:s(()=>a[8]||(a[8]=[m("导出报表",-1)])),_:1,__:[8]}),p(te,{type:"info",onClick:de},{default:s(()=>a[9]||(a[9]=[m("打印",-1)])),_:1,__:[9]}),p(te,{onClick:ce},{default:s(()=>a[10]||(a[10]=[m("取消",-1)])),_:1,__:[10]})]),p(re,{modelValue:ae.value,"onUpdate:modelValue":a[2]||(a[2]=e=>ae.value=e),title:"确认平账",width:"500px","before-close":oe},{footer:s(()=>[g("span",F,[p(te,{onClick:a[1]||(a[1]=e=>ae.value=!1)},{default:s(()=>a[18]||(a[18]=[m("取消",-1)])),_:1,__:[18]}),p(te,{type:"primary",onClick:ne},{default:s(()=>a[19]||(a[19]=[m("确认",-1)])),_:1,__:[19]})])]),default:s(()=>[g("div",M,[g("div",N,[a[11]||(a[11]=g("span",{class:"label"},"平账月份:",-1)),g("span",S,w(_.month),1)]),g("div",k,[a[12]||(a[12]=g("span",{class:"label"},"平账时间:",-1)),g("span",R,w(se(_.balanceDate)),1)]),g("div",I,[a[16]||(a[16]=g("div",{class:"section-title"},"平账内容:",-1)),g("div",z,[a[13]||(a[13]=g("span",{class:"label"},"人工费:",-1)),g("span",x,"¥"+w(pe(_.laborCost)),1)]),g("div",H,[a[14]||(a[14]=g("span",{class:"label"},"超领材料:",-1)),g("span",T,w(_.overIssuedItems)+"项，"+w(_.overIssuedQuantity)+"件",1)]),g("div",B,[a[15]||(a[15]=g("span",{class:"label"},"师傅手中未退回材料:",-1)),g("span",j,w(_.unreturnedItems)+"项，"+w(_.unreturnedQuantity)+"件",1)])]),a[17]||(a[17]=g("div",{class:"confirm-warning"}," 确认平账后将无法修改，是否继续？ ",-1))])]),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-68c126b9"]]);export{D as default};
