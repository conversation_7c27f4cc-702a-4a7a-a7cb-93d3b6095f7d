var e=Object.defineProperty,t=Object.defineProperties,a=Object.getOwnPropertyDescriptors,l=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,u=Object.prototype.propertyIsEnumerable,d=(t,a,l)=>a in t?e(t,a,{enumerable:!0,configurable:!0,writable:!0,value:l}):t[a]=l;import{E as i}from"./element-plus-ad78a7bf.js";import{l as s,_ as r,r as n,y as c,R as p,J as m,av as _,x as f,z as b,O as y,Q as v,aa as h,P as k}from"./vue-vendor-fc5a6493.js";import{_ as C}from"./_plugin-vue_export-helper-1b428a4d.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const P={class:"product-outbound-container"},w={style:{"margin-top":"15px"}},V={class:"add-product-actions"},Q={class:"statistic-item"},g={class:"statistic-item"},D={class:"total-price"},N={class:"form-actions"},x=C(s({__name:"ProductOutbound",setup(e){const s=r({outboundDate:"2024-01-15",operator:"张三",customerName:"",customerPhone:"",remarks:""}),C=n([{id:1,materialCode:"SP001",partyCode:["JD001","JD002"],productName:"智能开关",model:"KG-86",specification:"86型",unit:"个",stockQuantity:100,outboundQuantity:20,unitPrice:"¥45.00",subtotal:"¥900"},{id:2,materialCode:"SP002",partyCode:["JD003"],productName:"LED灯具",model:"LED-12W",specification:"12W",unit:"个",stockQuantity:200,outboundQuantity:15,unitPrice:"¥130.00",subtotal:"¥1,950"},{id:3,materialCode:"SP003",partyCode:["JD004","JD005"],productName:"插座面板",model:"ZP-86",specification:"86型",unit:"个",stockQuantity:150,outboundQuantity:25,unitPrice:"¥60.00",subtotal:"¥1,500"},{id:4,materialCode:"SP004",partyCode:["JD006"],productName:"电线",model:"BV-2.5",specification:"2.5mm²",unit:"米",stockQuantity:1e3,outboundQuantity:100,unitPrice:"¥5.00",subtotal:"¥500"},{id:5,materialCode:"SP005",partyCode:["JD007"],productName:"开关面板",model:"KP-86",specification:"86型",unit:"个",stockQuantity:100,outboundQuantity:30,unitPrice:"¥250.00",subtotal:"¥7,500"}]),x=n([{id:1,materialCode:"SP001",productName:"智能开关",model:"KG-86",specification:"86型",unit:"个",stockQuantity:100},{id:2,materialCode:"SP002",productName:"LED灯具",model:"LED-12W",specification:"12W",unit:"个",stockQuantity:200},{id:3,materialCode:"SP003",productName:"插座面板",model:"ZP-86",specification:"86型",unit:"个",stockQuantity:150},{id:4,materialCode:"SP004",productName:"电线",model:"BV-2.5",specification:"2.5mm²",unit:"米",stockQuantity:1e3}]),j=n([]),O=r({totalProducts:5,totalPrice:"12,350"}),S=n(!1),J=n(!1),U=n(""),Y=()=>{S.value=!0},E=()=>{i.success("搜索商品")},L=e=>{var s;C.value.some(t=>t.id===e.id)?i.warning("该商品已添加"):(C.value.push((s=((e,t)=>{for(var a in t||(t={}))o.call(t,a)&&d(e,a,t[a]);if(l)for(var a of l(t))u.call(t,a)&&d(e,a,t[a]);return e})({},e),t(s,a({partyCode:["JD001","JD002"],outboundQuantity:1,unitPrice:"¥0.00",subtotal:"¥0.00"})))),S.value=!1,i.success("添加成功"),M())},K=()=>{i.success("添加商品")},M=()=>{O.totalProducts=C.value.length},W=()=>{i.success("保存成功")},B=()=>{i.success("开始打印")},G=()=>{i.success("提交成功")},I=()=>{i.info("已取消")};return(e,t)=>{const a=_("el-col"),l=_("el-form-item"),o=_("el-date-picker"),u=_("el-input"),d=_("el-row"),r=_("el-table-column"),n=_("el-button"),Z=_("el-input-number"),z=_("el-table"),R=_("el-dialog"),$=_("el-card");return f(),c("div",P,[p($,{class:"main-card"},{header:m(()=>t[10]||(t[10]=[b("div",{class:"card-header"},[b("span",null,"商品售卖出库")],-1)])),default:m(()=>[p(d,{gutter:20,class:"form-section"},{default:m(()=>[p(a,{span:24},{default:m(()=>t[11]||(t[11]=[b("div",{class:"section-title"},"出库信息",-1)])),_:1,__:[11]}),p(a,{span:12},{default:m(()=>[p(l,{label:"出库单号:"},{default:m(()=>t[12]||(t[12]=[b("span",null,"CK20240115001",-1)])),_:1,__:[12]})]),_:1}),p(a,{span:12},{default:m(()=>[p(l,{label:"出库日期:"},{default:m(()=>[p(o,{modelValue:s.outboundDate,"onUpdate:modelValue":t[0]||(t[0]=e=>s.outboundDate=e),type:"date",placeholder:"请选择出库日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),p(a,{span:12},{default:m(()=>[p(l,{label:"操作员:"},{default:m(()=>[p(u,{modelValue:s.operator,"onUpdate:modelValue":t[1]||(t[1]=e=>s.operator=e),placeholder:"请输入操作员"},null,8,["modelValue"])]),_:1})]),_:1}),p(a,{span:12},{default:m(()=>[p(l,{label:"客户姓名:"},{default:m(()=>[p(u,{modelValue:s.customerName,"onUpdate:modelValue":t[2]||(t[2]=e=>s.customerName=e),placeholder:"请输入客户姓名"},null,8,["modelValue"])]),_:1})]),_:1}),p(a,{span:12},{default:m(()=>[p(l,{label:"联系电话:"},{default:m(()=>[p(u,{modelValue:s.customerPhone,"onUpdate:modelValue":t[3]||(t[3]=e=>s.customerPhone=e),placeholder:"请输入联系电话"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),p(d,{gutter:20,class:"form-section"},{default:m(()=>[p(a,{span:24},{default:m(()=>t[13]||(t[13]=[b("div",{class:"section-title"},"商品列表",-1)])),_:1,__:[13]}),p(a,{span:24},{default:m(()=>[p(z,{data:C.value,border:"",class:"product-table"},{default:m(()=>[p(r,{type:"index",label:"序号",width:"60"}),p(r,{prop:"materialCode",label:"公司物料编码",width:"120"}),p(r,{prop:"partyCode",label:"甲方编码",width:"100"},{default:m(e=>[p(n,{type:"primary",link:"",onClick:t=>{return a=e.row,j.value=a.partyCode,void(J.value=!0);var a}},{default:m(()=>t[14]||(t[14]=[y("显示",-1)])),_:2,__:[14]},1032,["onClick"])]),_:1}),p(r,{prop:"productName",label:"商品名称",width:"120"}),p(r,{prop:"model",label:"型号",width:"100"}),p(r,{prop:"specification",label:"规格",width:"100"}),p(r,{prop:"unit",label:"单位",width:"80"}),p(r,{prop:"stockQuantity",label:"库存数量",width:"100"}),p(r,{prop:"outboundQuantity",label:"出库数量"},{default:m(e=>[p(Z,{modelValue:e.row.outboundQuantity,"onUpdate:modelValue":t=>e.row.outboundQuantity=t,min:0,max:e.row.stockQuantity,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue","max"])]),_:1}),p(r,{prop:"unitPrice",label:"单价",width:"100"}),p(r,{prop:"subtotal",label:"小计",width:"100"}),p(r,{label:"操作",width:"80",fixed:"right"},{default:m(e=>[p(n,{type:"danger",link:"",onClick:t=>{return a=e.$index,C.value.splice(a,1),i.success("删除成功"),void M();var a}},{default:m(()=>t[15]||(t[15]=[y("删除",-1)])),_:2,__:[15]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),b("div",w,[p(n,{type:"primary",icon:"Plus",onClick:Y},{default:m(()=>t[16]||(t[16]=[y("添加商品",-1)])),_:1,__:[16]})])]),_:1})]),_:1}),p(d,{gutter:20,class:"form-section"},{default:m(()=>[p(a,{span:24},{default:m(()=>t[17]||(t[17]=[b("div",{class:"section-title"},"添加商品",-1)])),_:1,__:[17]}),p(a,{span:24},{default:m(()=>[b("div",V,[p(n,{type:"primary",onClick:Y},{default:m(()=>t[18]||(t[18]=[y("选择商品",-1)])),_:1,__:[18]}),p(n,{type:"success",onClick:K},{default:m(()=>t[19]||(t[19]=[y("添加",-1)])),_:1,__:[19]})])]),_:1})]),_:1}),p(R,{modelValue:S.value,"onUpdate:modelValue":t[6]||(t[6]=e=>S.value=e),title:"选择商品",width:"800"},{footer:m(()=>[p(n,{onClick:t[5]||(t[5]=e=>S.value=!1)},{default:m(()=>t[22]||(t[22]=[y("取消",-1)])),_:1,__:[22]})]),default:m(()=>[p(d,{gutter:20,style:{"margin-bottom":"20px"}},{default:m(()=>[p(a,{span:18},{default:m(()=>[p(u,{modelValue:U.value,"onUpdate:modelValue":t[4]||(t[4]=e=>U.value=e),placeholder:"请输入搜索关键词",clearable:""},null,8,["modelValue"])]),_:1}),p(a,{span:6},{default:m(()=>[p(n,{type:"primary",icon:"Search",onClick:E},{default:m(()=>t[20]||(t[20]=[y("搜索",-1)])),_:1,__:[20]})]),_:1})]),_:1}),p(z,{data:x.value,border:"",height:"400"},{default:m(()=>[p(r,{prop:"materialCode",label:"公司物料编码",width:"120"}),p(r,{prop:"productName",label:"商品名称",width:"120"}),p(r,{prop:"model",label:"型号",width:"100"}),p(r,{prop:"specification",label:"规格",width:"100"}),p(r,{prop:"unit",label:"单位",width:"80"}),p(r,{prop:"stockQuantity",label:"库存",width:"80"}),p(r,{label:"操作",width:"80",fixed:"right"},{default:m(e=>[p(n,{type:"primary",link:"",onClick:t=>L(e.row)},{default:m(()=>t[21]||(t[21]=[y("选择",-1)])),_:2,__:[21]},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1},8,["modelValue"]),p(R,{modelValue:J.value,"onUpdate:modelValue":t[8]||(t[8]=e=>J.value=e),title:"甲方编码列表",width:"500"},{footer:m(()=>[p(n,{onClick:t[7]||(t[7]=e=>J.value=!1)},{default:m(()=>t[23]||(t[23]=[y("关闭",-1)])),_:1,__:[23]})]),default:m(()=>[(f(!0),c(v,null,h(j.value,(e,t)=>(f(),c("div",{key:t,class:"party-code-item"}," 编码"+k(t+1)+": "+k(e),1))),128))]),_:1},8,["modelValue"]),p(d,{gutter:20,class:"form-section"},{default:m(()=>[p(a,{span:24},{default:m(()=>t[24]||(t[24]=[b("div",{class:"section-title"},"统计信息",-1)])),_:1,__:[24]}),p(a,{span:24},{default:m(()=>[p($,{class:"statistics-card"},{default:m(()=>[p(d,{gutter:20},{default:m(()=>[p(a,{span:12},{default:m(()=>[b("div",Q,[t[25]||(t[25]=b("span",{class:"label"},"出库商品总数:",-1)),b("span",null,k(O.totalProducts)+"项",1)])]),_:1}),p(a,{span:12},{default:m(()=>[b("div",g,[t[26]||(t[26]=b("span",{class:"label"},"出库商品总价:",-1)),b("span",D,"¥"+k(O.totalPrice),1)])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),p(d,{gutter:20,class:"form-section"},{default:m(()=>[p(a,{span:24},{default:m(()=>t[27]||(t[27]=[b("div",{class:"section-title"},"备注信息",-1)])),_:1,__:[27]}),p(a,{span:24},{default:m(()=>[p(l,{label:"备注:"},{default:m(()=>[p(u,{modelValue:s.remarks,"onUpdate:modelValue":t[9]||(t[9]=e=>s.remarks=e),type:"textarea",rows:3,placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),b("div",N,[p(n,{type:"primary",onClick:W},{default:m(()=>t[28]||(t[28]=[y("保存",-1)])),_:1,__:[28]}),p(n,{onClick:B},{default:m(()=>t[29]||(t[29]=[y("打印",-1)])),_:1,__:[29]}),p(n,{type:"success",onClick:G},{default:m(()=>t[30]||(t[30]=[y("提交",-1)])),_:1,__:[30]}),p(n,{onClick:I},{default:m(()=>t[31]||(t[31]=[y("取消",-1)])),_:1,__:[31]})])]),_:1})])}}}),[["__scopeId","data-v-08aa5190"]]);export{x as default};
