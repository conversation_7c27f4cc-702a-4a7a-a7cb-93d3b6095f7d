import{l as e,r as a,_ as s,y as r,z as l,R as o,J as n,Y as u,aT as t,av as i,x as d,ab as p,O as m}from"./vue-vendor-fc5a6493.js";import{u as c}from"./index-8b6647d3.js";import{E as f}from"./element-plus-7917fd46.js";import{_ as v}from"./_plugin-vue_export-helper-1b428a4d.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const g={class:"login-container"},_={class:"login-box"},h=v(e({__name:"index",setup(e){const v=t(),h=c(),y=a(),w=a(!1),x=s({username:"admin",password:"admin123"}),b={username:[{required:!0,message:"请输入用户名",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"}]},j=()=>{return e=this,a=null,s=function*(){if(y.value)try{yield y.value.validate(),w.value=!0;const e=yield h.login(x.username,x.password);e.success?(f.success("登录成功"),v.push("/dashboard")):f.error(e.message||"登录失败")}catch(e){f.error("登录失败")}finally{w.value=!1}},new Promise((r,l)=>{var o=e=>{try{u(s.next(e))}catch(a){l(a)}},n=e=>{try{u(s.throw(e))}catch(a){l(a)}},u=e=>e.done?r(e.value):Promise.resolve(e.value).then(o,n);u((s=s.apply(e,a)).next())});var e,a,s};return(e,a)=>{const s=i("el-input"),t=i("el-form-item"),c=i("el-button"),f=i("el-form");return d(),r("div",g,[l("div",_,[a[3]||(a[3]=l("div",{class:"login-header"},[l("h2",null,"工程管理系统"),l("p",null,"欢迎登录")],-1)),o(f,{ref_key:"loginFormRef",ref:y,model:x,rules:b,class:"login-form",onSubmit:u(j,["prevent"])},{default:n(()=>[o(t,{prop:"username"},{default:n(()=>[o(s,{modelValue:x.username,"onUpdate:modelValue":a[0]||(a[0]=e=>x.username=e),placeholder:"请输入用户名","prefix-icon":"User",size:"large"},null,8,["modelValue"])]),_:1}),o(t,{prop:"password"},{default:n(()=>[o(s,{modelValue:x.password,"onUpdate:modelValue":a[1]||(a[1]=e=>x.password=e),type:"password",placeholder:"请输入密码","prefix-icon":"Lock",size:"large","show-password":"",onKeyup:p(j,["enter"])},null,8,["modelValue"])]),_:1}),o(t,null,{default:n(()=>[o(c,{type:"primary",size:"large",class:"login-button",loading:w.value,onClick:j},{default:n(()=>a[2]||(a[2]=[m(" 登录 ",-1)])),_:1,__:[2]},8,["loading"])]),_:1})]),_:1},8,["model"])])])}}}),[["__scopeId","data-v-581f615f"]]);export{h as default};
