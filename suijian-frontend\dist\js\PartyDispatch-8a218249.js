var e=Object.defineProperty,t=Object.defineProperties,a=Object.getOwnPropertyDescriptors,l=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable,n=(t,a,l)=>a in t?e(t,a,{enumerable:!0,configurable:!0,writable:!0,value:l}):t[a]=l,i=(e,t)=>{for(var a in t||(t={}))r.call(t,a)&&n(e,a,t[a]);if(l)for(var a of l(t))o.call(t,a)&&n(e,a,t[a]);return e},d=(e,l)=>t(e,a(l)),s=(e,t,a)=>new Promise((l,r)=>{var o=e=>{try{i(a.next(e))}catch(t){r(t)}},n=e=>{try{i(a.throw(e))}catch(t){r(t)}},i=e=>e.done?l(e.value):Promise.resolve(e.value).then(o,n);i((a=a.apply(e,t)).next())});import{l as u,m,n as p,o as c,q as g,E as f,e as h}from"./element-plus-ad78a7bf.js";import{l as _,r as w,_ as y,c as b,q as v,y as D,R as T,J as j,av as V,x as S,z as O,u as Y,O as x,Q as M,aa as P,P as E,I as k}from"./vue-vendor-fc5a6493.js";import{_ as q}from"./_plugin-vue_export-helper-1b428a4d.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const N={class:"party-dispatch-container"},I={class:"section-title"},U={class:"section-title"},C={class:"info-content"},z={class:"duration-text"},A={class:"form-actions"},B=q(_({__name:"PartyDispatch",setup(e){const t=w(),a=w(!1),l=w(!1),r=y({dispatchDate:(new Date).toISOString().split("T")[0],operator:"",partyOrderNo:"",projectName:"",projectAddress:"",estimatedStartTime:"",estimatedEndTime:"",projectManager:"",contactPhone:"",projectDescription:"",remarks:""}),o=w([{id:1,name:"张三"},{id:2,name:"李四"},{id:3,name:"王五"},{id:4,name:"赵六"}]),n=w([{id:1,name:"项目经理A"},{id:2,name:"项目经理B"},{id:3,name:"项目经理C"},{id:4,name:"高级工程师"},{id:5,name:"技术负责人"}]),_={dispatchDate:[{required:!0,message:"请选择派单日期",trigger:"change"}],operator:[{required:!0,message:"请选择操作员",trigger:"change"}],partyOrderNo:[{required:!0,message:"请输入甲方订单号",trigger:"blur"},{min:3,max:50,message:"订单号长度在 3 到 50 个字符",trigger:"blur"}],projectName:[{required:!0,message:"请输入工程名称",trigger:"blur"},{min:2,max:100,message:"工程名称长度在 2 到 100 个字符",trigger:"blur"}],projectAddress:[{required:!0,message:"请输入工程地址",trigger:"blur"},{min:5,max:200,message:"工程地址长度在 5 到 200 个字符",trigger:"blur"}],estimatedStartTime:[{required:!0,message:"请选择预估开始时间",trigger:"change"}],estimatedEndTime:[{required:!0,message:"请选择预估结束时间",trigger:"change"}],projectManager:[{required:!0,message:"请选择工程负责人",trigger:"change"}],contactPhone:[{required:!0,message:"请输入联系电话",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],projectDescription:[{required:!0,message:"请输入工程描述",trigger:"blur"},{min:10,max:500,message:"工程描述长度在 10 到 500 个字符",trigger:"blur"}]},q=b(()=>{if(r.estimatedStartTime&&r.estimatedEndTime){const e=new Date(r.estimatedStartTime),t=new Date(r.estimatedEndTime).getTime()-e.getTime(),a=Math.ceil(t/864e5);return a>0?a:0}return 0}),B=e=>e.getTime()<Date.now()-864e5,R=e=>e.getTime()<Date.now()-864e5,F=e=>{if(!r.estimatedStartTime)return!1;const t=new Date(r.estimatedStartTime).getTime();return e.getTime()<=t},J=e=>{if(e&&r.estimatedEndTime){const t=new Date(e).getTime();new Date(r.estimatedEndTime).getTime()<=t&&(r.estimatedEndTime="",f.warning("结束时间必须晚于开始时间，请重新选择"))}},L=()=>s(this,null,function*(){if(t.value)try{yield t.value.validate(),a.value=!0,yield new Promise(e=>setTimeout(e,1e3));d(i({},r),{id:Date.now(),status:"draft",createTime:(new Date).toISOString(),updateTime:(new Date).toISOString()});f.success("保存成功")}catch(e){f.error("请检查表单填写是否正确")}finally{a.value=!1}}),Q=()=>s(this,null,function*(){if(t.value)try{yield t.value.validate(),yield h.confirm("确定要提交此派单吗？提交后将无法修改。","确认提交",{confirmButtonText:"确定提交",cancelButtonText:"取消",type:"warning"}),l.value=!0,yield new Promise(e=>setTimeout(e,1500));d(i({},r),{id:Date.now(),status:"submitted",createTime:(new Date).toISOString(),updateTime:(new Date).toISOString(),submitTime:(new Date).toISOString()});f.success("提交成功")}catch(e){"cancel"!==e&&f.error("请检查表单填写是否正确")}finally{l.value=!1}}),$=()=>{r.partyOrderNo?(f.success("正在准备打印..."),setTimeout(()=>{d(i({},r),{printTime:(new Date).toLocaleString("zh-CN")});window.print()},500)):f.warning("请先填写订单信息")},G=()=>s(this,null,function*(){var e;if(Object.values(r).some(e=>e&&e!==(new Date).toISOString().split("T")[0]))try{yield h.confirm("确定要取消吗？未保存的数据将丢失。","提示",{confirmButtonText:"确定",cancelButtonText:"继续编辑",type:"warning"})}catch(a){return}null==(e=t.value)||e.resetFields(),Object.keys(r).forEach(e=>{r[e]="dispatchDate"===e?(new Date).toISOString().split("T")[0]:""}),f.info("已取消")});return v(()=>{r.operator="张三"}),(e,i)=>{const d=V("el-icon"),s=V("el-col"),f=V("el-date-picker"),h=V("el-form-item"),w=V("el-option"),y=V("el-select"),b=V("el-row"),v=V("Document"),H=V("el-input"),K=V("el-alert"),W=V("el-button"),X=V("el-form"),Z=V("el-card");return S(),D("div",N,[T(Z,{class:"main-card"},{default:j(()=>[T(X,{model:r,rules:_,ref_key:"formRef",ref:t,"label-width":"120px"},{default:j(()=>[T(b,{gutter:20,class:"form-section"},{default:j(()=>[T(s,{span:24},{default:j(()=>[O("div",I,[T(d,null,{default:j(()=>[T(Y(u))]),_:1}),i[11]||(i[11]=x(" 派单信息 ",-1))])]),_:1}),T(s,{span:12},{default:j(()=>[T(h,{label:"派单日期:",prop:"dispatchDate"},{default:j(()=>[T(f,{modelValue:r.dispatchDate,"onUpdate:modelValue":i[0]||(i[0]=e=>r.dispatchDate=e),type:"date",placeholder:"请选择派单日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"100%"},"disabled-date":B},null,8,["modelValue"])]),_:1})]),_:1}),T(s,{span:12},{default:j(()=>[T(h,{label:"操作员:",prop:"operator"},{default:j(()=>[T(y,{modelValue:r.operator,"onUpdate:modelValue":i[1]||(i[1]=e=>r.operator=e),placeholder:"请选择操作员",style:{width:"100%"}},{default:j(()=>[(S(!0),D(M,null,P(o.value,e=>(S(),k(w,{key:e.id,label:e.name,value:e.name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),T(b,{gutter:20,class:"form-section"},{default:j(()=>[T(s,{span:24},{default:j(()=>[O("div",U,[T(d,null,{default:j(()=>[T(v)]),_:1}),i[12]||(i[12]=x(" 订单信息 ",-1))])]),_:1}),T(s,{span:12},{default:j(()=>[T(h,{label:"甲方订单号:",prop:"partyOrderNo"},{default:j(()=>[T(H,{modelValue:r.partyOrderNo,"onUpdate:modelValue":i[2]||(i[2]=e=>r.partyOrderNo=e),placeholder:"请输入甲方订单号",maxlength:"50","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1}),T(s,{span:12},{default:j(()=>[T(h,{label:"工程名称:",prop:"projectName"},{default:j(()=>[T(H,{modelValue:r.projectName,"onUpdate:modelValue":i[3]||(i[3]=e=>r.projectName=e),placeholder:"请输入工程名称",maxlength:"100","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1}),T(s,{span:24},{default:j(()=>[T(h,{label:"工程地址:",prop:"projectAddress"},{default:j(()=>[T(H,{modelValue:r.projectAddress,"onUpdate:modelValue":i[4]||(i[4]=e=>r.projectAddress=e),placeholder:"请输入工程地址",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1}),T(s,{span:12},{default:j(()=>[T(h,{label:"预估开始时间:",prop:"estimatedStartTime"},{default:j(()=>[T(f,{modelValue:r.estimatedStartTime,"onUpdate:modelValue":i[5]||(i[5]=e=>r.estimatedStartTime=e),type:"date",placeholder:"请选择预估开始时间",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"100%"},"disabled-date":R,onChange:J},null,8,["modelValue"])]),_:1})]),_:1}),T(s,{span:12},{default:j(()=>[T(h,{label:"预估结束时间:",prop:"estimatedEndTime"},{default:j(()=>[T(f,{modelValue:r.estimatedEndTime,"onUpdate:modelValue":i[6]||(i[6]=e=>r.estimatedEndTime=e),type:"date",placeholder:"请选择预估结束时间",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"100%"},"disabled-date":F},null,8,["modelValue"])]),_:1})]),_:1}),T(s,{span:12},{default:j(()=>[T(h,{label:"工程负责人:",prop:"projectManager"},{default:j(()=>[T(y,{modelValue:r.projectManager,"onUpdate:modelValue":i[7]||(i[7]=e=>r.projectManager=e),placeholder:"请选择工程负责人",style:{width:"100%"},filterable:"","allow-create":""},{default:j(()=>[(S(!0),D(M,null,P(n.value,e=>(S(),k(w,{key:e.id,label:e.name,value:e.name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),T(s,{span:12},{default:j(()=>[T(h,{label:"联系电话:",prop:"contactPhone"},{default:j(()=>[T(H,{modelValue:r.contactPhone,"onUpdate:modelValue":i[8]||(i[8]=e=>r.contactPhone=e),placeholder:"请输入联系电话",maxlength:"20"},null,8,["modelValue"])]),_:1})]),_:1}),T(s,{span:24},{default:j(()=>[T(h,{label:"工程描述:",prop:"projectDescription"},{default:j(()=>[T(H,{modelValue:r.projectDescription,"onUpdate:modelValue":i[9]||(i[9]=e=>r.projectDescription=e),type:"textarea",rows:4,placeholder:"请详细描述工程内容、要求、注意事项等",maxlength:"500","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1}),T(s,{span:24},{default:j(()=>[T(h,{label:"备注:"},{default:j(()=>[T(H,{modelValue:r.remarks,"onUpdate:modelValue":i[10]||(i[10]=e=>r.remarks=e),type:"textarea",rows:2,placeholder:"请输入备注信息",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),T(b,{gutter:20,class:"form-section"},{default:j(()=>[T(s,{span:24},{default:j(()=>[T(K,{title:"时间选择说明",type:"info",closable:!1,"show-icon":""},{default:j(()=>[O("div",C,[i[16]||(i[16]=O("p",null,[O("strong",null,"1. 预估开始时间:"),x(" 工程计划开始日期，不能早于当前日期")],-1)),i[17]||(i[17]=O("p",null,[O("strong",null,"2. 预估结束时间:"),x(" 工程计划完成日期，必须晚于开始时间")],-1)),i[18]||(i[18]=O("p",null,[O("strong",null,"3. 时间范围:"),x(" 应根据工程规模合理设定，建议预留适当缓冲时间")],-1)),O("p",null,[i[13]||(i[13]=O("strong",null,"4. 工期计算:",-1)),i[14]||(i[14]=x(" 当前设定工期为 ",-1)),O("span",z,E(q.value),1),i[15]||(i[15]=x(" 天",-1))])])]),_:1})]),_:1})]),_:1}),O("div",A,[T(W,{type:"primary",size:"large",loading:a.value,onClick:L},{default:j(()=>[T(d,null,{default:j(()=>[T(Y(m))]),_:1}),i[19]||(i[19]=x(" 保存 ",-1))]),_:1,__:[19]},8,["loading"]),T(W,{type:"success",size:"large",loading:l.value,onClick:Q},{default:j(()=>[T(d,null,{default:j(()=>[T(Y(p))]),_:1}),i[20]||(i[20]=x(" 提交 ",-1))]),_:1,__:[20]},8,["loading"]),T(W,{type:"info",size:"large",onClick:$},{default:j(()=>[T(d,null,{default:j(()=>[T(Y(c))]),_:1}),i[21]||(i[21]=x(" 打印 ",-1))]),_:1,__:[21]}),T(W,{size:"large",onClick:G},{default:j(()=>[T(d,null,{default:j(()=>[T(Y(g))]),_:1}),i[22]||(i[22]=x(" 取消 ",-1))]),_:1,__:[22]})])]),_:1},8,["model"])]),_:1})])}}}),[["__scopeId","data-v-c21ed302"]]);export{B as default};
