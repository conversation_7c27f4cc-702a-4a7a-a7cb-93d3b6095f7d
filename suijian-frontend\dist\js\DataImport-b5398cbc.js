import{E as e}from"./element-plus-ad78a7bf.js";import{l as a,_ as l,r as t,o as s,y as i,R as u,J as o,av as d,x as r,z as n,Q as p,aa as c,O as m,P as _,M as f,I as v}from"./vue-vendor-fc5a6493.js";import{_ as y}from"./_plugin-vue_export-helper-1b428a4d.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const b={class:"data-import-container"},g={key:0,class:"file-info"},C={key:1,class:"no-file"},k={class:"info-title"},w={class:"info-content"},h={key:0},V={key:1},x={key:2},T={key:3},N={key:4},M={key:5},J={key:6},D={key:7},R={key:8},L={class:"statistic-item"},j={class:"statistic-item"},S={class:"statistic-item"},W={class:"statistic-item"},H={class:"import-mode-description"},U={key:0},E={key:1},F={class:"form-actions"},I={class:"progress-info"},B={class:"result-item"},O={class:"result-item"},P={class:"result-item"},z={class:"result-item"},K={key:1,class:"failure-details"},Y={class:"form-actions"},Q=y(a({__name:"DataImport",setup(a){const y=l({importType:"material",dataSource:"local",fileFormat:"excel",importMode:"incremental",duplicateHandling:"skip",conflictHandling:"stop"}),Q=[{value:"material",label:"物料基础库"},{value:"employee",label:"员工信息"},{value:"workType",label:"工种设置"},{value:"supplier",label:"供应商信息"},{value:"customer",label:"客户信息"},{value:"system",label:"系统参数"}],Z=t(""),$=t(""),q=t(""),A=t([{rowNumber:1,materialCode:"WL001",partyCode:"JD001,JD002",materialCategory:"甲料",materialName:"电缆线",model:"YJV-3*4",specification:"3*4mm²",unit:"米",status:"valid",errorMessage:"无"},{rowNumber:2,materialCode:"WL002",partyCode:"JD003",materialCategory:"商品",materialName:"开关面板",model:"KP-86",specification:"86型",unit:"个",status:"valid",errorMessage:"无"},{rowNumber:3,materialCode:"WL003",partyCode:"",materialCategory:"辅料",materialName:"电线",model:"BV-2.5",specification:"2.5mm²",unit:"米",status:"valid",errorMessage:"无"},{rowNumber:4,materialCode:"",partyCode:"JD004",materialCategory:"甲料",materialName:"灯具",model:"LED-12W",specification:"12W",unit:"个",status:"invalid",errorMessage:"物料编码不能为空"},{rowNumber:5,materialCode:"WL005",partyCode:"JD005",materialCategory:"其他",materialName:"插座",model:"ZP-86",specification:"86型",unit:"个",status:"invalid",errorMessage:"物料分类错误"}]),G=l({totalRecords:100,validRecords:95,invalidRecords:5,duplicateRecords:2}),X=t(!1),ee=t(0),ae=t(0),le=t("00:02:30"),te=t("正在导入..."),se=t(!1),ie=t("2024-01-15 15:30:25"),ue=l({successCount:95,failureCount:5,skipCount:2,failureDetails:["1. 行4: 物料编码不能为空","2. 行5: 物料分类错误","3. 行15: 单位字段超长","4. 行28: 物料名称包含非法字符","5. 行67: 规格字段格式错误"]}),oe=a=>{Z.value=a.name,$.value=de(a.size),q.value=re(a.name),e.success("文件选择成功")},de=e=>e<1024?e+" B":e<1048576?(e/1024).toFixed(2)+" KB":(e/1048576).toFixed(2)+" MB",re=e=>{var a;return{xls:"Excel文件",xlsx:"Excel文件",csv:"CSV文件",json:"JSON文件"}[null==(a=e.split(".").pop())?void 0:a.toLowerCase()]||"未知"},ne=()=>{e.success("下载导入模板")},pe=()=>{e.success("预览数据")},ce=()=>{e.success("开始导入"),X.value=!0;const a=setInterval(()=>{ee.value+=5,ae.value=Math.floor(ee.value*G.validRecords/100),ee.value>=100&&(clearInterval(a),te.value="导入完成",setTimeout(()=>{X.value=!1,se.value=!0},1e3))},300)},me=()=>{e.success("保存导入结果")},_e=()=>{e.info("已取消")},fe=()=>{e.success("下载失败记录")},ve=()=>{e.success("重新导入失败记录")},ye=()=>{e.success("导入完成"),se.value=!1};return s(()=>y.importType,a=>{var l;e.success(`切换到${null==(l=Q.find(e=>e.value===a))?void 0:l.label}导入`)}),(e,a)=>{const l=d("el-col"),t=d("el-option"),s=d("el-select"),de=d("el-form-item"),re=d("el-radio"),be=d("el-radio-group"),ge=d("el-row"),Ce=d("el-button"),ke=d("el-upload"),we=d("el-card"),he=d("el-table-column"),Ve=d("el-tag"),xe=d("el-table"),Te=d("el-progress"),Ne=d("el-divider");return r(),i("div",b,[u(we,{class:"main-card"},{header:o(()=>a[6]||(a[6]=[n("div",{class:"card-header"},[n("span",null,"基础数据导入")],-1)])),default:o(()=>[u(ge,{gutter:20,class:"form-section"},{default:o(()=>[u(l,{span:24},{default:o(()=>a[7]||(a[7]=[n("div",{class:"section-title"},"导入设置",-1)])),_:1,__:[7]}),u(l,{span:12},{default:o(()=>[u(de,{label:"导入类型:"},{default:o(()=>[u(s,{modelValue:y.importType,"onUpdate:modelValue":a[0]||(a[0]=e=>y.importType=e),placeholder:"请选择导入类型",style:{width:"100%"}},{default:o(()=>[(r(),i(p,null,c(Q,e=>u(t,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1})]),_:1}),u(l,{span:12},{default:o(()=>[u(de,{label:"数据来源:"},{default:o(()=>[u(be,{modelValue:y.dataSource,"onUpdate:modelValue":a[1]||(a[1]=e=>y.dataSource=e)},{default:o(()=>[u(re,{label:"local"},{default:o(()=>a[8]||(a[8]=[m("本地文件",-1)])),_:1,__:[8]}),u(re,{label:"network"},{default:o(()=>a[9]||(a[9]=[m("网络地址",-1)])),_:1,__:[9]})]),_:1},8,["modelValue"])]),_:1})]),_:1}),u(l,{span:12},{default:o(()=>[u(de,{label:"文件格式:"},{default:o(()=>[u(s,{modelValue:y.fileFormat,"onUpdate:modelValue":a[2]||(a[2]=e=>y.fileFormat=e),placeholder:"请选择文件格式",style:{width:"100%"}},{default:o(()=>[u(t,{label:"Excel文件",value:"excel"}),u(t,{label:"CSV文件",value:"csv"}),u(t,{label:"JSON文件",value:"json"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),u(ge,{gutter:20,class:"form-section"},{default:o(()=>[u(l,{span:24},{default:o(()=>a[10]||(a[10]=[n("div",{class:"section-title"},"文件选择",-1)])),_:1,__:[10]}),u(l,{span:24},{default:o(()=>[u(de,{label:"选择文件:"},{default:o(()=>[u(ke,{class:"upload-demo",action:"","auto-upload":!1,"on-change":oe,"show-file-list":!1},{default:o(()=>[u(Ce,{type:"primary"},{default:o(()=>a[11]||(a[11]=[m("选择文件",-1)])),_:1,__:[11]})]),_:1}),Z.value?(r(),i("div",g,[n("p",null,"文件名: "+_(Z.value),1),n("p",null,"文件大小: "+_($.value),1),n("p",null,"文件格式: "+_(q.value),1)])):(r(),i("div",C,a[12]||(a[12]=[n("p",null,"未选择文件",-1)])))]),_:1})]),_:1}),u(l,{span:24},{default:o(()=>[u(de,{label:"模板下载:"},{default:o(()=>[u(Ce,{type:"primary",link:"",onClick:ne},{default:o(()=>a[13]||(a[13]=[m("下载导入模板",-1)])),_:1,__:[13]})]),_:1})]),_:1})]),_:1}),u(ge,{gutter:20,class:"form-section"},{default:o(()=>[u(l,{span:24},{default:o(()=>a[14]||(a[14]=[n("div",{class:"section-title"},"导入模板说明",-1)])),_:1,__:[14]}),u(l,{span:24},{default:o(()=>[u(we,{class:"info-card"},{default:o(()=>[n("div",k,_("material"===y.importType?"物料基础库":"其他")+"导入模板说明:",1),n("div",w,["material"===y.importType?(r(),i("p",h,"1. 必填字段: 公司物料编码、物料名称、物料分类、单位")):f("",!0),"material"===y.importType?(r(),i("p",V,"2. 可选字段: 型号、规格、甲方编码")):f("",!0),"material"===y.importType?(r(),i("p",x,"3. 物料分类: 甲料、商品、辅料")):f("",!0),"material"===y.importType?(r(),i("p",T,"4. 一个公司物料编码可对应多个甲方编码(用逗号分隔)")):f("",!0),"material"===y.importType?(r(),i("p",N,"5. 支持格式: .xls、.xlsx")):f("",!0),"material"===y.importType?(r(),i("p",M," ")):f("",!0),"material"===y.importType?(r(),i("p",J,"字段示例:")):f("",!0),"material"===y.importType?(r(),i("p",D,"公司物料编码 甲方编码 物料分类 物料名称 型号 规格 单位")):f("",!0),"material"===y.importType?(r(),i("p",R,"WL001 JD001,JD002 甲料 电缆线 YJV 3*4mm² 米")):f("",!0)])]),_:1})]),_:1})]),_:1}),u(ge,{gutter:20,class:"form-section"},{default:o(()=>[u(l,{span:24},{default:o(()=>a[15]||(a[15]=[n("div",{class:"section-title"},"数据预览",-1)])),_:1,__:[15]}),u(l,{span:24},{default:o(()=>[u(xe,{data:A.value,border:"",class:"preview-table",height:"300"},{default:o(()=>[u(he,{prop:"rowNumber",label:"行号",width:"60"}),u(he,{prop:"materialCode",label:"公司物料编码","min-width":"120"}),u(he,{prop:"partyCode",label:"甲方编码","min-width":"120"}),u(he,{prop:"materialCategory",label:"物料分类","min-width":"100"}),u(he,{prop:"materialName",label:"物料名称","min-width":"120"}),u(he,{prop:"model",label:"型号","min-width":"100"}),u(he,{prop:"specification",label:"规格","min-width":"100"}),u(he,{prop:"unit",label:"单位","min-width":"80"}),u(he,{prop:"status",label:"状态","min-width":"80"},{default:o(e=>[u(Ve,{type:"valid"===e.row.status?"success":"danger"},{default:o(()=>[m(_("valid"===e.row.status?"有效":"无效"),1)]),_:2},1032,["type"])]),_:1}),u(he,{prop:"errorMessage",label:"错误信息","min-width":"120"})]),_:1},8,["data"])]),_:1})]),_:1}),u(ge,{gutter:20,class:"form-section"},{default:o(()=>[u(l,{span:24},{default:o(()=>a[16]||(a[16]=[n("div",{class:"section-title"},"导入统计",-1)])),_:1,__:[16]}),u(l,{span:24},{default:o(()=>[u(we,{class:"statistics-card"},{default:o(()=>[u(ge,{gutter:20},{default:o(()=>[u(l,{span:6},{default:o(()=>[n("div",L,[a[17]||(a[17]=n("span",{class:"label"},"总记录数:",-1)),n("span",null,_(G.totalRecords)+"条",1)])]),_:1}),u(l,{span:6},{default:o(()=>[n("div",j,[a[18]||(a[18]=n("span",{class:"label"},"有效记录:",-1)),n("span",null,_(G.validRecords)+"条",1)])]),_:1}),u(l,{span:6},{default:o(()=>[n("div",S,[a[19]||(a[19]=n("span",{class:"label"},"无效记录:",-1)),n("span",null,_(G.invalidRecords)+"条",1)])]),_:1}),u(l,{span:6},{default:o(()=>[n("div",W,[a[20]||(a[20]=n("span",{class:"label"},"重复记录:",-1)),n("span",null,_(G.duplicateRecords)+"条 (将被跳过)",1)])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),u(ge,{gutter:20,class:"form-section"},{default:o(()=>[u(l,{span:24},{default:o(()=>a[21]||(a[21]=[n("div",{class:"section-title"},"导入选项",-1)])),_:1,__:[21]}),u(l,{span:12},{default:o(()=>[u(de,{label:"导入模式:"},{default:o(()=>[u(s,{modelValue:y.importMode,"onUpdate:modelValue":a[3]||(a[3]=e=>y.importMode=e),placeholder:"请选择导入模式",style:{width:"100%"}},{default:o(()=>[u(t,{label:"增量导入",value:"incremental"}),u(t,{label:"全量导入",value:"full"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),u(l,{span:24},{default:o(()=>[n("div",H,["incremental"===y.importMode?(r(),i("p",U,"增量导入: 只导入新数据，跳过重复数据")):(r(),i("p",E,"全量导入: 清空原数据，导入新数据(谨慎操作)"))])]),_:1}),u(l,{span:12},{default:o(()=>[u(de,{label:"重复处理:"},{default:o(()=>[u(be,{modelValue:y.duplicateHandling,"onUpdate:modelValue":a[4]||(a[4]=e=>y.duplicateHandling=e)},{default:o(()=>[u(re,{label:"skip"},{default:o(()=>a[22]||(a[22]=[m("跳过重复项",-1)])),_:1,__:[22]}),u(re,{label:"overwrite"},{default:o(()=>a[23]||(a[23]=[m("覆盖重复项",-1)])),_:1,__:[23]})]),_:1},8,["modelValue"])]),_:1})]),_:1}),u(l,{span:12},{default:o(()=>[u(de,{label:"冲突处理:"},{default:o(()=>[u(be,{modelValue:y.conflictHandling,"onUpdate:modelValue":a[5]||(a[5]=e=>y.conflictHandling=e)},{default:o(()=>[u(re,{label:"stop"},{default:o(()=>a[24]||(a[24]=[m("停止导入",-1)])),_:1,__:[24]}),u(re,{label:"continue"},{default:o(()=>a[25]||(a[25]=[m("继续导入",-1)])),_:1,__:[25]})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),n("div",F,[u(Ce,{type:"primary",onClick:pe},{default:o(()=>a[26]||(a[26]=[m("预览数据",-1)])),_:1,__:[26]}),u(Ce,{type:"success",onClick:ce},{default:o(()=>a[27]||(a[27]=[m("开始导入",-1)])),_:1,__:[27]}),u(Ce,{onClick:me},{default:o(()=>a[28]||(a[28]=[m("保存导入结果",-1)])),_:1,__:[28]}),u(Ce,{onClick:_e},{default:o(()=>a[29]||(a[29]=[m("取消",-1)])),_:1,__:[29]})]),X.value?(r(),v(ge,{key:0,gutter:20,class:"form-section"},{default:o(()=>[u(l,{span:24},{default:o(()=>a[30]||(a[30]=[n("div",{class:"section-title"},"导入进度",-1)])),_:1,__:[30]}),u(l,{span:24},{default:o(()=>[u(we,{class:"progress-card"},{default:o(()=>[u(Te,{percentage:ee.value,"stroke-width":20,striped:"","striped-flow":"",duration:ee.value<100?5:0},null,8,["percentage","duration"]),n("div",I,[n("p",null,"已导入: "+_(ae.value)+"条/"+_(G.validRecords)+"条",1),n("p",null,"预计剩余时间: "+_(le.value),1),n("p",null,"当前状态: "+_(te.value),1)])]),_:1})]),_:1})]),_:1})):f("",!0),se.value?(r(),v(ge,{key:1,gutter:20,class:"form-section"},{default:o(()=>[u(l,{span:24},{default:o(()=>a[31]||(a[31]=[n("div",{class:"section-title"},"导入结果",-1)])),_:1,__:[31]}),u(l,{span:24},{default:o(()=>[u(we,{class:"result-card"},{default:o(()=>[u(ge,{gutter:20},{default:o(()=>[u(l,{span:24},{default:o(()=>[n("div",B,[a[32]||(a[32]=n("span",{class:"label"},"导入完成时间:",-1)),n("span",null,_(ie.value),1)])]),_:1}),u(l,{span:24},{default:o(()=>[n("div",O,[a[33]||(a[33]=n("span",{class:"label"},"成功导入:",-1)),n("span",null,_(ue.successCount)+"条",1)])]),_:1}),u(l,{span:24},{default:o(()=>[n("div",P,[a[34]||(a[34]=n("span",{class:"label"},"导入失败:",-1)),n("span",null,_(ue.failureCount)+"条",1)])]),_:1}),u(l,{span:24},{default:o(()=>[n("div",z,[a[35]||(a[35]=n("span",{class:"label"},"重复跳过:",-1)),n("span",null,_(ue.skipCount)+"条",1)])]),_:1})]),_:1}),ue.failureCount>0?(r(),v(Ne,{key:0})):f("",!0),ue.failureCount>0?(r(),i("div",K,[a[36]||(a[36]=n("div",{class:"info-title"},"失败记录详情:",-1)),(r(!0),i(p,null,c(ue.failureDetails,(e,a)=>(r(),i("div",{key:a,class:"failure-item"},_(e),1))),128))])):f("",!0)]),_:1})]),_:1}),ue.failureCount>0?(r(),v(l,{key:0,span:24},{default:o(()=>[n("div",Y,[u(Ce,{onClick:fe},{default:o(()=>a[37]||(a[37]=[m("下载失败记录",-1)])),_:1,__:[37]}),u(Ce,{onClick:ve},{default:o(()=>a[38]||(a[38]=[m("重新导入失败记录",-1)])),_:1,__:[38]}),u(Ce,{type:"primary",onClick:ye},{default:o(()=>a[39]||(a[39]=[m("完成",-1)])),_:1,__:[39]})])]),_:1})):f("",!0)]),_:1})):f("",!0)]),_:1})])}}}),[["__scopeId","data-v-7c5ad2fc"]]);export{Q as default};
