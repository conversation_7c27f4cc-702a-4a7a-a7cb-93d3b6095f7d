import{p as e,r as a,E as l}from"./element-plus-ad78a7bf.js";import{l as t,r,_ as i,q as o,y as d,R as u,J as n,av as s,x as c,O as p,z as m,u as _,P as f,B as g}from"./vue-vendor-fc5a6493.js";import{_ as h}from"./_plugin-vue_export-helper-1b428a4d.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const b={class:"material-price"},v={class:"card-header"},C={class:"header-actions"},w={class:"search-section"},P={class:"pagination-section"},y={class:"dialog-footer"},V=h(t({__name:"MaterialPrice",setup(t){const h=r(),V=i({materialCode:"",materialName:""}),N=i({currentPage:1,pageSize:20,total:0}),z=r(!1),k=r(""),x=r(!1),U=i({materialCode:"",materialName:"",newPrice:0,changeReason:""}),j={materialCode:[{required:!0,message:"请输入物料编码",trigger:"blur"}],materialName:[{required:!0,message:"请输入物料名称",trigger:"blur"}],newPrice:[{required:!0,message:"请输入新价格",trigger:"blur"}],changeReason:[{required:!0,message:"请输入变更原因",trigger:"blur"}]},R=r([{materialCode:"M001",materialName:"燃气表",specification:"G2.5",unit:"个",currentPrice:300,lastPrice:280,priceChange:7.14,updateDate:"2024-01-15",operator:"张三"},{materialCode:"M002",materialName:"镀锌管件",specification:"DN20",unit:"个",currentPrice:25,lastPrice:26,priceChange:-3.85,updateDate:"2024-01-14",operator:"李四"},{materialCode:"M003",materialName:"波纹管",specification:"DN15",unit:"m",currentPrice:12.5,lastPrice:12.5,priceChange:0,updateDate:"2024-01-13",operator:"王五"}]),D=()=>{l.success("搜索完成")},S=()=>{Object.keys(V).forEach(e=>{V[e]=""}),l.info("搜索条件已重置")},q=()=>{l.success("数据已刷新")},M=()=>{k.value="新增价格",x.value=!1,Object.keys(U).forEach(e=>{U[e]="newPrice"===e?0:""}),z.value=!0},E=()=>{return e=this,a=null,t=function*(){if(h.value)try{yield h.value.validate(),l.success("价格更新成功"),z.value=!1}catch(e){l.error("请检查表单填写是否正确")}},new Promise((l,r)=>{var i=e=>{try{d(t.next(e))}catch(a){r(a)}},o=e=>{try{d(t.throw(e))}catch(a){r(a)}},d=e=>e.done?l(e.value):Promise.resolve(e.value).then(i,o);d((t=t.apply(e,a)).next())});var e,a,t},F=e=>{N.pageSize=e},O=e=>{N.currentPage=e};return o(()=>{N.total=R.value.length}),(t,r)=>{const i=s("el-breadcrumb-item"),o=s("el-breadcrumb"),B=s("el-icon"),G=s("el-button"),I=s("el-input"),J=s("el-form-item"),L=s("el-form"),$=s("el-table-column"),A=s("el-table"),H=s("el-pagination"),K=s("el-card"),Q=s("el-input-number"),T=s("el-dialog");return c(),d("div",b,[u(o,{class:"breadcrumb",separator:">"},{default:n(()=>[u(i,{to:{path:"/dashboard"}},{default:n(()=>r[10]||(r[10]=[p("首页",-1)])),_:1,__:[10]}),u(i,{to:{path:"/warehouse/material-list"}},{default:n(()=>r[11]||(r[11]=[p("仓库管理",-1)])),_:1,__:[11]}),u(i,null,{default:n(()=>r[12]||(r[12]=[p("甲料价格",-1)])),_:1,__:[12]})]),_:1}),u(K,{class:"main-card"},{header:n(()=>[m("div",v,[r[15]||(r[15]=m("span",null,"💰 甲料价格管理",-1)),m("div",C,[u(G,{type:"primary",size:"small",onClick:M},{default:n(()=>[u(B,null,{default:n(()=>[u(_(e))]),_:1}),r[13]||(r[13]=p(" 新增价格 ",-1))]),_:1,__:[13]}),u(G,{type:"success",size:"small",onClick:q},{default:n(()=>[u(B,null,{default:n(()=>[u(_(a))]),_:1}),r[14]||(r[14]=p(" 刷新 ",-1))]),_:1,__:[14]})])])]),default:n(()=>[m("div",w,[u(L,{model:V,inline:""},{default:n(()=>[u(J,{label:"物料编码:"},{default:n(()=>[u(I,{modelValue:V.materialCode,"onUpdate:modelValue":r[0]||(r[0]=e=>V.materialCode=e),placeholder:"请输入物料编码"},null,8,["modelValue"])]),_:1}),u(J,{label:"物料名称:"},{default:n(()=>[u(I,{modelValue:V.materialName,"onUpdate:modelValue":r[1]||(r[1]=e=>V.materialName=e),placeholder:"请输入物料名称"},null,8,["modelValue"])]),_:1}),u(J,null,{default:n(()=>[u(G,{type:"primary",onClick:D},{default:n(()=>r[16]||(r[16]=[p("搜索",-1)])),_:1,__:[16]}),u(G,{onClick:S},{default:n(()=>r[17]||(r[17]=[p("重置",-1)])),_:1,__:[17]})]),_:1})]),_:1},8,["model"])]),u(A,{data:R.value,border:"",style:{width:"100%"}},{default:n(()=>[u($,{prop:"materialCode",label:"物料编码",width:"120"}),u($,{prop:"materialName",label:"物料名称",width:"150"}),u($,{prop:"specification",label:"规格",width:"100"}),u($,{prop:"unit",label:"单位",width:"80"}),u($,{prop:"currentPrice",label:"当前价格",width:"120",align:"center"},{default:n(({row:e})=>[p(" ¥"+f(e.currentPrice.toFixed(2)),1)]),_:1}),u($,{prop:"lastPrice",label:"上次价格",width:"120",align:"center"},{default:n(({row:e})=>[p(" ¥"+f(e.lastPrice.toFixed(2)),1)]),_:1}),u($,{prop:"priceChange",label:"价格变动",width:"120",align:"center"},{default:n(({row:e})=>{return[m("span",{class:g((a=e.priceChange,a>0?"price-up":a<0?"price-down":"price-stable"))},f(e.priceChange>0?"+":"")+f(e.priceChange.toFixed(2))+"% ",3)];var a}),_:1}),u($,{prop:"updateDate",label:"更新日期",width:"120"}),u($,{prop:"operator",label:"操作员",width:"100"}),u($,{label:"操作",width:"150",fixed:"right"},{default:n(({row:e})=>[u(G,{type:"primary",size:"small",onClick:a=>(e=>{k.value="编辑价格",x.value=!0,U.materialCode=e.materialCode,U.materialName=e.materialName,U.newPrice=e.currentPrice,U.changeReason="",z.value=!0})(e)},{default:n(()=>r[18]||(r[18]=[p(" 编辑 ",-1)])),_:2,__:[18]},1032,["onClick"]),u(G,{type:"info",size:"small",onClick:a=>(e=>{l.info(`查看 ${e.materialName} 的价格历史`)})(e)},{default:n(()=>r[19]||(r[19]=[p(" 历史 ",-1)])),_:2,__:[19]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),m("div",P,[u(H,{"current-page":N.currentPage,"onUpdate:currentPage":r[2]||(r[2]=e=>N.currentPage=e),"page-size":N.pageSize,"onUpdate:pageSize":r[3]||(r[3]=e=>N.pageSize=e),"page-sizes":[10,20,50,100],total:N.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:F,onCurrentChange:O},null,8,["current-page","page-size","total"])])]),_:1}),u(T,{modelValue:z.value,"onUpdate:modelValue":r[9]||(r[9]=e=>z.value=e),title:k.value,width:"500px"},{footer:n(()=>[m("div",y,[u(G,{onClick:r[8]||(r[8]=e=>z.value=!1)},{default:n(()=>r[20]||(r[20]=[p("取消",-1)])),_:1,__:[20]}),u(G,{type:"primary",onClick:E},{default:n(()=>r[21]||(r[21]=[p("确定",-1)])),_:1,__:[21]})])]),default:n(()=>[u(L,{model:U,rules:j,ref_key:"formRef",ref:h,"label-width":"100px"},{default:n(()=>[u(J,{label:"物料编码:",prop:"materialCode"},{default:n(()=>[u(I,{modelValue:U.materialCode,"onUpdate:modelValue":r[4]||(r[4]=e=>U.materialCode=e),disabled:x.value},null,8,["modelValue","disabled"])]),_:1}),u(J,{label:"物料名称:",prop:"materialName"},{default:n(()=>[u(I,{modelValue:U.materialName,"onUpdate:modelValue":r[5]||(r[5]=e=>U.materialName=e),disabled:x.value},null,8,["modelValue","disabled"])]),_:1}),u(J,{label:"新价格:",prop:"newPrice"},{default:n(()=>[u(Q,{modelValue:U.newPrice,"onUpdate:modelValue":r[6]||(r[6]=e=>U.newPrice=e),min:0,step:.01,precision:2,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),u(J,{label:"变更原因:",prop:"changeReason"},{default:n(()=>[u(I,{modelValue:U.changeReason,"onUpdate:modelValue":r[7]||(r[7]=e=>U.changeReason=e),type:"textarea",rows:3,placeholder:"请输入价格变更原因"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}}),[["__scopeId","data-v-7fb3e005"]]);export{V as default};
