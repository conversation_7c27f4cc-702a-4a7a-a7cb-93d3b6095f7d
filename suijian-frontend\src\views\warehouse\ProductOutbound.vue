<template>
  <div class="product-outbound-container">
    <el-card class="main-card">
      <template #header>
        <div class="card-header">
          <span>商品售卖出库</span>
        </div>
      </template>
      
      <!-- 出库信息 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">出库信息</div>
        </el-col>
        <el-col :span="12">
          <el-form-item label="出库单号:">
            <span>CK20240115001</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="出库日期:">
            <el-date-picker
              v-model="outboundForm.outboundDate"
              type="date"
              placeholder="请选择出库日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="操作员:">
            <el-input v-model="outboundForm.operator" placeholder="请输入操作员" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="客户姓名:">
            <el-input v-model="outboundForm.customerName" placeholder="请输入客户姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话:">
            <el-input v-model="outboundForm.customerPhone" placeholder="请输入联系电话" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 商品列表 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">商品列表</div>
        </el-col>
        <el-col :span="24">
          <el-table :data="productList" border class="product-table">
            <el-table-column type="index" label="序号" width="60" />
            <el-table-column prop="materialCode" label="公司物料编码" width="120" />
            <el-table-column prop="partyCode" label="甲方编码" width="100">
              <template #default="scope">
                <el-button type="primary" link @click="showPartyCodes(scope.row)">显示</el-button>
              </template>
            </el-table-column>
            <el-table-column prop="productName" label="商品名称" width="120" />
            <el-table-column prop="model" label="型号" width="100" />
            <el-table-column prop="specification" label="规格" width="100" />
            <el-table-column prop="unit" label="单位" width="80" />
            <el-table-column prop="stockQuantity" label="库存数量" width="100" />
            <el-table-column prop="outboundQuantity" label="出库数量">
              <template #default="scope">
                <el-input-number 
                  v-model="scope.row.outboundQuantity" 
                  :min="0" 
                  :max="scope.row.stockQuantity"
                  controls-position="right" 
                  style="width: 100%" 
                />
              </template>
            </el-table-column>
            <el-table-column prop="unitPrice" label="单价" width="100" />
            <el-table-column prop="subtotal" label="小计" width="100" />
            <el-table-column label="操作" width="80" fixed="right">
              <template #default="scope">
                <el-button type="danger" link @click="removeProduct(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div style="margin-top: 15px;">
            <el-button type="primary" icon="Plus" @click="selectProduct">添加商品</el-button>
          </div>
        </el-col>
      </el-row>
      
      <!-- 添加商品 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">添加商品</div>
        </el-col>
        <el-col :span="24">
          <div class="add-product-actions">
            <el-button type="primary" @click="selectProduct">选择商品</el-button>
            <el-button type="success" @click="addProduct">添加</el-button>
          </div>
        </el-col>
      </el-row>
      
      <!-- 商品选择弹窗 -->
      <el-dialog v-model="productDialogVisible" title="选择商品" width="800">
        <el-row :gutter="20" style="margin-bottom: 20px;">
          <el-col :span="18">
            <el-input v-model="productSearch" placeholder="请输入搜索关键词" clearable />
          </el-col>
          <el-col :span="6">
            <el-button type="primary" icon="Search" @click="searchProducts">搜索</el-button>
          </el-col>
        </el-row>
        
        <el-table :data="productOptions" border height="400">
          <el-table-column prop="materialCode" label="公司物料编码" width="120" />
          <el-table-column prop="productName" label="商品名称" width="120" />
          <el-table-column prop="model" label="型号" width="100" />
          <el-table-column prop="specification" label="规格" width="100" />
          <el-table-column prop="unit" label="单位" width="80" />
          <el-table-column prop="stockQuantity" label="库存" width="80" />
          <el-table-column label="操作" width="80" fixed="right">
            <template #default="scope">
              <el-button type="primary" link @click="selectProductItem(scope.row)">选择</el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <template #footer>
          <el-button @click="productDialogVisible = false">取消</el-button>
        </template>
      </el-dialog>
      
      <!-- 甲方编码显示弹窗 -->
      <el-dialog v-model="partyCodeDialogVisible" title="甲方编码列表" width="500">
        <div v-for="(code, index) in currentPartyCodes" :key="index" class="party-code-item">
          编码{{ index + 1 }}: {{ code }}
        </div>
        <template #footer>
          <el-button @click="partyCodeDialogVisible = false">关闭</el-button>
        </template>
      </el-dialog>
      
      <!-- 统计信息 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">统计信息</div>
        </el-col>
        <el-col :span="24">
          <el-card class="statistics-card">
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="statistic-item">
                  <span class="label">出库商品总数:</span>
                  <span>{{ statistics.totalProducts }}项</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="statistic-item">
                  <span class="label">出库商品总价:</span>
                  <span class="total-price">¥{{ statistics.totalPrice }}</span>
                </div>
              </el-col>
            </el-row>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 备注信息 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">备注信息</div>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注:">
            <el-input
              v-model="outboundForm.remarks"
              type="textarea"
              :rows="3"
              placeholder="请输入备注"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 操作按钮 -->
      <div class="form-actions">
        <el-button type="primary" @click="handleSave">保存</el-button>
        <el-button @click="handlePrint">打印</el-button>
        <el-button type="success" @click="handleSubmit">提交</el-button>
        <el-button @click="handleCancel">取消</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

// 出库表单
const outboundForm = reactive({
  outboundDate: '2024-01-15',
  operator: '张三',
  customerName: '',
  customerPhone: '',
  remarks: ''
})

// 商品列表
const productList = ref([
  {
    id: 1,
    materialCode: 'SP001',
    partyCode: ['JD001', 'JD002'],
    productName: '智能开关',
    model: 'KG-86',
    specification: '86型',
    unit: '个',
    stockQuantity: 100,
    outboundQuantity: 20,
    unitPrice: '¥45.00',
    subtotal: '¥900'
  },
  {
    id: 2,
    materialCode: 'SP002',
    partyCode: ['JD003'],
    productName: 'LED灯具',
    model: 'LED-12W',
    specification: '12W',
    unit: '个',
    stockQuantity: 200,
    outboundQuantity: 15,
    unitPrice: '¥130.00',
    subtotal: '¥1,950'
  },
  {
    id: 3,
    materialCode: 'SP003',
    partyCode: ['JD004', 'JD005'],
    productName: '插座面板',
    model: 'ZP-86',
    specification: '86型',
    unit: '个',
    stockQuantity: 150,
    outboundQuantity: 25,
    unitPrice: '¥60.00',
    subtotal: '¥1,500'
  },
  {
    id: 4,
    materialCode: 'SP004',
    partyCode: ['JD006'],
    productName: '电线',
    model: 'BV-2.5',
    specification: '2.5mm²',
    unit: '米',
    stockQuantity: 1000,
    outboundQuantity: 100,
    unitPrice: '¥5.00',
    subtotal: '¥500'
  },
  {
    id: 5,
    materialCode: 'SP005',
    partyCode: ['JD007'],
    productName: '开关面板',
    model: 'KP-86',
    specification: '86型',
    unit: '个',
    stockQuantity: 100,
    outboundQuantity: 30,
    unitPrice: '¥250.00',
    subtotal: '¥7,500'
  }
])

// 商品选项
const productOptions = ref([
  {
    id: 1,
    materialCode: 'SP001',
    productName: '智能开关',
    model: 'KG-86',
    specification: '86型',
    unit: '个',
    stockQuantity: 100
  },
  {
    id: 2,
    materialCode: 'SP002',
    productName: 'LED灯具',
    model: 'LED-12W',
    specification: '12W',
    unit: '个',
    stockQuantity: 200
  },
  {
    id: 3,
    materialCode: 'SP003',
    productName: '插座面板',
    model: 'ZP-86',
    specification: '86型',
    unit: '个',
    stockQuantity: 150
  },
  {
    id: 4,
    materialCode: 'SP004',
    productName: '电线',
    model: 'BV-2.5',
    specification: '2.5mm²',
    unit: '米',
    stockQuantity: 1000
  }
])

// 当前甲方编码
const currentPartyCodes = ref([] as string[])

// 统计信息
const statistics = reactive({
  totalProducts: 5,
  totalPrice: '12,350'
})

// 弹窗控制
const productDialogVisible = ref(false)
const partyCodeDialogVisible = ref(false)

// 商品搜索
const productSearch = ref('')

// 选择商品
const selectProduct = () => {
  productDialogVisible.value = true
}

// 搜索商品
const searchProducts = () => {
  ElMessage.success('搜索商品')
  console.log('搜索关键词:', productSearch.value)
}

// 选择商品项
const selectProductItem = (row: any) => {
  // 检查是否已添加
  const exists = productList.value.some(item => item.id === row.id)
  if (exists) {
    ElMessage.warning('该商品已添加')
    return
  }
  
  productList.value.push({
    ...row,
    partyCode: ['JD001', 'JD002'], // 示例数据
    outboundQuantity: 1,
    unitPrice: '¥0.00',
    subtotal: '¥0.00'
  })
  
  productDialogVisible.value = false
  ElMessage.success('添加成功')
  updateStatistics()
}

// 删除商品
const removeProduct = (index: number) => {
  productList.value.splice(index, 1)
  ElMessage.success('删除成功')
  updateStatistics()
}

// 显示甲方编码
const showPartyCodes = (row: any) => {
  currentPartyCodes.value = row.partyCode
  partyCodeDialogVisible.value = true
}

// 添加商品
const addProduct = () => {
  ElMessage.success('添加商品')
}

// 更新统计信息
const updateStatistics = () => {
  statistics.totalProducts = productList.value.length
  // 实际项目中需要计算总价
}

// 保存
const handleSave = () => {
  ElMessage.success('保存成功')
  console.log('保存数据:', outboundForm, productList.value)
}

// 打印
const handlePrint = () => {
  ElMessage.success('开始打印')
}

// 提交
const handleSubmit = () => {
  ElMessage.success('提交成功')
  console.log('提交数据:', outboundForm, productList.value)
}

// 取消
const handleCancel = () => {
  ElMessage.info('已取消')
}
</script>

<style lang="scss" scoped>
.product-outbound-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
  
  .main-card {
    margin-bottom: 20px;
    
    .card-header {
      font-size: 18px;
      font-weight: bold;
      color: #303133;
    }
  }
  
  .form-section {
    margin-bottom: 20px;
    
    .section-title {
      font-size: 16px;
      font-weight: bold;
      color: #606266;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ebeef5;
    }
    
    .el-form-item {
      margin-bottom: 18px;
    }
  }
  
  .product-table {
    margin-top: 10px;
  }
  
  .add-product-actions {
    .el-button {
      margin-right: 10px;
    }
  }
  
  .party-code-item {
    margin-bottom: 10px;
    padding: 5px 0;
    border-bottom: 1px solid #ebeef5;
  }
  
  .statistics-card {
    background-color: #f0f9ff;
    border-color: #b3e0ff;
    
    .statistic-item {
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;
      
      .label {
        font-weight: bold;
        color: #409eff;
      }
      
      .total-price {
        font-weight: bold;
        color: #303133;
      }
    }
  }
  
  .form-actions {
    text-align: center;
    padding: 20px 0;
    
    .el-button {
      margin: 0 10px;
      padding: 12px 30px;
    }
  }
}
</style>