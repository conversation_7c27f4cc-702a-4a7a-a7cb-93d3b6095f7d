import{E as a}from"./element-plus-7917fd46.js";import{l as s,r as l,c as e,q as n,y as c,R as t,J as i,z as u,av as v,x as d,P as r,B as o,O as p}from"./vue-vendor-fc5a6493.js";import{_ as m}from"./_plugin-vue_export-helper-1b428a4d.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const b={class:"loose-material-statistics"},_={class:"balance-info"},h={class:"balance-item"},f={class:"value"},y={class:"balance-item"},g={class:"value days"},w={class:"material-summary"},j={class:"summary-row"},k={class:"summary-item unused"},z={class:"count"},D={class:"quantity"},M={class:"amount"},q={class:"summary-row"},x={class:"summary-item used"},C={class:"count"},L={class:"quantity"},S={class:"amount"},T={class:"stock-distribution"},B={class:"distribution-items"},E={class:"distribution-item"},I={class:"value"},J={class:"distribution-item"},N={class:"value"},O={class:"distribution-item total"},P={class:"value"},R={class:"amount"},A={class:"trend-summary"},F={class:"trend-item"},G={class:"value"},H={class:"trend-item"},K={class:"value"},Q={class:"trend-item"},U={class:"trend-item"},V={class:"value"},W={class:"trend-item"},X={class:"value"},Y={class:"action-buttons"},Z=m(s({__name:"LooseMaterialStatistics",setup(s){const m=l("2024-01-01"),Z=l(8),$=l(150),aa=l(25e3),sa=l(12),la=l(280),ea=l(45e3),na=l(200),ca=l(230),ta=l(85),ia=l(92),ua=l(2.8),va=e(()=>{const a=new Date(m.value),s=new Date,l=Math.abs(s.getTime()-a.getTime());return Math.ceil(l/864e5)}),da=e(()=>na.value+ca.value),ra=e(()=>7e4),oa=e(()=>0===ia.value?0:Math.round((ta.value-ia.value)/ia.value*100*10)/10),pa=e(()=>{const a=31-(new Date).getDate();return da.value-ua.value*a}),ma=a=>a.toLocaleString(),ba=()=>{a.info("查看甲料详细信息")},_a=()=>{a.success("数据已刷新")},ha=()=>{a.info("生成甲料使用报表")};return n(()=>{}),(a,s)=>{const l=v("el-card"),e=v("el-button");return d(),c("div",b,[t(l,{class:"balance-card",shadow:"hover"},{header:i(()=>s[0]||(s[0]=[u("div",{class:"card-header"},[u("span",null,"📅 平账信息")],-1)])),default:i(()=>[u("div",_,[u("div",h,[s[1]||(s[1]=u("span",{class:"icon"},"📋",-1)),s[2]||(s[2]=u("span",{class:"label"},"上次平账时间:",-1)),u("span",f,r(m.value),1)]),u("div",y,[s[3]||(s[3]=u("span",{class:"icon"},"⏰",-1)),s[4]||(s[4]=u("span",{class:"label"},"距离上次平账:",-1)),u("span",g,r(va.value)+"天",1)]),s[5]||(s[5]=u("div",{class:"balance-item warning"},[u("span",{class:"icon"},"⚠️"),u("span",{class:"label"},"提醒:"),u("span",{class:"value"},"建议每月进行平账操作")],-1))])]),_:1}),t(l,{class:"material-card",shadow:"hover"},{header:i(()=>s[6]||(s[6]=[u("div",{class:"card-header"},[u("span",null,"🏷️ 甲料分类统计")],-1)])),default:i(()=>[u("div",w,[u("div",j,[u("div",k,[s[7]||(s[7]=u("span",{class:"icon"},"🔴",-1)),s[8]||(s[8]=u("span",{class:"label"},"未使用甲料:",-1)),u("span",z,r(Z.value)+"项",1),u("span",D,"数量: "+r($.value)+"件",1),u("span",M,"总价: ¥"+r(ma(aa.value)),1)])]),u("div",q,[u("div",x,[s[9]||(s[9]=u("span",{class:"icon"},"✅",-1)),s[10]||(s[10]=u("span",{class:"label"},"已使用甲料:",-1)),u("span",C,r(sa.value)+"项",1),u("span",L,"数量: "+r(la.value)+"件",1),u("span",S,"总价: ¥"+r(ma(ea.value)),1)])])]),u("div",T,[s[17]||(s[17]=u("h4",null,"📦 甲料库存分布:",-1)),u("div",B,[u("div",E,[s[11]||(s[11]=u("span",{class:"icon"},"🏪",-1)),s[12]||(s[12]=u("span",{class:"label"},"仓库库存:",-1)),u("span",I,r(na.value)+"件",1)]),u("div",J,[s[13]||(s[13]=u("span",{class:"icon"},"👷",-1)),s[14]||(s[14]=u("span",{class:"label"},"工人师傅:",-1)),u("span",N,r(ca.value)+"件",1)]),u("div",O,[s[15]||(s[15]=u("span",{class:"icon"},"📊",-1)),s[16]||(s[16]=u("span",{class:"label"},"库存总计:",-1)),u("span",P,r(da.value)+"件",1),u("span",R,"💰 总价值: ¥"+r(ma(ra.value)),1)])])])]),_:1}),t(l,{class:"trend-card",shadow:"hover"},{header:i(()=>s[18]||(s[18]=[u("div",{class:"card-header"},[u("span",null,"📈 甲料使用趋势")],-1)])),default:i(()=>[u("div",A,[u("div",F,[s[19]||(s[19]=u("span",{class:"label"},"本月使用:",-1)),u("span",G,r(ta.value)+"件",1)]),u("div",H,[s[20]||(s[20]=u("span",{class:"label"},"上月使用:",-1)),u("span",K,r(ia.value)+"件",1)]),u("div",Q,[s[21]||(s[21]=u("span",{class:"label"},"环比:",-1)),u("span",{class:o(["value",{positive:oa.value>0,negative:oa.value<0}])},r(oa.value>0?"+":"")+r(oa.value)+"% ",3)]),u("div",U,[s[22]||(s[22]=u("span",{class:"label"},"平均日耗:",-1)),u("span",V,r(ua.value)+"件",1)]),u("div",W,[s[23]||(s[23]=u("span",{class:"label"},"预计月底库存:",-1)),u("span",X,r(pa.value)+"件",1)])])]),_:1}),u("div",Y,[t(e,{type:"success",size:"large",onClick:ba},{default:i(()=>s[24]||(s[24]=[p(" 📋 查看详情 ",-1)])),_:1,__:[24]}),t(e,{type:"default",size:"large",onClick:_a},{default:i(()=>s[25]||(s[25]=[p(" 🔄 刷新 ",-1)])),_:1,__:[25]}),t(e,{type:"warning",size:"large",onClick:ha},{default:i(()=>s[26]||(s[26]=[p(" 📈 使用报表 ",-1)])),_:1,__:[26]})])])}}}),[["__scopeId","data-v-fb97c0c5"]]);export{Z as default};
