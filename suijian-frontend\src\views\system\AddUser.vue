<template>
  <div class="add-user-container">
    <el-card class="main-card">
      <template #header>
        <div class="card-header">
          <span>新增用户</span>
        </div>
      </template>
      
      <!-- 用户基本信息 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">用户基本信息</div>
        </el-col>
        <el-col :span="12">
          <el-form-item label="用户名:" required>
            <el-input v-model="userForm.username" placeholder="请输入用户名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="真实姓名:" required>
            <el-input v-model="userForm.realName" placeholder="请输入真实姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="密码:" required>
            <el-input 
              v-model="userForm.password" 
              type="password" 
              placeholder="请输入密码" 
              show-password 
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="确认密码:" required>
            <el-input 
              v-model="userForm.confirmPassword" 
              type="password" 
              placeholder="请确认密码" 
              show-password 
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="手机号:" required>
            <el-input v-model="userForm.phone" placeholder="请输入手机号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="邮箱:">
            <el-input v-model="userForm.email" placeholder="请输入邮箱" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="微信号:">
            <el-input v-model="userForm.wechat" placeholder="请输入微信号" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 角色分配 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">角色分配</div>
        </el-col>
        <el-col :span="12">
          <el-form-item label="角色:">
            <el-select v-model="userForm.role" placeholder="请选择角色" style="width: 100%">
              <el-option
                v-for="role in roleOptions"
                :key="role.value"
                :label="role.label"
                :value="role.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 自定义角色权限 -->
      <el-row :gutter="20" class="form-section" v-if="userForm.role === 'custom'">
        <el-col :span="24">
          <div class="section-title">自定义角色权限</div>
        </el-col>
        <el-col :span="24">
          <el-card class="permission-card">
            <div class="permission-header">
              <span>选择角色: 自定义角色</span>
            </div>
            <el-scrollbar height="400">
              <el-checkbox-group v-model="userForm.permissions" class="permission-list">
                <el-checkbox 
                  v-for="permission in permissionOptions" 
                  :key="permission.value" 
                  :label="permission.value"
                  class="permission-item"
                >
                  {{ permission.label }}
                </el-checkbox>
              </el-checkbox-group>
            </el-scrollbar>
            <div class="permission-actions">
              <el-button @click="selectAllPermissions">全选</el-button>
              <el-button @click="deselectAllPermissions">全不选</el-button>
              <el-button @click="invertPermissions">反选</el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 状态设置 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">状态设置</div>
        </el-col>
        <el-col :span="12">
          <el-form-item label="用户状态:">
            <el-radio-group v-model="userForm.status">
              <el-radio label="active">正常</el-radio>
              <el-radio label="disabled">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="登录限制:">
            <el-checkbox v-model="userForm.limitIP">限制登录IP范围</el-checkbox>
            <el-checkbox v-model="userForm.limitTime">限制登录时间范围</el-checkbox>
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 备注信息 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">备注信息</div>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注:">
            <el-input
              v-model="userForm.remarks"
              type="textarea"
              :rows="3"
              placeholder="请输入备注"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 验证提示 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <el-card class="info-card">
            <div class="info-title">输入验证提示:</div>
            <div class="info-content">
              <p>- 用户名: 4-20个字符，只能包含字母、数字、下划线</p>
              <p>- 密码: 6-20个字符，需包含字母和数字</p>
              <p>- 手机号: 11位数字，符合手机号格式</p>
              <p>- 邮箱: 需符合邮箱格式</p>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 操作按钮 -->
      <div class="form-actions">
        <el-button type="primary" @click="handleSave">保存</el-button>
        <el-button type="success" @click="handleSaveAndContinue">保存并继续添加</el-button>
        <el-button @click="handleCancel">取消</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue'
import { ElMessage } from 'element-plus'

// 用户表单
const userForm = reactive({
  username: '',
  realName: '',
  password: '',
  confirmPassword: '',
  phone: '',
  email: '',
  wechat: '',
  role: '',
  permissions: [] as string[],
  status: 'active',
  limitIP: false,
  limitTime: false,
  remarks: ''
})

// 角色选项
const roleOptions = [
  { value: 'admin', label: '系统管理员' },
  { value: 'warehouse', label: '仓库管理员' },
  { value: 'order', label: '订单管理员' },
  { value: 'project', label: '工程管理员' },
  { value: 'finance', label: '财务人员' },
  { value: 'user', label: '普通用户' },
  { value: 'custom', label: '自定义角色' }
]

// 权限选项
const permissionOptions = [
  { value: 'dashboard_view', label: '首页 - 查看仪表板' },
  { value: 'material_list', label: '仓库管理 - 物料列表' },
  { value: 'material_apply', label: '仓库管理 - 领料申请' },
  { value: 'material_inbound', label: '仓库管理 - 甲料入库' },
  { value: 'material_return', label: '仓库管理 - 物料退仓' },
  { value: 'auxiliary_purchase', label: '仓库管理 - 辅料采购' },
  { value: 'product_inbound', label: '仓库管理 - 商品入库' },
  { value: 'material_records', label: '仓库管理 - 进出记录' },
  { value: 'product_outbound', label: '仓库管理 - 商品售卖出库' },
  { value: 'material_price', label: '仓库管理 - 物料价格' },
  { value: 'product_price', label: '仓库管理 - 商品价格' },
  { value: 'material_base', label: '仓库管理 - 物料基础库' },
  { value: 'stock_warning', label: '仓库管理 - 库存预警' },
  { value: 'loose_order_list', label: '散户订单 - 订单列表' },
  { value: 'loose_order_assign', label: '散户订单 - 订单分派' },
  { value: 'monthly_balance', label: '散户订单 - 月度平账' },
  { value: 'project_list', label: '工程订单 - 工程列表' },
  { value: 'project_progress', label: '工程订单 - 工程推进' },
  { value: 'employee_list', label: '员工管理 - 员工列表' },
  { value: 'user_management', label: '系统设置 - 用户管理' },
  { value: 'permission_management', label: '系统设置 - 权限管理' },
  { value: 'role_management', label: '系统设置 - 角色管理' },
  { value: 'system_log', label: '系统设置 - 系统日志' }
]

// 全选权限
const selectAllPermissions = () => {
  userForm.permissions = permissionOptions.map(item => item.value)
}

// 全不选权限
const deselectAllPermissions = () => {
  userForm.permissions = []
}

// 反选权限
const invertPermissions = () => {
  const allPermissions = permissionOptions.map(item => item.value)
  userForm.permissions = allPermissions.filter(permission => 
    !userForm.permissions.includes(permission)
  )
}

// 保存
const handleSave = () => {
  ElMessage.success('保存成功')
  console.log('保存用户:', userForm)
}

// 保存并继续添加
const handleSaveAndContinue = () => {
  ElMessage.success('保存成功，继续添加')
  // 重置表单，但保留一些字段
  userForm.username = ''
  userForm.realName = ''
  userForm.password = ''
  userForm.confirmPassword = ''
  userForm.remarks = ''
}

// 取消
const handleCancel = () => {
  ElMessage.info('已取消')
}
</script>

<style lang="scss" scoped>
.add-user-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
  
  .main-card {
    margin-bottom: 20px;
    
    .card-header {
      font-size: 18px;
      font-weight: bold;
      color: #303133;
    }
  }
  
  .form-section {
    margin-bottom: 20px;
    
    .section-title {
      font-size: 16px;
      font-weight: bold;
      color: #606266;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ebeef5;
    }
    
    .el-form-item {
      margin-bottom: 18px;
    }
  }
  
  .permission-card {
    .permission-header {
      font-weight: bold;
      margin-bottom: 15px;
      color: #606266;
    }
    
    .permission-list {
      display: flex;
      flex-direction: column;
      margin-bottom: 15px;
    }
    
    .permission-item {
      margin-bottom: 10px;
    }
    
    .permission-actions {
      text-align: right;
      padding-top: 15px;
      border-top: 1px solid #ebeef5;
      
      .el-button {
        margin-left: 10px;
      }
    }
  }
  
  .info-card {
    background-color: #f0f9ff;
    border-color: #b3e0ff;
    
    .info-title {
      font-weight: bold;
      color: #409eff;
      margin-bottom: 10px;
    }
    
    .info-content {
      p {
        margin: 5px 0;
        color: #606266;
      }
    }
  }
  
  .form-actions {
    text-align: center;
    padding: 20px 0;
    
    .el-button {
      margin: 0 10px;
      padding: 12px 30px;
    }
  }
}
</style>