<template>
  <div class="material-price">
    <!-- 面包屑导航 -->
    <el-breadcrumb class="breadcrumb" separator=">">
      <el-breadcrumb-item :to="{ path: '/dashboard' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item :to="{ path: '/warehouse/material-list' }">仓库管理</el-breadcrumb-item>
      <el-breadcrumb-item>甲料价格</el-breadcrumb-item>
    </el-breadcrumb>

    <el-card class="main-card">
      <template #header>
        <div class="card-header">
          <span>💰 甲料价格管理</span>
          <div class="header-actions">
            <el-button type="primary" size="small" @click="showAddDialog">
              <el-icon><Plus /></el-icon>
              新增价格
            </el-button>
            <el-button type="success" size="small" @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜索条件 -->
      <div class="search-section">
        <el-form :model="searchForm" inline>
          <el-form-item label="物料编码:">
            <el-input v-model="searchForm.materialCode" placeholder="请输入物料编码" />
          </el-form-item>
          <el-form-item label="物料名称:">
            <el-input v-model="searchForm.materialName" placeholder="请输入物料名称" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <el-table :data="tableData" border style="width: 100%">
        <el-table-column prop="materialCode" label="物料编码" width="120" />
        <el-table-column prop="materialName" label="物料名称" width="150" />
        <el-table-column prop="specification" label="规格" width="100" />
        <el-table-column prop="unit" label="单位" width="80" />
        <el-table-column prop="currentPrice" label="当前价格" width="120" align="center">
          <template #default="{ row }">
            ¥{{ row.currentPrice.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="lastPrice" label="上次价格" width="120" align="center">
          <template #default="{ row }">
            ¥{{ row.lastPrice.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="priceChange" label="价格变动" width="120" align="center">
          <template #default="{ row }">
            <span :class="getPriceChangeClass(row.priceChange)">
              {{ row.priceChange > 0 ? '+' : '' }}{{ row.priceChange.toFixed(2) }}%
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="updateDate" label="更新日期" width="120" />
        <el-table-column prop="operator" label="操作员" width="100" />
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="editPrice(row)">
              编辑
            </el-button>
            <el-button type="info" size="small" @click="viewHistory(row)">
              历史
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 价格编辑弹窗 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="500px">
      <el-form :model="formData" :rules="formRules" ref="formRef" label-width="100px">
        <el-form-item label="物料编码:" prop="materialCode">
          <el-input v-model="formData.materialCode" :disabled="isEdit" />
        </el-form-item>
        <el-form-item label="物料名称:" prop="materialName">
          <el-input v-model="formData.materialName" :disabled="isEdit" />
        </el-form-item>
        <el-form-item label="新价格:" prop="newPrice">
          <el-input-number
            v-model="formData.newPrice"
            :min="0"
            :step="0.01"
            :precision="2"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="变更原因:" prop="changeReason">
          <el-input
            v-model="formData.changeReason"
            type="textarea"
            :rows="3"
            placeholder="请输入价格变更原因"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { Plus, Refresh } from '@element-plus/icons-vue'

// 表单引用
const formRef = ref<FormInstance>()

// 搜索表单
const searchForm = reactive({
  materialCode: '',
  materialName: ''
})

// 分页信息
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 弹窗控制
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isEdit = ref(false)

// 表单数据
const formData = reactive({
  materialCode: '',
  materialName: '',
  newPrice: 0,
  changeReason: ''
})

// 表单验证规则
const formRules: FormRules = {
  materialCode: [
    { required: true, message: '请输入物料编码', trigger: 'blur' }
  ],
  materialName: [
    { required: true, message: '请输入物料名称', trigger: 'blur' }
  ],
  newPrice: [
    { required: true, message: '请输入新价格', trigger: 'blur' }
  ],
  changeReason: [
    { required: true, message: '请输入变更原因', trigger: 'blur' }
  ]
}

// 表格数据
const tableData = ref([
  {
    materialCode: 'M001',
    materialName: '燃气表',
    specification: 'G2.5',
    unit: '个',
    currentPrice: 300.00,
    lastPrice: 280.00,
    priceChange: 7.14,
    updateDate: '2024-01-15',
    operator: '张三'
  },
  {
    materialCode: 'M002',
    materialName: '镀锌管件',
    specification: 'DN20',
    unit: '个',
    currentPrice: 25.00,
    lastPrice: 26.00,
    priceChange: -3.85,
    updateDate: '2024-01-14',
    operator: '李四'
  },
  {
    materialCode: 'M003',
    materialName: '波纹管',
    specification: 'DN15',
    unit: 'm',
    currentPrice: 12.50,
    lastPrice: 12.50,
    priceChange: 0.00,
    updateDate: '2024-01-13',
    operator: '王五'
  }
])

// 获取价格变动样式
const getPriceChangeClass = (change: number): string => {
  if (change > 0) return 'price-up'
  if (change < 0) return 'price-down'
  return 'price-stable'
}

// 搜索
const handleSearch = () => {
  console.log('搜索条件:', searchForm)
  ElMessage.success('搜索完成')
}

// 重置
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    (searchForm as any)[key] = ''
  })
  ElMessage.info('搜索条件已重置')
}

// 刷新数据
const refreshData = () => {
  ElMessage.success('数据已刷新')
}

// 显示新增弹窗
const showAddDialog = () => {
  dialogTitle.value = '新增价格'
  isEdit.value = false
  Object.keys(formData).forEach(key => {
    (formData as any)[key] = key === 'newPrice' ? 0 : ''
  })
  dialogVisible.value = true
}

// 编辑价格
const editPrice = (row: any) => {
  dialogTitle.value = '编辑价格'
  isEdit.value = true
  formData.materialCode = row.materialCode
  formData.materialName = row.materialName
  formData.newPrice = row.currentPrice
  formData.changeReason = ''
  dialogVisible.value = true
}

// 查看历史
const viewHistory = (row: any) => {
  ElMessage.info(`查看 ${row.materialName} 的价格历史`)
}

// 提交
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    ElMessage.success('价格更新成功')
    dialogVisible.value = false
    console.log('价格数据:', formData)
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单填写是否正确')
  }
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
}

// 初始化
onMounted(() => {
  pagination.total = tableData.value.length
})
</script>

<style scoped lang="scss">
.material-price {
  padding: 20px;
  
  .breadcrumb {
    margin-bottom: 20px;
  }
  
  .main-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 18px;
      font-weight: bold;
      color: #303133;
      
      .header-actions {
        display: flex;
        gap: 10px;
      }
    }
  }
  
  .search-section {
    margin-bottom: 20px;
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }
  
  .pagination-section {
    margin-top: 20px;
    text-align: right;
  }
  
  .price-up {
    color: #f56c6c;
    font-weight: bold;
  }
  
  .price-down {
    color: #67c23a;
    font-weight: bold;
  }
  
  .price-stable {
    color: #909399;
  }
  
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
}
</style>
