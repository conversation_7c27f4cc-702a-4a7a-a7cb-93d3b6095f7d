<template>
  <div class="material-records">
    <!-- 面包屑导航 -->
    <el-breadcrumb class="breadcrumb" separator=">">
      <el-breadcrumb-item :to="{ path: '/dashboard' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item :to="{ path: '/warehouse/material-list' }">仓库管理</el-breadcrumb-item>
      <el-breadcrumb-item>甲料记录</el-breadcrumb-item>
    </el-breadcrumb>

    <el-card class="main-card">
      <template #header>
        <div class="card-header">
          <span>📋 甲料记录</span>
          <div class="header-actions">
            <el-button type="primary" size="small" @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜索条件 -->
      <div class="search-section">
        <el-form :model="searchForm" inline>
          <el-form-item label="物料编码:">
            <el-input v-model="searchForm.materialCode" placeholder="请输入物料编码" />
          </el-form-item>
          <el-form-item label="物料名称:">
            <el-input v-model="searchForm.materialName" placeholder="请输入物料名称" />
          </el-form-item>
          <el-form-item label="操作类型:">
            <el-select v-model="searchForm.operationType" placeholder="请选择操作类型">
              <el-option label="全部" value="" />
              <el-option label="入库" value="inbound" />
              <el-option label="出库" value="outbound" />
              <el-option label="退库" value="return" />
            </el-select>
          </el-form-item>
          <el-form-item label="日期范围:">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <el-table :data="tableData" border style="width: 100%">
        <el-table-column prop="recordId" label="记录ID" width="100" />
        <el-table-column prop="operationDate" label="操作日期" width="120" />
        <el-table-column prop="materialCode" label="物料编码" width="120" />
        <el-table-column prop="materialName" label="物料名称" width="150" />
        <el-table-column prop="specification" label="规格" width="100" />
        <el-table-column prop="unit" label="单位" width="80" />
        <el-table-column prop="operationType" label="操作类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getOperationTypeTag(row.operationType)">
              {{ getOperationTypeText(row.operationType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="quantity" label="数量" width="100" align="center" />
        <el-table-column prop="operator" label="操作员" width="100" />
        <el-table-column prop="remarks" label="备注" min-width="150" />
        <el-table-column label="操作" width="100" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewDetails(row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'

// 搜索表单
const searchForm = reactive({
  materialCode: '',
  materialName: '',
  operationType: '',
  dateRange: []
})

// 分页信息
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 表格数据
const tableData = ref([
  {
    recordId: 'R001',
    operationDate: '2024-01-15',
    materialCode: 'M001',
    materialName: '燃气表',
    specification: 'G2.5',
    unit: '个',
    operationType: 'inbound',
    quantity: 50,
    operator: '张三',
    remarks: '新采购入库'
  },
  {
    recordId: 'R002',
    operationDate: '2024-01-14',
    materialCode: 'M002',
    materialName: '镀锌管件',
    specification: 'DN20',
    unit: '个',
    operationType: 'outbound',
    quantity: 20,
    operator: '李四',
    remarks: '工程领用'
  },
  {
    recordId: 'R003',
    operationDate: '2024-01-13',
    materialCode: 'M003',
    materialName: '波纹管',
    specification: 'DN15',
    unit: 'm',
    operationType: 'return',
    quantity: 5,
    operator: '王五',
    remarks: '工程退库'
  }
])

// 获取操作类型标签
const getOperationTypeTag = (type: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  switch (type) {
    case 'inbound':
      return 'success'
    case 'outbound':
      return 'primary'
    case 'return':
      return 'warning'
    default:
      return 'info'
  }
}

// 获取操作类型文本
const getOperationTypeText = (type: string): string => {
  switch (type) {
    case 'inbound':
      return '入库'
    case 'outbound':
      return '出库'
    case 'return':
      return '退库'
    default:
      return '未知'
  }
}

// 搜索
const handleSearch = () => {
  console.log('搜索条件:', searchForm)
  ElMessage.success('搜索完成')
  // 这里可以调用API进行搜索
}

// 重置
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    (searchForm as any)[key] = key === 'dateRange' ? [] : ''
  })
  ElMessage.info('搜索条件已重置')
}

// 刷新数据
const refreshData = () => {
  ElMessage.success('数据已刷新')
  // 这里可以调用API刷新数据
}

// 查看详情
const viewDetails = (row: any) => {
  ElMessage.info(`查看记录 ${row.recordId} 的详情`)
  // 这里可以打开详情弹窗
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  // 重新加载数据
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  // 重新加载数据
}

// 初始化
onMounted(() => {
  pagination.total = tableData.value.length
})
</script>

<style scoped lang="scss">
.material-records {
  padding: 20px;
  
  .breadcrumb {
    margin-bottom: 20px;
  }
  
  .main-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 18px;
      font-weight: bold;
      color: #303133;
      
      .header-actions {
        display: flex;
        gap: 10px;
      }
    }
  }
  
  .search-section {
    margin-bottom: 20px;
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }
  
  .pagination-section {
    margin-top: 20px;
    text-align: right;
  }
}
</style>
