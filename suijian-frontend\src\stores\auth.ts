import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import request from '@/utils/request'

export interface User {
  id: number
  username: string
  role: string
  phone?: string
  email?: string
}

export const useAuthStore = defineStore('auth', () => {
  const token = ref<string>('')
  const user = ref<User | null>(null)
  const loading = ref(false)

  const isAuthenticated = computed(() => !!token.value)

  const login = async (username: string, password: string) => {
    loading.value = true
    try {
      // 使用request工具，会自动走mock适配
      const data = await request.post('/api/auth/login', { username, password })
      
      if (data.code === 200) {
        token.value = data.data.token
        user.value = data.data.user
        return { success: true }
      } else {
        return { success: false, message: data.message || '登录失败' }
      }
    } catch (error) {
      console.error('Login error:', error)
      return { success: false, message: '网络错误' }
    } finally {
      loading.value = false
    }
  }

  const logout = () => {
    token.value = ''
    user.value = null
  }

  const checkAuth = async () => {
    if (!token.value) return false
    
    try {
      const data = await request.get('/api/auth/check')
      
      if (data.code === 200) {
        user.value = data.data.user
        return true
      } else {
        logout()
        return false
      }
    } catch (error) {
      console.error('Check auth error:', error)
      logout()
      return false
    }
  }

  return {
    token,
    user,
    loading,
    isAuthenticated,
    login,
    logout,
    checkAuth
  }
}) 