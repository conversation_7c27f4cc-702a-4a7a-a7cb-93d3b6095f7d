var e=Object.defineProperty,t=Object.defineProperties,a=Object.getOwnPropertyDescriptors,l=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable,s=(t,a,l)=>a in t?e(t,a,{enumerable:!0,configurable:!0,writable:!0,value:l}):t[a]=l,n=(e,t,a)=>new Promise((l,r)=>{var o=e=>{try{n(a.next(e))}catch(t){r(t)}},s=e=>{try{n(a.throw(e))}catch(t){r(t)}},n=e=>e.done?l(e.value):Promise.resolve(e.value).then(o,s);n((a=a.apply(e,t)).next())});import{r as i}from"./index-b7904b56.js";import{l as m,av as d,x as u,I as c,J as p,R as f,O as h,P as b,z as y,_ as g,r as _,o as w,q as v,y as M,aD as C,K as N,M as D}from"./vue-vendor-fc5a6493.js";import{E as j}from"./element-plus-ad78a7bf.js";import{_ as k}from"./_plugin-vue_export-helper-1b428a4d.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const A=m({__name:"ProjectCostSummaryTable",props:{data:{},summaryMethod:{type:Function},formatNumber:{type:Function}},setup:e=>(e,t)=>{const a=d("el-table-column"),l=d("el-tooltip"),r=d("el-table");return u(),c(r,{data:e.data,style:{width:"100%"},border:"","show-summary":"","summary-method":e.summaryMethod},{default:p(()=>[f(a,{prop:"serialNo",label:"序号",width:"60",align:"center"}),f(a,{prop:"projectName",label:"单项工程名称",width:"200"}),f(a,{label:"工程安装人工费(元)",align:"center"},{default:p(()=>[f(a,{prop:"preMeterLaborCost",label:"表前安装人工费",width:"120",align:"center"},{default:p(({row:t})=>[h(" ¥"+b(e.formatNumber(t.preMeterLaborCost)),1)]),_:1}),f(a,{prop:"indoorLaborCost",label:"户内安装人工费",width:"120",align:"center"},{default:p(({row:t})=>[h(" ¥"+b(e.formatNumber(t.indoorLaborCost)),1)]),_:1}),f(a,{prop:"laborSubtotal",label:"小计(元)",width:"120",align:"center"},{default:p(({row:t})=>[h(" ¥"+b(e.formatNumber(t.laborSubtotal)),1)]),_:1})]),_:1}),f(a,{prop:"installedHouseholds",label:"安装户数",width:"100",align:"center"}),f(a,{label:"实际耗用材料金额",align:"center"},{default:p(()=>[f(a,{prop:"gasMeterCost",label:"气表",width:"100",align:"center"},{default:p(({row:t})=>[h(" ¥"+b(e.formatNumber(t.gasMeterCost)),1)]),_:1}),f(a,{prop:"postMeterCost",label:"表后",width:"100",align:"center"},{default:p(({row:t})=>[h(" ¥"+b(e.formatNumber(t.postMeterCost)),1)]),_:1}),f(a,{prop:"fittingsCost",label:"管件",width:"100",align:"center"},{default:p(({row:t})=>[h(" ¥"+b(e.formatNumber(t.fittingsCost)),1)]),_:1}),f(a,{prop:"materialSubtotal",label:"小计(元)",width:"120",align:"center"},{default:p(({row:t})=>[h(" ¥"+b(e.formatNumber(t.materialSubtotal)),1)]),_:1})]),_:1}),f(a,{prop:"actualReceivedAmount",label:"实际领用材料金额(元)",width:"150",align:"center"},{default:p(({row:t})=>[h(" ¥"+b(e.formatNumber(t.actualReceivedAmount)),1)]),_:1}),f(a,{prop:"overReceivedAmount",label:"超领甲供材料金额",width:"150",align:"center"},{default:p(({row:t})=>[h(" ¥"+b(e.formatNumber(t.overReceivedAmount)),1)]),_:1}),f(a,{prop:"totalProjectCost",label:"工程总造价(元)",width:"150",align:"center"},{header:p(()=>[f(l,{content:"计算公式：工程总造价 = 人工费小计 + 实际领用材料金额",placement:"top"},{default:p(()=>t[0]||(t[0]=[y("span",null,"工程总造价(元)",-1)])),_:1,__:[0]})]),default:p(({row:t})=>[h(" ¥"+b(e.formatNumber(t.totalProjectCost)),1)]),_:1}),f(a,{prop:"payableAmount",label:"应付施工单位金额",width:"150",align:"center"},{header:p(()=>[f(l,{content:"计算公式：应付施工单位金额 = 人工费小计 - 超领甲供材料金额",placement:"top"},{default:p(()=>t[1]||(t[1]=[y("span",null,"应付施工单位金额",-1)])),_:1,__:[1]})]),default:p(({row:t})=>[h(" ¥"+b(e.formatNumber(t.payableAmount)),1)]),_:1})]),_:1},8,["data","summary-method"])}}),P={class:"balance-records"},S={class:"card-header"},z={class:"header-actions"},R={class:"pagination-container"},O={key:0,class:"detail-content dialog-large-content"},V={class:"detail-section"},x={class:"dialog-footer"},T=k(m({__name:"BalanceRecords",setup(e){const m=g({month:"",status:"",dateRange:[]}),c=g({currentPage:1,pageSize:20,total:0}),k=_([{id:1,month:"2024年1月",balanceDate:new Date("2024-01-31 15:30:00"),operator:"张三",status:"confirmed",workerCount:8,totalOrders:88,totalAmount:377e3,materialCost:145800,laborCost:66e3,profit:165200,profitRate:43.8,salaryData:[{workerName:"李师傅",workType:"电工",workPrice:300,workDays:22,totalSalary:7920,paidAmount:5e3},{workerName:"王师傅",workType:"水工",workPrice:280,workDays:20,totalSalary:6720,paidAmount:4500},{workerName:"张师傅",workType:"安装工",workPrice:320,workDays:18,totalSalary:6912,paidAmount:4e3}],orderData:[{orderType:"水电安装",completedCount:25,totalAmount:125e3,materialCost:45e3,laborCost:18750,profit:61250},{orderType:"电路维修",completedCount:18,totalAmount:72e3,materialCost:28800,laborCost:13500,profit:29700}],projectCostData:[{category:"人工成本",subCategory:"电工",amount:15e3,unit:"人/天"},{category:"人工成本",subCategory:"水工",amount:12e3,unit:"人/天"},{category:"人工成本",subCategory:"安装工",amount:18e3,unit:"人/天"},{category:"物料成本",subCategory:"电线",amount:5e3,unit:"米"},{category:"物料成本",subCategory:"水管",amount:8e3,unit:"米"},{category:"物料成本",subCategory:"开关",amount:1e3,unit:"个"}]},{id:2,month:"2023年12月",balanceDate:new Date("2023-12-31 16:45:00"),operator:"李四",status:"confirmed",workerCount:7,totalOrders:76,totalAmount:325e3,materialCost:128500,laborCost:58e3,profit:138500,profitRate:42.6,salaryData:[],orderData:[],projectCostData:[]},{id:3,month:"2023年11月",balanceDate:new Date("2023-11-30 14:20:00"),operator:"王五",status:"confirmed",workerCount:6,totalOrders:65,totalAmount:286e3,materialCost:112300,laborCost:51500,profit:122200,profitRate:42.7,salaryData:[],orderData:[],projectCostData:[]},{id:4,month:"2023年10月",balanceDate:new Date("2023-10-31 17:10:00"),operator:"赵六",status:"confirmed",workerCount:7,totalOrders:72,totalAmount:312e3,materialCost:125600,laborCost:56800,profit:129600,profitRate:41.5,salaryData:[],orderData:[],projectCostData:[]},{id:5,month:"2023年9月",balanceDate:new Date("2023-09-30 15:55:00"),operator:"孙七",status:"confirmed",workerCount:6,totalOrders:68,totalAmount:298e3,materialCost:118900,laborCost:54200,profit:124900,profitRate:41.9,salaryData:[],orderData:[],projectCostData:[]}]),T=_(!1),U=_(null),E=_("project-cost-summary"),L=g({"project-cost-summary":{list:[],summaryMethod:()=>[]},"pre-meter-materials":{list:[],summaryMethod:()=>[]},"indoor-materials":{list:[],summaryMethod:()=>[]},"over-received-materials":{list:[],summaryMethod:()=>[]},"fittings-materials":{list:[],summaryMethod:()=>[]},"meter-installation-fittings":{list:[],summaryMethod:()=>[]},"minor-installation-fittings":{list:[],summaryMethod:()=>[]},"secondary-installation-fittings":{list:[],summaryMethod:()=>[]},"indoor-installation-fittings":{list:[],summaryMethod:()=>[]},"meter-settlement-half":{list:[],summaryMethod:()=>[]},"meter-settlement-no-half":{list:[],summaryMethod:()=>[]},"gas-minor-settlement-half":{list:[],summaryMethod:()=>[]},"gas-minor-settlement-no-half":{list:[],summaryMethod:()=>[]},"gas-minor-settlement-no-meter":{list:[],summaryMethod:()=>[]},"supplied-materials-receipt":{list:[],summaryMethod:()=>[]}}),I=g({"project-cost-summary":!1,"pre-meter-materials":!1,"indoor-materials":!1,"over-received-materials":!1,"fittings-materials":!1,"meter-installation-fittings":!1,"minor-installation-fittings":!1,"secondary-installation-fittings":!1,"indoor-installation-fittings":!1,"meter-settlement-half":!1,"meter-settlement-no-half":!1,"gas-minor-settlement-half":!1,"gas-minor-settlement-no-half":!1,"gas-minor-settlement-no-meter":!1,"supplied-materials-receipt":!1}),F=g({}),$={"project-cost-summary":"/api/loose-orders/balance/project-cost","pre-meter-materials":"/api/loose-orders/balance/pre-meter-materials","indoor-materials":"/api/loose-orders/balance/indoor-materials","over-received-materials":"/api/loose-orders/balance/over-received-materials","fittings-materials":"/api/loose-orders/balance/fittings-materials","meter-installation-fittings":"/api/loose-orders/balance/meter-installation-fittings","minor-installation-fittings":"/api/loose-orders/balance/minor-installation-fittings","secondary-installation-fittings":"/api/loose-orders/balance/secondary-installation-fittings","indoor-installation-fittings":"/api/loose-orders/balance/indoor-installation-fittings","meter-settlement-half":"/api/loose-orders/balance/meter-settlement-half","meter-settlement-no-half":"/api/loose-orders/balance/meter-settlement-no-half","gas-minor-settlement-half":"/api/loose-orders/balance/gas-minor-settlement-half","gas-minor-settlement-no-half":"/api/loose-orders/balance/gas-minor-settlement-no-half","gas-minor-settlement-no-meter":"/api/loose-orders/balance/gas-minor-settlement-no-meter","supplied-materials-receipt":"/api/loose-orders/balance/supplied-materials-receipt"},q=e=>{if(null==e||""===e||isNaN(Number(e)))return"0";try{return Number(e).toLocaleString()}catch(t){return String(e)}},B=e=>e?e.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}):"",H=()=>{ae(),j.success("查询成功")},J=()=>{m.month="",m.status="",m.dateRange=[],j.info("已重置查询条件")},K=()=>{T.value=!1,U.value=null},G=()=>{j.success("导出Excel成功")},Q=()=>{window.print()},W=()=>{j.info("打开统计图表")},X=()=>{j.success("导出详情成功")},Y=e=>{c.pageSize=e,ae()},Z=e=>{c.currentPage=e,ae()},ee=e=>n(this,null,function*(){var t;if(F[e])return L[e].list=F[e].list,void(L[e].summaryMethod=te(e,null==(t=F[e])?void 0:t.summary));I[e]=!0;try{const t=$[e],a=yield i.get(t);200===a.code&&a.data&&Array.isArray(a.data.list)?(L[e].list=a.data.list,L[e].summaryMethod=te(e,a.data.summary),F[e]=a.data):(L[e].list=[],L[e].summaryMethod=()=>[])}catch(a){L[e].list=[],L[e].summaryMethod=()=>[]}finally{I[e]=!1}});function te(e,t){return e=>{const{columns:a}=e,l=[];return t?a.forEach((e,a)=>{if(0===a)l[a]="合计";else{const r=e.property;void 0!==t[r]?r.includes("Amount")||r.includes("Cost")||r.includes("Price")?l[a]=`¥${q(t[r])}`:l[a]=q(t[r]):l[a]=""}}):a.forEach((e,t)=>{l[t]=0===t?"合计":""}),l}}w(T,e=>{e&&ee(E.value)}),v(()=>{ae()});const ae=()=>n(this,null,function*(){try{const e=yield i.get("/api/loose-orders/balance/records");if(200===e.code){const n=e.data;k.value=n.records.map(e=>{return n=((e,t)=>{for(var a in t||(t={}))r.call(t,a)&&s(e,a,t[a]);if(l)for(var a of l(t))o.call(t,a)&&s(e,a,t[a]);return e})({},e),i={balanceDate:new Date(e.balanceDate)},t(n,a(i));var n,i}),c.total=n.total,c.currentPage=n.page,c.pageSize=n.pageSize,j.success("数据加载成功")}else j.error("数据加载失败")}catch(e){j.error("数据加载失败")}});return(e,t)=>{const a=d("el-date-picker"),l=d("el-form-item"),r=d("el-col"),o=d("el-option"),s=d("el-select"),n=d("el-row"),i=d("el-button"),g=d("el-form"),_=d("el-card"),w=d("el-table-column"),v=d("el-tag"),F=d("el-table"),$=d("el-pagination"),te=d("el-descriptions-item"),ae=d("el-descriptions"),le=d("el-tab-pane"),re=d("el-tabs"),oe=d("el-dialog"),se=C("loading");return u(),M("div",P,[f(_,{class:"search-card"},{header:p(()=>t[8]||(t[8]=[y("div",{class:"card-header"},[y("span",null,"平账记录")],-1)])),default:p(()=>[f(g,{model:m,"label-width":"120px",class:"search-form"},{default:p(()=>[f(n,{gutter:20},{default:p(()=>[f(r,{span:8},{default:p(()=>[f(l,{label:"平账时间"},{default:p(()=>[f(a,{modelValue:m.dateRange,"onUpdate:modelValue":t[0]||(t[0]=e=>m.dateRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),f(r,{span:8},{default:p(()=>[f(l,{label:"平账月份"},{default:p(()=>[f(s,{modelValue:m.month,"onUpdate:modelValue":t[1]||(t[1]=e=>m.month=e),placeholder:"请选择平账月份",style:{width:"100%"}},{default:p(()=>[f(o,{label:"全部",value:""}),f(o,{label:"2024年1月",value:"2024-01"}),f(o,{label:"2024年2月",value:"2024-02"}),f(o,{label:"2024年3月",value:"2024-03"}),f(o,{label:"2024年4月",value:"2024-04"}),f(o,{label:"2024年5月",value:"2024-05"}),f(o,{label:"2024年6月",value:"2024-06"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),f(r,{span:8},{default:p(()=>[f(l,{label:"状态"},{default:p(()=>[f(s,{modelValue:m.status,"onUpdate:modelValue":t[2]||(t[2]=e=>m.status=e),placeholder:"请选择状态",style:{width:"100%"}},{default:p(()=>[f(o,{label:"全部",value:""}),f(o,{label:"已确认",value:"confirmed"}),f(o,{label:"待确认",value:"pending"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),f(n,null,{default:p(()=>[f(r,{span:24,style:{"text-align":"center"}},{default:p(()=>[f(i,{type:"primary",onClick:H},{default:p(()=>t[9]||(t[9]=[h("搜索",-1)])),_:1,__:[9]}),f(i,{onClick:J},{default:p(()=>t[10]||(t[10]=[h("重置",-1)])),_:1,__:[10]})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),f(_,{class:"records-card"},{header:p(()=>[y("div",S,[t[14]||(t[14]=y("span",null,"平账记录列表",-1)),y("div",z,[f(i,{type:"success",size:"small",onClick:G},{default:p(()=>t[11]||(t[11]=[h("导出Excel",-1)])),_:1,__:[11]}),f(i,{type:"info",size:"small",onClick:Q},{default:p(()=>t[12]||(t[12]=[h("打印报表",-1)])),_:1,__:[12]}),f(i,{type:"warning",size:"small",onClick:W},{default:p(()=>t[13]||(t[13]=[h("统计图表",-1)])),_:1,__:[13]})])])]),default:p(()=>[f(F,{data:k.value,style:{width:"100%"},border:""},{default:p(()=>[f(w,{prop:"balanceDate",label:"平账时间",width:"160"},{default:p(({row:e})=>[h(b(B(e.balanceDate)),1)]),_:1}),f(w,{prop:"month",label:"平账月份",width:"120"}),f(w,{prop:"workerCount",label:"师傅人数",width:"100"}),f(w,{prop:"totalOrders",label:"完成订单数",width:"120"}),f(w,{prop:"totalAmount",label:"订单总金额",width:"140"},{default:p(({row:e})=>[h(" ¥"+b(q(e.totalAmount)),1)]),_:1}),f(w,{prop:"materialCost",label:"物料成本",width:"120"},{default:p(({row:e})=>[h(" ¥"+b(q(e.materialCost)),1)]),_:1}),f(w,{prop:"laborCost",label:"人工成本",width:"120"},{default:p(({row:e})=>[h(" ¥"+b(q(e.laborCost)),1)]),_:1}),f(w,{prop:"profit",label:"利润",width:"120"},{default:p(({row:e})=>[h(" ¥"+b(q(e.profit)),1)]),_:1}),f(w,{prop:"profitRate",label:"利润率",width:"100"},{default:p(({row:e})=>[h(b(e.profitRate)+"% ",1)]),_:1}),f(w,{prop:"status",label:"状态",width:"100"},{default:p(({row:e})=>[f(v,{type:"confirmed"===e.status?"success":"warning"},{default:p(()=>[h(b("confirmed"===e.status?"已确认":"待确认"),1)]),_:2},1032,["type"])]),_:1}),f(w,{label:"操作",width:"150",fixed:"right"},{default:p(({row:e})=>[f(i,{type:"primary",size:"small",onClick:t=>(e=>{U.value=e,T.value=!0})(e)},{default:p(()=>t[15]||(t[15]=[h("查看",-1)])),_:2,__:[15]},1032,["onClick"]),f(i,{type:"success",size:"small",onClick:t=>(e=>{j.success(`导出记录 ${e.id} 成功`)})(e)},{default:p(()=>t[16]||(t[16]=[h("导出",-1)])),_:2,__:[16]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),y("div",R,[f($,{"current-page":c.currentPage,"onUpdate:currentPage":t[3]||(t[3]=e=>c.currentPage=e),"page-size":c.pageSize,"onUpdate:pageSize":t[4]||(t[4]=e=>c.pageSize=e),"page-sizes":[10,20,50,100],total:c.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Y,onCurrentChange:Z},null,8,["current-page","page-size","total"])])]),_:1}),f(oe,{modelValue:T.value,"onUpdate:modelValue":t[7]||(t[7]=e=>T.value=e),title:"平账详情",width:"1100px","before-close":K},{footer:p(()=>[y("span",x,[f(i,{onClick:t[6]||(t[6]=e=>T.value=!1)},{default:p(()=>t[18]||(t[18]=[h("关闭",-1)])),_:1,__:[18]}),f(i,{type:"primary",onClick:X},{default:p(()=>t[19]||(t[19]=[h("导出详情",-1)])),_:1,__:[19]})])]),default:p(()=>[U.value?(u(),M("div",O,[f(ae,{column:2,border:""},{default:p(()=>[f(te,{label:"平账时间"},{default:p(()=>[h(b(B(U.value.balanceDate)),1)]),_:1}),f(te,{label:"平账月份"},{default:p(()=>[h(b(U.value.month),1)]),_:1}),f(te,{label:"操作员"},{default:p(()=>[h(b(U.value.operator),1)]),_:1}),f(te,{label:"状态"},{default:p(()=>[f(v,{type:"confirmed"===U.value.status?"success":"warning"},{default:p(()=>[h(b("confirmed"===U.value.status?"已确认":"待确认"),1)]),_:1},8,["type"])]),_:1})]),_:1}),y("div",V,[t[17]||(t[17]=y("h4",null,"工程费用汇总",-1)),f(re,{modelValue:E.value,"onUpdate:modelValue":t[5]||(t[5]=e=>E.value=e),type:"border-card",class:"project-tabs",onTabChange:ee},{default:p(()=>[f(le,{label:"户内安装工程费用汇总",name:"project-cost-summary"},{default:p(()=>[N(f(A,{data:L["project-cost-summary"].list,summaryMethod:L["project-cost-summary"].summaryMethod,formatNumber:q},null,8,["data","summaryMethod"]),[[se,I["project-cost-summary"]]])]),_:1}),f(le,{label:"户内表前甲供材清单表",name:"pre-meter-materials"},{default:p(()=>[N(f(A,{data:L["pre-meter-materials"].list,summaryMethod:L["pre-meter-materials"].summaryMethod,formatNumber:q},null,8,["data","summaryMethod"]),[[se,I["pre-meter-materials"]]])]),_:1}),f(le,{label:"户内甲供材料清单表",name:"indoor-materials"},{default:p(()=>[N(f(A,{data:L["indoor-materials"].list,summaryMethod:L["indoor-materials"].summaryMethod,formatNumber:q},null,8,["data","summaryMethod"]),[[se,I["indoor-materials"]]])]),_:1}),f(le,{label:"户内结算做销售处理超领材料费用表",name:"over-received-materials"},{default:p(()=>[N(f(A,{data:L["over-received-materials"].list,summaryMethod:L["over-received-materials"].summaryMethod,formatNumber:q},null,8,["data","summaryMethod"]),[[se,I["over-received-materials"]]])]),_:1}),f(le,{label:"户内管件甲供材料费用表",name:"fittings-materials"},{default:p(()=>[N(f(A,{data:L["fittings-materials"].list,summaryMethod:L["fittings-materials"].summaryMethod,formatNumber:q},null,8,["data","summaryMethod"]),[[se,I["fittings-materials"]]])]),_:1}),f(le,{label:"户内挂表安装工程管件统计",name:"meter-installation-fittings"},{default:p(()=>[N(f(A,{data:L["meter-installation-fittings"].list,summaryMethod:L["meter-installation-fittings"].summaryMethod,formatNumber:q},null,8,["data","summaryMethod"]),[[se,I["meter-installation-fittings"]]])]),_:1}),f(le,{label:"户内零星安装工程管件统计",name:"minor-installation-fittings"},{default:p(()=>[N(f(A,{data:L["minor-installation-fittings"].list,summaryMethod:L["minor-installation-fittings"].summaryMethod,formatNumber:q},null,8,["data","summaryMethod"]),[[se,I["minor-installation-fittings"]]])]),_:1}),f(le,{label:"户内二次安装工程管件统计",name:"secondary-installation-fittings"},{default:p(()=>[N(f(A,{data:L["secondary-installation-fittings"].list,summaryMethod:L["secondary-installation-fittings"].summaryMethod,formatNumber:q},null,8,["data","summaryMethod"]),[[se,I["secondary-installation-fittings"]]])]),_:1}),f(le,{label:"户内安装管件统计表",name:"indoor-installation-fittings"},{default:p(()=>[N(f(A,{data:L["indoor-installation-fittings"].list,summaryMethod:L["indoor-installation-fittings"].summaryMethod,formatNumber:q},null,8,["data","summaryMethod"]),[[se,I["indoor-installation-fittings"]]])]),_:1}),f(le,{label:"户内挂表安装工程决算（半月板）",name:"meter-settlement-half"},{default:p(()=>[N(f(A,{data:L["meter-settlement-half"].list,summaryMethod:L["meter-settlement-half"].summaryMethod,formatNumber:q},null,8,["data","summaryMethod"]),[[se,I["meter-settlement-half"]]])]),_:1}),f(le,{label:"户内挂表安装工程决算（未半月板）",name:"meter-settlement-no-half"},{default:p(()=>[N(f(A,{data:L["meter-settlement-no-half"].list,summaryMethod:L["meter-settlement-no-half"].summaryMethod,formatNumber:q},null,8,["data","summaryMethod"]),[[se,I["meter-settlement-no-half"]]])]),_:1}),f(le,{label:"管道燃气户内零星安装工程决算（半月板）",name:"gas-minor-settlement-half"},{default:p(()=>[N(f(A,{data:L["gas-minor-settlement-half"].list,summaryMethod:L["gas-minor-settlement-half"].summaryMethod,formatNumber:q},null,8,["data","summaryMethod"]),[[se,I["gas-minor-settlement-half"]]])]),_:1}),f(le,{label:"管道燃气户内零星安装工程决算（未半月板）",name:"gas-minor-settlement-no-half"},{default:p(()=>[N(f(A,{data:L["gas-minor-settlement-no-half"].list,summaryMethod:L["gas-minor-settlement-no-half"].summaryMethod,formatNumber:q},null,8,["data","summaryMethod"]),[[se,I["gas-minor-settlement-no-half"]]])]),_:1}),f(le,{label:"管道燃气户内零星安装工程决算（不可用燃气表）",name:"gas-minor-settlement-no-meter"},{default:p(()=>[N(f(A,{data:L["gas-minor-settlement-no-meter"].list,summaryMethod:L["gas-minor-settlement-no-meter"].summaryMethod,formatNumber:q},null,8,["data","summaryMethod"]),[[se,I["gas-minor-settlement-no-meter"]]])]),_:1}),f(le,{label:"甲供材料领用表",name:"supplied-materials-receipt"},{default:p(()=>[N(f(A,{data:L["supplied-materials-receipt"].list,summaryMethod:L["supplied-materials-receipt"].summaryMethod,formatNumber:q},null,8,["data","summaryMethod"]),[[se,I["supplied-materials-receipt"]]])]),_:1})]),_:1},8,["modelValue"])])])):D("",!0)]),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-a34896d7"]]);export{T as default};
