<template>
  <div class="page-container">
    <div class="form-container">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="申请单号" prop="applyNo">
              <el-input v-model="form.applyNo" placeholder="系统自动生成" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申请日期" prop="applyDate">
              <el-date-picker
                v-model="form.applyDate"
                type="date"
                placeholder="选择申请日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="申请人" prop="applicant">
              <el-select
                v-model="form.applicant"
                placeholder="请选择申请人"
                filterable
                style="width: 100%"
              >
                <el-option
                  v-for="item in applicantOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="领料人" prop="receiver">
              <el-select
                v-model="form.receiver"
                placeholder="请选择领料人"
                filterable
                style="width: 100%"
              >
                <el-option
                  v-for="item in receiverOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="关联订单" prop="orderNo">
              <el-select
                v-model="form.orderNo"
                placeholder="请选择关联订单"
                filterable
                style="width: 100%"
              >
                <el-option
                  v-for="item in orderOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      
      <!-- 物料清单 -->
      <div class="material-list">
        <div class="list-header">
          <h3>物料清单</h3>
          <el-button type="primary" @click="handleAddMaterial">添加物料</el-button>
        </div>
        
        <el-table :data="materialList" border stripe>
          <el-table-column type="index" label="序号" width="60" />
          <el-table-column prop="code" label="物料编码" width="150" />
          <el-table-column prop="name" label="物料名称" width="200" />
          <el-table-column prop="model" label="型号" width="120" />
          <el-table-column prop="specification" label="规格" width="150" />
          <el-table-column prop="unit" label="单位" width="80" />
          <el-table-column prop="price" label="单价" width="100">
            <template #default="{ row }">
              ¥{{ row.price }}
            </template>
          </el-table-column>
          <el-table-column prop="quantity" label="申请数量" width="120">
            <template #default="{ row }">
              <el-input-number
                v-model="row.quantity"
                :min="1"
                :max="row.stockQuantity"
                size="small"
                style="width: 80px"
              />
            </template>
          </el-table-column>
          <el-table-column prop="totalPrice" label="小计" width="100">
            <template #default="{ row }">
              ¥{{ (row.price * row.quantity).toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template #default="{ $index }">
              <el-button type="danger" size="small" @click="handleRemoveMaterial($index)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="list-footer">
          <div class="total-info">
            <span>物料总数：{{ materialList.length }} 项</span>
            <span class="total-amount">总金额：¥{{ totalAmount.toFixed(2) }}</span>
          </div>
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div class="form-actions">
        <el-button @click="handleReset">重置</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
        <el-button type="success" @click="handleSaveAndPrint">保存并打印</el-button>
      </div>
    </div>
    
    <!-- 物料选择对话框 -->
    <el-dialog
      v-model="materialDialogVisible"
      title="选择物料"
      width="800px"
    >
      <div class="material-search">
        <el-input
          v-model="materialSearchKeyword"
          placeholder="请输入物料编码、名称、型号"
          clearable
          style="width: 300px"
        >
          <template #append>
            <el-button @click="handleSearchMaterial">搜索</el-button>
          </template>
        </el-input>
      </div>
      
      <el-table
        :data="materialOptions"
        border
        stripe
        @row-click="handleSelectMaterial"
        style="margin-top: 20px"
      >
        <el-table-column prop="code" label="物料编码" width="150" />
        <el-table-column prop="name" label="物料名称" width="200" />
        <el-table-column prop="model" label="型号" width="120" />
        <el-table-column prop="specification" label="规格" width="150" />
        <el-table-column prop="unit" label="单位" width="80" />
        <el-table-column prop="price" label="单价" width="100">
          <template #default="{ row }">
            ¥{{ row.price }}
          </template>
        </el-table-column>
        <el-table-column prop="stockQuantity" label="库存数量" width="100" />
      </el-table>
      
      <template #footer>
        <el-button @click="materialDialogVisible = false">取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// 表单数据
const form = reactive({
  applyNo: '',
  applyDate: new Date(),
  applicant: '',
  orderNo: '',
  receiver: '',
  remark: ''
})

// 申请人选项
const applicantOptions = ref([
  { value: '张三', label: '张三 - 工程部' },
  { value: '李四', label: '李四 - 技术部' },
  { value: '王五', label: '王五 - 施工部' },
  { value: '赵六', label: '赵六 - 工程部' },
  { value: '钱七', label: '钱七 - 技术部' }
])

// 领料人选项
const receiverOptions = ref([
  { value: '张三', label: '张三 - 工程部' },
  { value: '李四', label: '李四 - 技术部' },
  { value: '王五', label: '王五 - 施工部' },
  { value: '赵六', label: '赵六 - 工程部' },
  { value: '钱七', label: '钱七 - 技术部' }
])

// 订单选项
const orderOptions = ref([
  { value: 'DD001', label: 'DD001 - 某小区燃气安装工程' },
  { value: 'DD002', label: 'DD002 - 某商场燃气管道改造' },
  { value: 'DD003', label: 'DD003 - 某工厂燃气设备安装' },
  { value: 'DD004', label: 'DD004 - 某住宅楼燃气入户工程' },
  { value: 'DD005', label: 'DD005 - 某商业街燃气管道铺设' }
])

// 表单验证规则
const rules = {
  applyDate: [{ required: true, message: '请选择申请日期', trigger: 'change' }],
  applicant: [{ required: true, message: '请选择申请人', trigger: 'change' }],
  receiver: [{ required: true, message: '请选择领料人', trigger: 'change' }]
}

// 物料清单
const materialList = ref([])

// 物料选择对话框
const materialDialogVisible = ref(false)
const materialSearchKeyword = ref('')
const materialOptions = ref([])

// 计算总金额
const totalAmount = computed(() => {
  return materialList.value.reduce((total, item) => {
    return total + (item.price * item.quantity)
  }, 0)
})

// 生成申请单号
const generateApplyNo = () => {
  const date = new Date()
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  form.applyNo = `LY${year}${month}${day}${random}`
}

// 添加物料
const handleAddMaterial = () => {
  materialDialogVisible.value = true
  loadMaterialOptions()
}

// 删除物料
const handleRemoveMaterial = (index: number) => {
  materialList.value.splice(index, 1)
}

// 搜索物料
const handleSearchMaterial = () => {
  loadMaterialOptions()
}

// 选择物料
const handleSelectMaterial = (row: any) => {
  // 检查是否已存在
  const exists = materialList.value.find(item => item.id === row.id)
  if (exists) {
    ElMessage.warning('该物料已添加')
    return
  }
  
  // 添加到清单
  materialList.value.push({
    ...row,
    quantity: 1
  })
  
  materialDialogVisible.value = false
}

// 加载物料选项
const loadMaterialOptions = async () => {
  // 模拟API调用
  await new Promise(resolve => setTimeout(resolve, 500))
  
  materialOptions.value = [
    {
      id: 1,
      code: 'WL001',
      name: '电缆线',
      model: 'YJV-3*4',
      specification: '3*4mm²',
      unit: '米',
      price: 30.00,
      stockQuantity: 500
    },
    {
      id: 2,
      code: 'WL002',
      name: '电线',
      model: 'BV-2.5',
      specification: '2.5mm²',
      unit: '米',
      price: 15.00,
      stockQuantity: 800
    }
  ]
}

// 保存
const handleSave = async () => {
  if (materialList.value.length === 0) {
    ElMessage.warning('请至少添加一个物料')
    return
  }
  
  ElMessage.success('保存成功')
}

// 保存并打印
const handleSaveAndPrint = async () => {
  await handleSave()
  ElMessage.success('打印功能待实现')
}

// 重置
const handleReset = () => {
  Object.assign(form, {
    applyNo: '',
    applyDate: new Date(),
    applicant: '',
    orderNo: '',
    receiver: '',
    remark: ''
  })
  materialList.value = []
  generateApplyNo()
}

onMounted(() => {
  generateApplyNo()
})
</script>

<style lang="scss" scoped>
.material-list {
  margin-top: 20px;
  
  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h3 {
      margin: 0;
    }
  }
  
  .list-footer {
    margin-top: 20px;
    
    .total-info {
      display: flex;
      justify-content: space-between;
      font-size: 14px;
      
      .total-amount {
        font-weight: bold;
        color: #f56c6c;
      }
    }
  }
}

.form-actions {
  margin-top: 20px;
  text-align: center;
  
  .el-button {
    margin: 0 10px;
  }
}

.material-search {
  margin-bottom: 20px;
}
</style> 