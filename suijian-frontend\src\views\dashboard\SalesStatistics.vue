<template>
  <div class="sales-statistics">

    <!-- 本月售卖统计 -->
    <el-card class="sales-summary-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>💰 本月售卖统计</span>
          <div class="header-actions">
            <el-button type="primary" size="small" @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>
      
      <div class="sales-summary">
        <div class="summary-row">
          <div class="summary-item">
            <span class="icon">📦</span>
            <span class="label">本月售卖:</span>
            <span class="value">{{ monthlySalesCount }}项</span>
            <span class="amount">💵 总价: ¥{{ formatNumber(monthlySalesAmount) }}</span>
          </div>
        </div>
        <div class="summary-row">
          <div class="summary-item">
            <span class="icon">📈</span>
            <span class="label">较上月:</span>
            <span class="value" :class="{ positive: monthlyGrowth > 0, negative: monthlyGrowth < 0 }">
              {{ monthlyGrowth > 0 ? '+' : '' }}{{ monthlyGrowth }}%
            </span>
            <span class="target">🎯 月度目标: ¥{{ formatNumber(monthlyTarget) }}</span>
          </div>
        </div>
        <div class="summary-row">
          <div class="summary-item">
            <span class="icon">📊</span>
            <span class="label">完成度:</span>
            <span class="value completion">{{ completionRate }}%</span>
            <span class="remaining">📅 剩余天数: {{ remainingDays }}天</span>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 销售排行榜 -->
    <el-card class="ranking-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>🏆 销售排行榜</span>
        </div>
      </template>

      <el-table :data="salesRanking" style="width: 100%" border>
        <el-table-column prop="rank" label="排名" width="80" align="center">
          <template #default="{ row }">
            <span class="rank-icon">{{ getRankIcon(row.rank) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="salesperson" label="销售人" width="120" />
        <el-table-column prop="salesCount" label="销售数量" width="120" align="center">
          <template #default="{ row }">
            {{ row.salesCount }}件
          </template>
        </el-table-column>
        <el-table-column prop="salesAmount" label="销售总价" width="150" align="center">
          <template #default="{ row }">
            ¥{{ formatNumber(row.salesAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="commission" label="提成" width="120" align="center">
          <template #default="{ row }">
            ¥{{ formatNumber(row.commission) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" align="center">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewSalespersonDetails(row)">
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="table-footer">
        <span>📋 查看完整排行榜...</span>
      </div>
    </el-card>

    <!-- 畅销排行榜 -->
    <el-card class="product-ranking-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>🔥 畅销排行榜</span>
        </div>
      </template>

      <el-table :data="productRanking" style="width: 100%" border>
        <el-table-column prop="icon" label="" width="50" align="center">
          <template #default="{ row }">
            <span class="product-icon">{{ row.icon }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="productName" label="商品名称" width="150" />
        <el-table-column prop="salesCount" label="销售数量" width="120" align="center">
          <template #default="{ row }">
            {{ row.salesCount }}个
          </template>
        </el-table-column>
        <el-table-column prop="salesAmount" label="销售总价" width="150" align="center">
          <template #default="{ row }">
            ¥{{ formatNumber(row.salesAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="stock" label="库存" width="100" align="center">
          <template #default="{ row }">
            {{ row.stock }}个
          </template>
        </el-table-column>
      </el-table>

      <div class="table-footer">
        <span>📋 查看完整排行榜...</span>
      </div>
    </el-card>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <el-button type="primary" size="large" @click="salesReport">
        📊 销售报表
      </el-button>
      <el-button type="success" size="large" @click="employeePerformance">
        👥 员工绩效
      </el-button>
      <el-button type="default" size="large" @click="refreshData">
        🔄 刷新
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'

// 数据定义
interface SalesRanking {
  rank: number
  salesperson: string
  salesCount: number
  salesAmount: number
  commission: number
}

interface ProductRanking {
  id: number
  productName: string
  icon: string
  salesCount: number
  salesAmount: number
  stock: number
}

// 响应式数据
const monthlySalesCount = ref(15)
const monthlySalesAmount = ref(35680)
const monthlyTarget = ref(50000)
const monthlyGrowth = ref(12.5)

const salesRanking = ref<SalesRanking[]>([
  { rank: 1, salesperson: '张三', salesCount: 25, salesAmount: 8500, commission: 425 },
  { rank: 2, salesperson: '李四', salesCount: 18, salesAmount: 6200, commission: 310 },
  { rank: 3, salesperson: '王五', salesCount: 15, salesAmount: 5100, commission: 255 },
  { rank: 4, salesperson: '赵六', salesCount: 12, salesAmount: 4200, commission: 210 }
])

const productRanking = ref<ProductRanking[]>([
  { id: 1, productName: '智能开关', icon: '🔌', salesCount: 45, salesAmount: 13500, stock: 28 },
  { id: 2, productName: 'LED灯具', icon: '💡', salesCount: 38, salesAmount: 11400, stock: 15 },
  { id: 3, productName: '插座面板', icon: '🔌', salesCount: 32, salesAmount: 9600, stock: 42 },
  { id: 4, productName: '燃气配件', icon: '🔧', salesCount: 28, salesAmount: 8400, stock: 8 }
])

// 计算属性
const completionRate = computed(() => {
  return Math.round((monthlySalesAmount.value / monthlyTarget.value) * 100 * 10) / 10
})

const remainingDays = computed(() => {
  const today = new Date()
  const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0)
  return lastDay.getDate() - today.getDate()
})

// 方法
const formatNumber = (num: number): string => {
  return num.toLocaleString()
}

const getRankIcon = (rank: number): string => {
  const icons = ['', '🥇', '🥈', '🥉', '4️⃣', '5️⃣']
  return icons[rank] || `${rank}️⃣`
}

const refreshData = () => {
  ElMessage.success('数据已刷新')
  // 这里可以调用API刷新数据
}

const viewSalespersonDetails = (salesperson: SalesRanking) => {
  ElMessage.info(`查看 ${salesperson.salesperson} 的详细业绩`)
  // 这里可以打开详情弹窗或跳转到详情页面
}

const salesReport = () => {
  ElMessage.info('生成销售统计报表')
  // 这里可以跳转到销售报表页面
}

const employeePerformance = () => {
  ElMessage.info('查看员工绩效详情')
  // 这里可以跳转到员工绩效页面
}

// 生命周期
onMounted(() => {
  // 页面加载时获取数据
})
</script>

<style scoped>
.sales-statistics {
  padding: 20px;
}

.breadcrumb {
  margin-bottom: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: bold;
  margin: 0 0 8px 0;
  color: #303133;
}

.page-description {
  color: #606266;
  margin: 0;
}

.sales-summary-card, .ranking-card, .product-ranking-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.sales-summary {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.summary-row {
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 8px;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 15px;
}

.summary-item .icon {
  font-size: 16px;
}

.summary-item .label {
  color: #606266;
  min-width: 80px;
}

.summary-item .value {
  font-weight: bold;
  color: #303133;
}

.summary-item .value.positive {
  color: #67c23a;
}

.summary-item .value.negative {
  color: #f56c6c;
}

.summary-item .value.completion {
  color: #409eff;
}

.summary-item .amount,
.summary-item .target,
.summary-item .remaining {
  color: #909399;
  margin-left: 10px;
}

.rank-icon {
  font-size: 18px;
}

.product-icon {
  font-size: 18px;
}

.table-footer {
  text-align: center;
  padding: 15px;
  color: #909399;
  background-color: #fafafa;
  border-top: 1px solid #ebeef5;
  cursor: pointer;
}

.table-footer:hover {
  background-color: #f0f0f0;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 30px;
}
</style>
