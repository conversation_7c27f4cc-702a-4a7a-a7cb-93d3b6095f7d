<template>
  <div class="order-assign-container">
    <el-card class="main-card">
      <template #header>
        <div class="card-header">
          <span>甲方派单</span>
        </div>
      </template>
      
      <!-- 派单信息 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">派单信息</div>
        </el-col>
        <el-col :span="12">
          <el-form-item label="派单日期:">
            <el-date-picker
              v-model="formData.dispatchDate"
              type="date"
              placeholder="请选择派单日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 订单信息 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">订单信息</div>
        </el-col>
        <el-col :span="12">
          <el-form-item label="甲方订单号:">
            <el-input v-model="formData.partyOrderNo" placeholder="请输入甲方订单号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="户名:">
            <el-input v-model="formData.customerName" placeholder="请输入户名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="用户编号:">
            <el-input v-model="formData.userNumber" placeholder="请输入用户编号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="小区名称:">
            <el-input v-model="formData.communityName" placeholder="请输入小区名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="楼橦:">
            <el-input v-model="formData.building" placeholder="请输入楼橦" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="房号:">
            <el-input v-model="formData.roomNumber" placeholder="请输入房号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="电话:">
            <el-input v-model="formData.phone" placeholder="请输入电话" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系人:">
            <el-input v-model="formData.contactPerson" placeholder="请输入联系人" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="地址:">
            <el-input v-model="formData.address" placeholder="请输入地址" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="订单分类:">
            <el-select v-model="formData.orderCategory" placeholder="请选择订单分类" style="width: 100%">
              <el-option label="一次挂表" value="firstMeter" />
              <el-option label="二次挂表" value="secondMeter" />
              <el-option label="一次安装" value="firstInstall" />
              <el-option label="二次安装" value="secondInstall" />
              <el-option label="售后" value="afterSales" />
              <el-option label="单项工程" value="singleProject" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="formData.orderCategory === 'singleProject'">
          <el-form-item label="单项工程名称:">
            <el-input v-model="formData.projectName" placeholder="请输入单项工程名称" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注:">
            <el-input
              v-model="formData.remarks"
              type="textarea"
              :rows="3"
              placeholder="请输入备注"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 订单分类说明 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <el-card class="info-card">
            <div class="info-title">订单分类说明:</div>
            <div class="info-content">
              <p>1. 一次挂表: 首次水表/电表安装</p>
              <p>2. 二次挂表: 补充或更换水表/电表</p>
              <p>3. 一次安装: 首次水电安装</p>
              <p>4. 二次安装: 补充或改造水电设施</p>
              <p>5. 售后: 已完成订单的维修服务</p>
              <p>6. 单项工程: 特定项目的独立工程</p>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 操作按钮 -->
      <div class="form-actions">
        <el-button type="primary" @click="handleSave">保存</el-button>
        <el-button type="success" @click="handleSubmit">提交</el-button>
        <el-button @click="handlePrint">打印</el-button>
        <el-button @click="handleCancel">取消</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

// 表单数据
const formData = reactive({
  dispatchDate: '', // 派单日期
  partyOrderNo: '', // 甲方订单号
  customerName: '', // 户名
  userNumber: '', // 用户编号
  communityName: '', // 小区名称
  building: '', // 楼橦
  roomNumber: '', // 房号
  phone: '', // 电话
  contactPerson: '', // 联系人
  address: '', // 地址
  orderCategory: '', // 订单分类
  projectName: '', // 单项工程名称
  remarks: '' // 备注
})

// 保存
const handleSave = () => {
  ElMessage.success('保存成功')
  console.log('保存数据:', formData)
}

// 提交
const handleSubmit = () => {
  ElMessage.success('提交成功')
  console.log('提交数据:', formData)
}

// 打印
const handlePrint = () => {
  ElMessage.success('开始打印')
  console.log('打印数据:', formData)
}

// 取消
const handleCancel = () => {
  ElMessage.info('已取消')
  // 重置表单
  Object.keys(formData).forEach(key => {
    (formData as any)[key] = ''
  })
}
</script>

<style lang="scss" scoped>
.order-assign-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
  
  .main-card {
    margin-bottom: 20px;
    
    .card-header {
      font-size: 18px;
      font-weight: bold;
      color: #303133;
    }
  }
  
  .form-section {
    margin-bottom: 20px;
    
    .section-title {
      font-size: 16px;
      font-weight: bold;
      color: #606266;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ebeef5;
    }
    
    .el-form-item {
      margin-bottom: 18px;
    }
  }
  
  .info-card {
    background-color: #f0f9ff;
    border-color: #b3e0ff;
    
    .info-title {
      font-weight: bold;
      color: #409eff;
      margin-bottom: 10px;
    }
    
    .info-content {
      p {
        margin: 5px 0;
        color: #606266;
      }
    }
  }
  
  .form-actions {
    text-align: center;
    padding: 20px 0;
    
    .el-button {
      margin: 0 10px;
      padding: 12px 30px;
    }
  }
}
</style>