{"name": "<PERSON><PERSON>an-frontend", "version": "1.0.0", "description": "工程管理系统前端", "type": "module", "scripts": {"dev": "vite --mode development", "dev:test": "vite --mode test", "build": "vue-tsc --noEmit && vite build --mode production", "build:prod": "vue-tsc --noEmit && vite build --mode production", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"axios": "^1.4.0", "dayjs": "^1.11.0", "element-plus": "^2.3.0", "lodash-es": "^4.17.21", "mockjs": "^1.1.0", "pinia": "^2.1.0", "vue": "^3.3.0", "vue-router": "^4.2.0"}, "devDependencies": {"@types/lodash-es": "^4.17.9", "@types/mockjs": "^1.0.7", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-vue": "^4.2.0", "@vue/tsconfig": "^0.4.0", "eslint": "^8.45.0", "eslint-plugin-vue": "^9.15.0", "prettier": "^3.0.0", "sass": "^1.64.0", "terser": "^5.43.1", "typescript": "^5.9.2", "vite": "^4.4.0", "vue-tsc": "^3.0.4"}}