<template>
  <div class="permission-management">
    <!-- 面包屑导航 -->
    <el-breadcrumb class="breadcrumb" separator=">">
      <el-breadcrumb-item :to="{ path: '/dashboard' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item :to="{ path: '/system/user-management' }">系统设置</el-breadcrumb-item>
      <el-breadcrumb-item>权限管理</el-breadcrumb-item>
    </el-breadcrumb>

    <el-card class="main-card">
      <template #header>
        <div class="card-header">
          <span>🔐 权限管理</span>
          <div class="header-actions">
            <el-button type="primary" size="small" @click="showAddDialog">
              <el-icon><Plus /></el-icon>
              新增角色
            </el-button>
            <el-button type="success" size="small" @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 角色列表 -->
      <el-table :data="roleList" border style="width: 100%">
        <el-table-column prop="roleId" label="角色ID" width="100" />
        <el-table-column prop="roleName" label="角色名称" width="150" />
        <el-table-column prop="roleDescription" label="角色描述" min-width="200" />
        <el-table-column prop="userCount" label="用户数量" width="100" align="center" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
              {{ row.status === 'active' ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="editRole(row)">
              编辑
            </el-button>
            <el-button type="info" size="small" @click="setPermissions(row)">
              权限
            </el-button>
            <el-button 
              :type="row.status === 'active' ? 'warning' : 'success'" 
              size="small" 
              @click="toggleStatus(row)"
            >
              {{ row.status === 'active' ? '禁用' : '启用' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 角色编辑弹窗 -->
    <el-dialog v-model="roleDialogVisible" :title="roleDialogTitle" width="500px">
      <el-form :model="roleForm" :rules="roleRules" ref="roleFormRef" label-width="100px">
        <el-form-item label="角色名称:" prop="roleName">
          <el-input v-model="roleForm.roleName" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item label="角色描述:" prop="roleDescription">
          <el-input
            v-model="roleForm.roleDescription"
            type="textarea"
            :rows="3"
            placeholder="请输入角色描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="roleDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleRoleSubmit">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 权限设置弹窗 -->
    <el-dialog v-model="permissionDialogVisible" title="权限设置" width="600px">
      <div class="permission-tree">
        <el-tree
          ref="permissionTreeRef"
          :data="permissionTree"
          :props="treeProps"
          show-checkbox
          node-key="id"
          :default-checked-keys="checkedPermissions"
          :default-expand-all="true"
        />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="permissionDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handlePermissionSubmit">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Plus, Refresh } from '@element-plus/icons-vue'

// 表单引用
const roleFormRef = ref<FormInstance>()
const permissionTreeRef = ref()

// 弹窗控制
const roleDialogVisible = ref(false)
const permissionDialogVisible = ref(false)
const roleDialogTitle = ref('')
const isEdit = ref(false)
const currentRole = ref<any>(null)

// 角色表单
const roleForm = reactive({
  roleName: '',
  roleDescription: ''
})

// 表单验证规则
const roleRules: FormRules = {
  roleName: [
    { required: true, message: '请输入角色名称', trigger: 'blur' }
  ],
  roleDescription: [
    { required: true, message: '请输入角色描述', trigger: 'blur' }
  ]
}

// 角色列表
const roleList = ref([
  {
    roleId: 'R001',
    roleName: '系统管理员',
    roleDescription: '拥有系统所有权限',
    userCount: 2,
    status: 'active',
    createTime: '2024-01-01 10:00:00'
  },
  {
    roleId: 'R002',
    roleName: '仓库管理员',
    roleDescription: '负责仓库物料管理',
    userCount: 5,
    status: 'active',
    createTime: '2024-01-02 10:00:00'
  },
  {
    roleId: 'R003',
    roleName: '工程负责人',
    roleDescription: '负责工程项目管理',
    userCount: 8,
    status: 'active',
    createTime: '2024-01-03 10:00:00'
  },
  {
    roleId: 'R004',
    roleName: '普通员工',
    roleDescription: '基础操作权限',
    userCount: 15,
    status: 'active',
    createTime: '2024-01-04 10:00:00'
  }
])

// 权限树配置
const treeProps = {
  children: 'children',
  label: 'label'
}

// 权限树数据
const permissionTree = ref([
  {
    id: 'dashboard',
    label: '首页',
    children: [
      { id: 'dashboard.view', label: '查看首页' },
      { id: 'dashboard.statistics', label: '查看统计' }
    ]
  },
  {
    id: 'warehouse',
    label: '仓库管理',
    children: [
      { id: 'warehouse.view', label: '查看物料' },
      { id: 'warehouse.add', label: '添加物料' },
      { id: 'warehouse.edit', label: '编辑物料' },
      { id: 'warehouse.delete', label: '删除物料' },
      { id: 'warehouse.inbound', label: '物料入库' },
      { id: 'warehouse.outbound', label: '物料出库' }
    ]
  },
  {
    id: 'projects',
    label: '工程订单',
    children: [
      { id: 'projects.view', label: '查看工程' },
      { id: 'projects.add', label: '新建工程' },
      { id: 'projects.edit', label: '编辑工程' },
      { id: 'projects.delete', label: '删除工程' },
      { id: 'projects.assign', label: '甲方派单' },
      { id: 'projects.progress', label: '工程推进' }
    ]
  },
  {
    id: 'loose',
    label: '散户订单',
    children: [
      { id: 'loose.view', label: '查看订单' },
      { id: 'loose.add', label: '新建订单' },
      { id: 'loose.edit', label: '编辑订单' },
      { id: 'loose.balance', label: '月度平账' }
    ]
  },
  {
    id: 'employees',
    label: '员工管理',
    children: [
      { id: 'employees.view', label: '查看员工' },
      { id: 'employees.add', label: '添加员工' },
      { id: 'employees.edit', label: '编辑员工' },
      { id: 'employees.delete', label: '删除员工' }
    ]
  },
  {
    id: 'system',
    label: '系统设置',
    children: [
      { id: 'system.user', label: '用户管理' },
      { id: 'system.permission', label: '权限管理' },
      { id: 'system.log', label: '系统日志' },
      { id: 'system.backup', label: '数据备份' }
    ]
  }
])

// 已选中的权限
const checkedPermissions = ref<string[]>([])

// 刷新数据
const refreshData = () => {
  ElMessage.success('数据已刷新')
}

// 显示新增弹窗
const showAddDialog = () => {
  roleDialogTitle.value = '新增角色'
  isEdit.value = false
  Object.keys(roleForm).forEach(key => {
    (roleForm as any)[key] = ''
  })
  roleDialogVisible.value = true
}

// 编辑角色
const editRole = (row: any) => {
  roleDialogTitle.value = '编辑角色'
  isEdit.value = true
  currentRole.value = row
  roleForm.roleName = row.roleName
  roleForm.roleDescription = row.roleDescription
  roleDialogVisible.value = true
}

// 设置权限
const setPermissions = (row: any) => {
  currentRole.value = row
  // 模拟获取角色已有权限
  checkedPermissions.value = [
    'dashboard.view',
    'warehouse.view',
    'warehouse.add',
    'projects.view'
  ]
  permissionDialogVisible.value = true
}

// 切换状态
const toggleStatus = async (row: any) => {
  try {
    const action = row.status === 'active' ? '禁用' : '启用'
    await ElMessageBox.confirm(`确定要${action}角色"${row.roleName}"吗？`, '确认操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    row.status = row.status === 'active' ? 'inactive' : 'active'
    ElMessage.success(`角色${action}成功`)
  } catch {
    // 用户取消
  }
}

// 提交角色
const handleRoleSubmit = async () => {
  if (!roleFormRef.value) return
  
  try {
    await roleFormRef.value.validate()
    ElMessage.success(isEdit.value ? '角色更新成功' : '角色创建成功')
    roleDialogVisible.value = false
    console.log('角色数据:', roleForm)
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单填写是否正确')
  }
}

// 提交权限
const handlePermissionSubmit = () => {
  const checkedKeys = permissionTreeRef.value.getCheckedKeys()
  const halfCheckedKeys = permissionTreeRef.value.getHalfCheckedKeys()
  const allCheckedKeys = [...checkedKeys, ...halfCheckedKeys]
  
  console.log('选中的权限:', allCheckedKeys)
  ElMessage.success('权限设置成功')
  permissionDialogVisible.value = false
}

// 初始化
onMounted(() => {
  // 初始化数据
})
</script>

<style scoped lang="scss">
.permission-management {
  padding: 20px;
  
  .breadcrumb {
    margin-bottom: 20px;
  }
  
  .main-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 18px;
      font-weight: bold;
      color: #303133;
      
      .header-actions {
        display: flex;
        gap: 10px;
      }
    }
  }
  
  .permission-tree {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    padding: 10px;
  }
  
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
}
</style>
