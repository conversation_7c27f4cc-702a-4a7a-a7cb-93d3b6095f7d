系统设置 - 基础数据导入 - UI线框图示意图
==========================================

+----------------------------------------------------------+
|                    工程管理系统                            |
+----------------------------------------------------------+
|  首页  |  仓库管理  |  散户订单  |  工程订单  |  员工管理  |  系统设置  |
+----------------------------------------------------------+

+----------------------------------------------------------+
|                        基础数据导入                        |
+----------------------------------------------------------+

导入设置:
┌────────────────────────────────────────────────────────────┐
│ 导入类型: [物料基础库 ▼]                                  │
│ ├─ 物料基础库                                             │
│ ├─ 员工信息                                               │
│ ├─ 工种设置                                               │
│ ├─ 供应商信息                                             │
│ ├─ 客户信息                                               │
│ └─ 系统参数                                               │
│                                                          │
│ 数据来源: [本地文件] [网络地址]                            │
│ 文件格式: [Excel文件 ▼] [CSV文件] [JSON文件]              │
└────────────────────────────────────────────────────────────┘

文件选择:
┌────────────────────────────────────────────────────────────┐
│ 选择文件: [选择文件按钮]                                  │
│ 文件名: [未选择文件]                                      │
│ 文件大小: [0 KB]                                          │
│ 文件格式: [未知]                                          │
│                                                          │
│ 模板下载: [下载导入模板]                                  │
└────────────────────────────────────────────────────────────┘

导入模板说明:
┌────────────────────────────────────────────────────────────┐
│ 物料基础库导入模板说明:                                  │
│ 1. 必填字段: 公司物料编码、物料名称、物料分类、单位      │
│ 2. 可选字段: 型号、规格、甲方编码                        │
│ 3. 物料分类: 甲料、商品、辅料                            │
│ 4. 一个公司物料编码可对应多个甲方编码(用逗号分隔)        │
│ 5. 支持格式: .xls、.xlsx                                 │
│                                                          │
│ 字段示例:                                                │
│ 公司物料编码  甲方编码    物料分类  物料名称  型号  规格  单位│
│ WL001        JD001,JD002  甲料      电缆线    YJV   3*4mm² 米 │
└────────────────────────────────────────────────────────────┘

数据预览:
┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┐
│ 行号 │ 公司物料编码 │ 甲方编码    │ 物料分类 │ 物料名称 │ 型号     │ 规格     │ 单位 │ 状态   │ 错误信息        │
├─────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ 1    │ WL001       │ JD001,JD002 │ 甲料     │ 电缆线   │ YJV-3*4  │ 3*4mm²   │ 米   │ 有效   │ 无              │
│ 2    │ WL002       │ JD003       │ 商品     │ 开关面板 │ KP-86    │ 86型     │ 个   │ 有效   │ 无              │
│ 3    │ WL003       │             │ 辅料     │ 电线     │ BV-2.5   │ 2.5mm²   │ 米   │ 有效   │ 无              │
│ 4    │             │ JD004       │ 甲料     │ 灯具     │ LED-12W  │ 12W      │ 个   │ 无效   │ 物料编码不能为空 │
│ 5    │ WL005       │ JD005       │ 其他     │ 插座     │ ZP-86    │ 86型     │ 个   │ 无效   │ 物料分类错误    │
│ ...  │ ...         │ ...         │ ...      │ ...      │ ...      │ ...      │ ...  │ ...    │ ...             │
└─────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┘

导入统计:
┌────────────────────────────────────────────────────────────┐
│ 总记录数: 100条                                           │
│ 有效记录: 95条                                            │
│ 无效记录: 5条                                             │
│ 重复记录: 2条 (将被跳过)                                  │
└────────────────────────────────────────────────────────────┘

导入选项:
┌────────────────────────────────────────────────────────────┐
│ 导入模式: [增量导入 ▼] [全量导入]                          │
│ ├─ 增量导入: 只导入新数据，跳过重复数据                   │
│ └─ 全量导入: 清空原数据，导入新数据(谨慎操作)             │
│                                                          │
│ 重复处理: [跳过重复项] [覆盖重复项]                        │
│ 冲突处理: [停止导入] [继续导入]                            │
└────────────────────────────────────────────────────────────┘

操作按钮:
┌────────────────────────────────────────────────────────────┐
│ [预览数据] [开始导入] [保存导入结果] [取消]               │
└────────────────────────────────────────────────────────────┘

导入进度:
┌────────────────────────────────────────────────────────────┐
│ 导入进度: [██████████████████░░░░░░░░░░░░░░░░░░░░] 50%   │
│ 已导入: 45条/95条                                         │
│ 预计剩余时间: 00:02:30                                    │
│ 当前状态: 正在导入...                                     │
└────────────────────────────────────────────────────────────┘

导入结果:
┌────────────────────────────────────────────────────────────┐
│ 导入完成时间: 2024-01-15 15:30:25                        │
│ 成功导入: 95条                                            │
│ 导入失败: 5条                                             │
│ 重复跳过: 2条                                             │
│                                                          │
│ 失败记录详情:                                            │
│ 1. 行4: 物料编码不能为空                                 │
│ 2. 行5: 物料分类错误                                     │
│ 3. 行15: 单位字段超长                                   │
│ 4. 行28: 物料名称包含非法字符                            │
│ 5. 行67: 规格字段格式错误                                │
└────────────────────────────────────────────────────────────┘

操作按钮:
┌────────────────────────────────────────────────────────────┐
│ [下载失败记录] [重新导入失败记录] [完成]                  │
└────────────────────────────────────────────────────────────┘