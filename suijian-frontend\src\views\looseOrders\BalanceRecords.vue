<template>
  <div class="balance-records">
    <el-card class="search-card">
      <template #header>
        <div class="card-header">
          <span>平账记录</span>
        </div>
      </template>
      
      <el-form :model="searchForm" label-width="120px" class="search-form">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="平账时间">
              <el-date-picker
                v-model="searchForm.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="平账月份">
              <el-select v-model="searchForm.month" placeholder="请选择平账月份" style="width: 100%">
                <el-option label="全部" value="" />
                <el-option label="2024年1月" value="2024-01" />
                <el-option label="2024年2月" value="2024-02" />
                <el-option label="2024年3月" value="2024-03" />
                <el-option label="2024年4月" value="2024-04" />
                <el-option label="2024年5月" value="2024-05" />
                <el-option label="2024年6月" value="2024-06" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="状态">
              <el-select v-model="searchForm.status" placeholder="请选择状态" style="width: 100%">
                <el-option label="全部" value="" />
                <el-option label="已确认" value="confirmed" />
                <el-option label="待确认" value="pending" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" style="text-align: center;">
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <el-card class="records-card">
      <template #header>
        <div class="card-header">
          <span>平账记录列表</span>
          <div class="header-actions">
            <el-button type="success" size="small" @click="handleExportExcel">导出Excel</el-button>
            <el-button type="info" size="small" @click="handlePrint">打印报表</el-button>
            <el-button type="warning" size="small" @click="handleStatistics">统计图表</el-button>
          </div>
        </div>
      </template>
      
      <el-table :data="recordsData" style="width: 100%" border>
        <el-table-column prop="balanceDate" label="平账时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.balanceDate) }}
          </template>
        </el-table-column>
        <el-table-column prop="month" label="平账月份" width="120" />
        <el-table-column prop="workerCount" label="师傅人数" width="100" />
        <el-table-column prop="totalOrders" label="完成订单数" width="120" />
        <el-table-column prop="totalAmount" label="订单总金额" width="140">
          <template #default="{ row }">
            ¥{{ formatNumber(row.totalAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="materialCost" label="物料成本" width="120">
          <template #default="{ row }">
            ¥{{ formatNumber(row.materialCost) }}
          </template>
        </el-table-column>
        <el-table-column prop="laborCost" label="人工成本" width="120">
          <template #default="{ row }">
            ¥{{ formatNumber(row.laborCost) }}
          </template>
        </el-table-column>
        <el-table-column prop="profit" label="利润" width="120">
          <template #default="{ row }">
            ¥{{ formatNumber(row.profit) }}
          </template>
        </el-table-column>
        <el-table-column prop="profitRate" label="利润率" width="100">
          <template #default="{ row }">
            {{ row.profitRate }}%
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'confirmed' ? 'success' : 'warning'">
              {{ row.status === 'confirmed' ? '已确认' : '待确认' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)">查看</el-button>
            <el-button type="success" size="small" @click="handleExport(row)">导出</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 查看平账详情弹窗 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="平账详情"
      width="1100px"
      :before-close="handleCloseDetailDialog"
    >
      <div v-if="selectedRecord" class="detail-content dialog-large-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="平账时间">{{ formatDate(selectedRecord.balanceDate) }}</el-descriptions-item>
          <el-descriptions-item label="平账月份">{{ selectedRecord.month }}</el-descriptions-item>
          <el-descriptions-item label="操作员">{{ selectedRecord.operator }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="selectedRecord.status === 'confirmed' ? 'success' : 'warning'">
              {{ selectedRecord.status === 'confirmed' ? '已确认' : '待确认' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
        <div class="detail-section">
          <h4>工程费用汇总</h4>
          <el-tabs v-model="activeDetailTab" type="border-card" class="project-tabs" @tab-change="handleDetailTabChange">
            <el-tab-pane label="户内安装工程费用汇总" name="project-cost-summary">
              <ProjectCostSummaryTable
                :data="detailTabData['project-cost-summary'].list"
                :summaryMethod="detailTabData['project-cost-summary'].summaryMethod"
                :formatNumber="formatNumber"
                v-loading="loadingStates['project-cost-summary']"
              />
            </el-tab-pane>
            <el-tab-pane label="户内表前甲供材清单表" name="pre-meter-materials">
              <ProjectCostSummaryTable
                :data="detailTabData['pre-meter-materials'].list"
                :summaryMethod="detailTabData['pre-meter-materials'].summaryMethod"
                :formatNumber="formatNumber"
                v-loading="loadingStates['pre-meter-materials']"
              />
            </el-tab-pane>
            <el-tab-pane label="户内甲供材料清单表" name="indoor-materials">
              <ProjectCostSummaryTable
                :data="detailTabData['indoor-materials'].list"
                :summaryMethod="detailTabData['indoor-materials'].summaryMethod"
                :formatNumber="formatNumber"
                v-loading="loadingStates['indoor-materials']"
              />
            </el-tab-pane>
            <el-tab-pane label="户内结算做销售处理超领材料费用表" name="over-received-materials">
              <ProjectCostSummaryTable
                :data="detailTabData['over-received-materials'].list"
                :summaryMethod="detailTabData['over-received-materials'].summaryMethod"
                :formatNumber="formatNumber"
                v-loading="loadingStates['over-received-materials']"
              />
            </el-tab-pane>
            <el-tab-pane label="户内管件甲供材料费用表" name="fittings-materials">
              <ProjectCostSummaryTable
                :data="detailTabData['fittings-materials'].list"
                :summaryMethod="detailTabData['fittings-materials'].summaryMethod"
                :formatNumber="formatNumber"
                v-loading="loadingStates['fittings-materials']"
              />
            </el-tab-pane>
            <el-tab-pane label="户内挂表安装工程管件统计" name="meter-installation-fittings">
              <ProjectCostSummaryTable
                :data="detailTabData['meter-installation-fittings'].list"
                :summaryMethod="detailTabData['meter-installation-fittings'].summaryMethod"
                :formatNumber="formatNumber"
                v-loading="loadingStates['meter-installation-fittings']"
              />
            </el-tab-pane>
            <el-tab-pane label="户内零星安装工程管件统计" name="minor-installation-fittings">
              <ProjectCostSummaryTable
                :data="detailTabData['minor-installation-fittings'].list"
                :summaryMethod="detailTabData['minor-installation-fittings'].summaryMethod"
                :formatNumber="formatNumber"
                v-loading="loadingStates['minor-installation-fittings']"
              />
            </el-tab-pane>
            <el-tab-pane label="户内二次安装工程管件统计" name="secondary-installation-fittings">
              <ProjectCostSummaryTable
                :data="detailTabData['secondary-installation-fittings'].list"
                :summaryMethod="detailTabData['secondary-installation-fittings'].summaryMethod"
                :formatNumber="formatNumber"
                v-loading="loadingStates['secondary-installation-fittings']"
              />
            </el-tab-pane>
            <el-tab-pane label="户内安装管件统计表" name="indoor-installation-fittings">
              <ProjectCostSummaryTable
                :data="detailTabData['indoor-installation-fittings'].list"
                :summaryMethod="detailTabData['indoor-installation-fittings'].summaryMethod"
                :formatNumber="formatNumber"
                v-loading="loadingStates['indoor-installation-fittings']"
              />
            </el-tab-pane>
            <el-tab-pane label="户内挂表安装工程决算（半月板）" name="meter-settlement-half">
              <ProjectCostSummaryTable
                :data="detailTabData['meter-settlement-half'].list"
                :summaryMethod="detailTabData['meter-settlement-half'].summaryMethod"
                :formatNumber="formatNumber"
                v-loading="loadingStates['meter-settlement-half']"
              />
            </el-tab-pane>
            <el-tab-pane label="户内挂表安装工程决算（未半月板）" name="meter-settlement-no-half">
              <ProjectCostSummaryTable
                :data="detailTabData['meter-settlement-no-half'].list"
                :summaryMethod="detailTabData['meter-settlement-no-half'].summaryMethod"
                :formatNumber="formatNumber"
                v-loading="loadingStates['meter-settlement-no-half']"
              />
            </el-tab-pane>
            <el-tab-pane label="管道燃气户内零星安装工程决算（半月板）" name="gas-minor-settlement-half">
              <ProjectCostSummaryTable
                :data="detailTabData['gas-minor-settlement-half'].list"
                :summaryMethod="detailTabData['gas-minor-settlement-half'].summaryMethod"
                :formatNumber="formatNumber"
                v-loading="loadingStates['gas-minor-settlement-half']"
              />
            </el-tab-pane>
            <el-tab-pane label="管道燃气户内零星安装工程决算（未半月板）" name="gas-minor-settlement-no-half">
              <ProjectCostSummaryTable
                :data="detailTabData['gas-minor-settlement-no-half'].list"
                :summaryMethod="detailTabData['gas-minor-settlement-no-half'].summaryMethod"
                :formatNumber="formatNumber"
                v-loading="loadingStates['gas-minor-settlement-no-half']"
              />
            </el-tab-pane>
            <el-tab-pane label="管道燃气户内零星安装工程决算（不可用燃气表）" name="gas-minor-settlement-no-meter">
              <ProjectCostSummaryTable
                :data="detailTabData['gas-minor-settlement-no-meter'].list"
                :summaryMethod="detailTabData['gas-minor-settlement-no-meter'].summaryMethod"
                :formatNumber="formatNumber"
                v-loading="loadingStates['gas-minor-settlement-no-meter']"
              />
            </el-tab-pane>
            <el-tab-pane label="甲供材料领用表" name="supplied-materials-receipt">
              <ProjectCostSummaryTable
                :data="detailTabData['supplied-materials-receipt'].list"
                :summaryMethod="detailTabData['supplied-materials-receipt'].summaryMethod"
                :formatNumber="formatNumber"
                v-loading="loadingStates['supplied-materials-receipt']"
              />
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleExportDetail">导出详情</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import request from '../../utils/request'
import ProjectCostSummaryTable from './ProjectCostSummaryTable.vue'

// 搜索表单
const searchForm = reactive({
  month: '',
  status: '',
  dateRange: []
})

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 平账记录数据
const recordsData = ref([
  {
    id: 1,
    month: '2024年1月',
    balanceDate: new Date('2024-01-31 15:30:00'),
    operator: '张三',
    status: 'confirmed',
    workerCount: 8,
    totalOrders: 88,
    totalAmount: 377000,
    materialCost: 145800,
    laborCost: 66000,
    profit: 165200,
    profitRate: 43.8,
    salaryData: [
      {
        workerName: '李师傅',
        workType: '电工',
        workPrice: 300,
        workDays: 22,
        totalSalary: 7920,
        paidAmount: 5000
      },
      {
        workerName: '王师傅',
        workType: '水工',
        workPrice: 280,
        workDays: 20,
        totalSalary: 6720,
        paidAmount: 4500
      },
      {
        workerName: '张师傅',
        workType: '安装工',
        workPrice: 320,
        workDays: 18,
        totalSalary: 6912,
        paidAmount: 4000
      }
    ],
    orderData: [
      {
        orderType: '水电安装',
        completedCount: 25,
        totalAmount: 125000,
        materialCost: 45000,
        laborCost: 18750,
        profit: 61250
      },
      {
        orderType: '电路维修',
        completedCount: 18,
        totalAmount: 72000,
        materialCost: 28800,
        laborCost: 13500,
        profit: 29700
      }
    ],
    projectCostData: [
      {
        category: '人工成本',
        subCategory: '电工',
        amount: 15000,
        unit: '人/天'
      },
      {
        category: '人工成本',
        subCategory: '水工',
        amount: 12000,
        unit: '人/天'
      },
      {
        category: '人工成本',
        subCategory: '安装工',
        amount: 18000,
        unit: '人/天'
      },
      {
        category: '物料成本',
        subCategory: '电线',
        amount: 5000,
        unit: '米'
      },
      {
        category: '物料成本',
        subCategory: '水管',
        amount: 8000,
        unit: '米'
      },
      {
        category: '物料成本',
        subCategory: '开关',
        amount: 1000,
        unit: '个'
      }
    ]
  },
  {
    id: 2,
    month: '2023年12月',
    balanceDate: new Date('2023-12-31 16:45:00'),
    operator: '李四',
    status: 'confirmed',
    workerCount: 7,
    totalOrders: 76,
    totalAmount: 325000,
    materialCost: 128500,
    laborCost: 58000,
    profit: 138500,
    profitRate: 42.6,
    salaryData: [],
    orderData: [],
    projectCostData: []
  },
  {
    id: 3,
    month: '2023年11月',
    balanceDate: new Date('2023-11-30 14:20:00'),
    operator: '王五',
    status: 'confirmed',
    workerCount: 6,
    totalOrders: 65,
    totalAmount: 286000,
    materialCost: 112300,
    laborCost: 51500,
    profit: 122200,
    profitRate: 42.7,
    salaryData: [],
    orderData: [],
    projectCostData: []
  },
  {
    id: 4,
    month: '2023年10月',
    balanceDate: new Date('2023-10-31 17:10:00'),
    operator: '赵六',
    status: 'confirmed',
    workerCount: 7,
    totalOrders: 72,
    totalAmount: 312000,
    materialCost: 125600,
    laborCost: 56800,
    profit: 129600,
    profitRate: 41.5,
    salaryData: [],
    orderData: [],
    projectCostData: []
  },
  {
    id: 5,
    month: '2023年9月',
    balanceDate: new Date('2023-09-30 15:55:00'),
    operator: '孙七',
    status: 'confirmed',
    workerCount: 6,
    totalOrders: 68,
    totalAmount: 298000,
    materialCost: 118900,
    laborCost: 54200,
    profit: 124900,
    profitRate: 41.9,
    salaryData: [],
    orderData: [],
    projectCostData: []
  }
])

// 弹窗控制
const detailDialogVisible = ref(false)
const selectedRecord = ref<any>(null)
const activeDetailTab = ref('project-cost-summary')

// 详情弹窗数据
const detailTabData = reactive({
  'project-cost-summary': { list: [], summaryMethod: () => [] },
  'pre-meter-materials': { list: [], summaryMethod: () => [] },
  'indoor-materials': { list: [], summaryMethod: () => [] },
  'over-received-materials': { list: [], summaryMethod: () => [] },
  'fittings-materials': { list: [], summaryMethod: () => [] },
  'meter-installation-fittings': { list: [], summaryMethod: () => [] },
  'minor-installation-fittings': { list: [], summaryMethod: () => [] },
  'secondary-installation-fittings': { list: [], summaryMethod: () => [] },
  'indoor-installation-fittings': { list: [], summaryMethod: () => [] },
  'meter-settlement-half': { list: [], summaryMethod: () => [] },
  'meter-settlement-no-half': { list: [], summaryMethod: () => [] },
  'gas-minor-settlement-half': { list: [], summaryMethod: () => [] },
  'gas-minor-settlement-no-half': { list: [], summaryMethod: () => [] },
  'gas-minor-settlement-no-meter': { list: [], summaryMethod: () => [] },
  'supplied-materials-receipt': { list: [], summaryMethod: () => [] },
})
const loadingStates = reactive({
  'project-cost-summary': false,
  'pre-meter-materials': false,
  'indoor-materials': false,
  'over-received-materials': false,
  'fittings-materials': false,
  'meter-installation-fittings': false,
  'minor-installation-fittings': false,
  'secondary-installation-fittings': false,
  'indoor-installation-fittings': false,
  'meter-settlement-half': false,
  'meter-settlement-no-half': false,
  'gas-minor-settlement-half': false,
  'gas-minor-settlement-no-half': false,
  'gas-minor-settlement-no-meter': false,
  'supplied-materials-receipt': false,
})
const dataCache = reactive({})

const detailTabApiMap = {
  'project-cost-summary': '/api/loose-orders/balance/project-cost',
  'pre-meter-materials': '/api/loose-orders/balance/pre-meter-materials',
  'indoor-materials': '/api/loose-orders/balance/indoor-materials',
  'over-received-materials': '/api/loose-orders/balance/over-received-materials',
  'fittings-materials': '/api/loose-orders/balance/fittings-materials',
  'meter-installation-fittings': '/api/loose-orders/balance/meter-installation-fittings',
  'minor-installation-fittings': '/api/loose-orders/balance/minor-installation-fittings',
  'secondary-installation-fittings': '/api/loose-orders/balance/secondary-installation-fittings',
  'indoor-installation-fittings': '/api/loose-orders/balance/indoor-installation-fittings',
  'meter-settlement-half': '/api/loose-orders/balance/meter-settlement-half',
  'meter-settlement-no-half': '/api/loose-orders/balance/meter-settlement-no-half',
  'gas-minor-settlement-half': '/api/loose-orders/balance/gas-minor-settlement-half',
  'gas-minor-settlement-no-half': '/api/loose-orders/balance/gas-minor-settlement-no-half',
  'gas-minor-settlement-no-meter': '/api/loose-orders/balance/gas-minor-settlement-no-meter',
  'supplied-materials-receipt': '/api/loose-orders/balance/supplied-materials-receipt',
}

// 格式化数字
const formatNumber = (num: any) => {
  if (num === undefined || num === null || num === '' || isNaN(Number(num))) {
    return '0'
  }
  try {
    return Number(num).toLocaleString()
  } catch {
    return String(num)
  }
}

// 格式化日期
const formatDate = (date: Date) => {
  if (!date) return ''
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 查询
const handleSearch = () => {
  loadBalanceRecords()
  ElMessage.success('查询成功')
}

// 重置
const handleReset = () => {
  searchForm.month = ''
  searchForm.status = ''
  searchForm.dateRange = []
  ElMessage.info('已重置查询条件')
}

// 查看
const handleView = (row: any) => {
  selectedRecord.value = row
  detailDialogVisible.value = true
}

// 关闭详情弹窗
const handleCloseDetailDialog = () => {
  detailDialogVisible.value = false
  selectedRecord.value = null
}

// 导出Excel
const handleExportExcel = () => {
  ElMessage.success('导出Excel成功')
}

// 打印报表
const handlePrint = () => {
  window.print()
}

// 统计图表
const handleStatistics = () => {
  ElMessage.info('打开统计图表')
}

// 导出单条记录
const handleExport = (row: any) => {
  ElMessage.success(`导出记录 ${row.id} 成功`)
}

// 导出详情
const handleExportDetail = () => {
  ElMessage.success('导出详情成功')
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  loadBalanceRecords()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadBalanceRecords()
}

// 详情选项卡切换
const handleDetailTabChange = async (tabName) => {
  if (dataCache[tabName]) {
    detailTabData[tabName].list = dataCache[tabName].list
    detailTabData[tabName].summaryMethod = getTableSummary(tabName, dataCache[tabName]?.summary)
    return
  }
  loadingStates[tabName] = true
  try {
    const apiUrl = detailTabApiMap[tabName]
    const result = await request.get(apiUrl)
    if (result.code === 200 && result.data && Array.isArray(result.data.list)) {
      detailTabData[tabName].list = result.data.list
      detailTabData[tabName].summaryMethod = getTableSummary(tabName, result.data.summary)
      dataCache[tabName] = result.data
    } else {
      detailTabData[tabName].list = []
      detailTabData[tabName].summaryMethod = () => []
    }
  } catch (e) {
    detailTabData[tabName].list = []
    detailTabData[tabName].summaryMethod = () => []
  } finally {
    loadingStates[tabName] = false
  }
}

function getTableSummary(tabName, summary) {
  return (param) => {
    const { columns } = param
    const sums = []
    if (summary) {
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计'
        } else {
          const property = column.property
          if (summary[property] !== undefined) {
            if (property.includes('Amount') || property.includes('Cost') || property.includes('Price')) {
              sums[index] = `¥${formatNumber(summary[property])}`
            } else {
              sums[index] = formatNumber(summary[property])
            }
          } else {
            sums[index] = ''
          }
        }
      })
    } else {
      columns.forEach((column, index) => {
        sums[index] = index === 0 ? '合计' : ''
      })
    }
    return sums
  }
}

// 弹窗打开时默认加载第一个tab
watch(detailDialogVisible, (val) => {
  if (val) {
    handleDetailTabChange(activeDetailTab.value)
  }
})

// 页面加载时获取数据
onMounted(() => {
  loadBalanceRecords()
})

// 加载平账记录数据
const loadBalanceRecords = async () => {
  try {
    // 调用API
    const result = await request.get('/api/loose-orders/balance/records')
    
    if (result.code === 200) {
      const data = result.data
      
      // 更新记录数据
      recordsData.value = data.records.map((record: any) => ({
        ...record,
        balanceDate: new Date(record.balanceDate)
      }))
      
      // 更新分页信息
      pagination.total = data.total
      pagination.currentPage = data.page
      pagination.pageSize = data.pageSize
      
      ElMessage.success('数据加载成功')
    } else {
      ElMessage.error('数据加载失败')
    }
  } catch (error) {
    console.error('加载平账记录数据失败:', error)
    ElMessage.error('数据加载失败')
  }
}

// 获取项目费用汇总（示例，实际可根据 selectedRecord 结构调整）
const getProjectCostSummary = () => []
</script>

<style scoped>
.balance-records {
  padding: 20px;
}

.search-card,
.records-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.search-form {
  margin-top: 10px;
}

.pagination-container {
  margin-top: 20px;
  text-align: center;
}

.detail-content {
  max-height: 600px;
  overflow-y: auto;
}

.detail-section {
  margin-top: 20px;
}

.detail-section h4 {
  margin-bottom: 15px;
  color: #303133;
  font-size: 16px;
  font-weight: bold;
  border-bottom: 2px solid #409eff;
  padding-bottom: 5px;
}

.stat-item {
  text-align: center;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .balance-records {
    padding: 10px;
  }
  
  .header-actions {
    flex-direction: column;
    gap: 5px;
  }
  
  .stat-item {
    margin-bottom: 10px;
  }
}
.dialog-large-content {
  font-size: 18px;
}
.project-tabs {
  margin-top: 20px;
}
</style> 