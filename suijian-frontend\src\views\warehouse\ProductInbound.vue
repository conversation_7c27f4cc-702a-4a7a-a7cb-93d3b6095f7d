<template>
  <div class="product-inbound-container">
    <el-card class="main-card">
      <template #header>
        <div class="card-header">
          <span>商品入库</span>
        </div>
      </template>
      
      <!-- 入库信息 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">入库信息</div>
        </el-col>
        <el-col :span="12">
          <el-form-item label="入库单号:">
            <span>RK20240115001</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="入库日期:">
            <el-date-picker
              v-model="inboundForm.inboundDate"
              type="date"
              placeholder="请选择入库日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="操作员:">
            <el-input v-model="inboundForm.operator" placeholder="请输入操作员" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="供应商:">
            <el-select v-model="inboundForm.supplier" placeholder="请选择供应商" style="width: 100%">
              <el-option
                v-for="supplier in supplierList"
                :key="supplier.id"
                :label="supplier.name"
                :value="supplier.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="采购单号:">
            <el-input v-model="inboundForm.purchaseOrderNo" placeholder="请输入采购单号" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 入库商品 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">入库商品</div>
        </el-col>
        <el-col :span="24">
          <el-table :data="inboundItems" border class="inbound-table">
            <el-table-column type="index" label="序号" width="60" />
            <el-table-column prop="materialCode" label="公司物料编码" width="120" />
            <el-table-column prop="materialName" label="商品名称" width="120" />
            <el-table-column prop="model" label="型号" width="100" />
            <el-table-column prop="specification" label="规格" width="100" />
            <el-table-column prop="unit" label="单位" width="80" />
            <el-table-column prop="plannedQuantity" label="计划数量" width="100" />
            <el-table-column prop="actualQuantity" label="实际数量">
              <template #default="scope">
                <el-input-number
                  v-model="scope.row.actualQuantity"
                  :min="0"
                  controls-position="right"
                  style="width: 100%"
                />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80" fixed="right">
              <template #default="scope">
                <el-button type="danger" link @click="removeItem(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div style="margin-top: 15px;">
            <el-button type="primary" icon="Plus" @click="selectProduct">添加商品</el-button>
          </div>
        </el-col>
      </el-row>
      
      <!-- 添加商品 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">添加商品</div>
        </el-col>
        <el-col :span="24">
          <div class="add-product-actions">
            <el-button type="primary" @click="selectProduct">选择商品</el-button>
            <el-button type="success" @click="addProduct">添加</el-button>
          </div>
        </el-col>
      </el-row>
      
      <!-- 商品选择弹窗 -->
      <el-dialog v-model="productDialogVisible" title="选择商品" width="800">
        <el-row :gutter="20" style="margin-bottom: 20px;">
          <el-col :span="18">
            <el-input v-model="productSearch" placeholder="请输入搜索关键词" clearable />
          </el-col>
          <el-col :span="6">
            <el-button type="primary" icon="Search" @click="searchProducts">搜索</el-button>
          </el-col>
        </el-row>
        
        <el-table :data="productOptions" border height="400">
          <el-table-column prop="materialCode" label="公司物料编码" width="120" />
          <el-table-column prop="materialName" label="商品名称" width="120" />
          <el-table-column prop="model" label="型号" width="100" />
          <el-table-column prop="specification" label="规格" width="100" />
          <el-table-column prop="unit" label="单位" width="80" />
          <el-table-column prop="plannedQuantity" label="计划数量" width="100" />
          <el-table-column label="操作" width="80" fixed="right">
            <template #default="scope">
              <el-button type="primary" link @click="selectProductItem(scope.row)">选择</el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <template #footer>
          <el-button @click="productDialogVisible = false">取消</el-button>
        </template>
      </el-dialog>
      
      <!-- 入库统计 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">入库统计</div>
        </el-col>
        <el-col :span="24">
          <el-card class="statistics-card">
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="statistic-item">
                  <span class="label">入库商品种类:</span>
                  <span>{{ statistics.productTypes }}种</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="statistic-item">
                  <span class="label">入库商品总数:</span>
                  <span>{{ statistics.totalQuantity }}件</span>
                </div>
              </el-col>
            </el-row>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 备注信息 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">备注信息</div>
        </el-col>
        <el-col :span="24">
          <el-form-item label="入库说明:">
            <el-input
              v-model="inboundForm.inboundDescription"
              type="textarea"
              :rows="3"
              placeholder="请输入入库说明"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注:">
            <el-input
              v-model="inboundForm.remarks"
              type="textarea"
              :rows="2"
              placeholder="请输入备注"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 操作按钮 -->
      <div class="form-actions">
        <el-button type="primary" @click="handleSave">保存</el-button>
        <el-button @click="handlePrint">打印</el-button>
        <el-button type="success" @click="handleSubmit">提交</el-button>
        <el-button @click="handleCancel">取消</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

// 入库表单
const inboundForm = reactive({
  inboundDate: '2024-01-15',
  operator: '张三',
  supplier: 1,
  purchaseOrderNo: 'CG2024011001',
  inboundDescription: '',
  remarks: ''
})

// 供应商列表
const supplierList = ref([
  { id: 1, name: '华强电子' },
  { id: 2, name: '立创电子' },
  { id: 3, name: '得捷电子' },
  { id: 4, name: '贸泽电子' }
])

// 入库商品
const inboundItems = ref([
  {
    id: 1,
    materialCode: 'SP001',
    materialName: '智能开关',
    model: 'KG-86',
    specification: '86型',
    unit: '个',
    plannedQuantity: 100,
    actualQuantity: 100
  },
  {
    id: 2,
    materialCode: 'SP002',
    materialName: 'LED灯具',
    model: 'LED-12W',
    specification: '12W',
    unit: '个',
    plannedQuantity: 50,
    actualQuantity: 50
  },
  {
    id: 3,
    materialCode: 'SP003',
    materialName: '插座面板',
    model: 'ZP-86',
    specification: '86型',
    unit: '个',
    plannedQuantity: 200,
    actualQuantity: 200
  }
])

// 商品选项
const productOptions = ref([
  {
    id: 1,
    materialCode: 'SP001',
    materialName: '智能开关',
    model: 'KG-86',
    specification: '86型',
    unit: '个',
    plannedQuantity: 100
  },
  {
    id: 2,
    materialCode: 'SP002',
    materialName: 'LED灯具',
    model: 'LED-12W',
    specification: '12W',
    unit: '个',
    plannedQuantity: 50
  },
  {
    id: 4,
    materialCode: 'SP004',
    materialName: '电线',
    model: 'BV-2.5',
    specification: '2.5mm²',
    unit: '米',
    plannedQuantity: 1000
  }
])

// 统计信息
const statistics = reactive({
  productTypes: 3,
  totalQuantity: 350
})

// 弹窗控制
const productDialogVisible = ref(false)

// 商品搜索
const productSearch = ref('')

// 选择商品
const selectProduct = () => {
  productDialogVisible.value = true
}

// 搜索商品
const searchProducts = () => {
  ElMessage.success('搜索商品')
  console.log('搜索关键词:', productSearch.value)
}

// 选择商品项
const selectProductItem = (row: any) => {
  // 检查是否已添加
  const exists = inboundItems.value.some(item => item.id === row.id)
  if (exists) {
    ElMessage.warning('该商品已添加')
    return
  }
  
  inboundItems.value.push({
    ...row,
    actualQuantity: row.plannedQuantity
  })
  
  productDialogVisible.value = false
  ElMessage.success('添加成功')
  updateStatistics()
}

// 删除商品
const removeItem = (index: number) => {
  inboundItems.value.splice(index, 1)
  ElMessage.success('删除成功')
  updateStatistics()
}

// 添加商品
const addProduct = () => {
  ElMessage.success('添加商品')
}

// 更新统计信息
const updateStatistics = () => {
  statistics.productTypes = inboundItems.value.length
  statistics.totalQuantity = inboundItems.value.reduce((total, item) => total + item.actualQuantity, 0)
}

// 保存
const handleSave = () => {
  ElMessage.success('保存成功')
  console.log('保存数据:', inboundForm, inboundItems.value)
}

// 打印
const handlePrint = () => {
  ElMessage.success('开始打印')
}

// 提交
const handleSubmit = () => {
  ElMessage.success('提交成功')
  console.log('提交数据:', inboundForm, inboundItems.value)
}

// 取消
const handleCancel = () => {
  ElMessage.info('已取消')
}
</script>

<style lang="scss" scoped>
.product-inbound-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
  
  .main-card {
    margin-bottom: 20px;
    
    .card-header {
      font-size: 18px;
      font-weight: bold;
      color: #303133;
    }
  }
  
  .form-section {
    margin-bottom: 20px;
    
    .section-title {
      font-size: 16px;
      font-weight: bold;
      color: #606266;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ebeef5;
    }
    
    .el-form-item {
      margin-bottom: 18px;
    }
  }
  
  .inbound-table {
    margin-top: 10px;
  }
  
  .add-product-actions {
    .el-button {
      margin-right: 10px;
    }
  }
  
  .statistics-card {
    background-color: #f0f9ff;
    border-color: #b3e0ff;
    
    .statistic-item {
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;
      
      .label {
        font-weight: bold;
        color: #409eff;
      }
      
      .total-amount {
        font-weight: bold;
        color: #303133;
      }
    }
  }
  
  .form-actions {
    text-align: center;
    padding: 20px 0;
    
    .el-button {
      margin: 0 10px;
      padding: 12px 30px;
    }
  }
}
</style>