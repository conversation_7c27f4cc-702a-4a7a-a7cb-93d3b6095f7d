var e=Object.defineProperty,l=Object.defineProperties,a=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,d=Object.prototype.propertyIsEnumerable,u=(l,a,t)=>a in l?e(l,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):l[a]=t,r=(e,l,a)=>new Promise((t,o)=>{var d=e=>{try{r(a.next(e))}catch(l){o(l)}},u=e=>{try{r(a.throw(e))}catch(l){o(l)}},r=e=>e.done?t(e.value):Promise.resolve(e.value).then(d,u);r((a=a.apply(e,l)).next())});import{r as i}from"./index-c181cb52.js";import{E as n}from"./element-plus-ad78a7bf.js";import{l as s,_ as p,r as c,q as m,y as g,z as f,R as b,J as _,K as y,I as v,av as h,aD as w,x as V,O as C,P as j,Q as k,aa as x,B as U}from"./vue-vendor-fc5a6493.js";import{_ as q}from"./_plugin-vue_export-helper-1b428a4d.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const z={class:"page-container"},O={class:"search-form"},P={class:"action-buttons"},S={class:"table-container"},E={class:"pagination-container"},I=q(s({__name:"MaterialList",setup(e){const s=p({keyword:"",category:"",tags:[],status:""}),q=c([]),I=c(!1),D=p({page:1,pageSize:20,total:0}),L=c(!1),R=c(""),B=c(!1),F=c([]),J=c(),K=p({id:"",companyCode:"",clientCodes:"",name:"",model:"",specification:"",unit:"",category:"",tags:[]}),M={companyCode:[{required:!1,message:"公司物料编码由后台自动生成",trigger:"blur"}],name:[{required:!0,message:"请输入物料名称",trigger:"blur"}],category:[{required:!0,message:"请选择分类",trigger:"change"}],unit:[{required:!0,message:"请输入单位",trigger:"blur"}],clientCodes:[{required:!1,message:"请输入甲方编码",trigger:"blur"}]},Q=()=>{D.page=1,Y()},$=()=>{Object.assign(s,{keyword:"",category:"",tags:[],status:""}),Q()},A=()=>{R.value="新增物料",Object.assign(K,{id:"",companyCode:"",clientCodes:"",name:"",model:"",specification:"",unit:"",category:"",tags:[]}),L.value=!0},G=e=>{R.value="编辑物料";const r=e.clientCodes&&e.clientCodes.length>0?e.clientCodes.map(e=>e.code).join(","):"";var i;Object.assign(K,(i=((e,l)=>{for(var a in l||(l={}))o.call(l,a)&&u(e,a,l[a]);if(t)for(var a of t(l))d.call(l,a)&&u(e,a,l[a]);return e})({},e),l(i,a({clientCodes:r})))),L.value=!0},H=()=>{n.info("导出功能待实现")},N=()=>r(this,null,function*(){if(J.value)try{yield J.value.validate(),n.success(K.id?"更新成功":"新增成功"),L.value=!1,Y()}catch(e){n.error("表单验证失败")}}),T=()=>{var e;null==(e=J.value)||e.resetFields()},W=e=>{D.pageSize=e,Y()},X=e=>{D.page=e,Y()},Y=()=>r(this,null,function*(){I.value=!0;try{const e=yield i.get("/api/materials");q.value=e.data.list,D.total=e.data.total}catch(e){n.error("加载数据失败")}finally{I.value=!1}});return m(()=>{Y()}),(e,l)=>{const a=h("el-input"),t=h("el-form-item"),o=h("el-option"),d=h("el-select"),u=h("el-button"),r=h("el-form"),i=h("el-table-column"),n=h("el-tag"),p=h("el-table"),c=h("el-pagination"),m=h("el-col"),Y=h("el-row"),Z=h("el-dialog"),ee=w("loading");return V(),g("div",z,[f("div",O,[b(r,{model:s,inline:""},{default:_(()=>[b(t,{label:"关键字"},{default:_(()=>[b(a,{modelValue:s.keyword,"onUpdate:modelValue":l[0]||(l[0]=e=>s.keyword=e),placeholder:"请输入物料编码、名称、型号",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),b(t,{label:"分类"},{default:_(()=>[b(d,{modelValue:s.category,"onUpdate:modelValue":l[1]||(l[1]=e=>s.category=e),placeholder:"请选择分类",clearable:""},{default:_(()=>[b(o,{label:"甲料",value:"甲料"}),b(o,{label:"商品",value:"商品"}),b(o,{label:"辅料",value:"辅料"})]),_:1},8,["modelValue"])]),_:1}),b(t,{label:"标签"},{default:_(()=>[b(d,{modelValue:s.tags,"onUpdate:modelValue":l[2]||(l[2]=e=>s.tags=e),placeholder:"请选择标签",clearable:"",multiple:""},{default:_(()=>[b(o,{label:"管材",value:"管材"}),b(o,{label:"赠品",value:"赠品"}),b(o,{label:"工具",value:"工具"}),b(o,{label:"耗材",value:"耗材"}),b(o,{label:"设备",value:"设备"})]),_:1},8,["modelValue"])]),_:1}),b(t,{label:"状态"},{default:_(()=>[b(d,{modelValue:s.status,"onUpdate:modelValue":l[3]||(l[3]=e=>s.status=e),placeholder:"请选择状态",clearable:""},{default:_(()=>[b(o,{label:"正常",value:"正常"}),b(o,{label:"需进仓",value:"需进仓"})]),_:1},8,["modelValue"])]),_:1}),b(t,null,{default:_(()=>[b(u,{type:"primary",onClick:Q},{default:_(()=>l[17]||(l[17]=[C("搜索",-1)])),_:1,__:[17]}),b(u,{onClick:$},{default:_(()=>l[18]||(l[18]=[C("重置",-1)])),_:1,__:[18]})]),_:1})]),_:1},8,["model"])]),f("div",P,[b(u,{type:"primary",onClick:A},{default:_(()=>l[19]||(l[19]=[C("新增物料",-1)])),_:1,__:[19]}),b(u,{type:"warning",onClick:H},{default:_(()=>l[20]||(l[20]=[C("导出Excel",-1)])),_:1,__:[20]})]),f("div",S,[y((V(),v(p,{data:q.value,border:"",stripe:"",style:{width:"100%"}},{default:_(()=>[b(i,{type:"selection",width:"55"}),b(i,{prop:"companyCode",label:"公司物料编码",width:"150"}),b(i,{label:"甲方编码",width:"120"},{default:_(({row:e})=>[b(u,{type:"text",onClick:l=>(e=>{F.value=e.clientCodes.map(e=>({code:e.code,name:e.name,quantity:e.quantity,unit:e.unit})),B.value=!0})(e)},{default:_(()=>{return[C(j((l=e.clientCodes,l&&0!==l.length?1===l.length?l[0].code:`${l[0].code}...`:"无")),1)];var l}),_:2},1032,["onClick"])]),_:1}),b(i,{label:"标签",width:"150"},{default:_(({row:e})=>[(V(!0),g(k,null,x(e.tags,e=>(V(),v(n,{key:e,size:"small",style:{"margin-right":"4px","margin-bottom":"4px"}},{default:_(()=>[C(j(e),1)]),_:2},1024))),128))]),_:1}),b(i,{prop:"category",label:"分类",width:"100"},{default:_(({row:e})=>{return[b(n,{type:(l=e.category,{"甲料":"primary","商品":"success","辅料":"warning"}[l]||"info")},{default:_(()=>[C(j(e.category),1)]),_:2},1032,["type"])];var l}),_:1}),b(i,{prop:"name",label:"名称",width:"150"}),b(i,{prop:"model",label:"型号",width:"120"}),b(i,{prop:"specification",label:"规格",width:"150"}),b(i,{prop:"unit",label:"单位",width:"80"}),b(i,{prop:"quantity",label:"数量",width:"100"},{default:_(({row:e})=>[f("span",{class:U({"text-danger":e.quantity<=10})},j(e.quantity),3)]),_:1}),b(i,{prop:"status",label:"状态",width:"80"},{default:_(({row:e})=>[b(n,{type:"正常"===e.status?"success":"warning"},{default:_(()=>[C(j("正常"===e.status?"正常":"需进仓"),1)]),_:2},1032,["type"])]),_:1}),b(i,{label:"操作",width:"200",fixed:"right"},{default:_(({row:e})=>[b(u,{type:"primary",size:"small",onClick:l=>G(e)},{default:_(()=>l[21]||(l[21]=[C("编辑",-1)])),_:2,__:[21]},1032,["onClick"]),b(u,{type:"success",size:"small",onClick:l=>(e=>{R.value="新增物料",Object.assign(K,{id:"",companyCode:"",clientCodes:"",name:e.name,model:"",specification:"",unit:e.unit,category:e.category,tags:[...e.tags]}),L.value=!0})(e)},{default:_(()=>l[22]||(l[22]=[C("新增物料",-1)])),_:2,__:[22]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[ee,I.value]]),f("div",E,[b(c,{"current-page":D.page,"onUpdate:currentPage":l[4]||(l[4]=e=>D.page=e),"page-size":D.pageSize,"onUpdate:pageSize":l[5]||(l[5]=e=>D.pageSize=e),total:D.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:W,onCurrentChange:X},null,8,["current-page","page-size","total"])])]),b(Z,{modelValue:L.value,"onUpdate:modelValue":l[15]||(l[15]=e=>L.value=e),title:R.value,width:"700px",onClose:T},{footer:_(()=>[b(u,{onClick:l[14]||(l[14]=e=>L.value=!1)},{default:_(()=>l[23]||(l[23]=[C("取消",-1)])),_:1,__:[23]}),b(u,{type:"primary",onClick:N},{default:_(()=>l[24]||(l[24]=[C("确定",-1)])),_:1,__:[24]})]),default:_(()=>[b(r,{ref_key:"formRef",ref:J,model:K,rules:M,"label-width":"120px"},{default:_(()=>[b(Y,{gutter:20},{default:_(()=>[b(m,{span:12},{default:_(()=>[b(t,{label:"公司物料编码",prop:"companyCode"},{default:_(()=>[b(a,{modelValue:K.companyCode,"onUpdate:modelValue":l[6]||(l[6]=e=>K.companyCode=e),placeholder:K.id?"请输入公司物料编码":"后台自动生成",disabled:!K.id},null,8,["modelValue","placeholder","disabled"])]),_:1})]),_:1}),b(m,{span:12},{default:_(()=>[b(t,{label:"物料名称",prop:"name"},{default:_(()=>[b(a,{modelValue:K.name,"onUpdate:modelValue":l[7]||(l[7]=e=>K.name=e),placeholder:"请输入物料名称"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),b(t,{label:"甲方编码",prop:"clientCodes"},{default:_(()=>[b(a,{modelValue:K.clientCodes,"onUpdate:modelValue":l[8]||(l[8]=e=>K.clientCodes=e),placeholder:"请输入甲方编码",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),b(Y,{gutter:20},{default:_(()=>[b(m,{span:12},{default:_(()=>[b(t,{label:"型号",prop:"model"},{default:_(()=>[b(a,{modelValue:K.model,"onUpdate:modelValue":l[9]||(l[9]=e=>K.model=e),placeholder:"请输入型号"},null,8,["modelValue"])]),_:1})]),_:1}),b(m,{span:12},{default:_(()=>[b(t,{label:"规格",prop:"specification"},{default:_(()=>[b(a,{modelValue:K.specification,"onUpdate:modelValue":l[10]||(l[10]=e=>K.specification=e),placeholder:"请输入规格"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),b(Y,{gutter:20},{default:_(()=>[b(m,{span:12},{default:_(()=>[b(t,{label:"单位",prop:"unit"},{default:_(()=>[b(a,{modelValue:K.unit,"onUpdate:modelValue":l[11]||(l[11]=e=>K.unit=e),placeholder:"请输入单位"},null,8,["modelValue"])]),_:1})]),_:1}),b(m,{span:12},{default:_(()=>[b(t,{label:"分类",prop:"category"},{default:_(()=>[b(d,{modelValue:K.category,"onUpdate:modelValue":l[12]||(l[12]=e=>K.category=e),placeholder:"请选择分类"},{default:_(()=>[b(o,{label:"甲料",value:"甲料"}),b(o,{label:"商品",value:"商品"}),b(o,{label:"辅料",value:"辅料"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),b(t,{label:"标签",prop:"tags"},{default:_(()=>[b(d,{modelValue:K.tags,"onUpdate:modelValue":l[13]||(l[13]=e=>K.tags=e),placeholder:"请选择标签",multiple:""},{default:_(()=>[b(o,{label:"管材",value:"管材"}),b(o,{label:"赠品",value:"赠品"}),b(o,{label:"工具",value:"工具"}),b(o,{label:"耗材",value:"耗材"}),b(o,{label:"设备",value:"设备"})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),b(Z,{modelValue:B.value,"onUpdate:modelValue":l[16]||(l[16]=e=>B.value=e),title:"甲方编码列表",width:"600px"},{default:_(()=>[b(p,{data:F.value,border:""},{default:_(()=>[b(i,{prop:"code",label:"甲方编码",width:"150"}),b(i,{prop:"name",label:"甲方名称",width:"200"}),b(i,{prop:"quantity",label:"数量",width:"100"},{default:_(({row:e})=>[f("span",{class:U({"text-danger":e.quantity<=10})},j(e.quantity),3)]),_:1}),b(i,{prop:"unit",label:"单位",width:"80"})]),_:1},8,["data"])]),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-2056e90d"]]);export{I as default};
