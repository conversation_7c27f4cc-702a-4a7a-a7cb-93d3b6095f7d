var e=(e,a,l)=>new Promise((t,s)=>{var o=e=>{try{d(l.next(e))}catch(a){s(a)}},r=e=>{try{d(l.throw(e))}catch(a){s(a)}},d=e=>e.done?t(e.value):Promise.resolve(e.value).then(o,r);d((l=l.apply(e,a)).next())});import{l as a,_ as l,r as t,q as s,y as o,z as r,R as d,J as n,K as i,I as u,aT as c,av as p,aD as m,x as f,O as y,P as _}from"./vue-vendor-fc5a6493.js";import{E as v,e as g}from"./element-plus-7917fd46.js";import{_ as b}from"./_plugin-vue_export-helper-1b428a4d.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const h={class:"page-container"},N={class:"search-form"},w={class:"action-buttons"},k={class:"table-container"},V={class:"pagination-container"},C={class:"order-info"},D={class:"info-item"},T={class:"value"},P={class:"info-item"},W={class:"value"},x={class:"info-item"},z={class:"value"},U={class:"info-item"},S={class:"value"},j={class:"info-item"},A={class:"value"},J={class:"info-item"},O={class:"value"},H={class:"info-item"},Y={class:"value"},E={class:"info-item"},I={class:"value"},q={class:"info-item"},K={class:"value"},L={class:"info-item"},R={class:"value"},$={class:"master-selection"},B={class:"remarks-section"},F={class:"dialog-footer"},G=b(a({__name:"OrderList",setup(a){const b=c(),G=l({orderNo:"",customerName:"",address:"",phone:"",contactPerson:"",orderType:"",status:"",assignedWorker:""}),M=t([]),Q=t(!1),X=t([]),Z=l({page:1,pageSize:20,total:0}),ee=t(!1),ae=l({orderNo:"",customerName:"",userNo:"",communityName:"",building:"",roomNo:"",phone:"",contactPerson:"",orderType:"",status:"",assignedWorker:"",estimatedDays:1,startDate:"",remark:""}),le=t(""),te=t([{id:1,name:"李师傅",workType:"电工",dailyWage:300,status:"active",taskCount:2},{id:2,name:"王师傅",workType:"水工",dailyWage:280,status:"active",taskCount:1},{id:3,name:"张师傅",workType:"安装工",dailyWage:320,status:"active",taskCount:3},{id:4,name:"赵师傅",workType:"电工",dailyWage:300,status:"active",taskCount:0}]),se=()=>{Z.page=1,ve()},oe=()=>{Object.assign(G,{orderNo:"",customerName:"",address:"",phone:"",contactPerson:"",orderType:"",status:"",assignedWorker:""}),se()},re=e=>{X.value=e},de=()=>{v.info("新增订单功能待实现")},ne=()=>{v.success("搜索师傅")},ie=()=>{v.success("保存成功")},ue=()=>{0!==X.value.length?v.info("批量分派功能待实现"):v.warning("请选择要分派的订单")},ce=a=>e(this,null,function*(){try{yield g.confirm("确定要删除该订单吗？","提示",{type:"warning"}),v.success("删除成功"),ve()}catch(e){}}),pe=()=>{v.info("导出功能待实现")},me=()=>e(this,null,function*(){ae.assignedWorker?(v.success("分派成功"),ee.value=!1,ve()):v.warning("请选择分派师傅")}),fe=()=>{v.success("开始打印")},ye=e=>{Z.pageSize=e,ve()},_e=e=>{Z.page=e,ve()},ve=()=>e(this,null,function*(){Q.value=!0;try{yield new Promise(e=>setTimeout(e,1e3)),M.value=[{id:1,orderNo:"JD202401001",customerName:"张先生",userNo:"YH001234",communityName:"阳光小区",building:"1栋",roomNo:"101",address:"阳光小区1栋101",phone:"138****1234",contactPerson:"张先生",orderType:"水电安装",status:"待分派",assignedWorker:"",totalAmount:1500,estimatedDays:3,startDate:"",endDate:""},{id:2,orderNo:"JD202401002",customerName:"李女士",userNo:"YH001235",communityName:"花园小区",building:"2栋",roomNo:"202",address:"花园小区2栋202",phone:"139****5678",contactPerson:"李女士",orderType:"一次挂表",status:"待分派",assignedWorker:"",totalAmount:800,estimatedDays:2,startDate:"",endDate:""},{id:3,orderNo:"JD202401003",customerName:"王先生",userNo:"YH001236",communityName:"商业街",building:"3号楼",roomNo:"303",address:"商业街3号楼303",phone:"137****9012",contactPerson:"王先生",orderType:"二次安装",status:"待分派",assignedWorker:"",totalAmount:1200,estimatedDays:4,startDate:"",endDate:""},{id:4,orderNo:"JD202401004",customerName:"赵女士",userNo:"YH001237",communityName:"住宅区",building:"4栋",roomNo:"404",address:"住宅区4栋404",phone:"136****3456",contactPerson:"赵女士",orderType:"售后",status:"待分派",assignedWorker:"",totalAmount:600,estimatedDays:1,startDate:"",endDate:""}],Z.total=156}catch(e){v.error("加载数据失败")}finally{Q.value=!1}});return s(()=>{ve()}),(e,a)=>{const l=p("el-input"),t=p("el-form-item"),s=p("el-option"),c=p("el-select"),g=p("el-button"),X=p("el-form"),ve=p("el-table-column"),ge=p("el-tag"),be=p("el-table"),he=p("el-pagination"),Ne=p("el-col"),we=p("el-row"),ke=p("el-dialog"),Ve=m("loading");return f(),o("div",h,[r("div",N,[d(X,{model:G,inline:""},{default:n(()=>[d(t,{label:"订单号"},{default:n(()=>[d(l,{modelValue:G.orderNo,"onUpdate:modelValue":a[0]||(a[0]=e=>G.orderNo=e),placeholder:"请输入甲方订单号",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),d(t,{label:"户名"},{default:n(()=>[d(l,{modelValue:G.customerName,"onUpdate:modelValue":a[1]||(a[1]=e=>G.customerName=e),placeholder:"请输入户名",clearable:"",style:{width:"150px"}},null,8,["modelValue"])]),_:1}),d(t,{label:"地址"},{default:n(()=>[d(l,{modelValue:G.address,"onUpdate:modelValue":a[2]||(a[2]=e=>G.address=e),placeholder:"请输入地址",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),d(t,{label:"电话"},{default:n(()=>[d(l,{modelValue:G.phone,"onUpdate:modelValue":a[3]||(a[3]=e=>G.phone=e),placeholder:"请输入电话",clearable:"",style:{width:"150px"}},null,8,["modelValue"])]),_:1}),d(t,{label:"联系人"},{default:n(()=>[d(l,{modelValue:G.contactPerson,"onUpdate:modelValue":a[4]||(a[4]=e=>G.contactPerson=e),placeholder:"请输入联系人",clearable:"",style:{width:"120px"}},null,8,["modelValue"])]),_:1}),d(t,{label:"订单分类"},{default:n(()=>[d(c,{modelValue:G.orderType,"onUpdate:modelValue":a[5]||(a[5]=e=>G.orderType=e),placeholder:"请选择分类",clearable:""},{default:n(()=>[d(s,{label:"安装",value:"安装"}),d(s,{label:"维修",value:"维修"}),d(s,{label:"改造",value:"改造"})]),_:1},8,["modelValue"])]),_:1}),d(t,{label:"订单状态"},{default:n(()=>[d(c,{modelValue:G.status,"onUpdate:modelValue":a[6]||(a[6]=e=>G.status=e),placeholder:"请选择状态",clearable:""},{default:n(()=>[d(s,{label:"待分派",value:"待分派"}),d(s,{label:"进行中",value:"进行中"}),d(s,{label:"已完成",value:"已完成"}),d(s,{label:"已取消",value:"已取消"})]),_:1},8,["modelValue"])]),_:1}),d(t,{label:"跟进师傅"},{default:n(()=>[d(l,{modelValue:G.assignedWorker,"onUpdate:modelValue":a[7]||(a[7]=e=>G.assignedWorker=e),placeholder:"请输入跟进师傅",clearable:"",style:{width:"120px"}},null,8,["modelValue"])]),_:1}),d(t,null,{default:n(()=>[d(g,{type:"primary",onClick:se},{default:n(()=>a[14]||(a[14]=[y("搜索",-1)])),_:1,__:[14]}),d(g,{onClick:oe},{default:n(()=>a[15]||(a[15]=[y("重置",-1)])),_:1,__:[15]})]),_:1})]),_:1},8,["model"])]),r("div",w,[d(g,{type:"primary",onClick:de},{default:n(()=>a[16]||(a[16]=[y("新增订单",-1)])),_:1,__:[16]}),d(g,{type:"success",onClick:ue},{default:n(()=>a[17]||(a[17]=[y("批量分派",-1)])),_:1,__:[17]}),d(g,{type:"warning",onClick:pe},{default:n(()=>a[18]||(a[18]=[y("导出Excel",-1)])),_:1,__:[18]})]),r("div",k,[i((f(),u(be,{data:M.value,border:"",stripe:"",onSelectionChange:re},{default:n(()=>[d(ve,{type:"selection",width:"55"}),d(ve,{prop:"orderNo",label:"甲方订单号",width:"150"}),d(ve,{prop:"customerName",label:"户名",width:"120"}),d(ve,{prop:"address",label:"地址","min-width":"200","show-overflow-tooltip":""}),d(ve,{prop:"phone",label:"电话",width:"120"}),d(ve,{prop:"contactPerson",label:"联系人",width:"100"}),d(ve,{prop:"orderType",label:"订单分类",width:"100"},{default:n(({row:e})=>{return[d(ge,{type:(a=e.orderType,{"安装":"primary","维修":"warning","改造":"success","水电安装":"primary","一次挂表":"success","二次安装":"warning","售后":"info"}[a]||"info")},{default:n(()=>[y(_(e.orderType),1)]),_:2},1032,["type"])];var a}),_:1}),d(ve,{prop:"status",label:"订单状态",width:"100"},{default:n(({row:e})=>{return[d(ge,{type:(a=e.status,{"待分派":"info","进行中":"warning","已完成":"success","已取消":"danger"}[a]||"info")},{default:n(()=>[y(_(e.status),1)]),_:2},1032,["type"])];var a}),_:1}),d(ve,{prop:"assignedWorker",label:"跟进师傅",width:"100"}),d(ve,{prop:"totalAmount",label:"总金额",width:"120"},{default:n(({row:e})=>[y(" ¥"+_(e.totalAmount),1)]),_:1}),d(ve,{prop:"estimatedDays",label:"预计用时",width:"100"},{default:n(({row:e})=>[y(_(e.estimatedDays)+"天 ",1)]),_:1}),d(ve,{prop:"startDate",label:"开始时间",width:"120"}),d(ve,{prop:"endDate",label:"完成时间",width:"120"}),d(ve,{label:"操作",width:"280",fixed:"right"},{default:n(({row:e})=>[d(g,{type:"primary",size:"small",onClick:e=>{v.info("编辑订单功能待实现")}},{default:n(()=>a[19]||(a[19]=[y("编辑",-1)])),_:2,__:[19]},1032,["onClick"]),d(g,{type:"info",size:"small",onClick:e=>{v.info("查看订单详情功能待实现")}},{default:n(()=>a[20]||(a[20]=[y("查看",-1)])),_:2,__:[20]},1032,["onClick"]),d(g,{type:"success",size:"small",onClick:a=>(e=>{var a,l,t,s,o;Object.assign(ae,{orderNo:e.orderNo,customerName:e.customerName,userNo:e.userNo||"YH"+e.orderNo.slice(-6),communityName:e.communityName||(null==(a=e.address)?void 0:a.split("区")[0])+"区",building:e.building||(null==(t=null==(l=e.address)?void 0:l.split("区")[1])?void 0:t.split("号")[0])+"栋",roomNo:e.roomNo||(null==(o=null==(s=e.address)?void 0:s.split("号")[1])?void 0:o.split("号")[0])||"101",phone:e.phone,contactPerson:e.contactPerson,orderType:e.orderType,status:e.status,assignedWorker:"",estimatedDays:1,startDate:"",remark:""}),ee.value=!0})(e)},{default:n(()=>a[21]||(a[21]=[y("分派",-1)])),_:2,__:[21]},1032,["onClick"]),d(g,{type:"warning",size:"small",onClick:a=>(e=>{const a={orderNo:e.orderNo,customerName:e.customerName,userNo:e.userNo,communityName:e.communityName,building:e.building,roomNo:e.roomNo,address:e.address,phone:e.phone,contactPerson:e.contactPerson,orderType:e.orderType,status:e.status,assignedWorker:e.assignedWorker,totalAmount:e.totalAmount,estimatedDays:e.estimatedDays,startDate:e.startDate,endDate:e.endDate};b.push({name:"OrderExecute",query:{orderInfo:JSON.stringify(a)}})})(e)},{default:n(()=>a[22]||(a[22]=[y("执行",-1)])),_:2,__:[22]},1032,["onClick"]),d(g,{type:"danger",size:"small",onClick:e=>ce()},{default:n(()=>a[23]||(a[23]=[y("删除",-1)])),_:2,__:[23]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Ve,Q.value]]),r("div",V,[d(he,{"current-page":Z.page,"onUpdate:currentPage":a[8]||(a[8]=e=>Z.page=e),"page-size":Z.pageSize,"onUpdate:pageSize":a[9]||(a[9]=e=>Z.pageSize=e),total:Z.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:ye,onCurrentChange:_e},null,8,["current-page","page-size","total"])])]),d(ke,{modelValue:ee.value,"onUpdate:modelValue":a[13]||(a[13]=e=>ee.value=e),title:"订单分派",width:"900px",center:""},{footer:n(()=>[r("div",F,[d(g,{type:"primary",onClick:ie},{default:n(()=>a[37]||(a[37]=[y("保存",-1)])),_:1,__:[37]}),d(g,{type:"success",onClick:me},{default:n(()=>a[38]||(a[38]=[y("提交",-1)])),_:1,__:[38]}),d(g,{onClick:fe},{default:n(()=>a[39]||(a[39]=[y("打印",-1)])),_:1,__:[39]}),d(g,{onClick:a[12]||(a[12]=e=>ee.value=!1)},{default:n(()=>a[40]||(a[40]=[y("取消",-1)])),_:1,__:[40]})])]),default:n(()=>[r("div",C,[d(we,{gutter:20},{default:n(()=>[d(Ne,{span:12},{default:n(()=>[r("div",D,[a[24]||(a[24]=r("span",{class:"label"},"甲方订单号:",-1)),r("span",T,_(ae.orderNo),1)])]),_:1}),d(Ne,{span:12},{default:n(()=>[r("div",P,[a[25]||(a[25]=r("span",{class:"label"},"户名:",-1)),r("span",W,_(ae.customerName),1)])]),_:1}),d(Ne,{span:12},{default:n(()=>[r("div",x,[a[26]||(a[26]=r("span",{class:"label"},"用户编号:",-1)),r("span",z,_(ae.userNo),1)])]),_:1}),d(Ne,{span:12},{default:n(()=>[r("div",U,[a[27]||(a[27]=r("span",{class:"label"},"小区名称:",-1)),r("span",S,_(ae.communityName),1)])]),_:1}),d(Ne,{span:12},{default:n(()=>[r("div",j,[a[28]||(a[28]=r("span",{class:"label"},"楼橦:",-1)),r("span",A,_(ae.building),1)])]),_:1}),d(Ne,{span:12},{default:n(()=>[r("div",J,[a[29]||(a[29]=r("span",{class:"label"},"房号:",-1)),r("span",O,_(ae.roomNo),1)])]),_:1}),d(Ne,{span:12},{default:n(()=>[r("div",H,[a[30]||(a[30]=r("span",{class:"label"},"电话:",-1)),r("span",Y,_(ae.phone),1)])]),_:1}),d(Ne,{span:12},{default:n(()=>[r("div",E,[a[31]||(a[31]=r("span",{class:"label"},"联系人:",-1)),r("span",I,_(ae.contactPerson),1)])]),_:1}),d(Ne,{span:12},{default:n(()=>[r("div",q,[a[32]||(a[32]=r("span",{class:"label"},"订单分类:",-1)),r("span",K,_(ae.orderType),1)])]),_:1}),d(Ne,{span:12},{default:n(()=>[r("div",L,[a[33]||(a[33]=r("span",{class:"label"},"订单状态:",-1)),r("span",R,_(ae.status),1)])]),_:1})]),_:1})]),r("div",$,[a[36]||(a[36]=r("div",{class:"section-title"},"选择师傅",-1)),d(we,{gutter:20,style:{"margin-bottom":"20px"}},{default:n(()=>[d(Ne,{span:18},{default:n(()=>[d(l,{modelValue:le.value,"onUpdate:modelValue":a[10]||(a[10]=e=>le.value=e),placeholder:"请输入搜索关键词",clearable:""},null,8,["modelValue"])]),_:1}),d(Ne,{span:6},{default:n(()=>[d(g,{type:"primary",icon:"Search",onClick:ne},{default:n(()=>a[34]||(a[34]=[y("搜索",-1)])),_:1,__:[34]})]),_:1})]),_:1}),d(be,{data:te.value,border:"",height:"300",class:"master-table"},{default:n(()=>[d(ve,{prop:"name",label:"师傅姓名",width:"100"}),d(ve,{prop:"workType",label:"工种",width:"120"}),d(ve,{prop:"dailyWage",label:"工价(元/天)",width:"120"}),d(ve,{prop:"status",label:"状态",width:"100"},{default:n(e=>[d(ge,{type:"active"===e.row.status?"success":"info"},{default:n(()=>[y(_("active"===e.row.status?"在职":"离职"),1)]),_:2},1032,["type"])]),_:1}),d(ve,{prop:"taskCount",label:"当前任务数",width:"120"}),d(ve,{label:"操作",width:"100",fixed:"right"},{default:n(e=>[d(g,{type:"primary",onClick:a=>{return l=e.row,ae.assignedWorker=l.id,void v.success(`已选择师傅: ${l.name}`);var l}},{default:n(()=>a[35]||(a[35]=[y("选择",-1)])),_:2,__:[35]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),r("div",B,[d(l,{modelValue:ae.remark,"onUpdate:modelValue":a[11]||(a[11]=e=>ae.remark=e),type:"textarea",rows:3,placeholder:"请输入备注"},null,8,["modelValue"])])])]),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-bf6cb328"]]);export{G as default};
