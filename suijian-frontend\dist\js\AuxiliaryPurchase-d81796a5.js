var e=Object.defineProperty,a=Object.defineProperties,l=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,i=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable,u=(a,l,t)=>l in a?e(a,l,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[l]=t;import{E as d}from"./element-plus-ad78a7bf.js";import{l as s,_ as r,r as n,y as p,R as c,J as m,av as _,x as f,z as b,Q as y,aa as h,O as v,P as V,I as w}from"./vue-vendor-fc5a6493.js";import{_ as k}from"./_plugin-vue_export-helper-1b428a4d.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const C={class:"auxiliary-purchase-container"},g={style:{"margin-top":"15px"}},M={class:"add-material-actions"},D={class:"statistic-item"},P={class:"statistic-item"},Y={class:"statistic-item"},x={class:"total-amount"},U={class:"form-actions"},j=k(s({__name:"AuxiliaryPurchase",setup(e){const s=r({purchaseDate:"2024-01-15",applicant:"张三",department:"仓库部",supplier:1,estimatedArrivalDate:"2024-01-20",purchaseDescription:"",remarks:""}),k=n([{id:1,name:"华强电子"},{id:2,name:"立创电子"},{id:3,name:"得捷电子"},{id:4,name:"贸泽电子"}]),j=n([{id:1,materialCode:"FL001",materialName:"螺丝",model:"M3",specification:"M3*10",unit:"个",quantity:100,unitPrice:"0.50",subtotal:"50.00"},{id:2,materialCode:"FL002",materialName:"螺母",model:"M3",specification:"M3",unit:"个",quantity:100,unitPrice:"0.30",subtotal:"30.00"},{id:3,materialCode:"FL003",materialName:"垫片",model:"M3",specification:"M3",unit:"个",quantity:100,unitPrice:"0.10",subtotal:"10.00"},{id:4,materialCode:"FL004",materialName:"胶带",model:"T-10",specification:"10mm*10m",unit:"卷",quantity:20,unitPrice:"5.00",subtotal:"100.00"},{id:5,materialCode:"FL005",materialName:"扎带",model:"ZD-100",specification:"100mm",unit:"包",quantity:10,unitPrice:"15.00",subtotal:"150.00"}]),N=n([{id:1,materialCode:"FL001",materialName:"螺丝",model:"M3",specification:"M3*10",unit:"个",stockQuantity:500},{id:2,materialCode:"FL002",materialName:"螺母",model:"M3",specification:"M3",unit:"个",stockQuantity:400},{id:3,materialCode:"FL003",materialName:"垫片",model:"M3",specification:"M3",unit:"个",stockQuantity:600},{id:4,materialCode:"FL006",materialName:"标签",model:"BQ-50",specification:"50*30mm",unit:"张",stockQuantity:1e3}]),q=r({materialTypes:5,totalQuantity:330,totalAmount:"340.00"}),L=n(!1),O=n(""),Q=()=>{L.value=!0},F=()=>{d.success("搜索物料")},A=e=>{var s;j.value.some(a=>a.id===e.id)?d.warning("该物料已添加"):(j.value.push((s=((e,a)=>{for(var l in a||(a={}))i.call(a,l)&&u(e,l,a[l]);if(t)for(var l of t(a))o.call(a,l)&&u(e,l,a[l]);return e})({},e),a(s,l({quantity:1,unitPrice:"0.00",subtotal:"0.00"})))),L.value=!1,d.success("添加成功"),I())},T=()=>{d.success("添加物料")},I=()=>{q.materialTypes=j.value.length,q.totalQuantity=j.value.reduce((e,a)=>e+a.quantity,0)},E=()=>{d.success("保存成功")},S=()=>{d.success("开始打印")},z=()=>{d.success("提交成功")},B=()=>{d.info("已取消")};return(e,a)=>{const l=_("el-col"),t=_("el-form-item"),i=_("el-date-picker"),o=_("el-input"),u=_("el-option"),r=_("el-select"),n=_("el-row"),G=_("el-table-column"),J=_("el-input-number"),R=_("el-button"),Z=_("el-table"),$=_("el-dialog"),H=_("el-card");return f(),p("div",C,[c(H,{class:"main-card"},{header:m(()=>a[10]||(a[10]=[b("div",{class:"card-header"},[b("span",null,"辅料采购")],-1)])),default:m(()=>[c(n,{gutter:20,class:"form-section"},{default:m(()=>[c(l,{span:24},{default:m(()=>a[11]||(a[11]=[b("div",{class:"section-title"},"采购信息",-1)])),_:1,__:[11]}),c(l,{span:12},{default:m(()=>[c(t,{label:"采购单号:"},{default:m(()=>a[12]||(a[12]=[b("span",null,"CG20240115001",-1)])),_:1,__:[12]})]),_:1}),c(l,{span:12},{default:m(()=>[c(t,{label:"采购日期:"},{default:m(()=>[c(i,{modelValue:s.purchaseDate,"onUpdate:modelValue":a[0]||(a[0]=e=>s.purchaseDate=e),type:"date",placeholder:"请选择采购日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),c(l,{span:12},{default:m(()=>[c(t,{label:"申请人:"},{default:m(()=>[c(o,{modelValue:s.applicant,"onUpdate:modelValue":a[1]||(a[1]=e=>s.applicant=e),placeholder:"请输入申请人"},null,8,["modelValue"])]),_:1})]),_:1}),c(l,{span:12},{default:m(()=>[c(t,{label:"申请部门:"},{default:m(()=>[c(o,{modelValue:s.department,"onUpdate:modelValue":a[2]||(a[2]=e=>s.department=e),placeholder:"请输入申请部门"},null,8,["modelValue"])]),_:1})]),_:1}),c(l,{span:12},{default:m(()=>[c(t,{label:"供应商:"},{default:m(()=>[c(r,{modelValue:s.supplier,"onUpdate:modelValue":a[3]||(a[3]=e=>s.supplier=e),placeholder:"请选择供应商",style:{width:"100%"}},{default:m(()=>[(f(!0),p(y,null,h(k.value,e=>(f(),w(u,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),c(l,{span:12},{default:m(()=>[c(t,{label:"预计到货日期:"},{default:m(()=>[c(i,{modelValue:s.estimatedArrivalDate,"onUpdate:modelValue":a[4]||(a[4]=e=>s.estimatedArrivalDate=e),type:"date",placeholder:"请选择预计到货日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),c(n,{gutter:20,class:"form-section"},{default:m(()=>[c(l,{span:24},{default:m(()=>a[13]||(a[13]=[b("div",{class:"section-title"},"采购明细",-1)])),_:1,__:[13]}),c(l,{span:24},{default:m(()=>[c(Z,{data:j.value,border:"",class:"purchase-table"},{default:m(()=>[c(G,{type:"index",label:"序号",width:"60"}),c(G,{prop:"materialCode",label:"公司物料编码",width:"120"}),c(G,{prop:"materialName",label:"物料名称",width:"120"}),c(G,{prop:"model",label:"型号",width:"100"}),c(G,{prop:"specification",label:"规格",width:"100"}),c(G,{prop:"unit",label:"单位",width:"80"}),c(G,{prop:"quantity",label:"采购数量"},{default:m(e=>[c(J,{modelValue:e.row.quantity,"onUpdate:modelValue":a=>e.row.quantity=a,min:1,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),c(G,{prop:"unitPrice",label:"单价(元)"},{default:m(e=>[c(o,{modelValue:e.row.unitPrice,"onUpdate:modelValue":a=>e.row.unitPrice=a,placeholder:"请输入单价"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),c(G,{prop:"subtotal",label:"小计(元)",width:"100"}),c(G,{label:"操作",width:"80",fixed:"right"},{default:m(e=>[c(R,{type:"danger",link:"",onClick:a=>{return l=e.$index,j.value.splice(l,1),d.success("删除成功"),void I();var l}},{default:m(()=>a[14]||(a[14]=[v("删除",-1)])),_:2,__:[14]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),b("div",g,[c(R,{type:"primary",icon:"Plus",onClick:Q},{default:m(()=>a[15]||(a[15]=[v("添加物料",-1)])),_:1,__:[15]})])]),_:1})]),_:1}),c(n,{gutter:20,class:"form-section"},{default:m(()=>[c(l,{span:24},{default:m(()=>a[16]||(a[16]=[b("div",{class:"section-title"},"添加物料",-1)])),_:1,__:[16]}),c(l,{span:24},{default:m(()=>[b("div",M,[c(R,{type:"primary",onClick:Q},{default:m(()=>a[17]||(a[17]=[v("选择物料",-1)])),_:1,__:[17]}),c(R,{type:"success",onClick:T},{default:m(()=>a[18]||(a[18]=[v("添加",-1)])),_:1,__:[18]})])]),_:1})]),_:1}),c($,{modelValue:L.value,"onUpdate:modelValue":a[7]||(a[7]=e=>L.value=e),title:"选择物料",width:"800"},{footer:m(()=>[c(R,{onClick:a[6]||(a[6]=e=>L.value=!1)},{default:m(()=>a[21]||(a[21]=[v("取消",-1)])),_:1,__:[21]})]),default:m(()=>[c(n,{gutter:20,style:{"margin-bottom":"20px"}},{default:m(()=>[c(l,{span:18},{default:m(()=>[c(o,{modelValue:O.value,"onUpdate:modelValue":a[5]||(a[5]=e=>O.value=e),placeholder:"请输入搜索关键词",clearable:""},null,8,["modelValue"])]),_:1}),c(l,{span:6},{default:m(()=>[c(R,{type:"primary",icon:"Search",onClick:F},{default:m(()=>a[19]||(a[19]=[v("搜索",-1)])),_:1,__:[19]})]),_:1})]),_:1}),c(Z,{data:N.value,border:"",height:"400"},{default:m(()=>[c(G,{prop:"materialCode",label:"公司物料编码",width:"120"}),c(G,{prop:"materialName",label:"物料名称",width:"120"}),c(G,{prop:"model",label:"型号",width:"100"}),c(G,{prop:"specification",label:"规格",width:"100"}),c(G,{prop:"unit",label:"单位",width:"80"}),c(G,{prop:"stockQuantity",label:"库存",width:"80"}),c(G,{label:"操作",width:"80",fixed:"right"},{default:m(e=>[c(R,{type:"primary",link:"",onClick:a=>A(e.row)},{default:m(()=>a[20]||(a[20]=[v("选择",-1)])),_:2,__:[20]},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1},8,["modelValue"]),c(n,{gutter:20,class:"form-section"},{default:m(()=>[c(l,{span:24},{default:m(()=>a[22]||(a[22]=[b("div",{class:"section-title"},"采购统计",-1)])),_:1,__:[22]}),c(l,{span:24},{default:m(()=>[c(H,{class:"statistics-card"},{default:m(()=>[c(n,{gutter:20},{default:m(()=>[c(l,{span:12},{default:m(()=>[b("div",D,[a[23]||(a[23]=b("span",{class:"label"},"采购物料种类:",-1)),b("span",null,V(q.materialTypes)+"种",1)])]),_:1}),c(l,{span:12},{default:m(()=>[b("div",P,[a[24]||(a[24]=b("span",{class:"label"},"采购物料总数:",-1)),b("span",null,V(q.totalQuantity)+"件",1)])]),_:1}),c(l,{span:12},{default:m(()=>[b("div",Y,[a[25]||(a[25]=b("span",{class:"label"},"采购总金额:",-1)),b("span",x,"¥"+V(q.totalAmount),1)])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),c(n,{gutter:20,class:"form-section"},{default:m(()=>[c(l,{span:24},{default:m(()=>a[26]||(a[26]=[b("div",{class:"section-title"},"备注信息",-1)])),_:1,__:[26]}),c(l,{span:24},{default:m(()=>[c(t,{label:"采购说明:"},{default:m(()=>[c(o,{modelValue:s.purchaseDescription,"onUpdate:modelValue":a[8]||(a[8]=e=>s.purchaseDescription=e),type:"textarea",rows:3,placeholder:"请输入采购说明"},null,8,["modelValue"])]),_:1})]),_:1}),c(l,{span:24},{default:m(()=>[c(t,{label:"备注:"},{default:m(()=>[c(o,{modelValue:s.remarks,"onUpdate:modelValue":a[9]||(a[9]=e=>s.remarks=e),type:"textarea",rows:2,placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),b("div",U,[c(R,{type:"primary",onClick:E},{default:m(()=>a[27]||(a[27]=[v("保存",-1)])),_:1,__:[27]}),c(R,{onClick:S},{default:m(()=>a[28]||(a[28]=[v("打印",-1)])),_:1,__:[28]}),c(R,{type:"success",onClick:z},{default:m(()=>a[29]||(a[29]=[v("提交",-1)])),_:1,__:[29]}),c(R,{onClick:B},{default:m(()=>a[30]||(a[30]=[v("取消",-1)])),_:1,__:[30]})])]),_:1})])}}}),[["__scopeId","data-v-eaa231e1"]]);export{j as default};
