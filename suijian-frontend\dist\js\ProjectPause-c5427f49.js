import{t as e,d as a,v as l,E as s}from"./element-plus-ad78a7bf.js";import{l as t,r as u,_ as d,y as n,R as o,J as r,av as p,x as _,z as m,u as i,O as f,Q as c,aa as g,I as v,P as b,M as V}from"./vue-vendor-fc5a6493.js";import{_ as y}from"./_plugin-vue_export-helper-1b428a4d.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const h={class:"project-pause-container"},x={class:"section-title"},j={class:"section-title"},w={class:"section-title"},D={class:"form-actions"},R=y(t({__name:"ProjectPause",setup(t){const y=u(),R=u([{id:1,name:"阳光小区A栋",status:"在建",progress:65,startDate:"2024-01-01"},{id:2,name:"花园广场项目",status:"在建",progress:45,startDate:"2024-01-05"},{id:3,name:"绿城花园D区",status:"在建",progress:75,startDate:"2023-12-01"},{id:4,name:"商业中心B区",status:"已完成",progress:100,startDate:"2023-10-01"},{id:5,name:"工业园区C栋",status:"未开始",progress:0,startDate:""}]),U=u(null),k=d({projectId:"",pauseReason:"",pauseReasonTags:[],estimatedResumeTime:"",pauseArrangement:"",detailedExplanation:"",followUpPlan:""}),Y={projectId:[{required:!0,message:"请选择工程",trigger:"change"}],pauseReason:[{required:!0,message:"请输入暂停原因",trigger:"blur"}]},I=e=>{U.value=R.value.find(a=>a.id===Number(e))},P=()=>{s.success("保存成功")},T=()=>{s.success("提交成功")},C=()=>{s.info("已取消")};return(s,t)=>{const u=p("el-icon"),d=p("el-col"),M=p("el-option"),A=p("el-select"),E=p("el-form-item"),q=p("el-row"),$=p("el-input"),z=p("el-tag"),B=p("el-progress"),J=p("el-checkbox"),L=p("el-checkbox-group"),N=p("el-date-picker"),O=p("el-card"),Q=p("el-button"),F=p("el-form");return _(),n("div",h,[o(O,{class:"main-card"},{default:r(()=>[o(F,{model:k,rules:Y,ref_key:"formRef",ref:y,"label-width":"120px"},{default:r(()=>[o(q,{gutter:20,class:"form-section"},{default:r(()=>[o(d,{span:24},{default:r(()=>[m("div",x,[o(u,null,{default:r(()=>[o(i(e))]),_:1}),t[9]||(t[9]=f(" 工程选择 ",-1))])]),_:1}),o(d,{span:24},{default:r(()=>[o(E,{label:"选择工程:",prop:"projectId"},{default:r(()=>[o(A,{modelValue:k.projectId,"onUpdate:modelValue":t[0]||(t[0]=e=>k.projectId=e),placeholder:"请选择要暂停的工程",style:{width:"100%"},onChange:I},{default:r(()=>[(_(!0),n(c,null,g(R.value,e=>(_(),v(M,{key:e.id,label:`${e.name} (${e.status})`,value:e.id,disabled:"在建"!==e.status},null,8,["label","value","disabled"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),U.value?(_(),v(q,{key:0,gutter:20,class:"form-section"},{default:r(()=>[o(d,{span:24},{default:r(()=>[m("div",j,[o(u,null,{default:r(()=>[o(i(a))]),_:1}),t[10]||(t[10]=f(" 当前工程信息 ",-1))])]),_:1}),o(d,{span:12},{default:r(()=>[o(E,{label:"工程名称:"},{default:r(()=>[o($,{modelValue:U.value.name,"onUpdate:modelValue":t[1]||(t[1]=e=>U.value.name=e),readonly:""},null,8,["modelValue"])]),_:1})]),_:1}),o(d,{span:12},{default:r(()=>[o(E,{label:"当前状态:"},{default:r(()=>{return[o(z,{type:(e=U.value.status,{"未开始":"info","在建":"warning","暂停":"danger","已完成":"success"}[e]||"info")},{default:r(()=>[f(b(U.value.status),1)]),_:1},8,["type"])];var e}),_:1})]),_:1}),o(d,{span:12},{default:r(()=>[o(E,{label:"当前进度:"},{default:r(()=>[o(B,{percentage:U.value.progress},null,8,["percentage"])]),_:1})]),_:1}),o(d,{span:12},{default:r(()=>[o(E,{label:"开始时间:"},{default:r(()=>[o($,{modelValue:U.value.startDate,"onUpdate:modelValue":t[2]||(t[2]=e=>U.value.startDate=e),readonly:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})):V("",!0),U.value?(_(),v(q,{key:1,gutter:20,class:"form-section"},{default:r(()=>[o(d,{span:24},{default:r(()=>[m("div",w,[o(u,null,{default:r(()=>[o(i(l))]),_:1}),t[11]||(t[11]=f(" 暂停信息 ",-1))])]),_:1}),o(d,{span:24},{default:r(()=>[o(E,{label:"暂停原因:"},{default:r(()=>[o($,{modelValue:k.pauseReason,"onUpdate:modelValue":t[3]||(t[3]=e=>k.pauseReason=e),type:"textarea",rows:3,placeholder:"请输入暂停原因"},null,8,["modelValue"])]),_:1})]),_:1}),o(d,{span:24},{default:r(()=>[o(L,{modelValue:k.pauseReasonTags,"onUpdate:modelValue":t[4]||(t[4]=e=>k.pauseReasonTags=e)},{default:r(()=>[o(J,{label:"材料供应不足"},{default:r(()=>t[12]||(t[12]=[f("材料供应不足",-1)])),_:1,__:[12]}),o(J,{label:"人员不足"},{default:r(()=>t[13]||(t[13]=[f("人员不足",-1)])),_:1,__:[13]}),o(J,{label:"天气原因"},{default:r(()=>t[14]||(t[14]=[f("天气原因",-1)])),_:1,__:[14]}),o(J,{label:"客户要求"},{default:r(()=>t[15]||(t[15]=[f("客户要求",-1)])),_:1,__:[15]}),o(J,{label:"其他"},{default:r(()=>t[16]||(t[16]=[f("其他",-1)])),_:1,__:[16]})]),_:1},8,["modelValue"])]),_:1}),o(d,{span:12,style:{"margin-top":"20px"}},{default:r(()=>[o(E,{label:"预计恢复时间:"},{default:r(()=>[o(N,{modelValue:k.estimatedResumeTime,"onUpdate:modelValue":t[5]||(t[5]=e=>k.estimatedResumeTime=e),type:"date",placeholder:"请选择预计恢复时间",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),o(d,{span:24},{default:r(()=>[o(E,{label:"暂停期间安排:"},{default:r(()=>[o($,{modelValue:k.pauseArrangement,"onUpdate:modelValue":t[6]||(t[6]=e=>k.pauseArrangement=e),type:"textarea",rows:3,placeholder:"请输入暂停期间安排"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})):V("",!0),o(q,{gutter:20,class:"form-section"},{default:r(()=>[o(d,{span:24},{default:r(()=>t[17]||(t[17]=[m("div",{class:"section-title"},"暂停影响评估",-1)])),_:1,__:[17]}),o(d,{span:24},{default:r(()=>[o(O,{class:"assessment-card"},{default:r(()=>[o(q,{gutter:20},{default:r(()=>[o(d,{span:24},{default:r(()=>t[18]||(t[18]=[m("div",{class:"assessment-item"},[m("span",{class:"label"},"进度影响:"),m("span",null,"已完成65%，暂停后预计延期5天")],-1)])),_:1,__:[18]}),o(d,{span:24},{default:r(()=>t[19]||(t[19]=[m("div",{class:"assessment-item"},[m("span",{class:"label"},"成本影响:"),m("span",null,"暂停期间人员费用约¥3,200")],-1)])),_:1,__:[19]}),o(d,{span:24},{default:r(()=>t[20]||(t[20]=[m("div",{class:"assessment-item"},[m("span",{class:"label"},"物料影响:"),m("span",null,"已使用物料价值¥28,500")],-1)])),_:1,__:[20]}),o(d,{span:24},{default:r(()=>t[21]||(t[21]=[m("div",{class:"assessment-item"},[m("span",{class:"label"},"客户沟通:"),m("span",null,"已通知客户并获得确认")],-1)])),_:1,__:[21]})]),_:1})]),_:1})]),_:1})]),_:1}),o(q,{gutter:20,class:"form-section"},{default:r(()=>[o(d,{span:24},{default:r(()=>t[22]||(t[22]=[m("div",{class:"section-title"},"备注信息",-1)])),_:1,__:[22]}),o(d,{span:24},{default:r(()=>[o(E,{label:"详细说明:"},{default:r(()=>[o($,{modelValue:k.detailedExplanation,"onUpdate:modelValue":t[7]||(t[7]=e=>k.detailedExplanation=e),type:"textarea",rows:3,placeholder:"请输入详细说明"},null,8,["modelValue"])]),_:1})]),_:1}),o(d,{span:24},{default:r(()=>[o(E,{label:"后续处理计划:"},{default:r(()=>[o($,{modelValue:k.followUpPlan,"onUpdate:modelValue":t[8]||(t[8]=e=>k.followUpPlan=e),type:"textarea",rows:3,placeholder:"请输入后续处理计划"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),m("div",D,[o(Q,{type:"primary",onClick:P},{default:r(()=>t[23]||(t[23]=[f("保存",-1)])),_:1,__:[23]}),o(Q,{type:"success",onClick:T},{default:r(()=>t[24]||(t[24]=[f("提交",-1)])),_:1,__:[24]}),o(Q,{onClick:C},{default:r(()=>t[25]||(t[25]=[f("取消",-1)])),_:1,__:[25]})])]),_:1},8,["model"])]),_:1})])}}}),[["__scopeId","data-v-d24f6796"]]);export{R as default};
