<template>
  <div class="material-base-container">
    <el-card class="main-card">
      <template #header>
        <div class="card-header">
          <span>物料基础库</span>
        </div>
      </template>
      
      <!-- 搜索栏 -->
      <el-row :gutter="20" class="search-section">
        <el-col :span="6">
          <el-input v-model="searchForm.materialCode" placeholder="公司物料编码" clearable />
        </el-col>
        <el-col :span="6">
          <el-input v-model="searchForm.materialName" placeholder="物料名称" clearable />
        </el-col>
        <el-col :span="6">
          <el-select v-model="searchForm.category" placeholder="分类" clearable style="width: 100%">
            <el-option label="甲料" value="material" />
            <el-option label="商品" value="product" />
            <el-option label="辅料" value="auxiliary" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <div class="search-buttons">
            <el-button type="primary" icon="Search" @click="handleSearch">搜索</el-button>
            <el-button icon="Refresh" @click="handleReset">重置</el-button>
          </div>
        </el-col>
      </el-row>
      
      <!-- 物料列表 -->
      <el-table :data="materialList" border class="material-table" style="width: 100%; margin-top: 20px;">
        <el-table-column prop="materialCode" label="公司物料编码" min-width="120" />
        <el-table-column prop="partyCode" label="甲方编码" min-width="100">
          <template #default="scope">
            <el-button type="primary" link @click="showPartyCodes(scope.row)">显示</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="materialCategory" label="物料分类" min-width="100" />
        <el-table-column prop="materialName" label="物料名称" min-width="120" />
        <el-table-column prop="model" label="型号" min-width="100" />
        <el-table-column prop="specification" label="规格" min-width="100" />
        <el-table-column prop="unit" label="单位" min-width="80" />
        <el-table-column prop="createTime" label="创建时间" min-width="120" />
        <el-table-column label="操作" min-width="150" fixed="right">
          <template #default="scope">
            <el-button type="primary" link @click="editMaterial(scope.row)">修改</el-button>
            <el-button type="primary" link @click="viewDetail(scope.row)">详情</el-button>
            <el-button type="danger" link @click="deleteMaterial(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        class="pagination"
      />
      
      <!-- 操作按钮 -->
      <div class="form-actions">
        <el-button type="primary" icon="Plus" @click="addMaterial">新增物料</el-button>
        <el-button icon="Upload" @click="batchImport">批量导入</el-button>
        <el-button icon="Download" @click="exportExcel">导出Excel</el-button>
        <el-button icon="DataAnalysis" @click="showStatistics">统计分析</el-button>
      </div>
      
      <!-- 新增/修改物料弹窗 -->
      <el-dialog 
        v-model="materialDialogVisible" 
        :title="dialogTitle" 
        width="600"
        @close="handleDialogClose"
      >
        <el-form 
          ref="materialFormRef" 
          :model="currentMaterial" 
          :rules="materialRules" 
          label-width="120px"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="公司物料编码:" prop="materialCode">
                <el-input v-model="currentMaterial.materialCode" placeholder="请输入公司物料编码" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="物料分类:" prop="materialCategory">
                <el-select v-model="currentMaterial.materialCategory" placeholder="请选择物料分类" style="width: 100%">
                  <el-option label="甲料" value="甲料" />
                  <el-option label="商品" value="商品" />
                  <el-option label="辅料" value="辅料" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="物料名称:" prop="materialName">
                <el-input v-model="currentMaterial.materialName" placeholder="请输入物料名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="型号:" prop="model">
                <el-input v-model="currentMaterial.model" placeholder="请输入型号" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="规格:" prop="specification">
                <el-input v-model="currentMaterial.specification" placeholder="请输入规格" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="单位:" prop="unit">
                <el-input v-model="currentMaterial.unit" placeholder="请输入单位" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="甲方编码:" prop="partyCode">
                <el-input 
                  v-model="currentMaterial.partyCodeInput" 
                  placeholder="请输入甲方编码，多个编码用逗号分隔" 
                />
                <div class="party-code-explanation">
                  示例: JD001,JD002,JD003 (多个编码用逗号分隔)
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="备注:">
                <el-input
                  v-model="currentMaterial.remarks"
                  type="textarea"
                  :rows="2"
                  placeholder="请输入备注"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template #footer>
          <el-button @click="materialDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveMaterial">保存</el-button>
        </template>
      </el-dialog>
      
      <!-- 物料详情弹窗 -->
      <el-dialog v-model="detailDialogVisible" title="物料详情" width="600">
        <el-form :model="currentMaterial" label-width="120px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="公司物料编码:">
                <span>{{ currentMaterial.materialCode }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="物料分类:">
                <span>{{ currentMaterial.materialCategory }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="物料名称:">
                <span>{{ currentMaterial.materialName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="型号:">
                <span>{{ currentMaterial.model }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="规格:">
                <span>{{ currentMaterial.specification }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="单位:">
                <span>{{ currentMaterial.unit }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="甲方编码:">
                <div v-for="(code, index) in currentMaterial.partyCode" :key="index" class="party-code-item">
                  编码{{ index + 1 }}: {{ code }}
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="创建时间:">
                <span>{{ currentMaterial.createTime }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="更新时间:">
                <span>{{ currentMaterial.updateTime }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="备注:">
                <span>{{ currentMaterial.remarks }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template #footer>
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </template>
      </el-dialog>
      
      <!-- 甲方编码显示弹窗 -->
      <el-dialog v-model="partyCodeDialogVisible" title="甲方编码列表" width="500">
        <div v-for="(code, index) in currentPartyCodes" :key="index" class="party-code-item">
          编码{{ index + 1 }}: {{ code }}
        </div>
        <template #footer>
          <el-button @click="partyCodeDialogVisible = false">关闭</el-button>
        </template>
      </el-dialog>
      
      <!-- 批量导入弹窗 -->
      <el-dialog v-model="importDialogVisible" title="批量导入" width="500">
        <el-form label-width="100px">
          <el-form-item label="导入类型:">
            <span>物料基础库</span>
          </el-form-item>
          <el-form-item label="选择文件:">
            <el-upload
              class="upload-demo"
              action=""
              :auto-upload="false"
              :on-change="handleFileChange"
            >
              <el-button type="primary">选择文件</el-button>
            </el-upload>
            <div v-if="fileName">文件名: {{ fileName }}</div>
          </el-form-item>
          <el-form-item label="模板下载:">
            <el-button type="primary" link @click="downloadTemplate">下载模板</el-button>
          </el-form-item>
          <el-form-item label="导入说明:">
            <div class="import-instructions">
              <p>1. 请按照模板格式填写物料信息</p>
              <p>2. 公司物料编码为必填项且不能重复</p>
              <p>3. 物料分类: 甲料、商品、辅料</p>
              <p>4. 一个公司物料编码可对应多个甲方编码(用逗号分隔)</p>
              <p>5. 支持.xls、.xlsx格式文件</p>
            </div>
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="importMaterials">导入</el-button>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 搜索表单
const searchForm = reactive({
  materialCode: '',
  materialName: '',
  category: ''
})

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 236
})

// 物料列表
const materialList = ref([
  {
    id: 1,
    materialCode: 'WL001',
    partyCode: ['JD001', 'JD002'],
    materialCategory: '甲料',
    materialName: '电缆线',
    model: 'YJV-3*4',
    specification: '3*4mm²',
    unit: '米',
    createTime: '2023-01-15'
  },
  {
    id: 2,
    materialCode: 'WL002',
    partyCode: ['JD003'],
    materialCategory: '商品',
    materialName: '开关面板',
    model: 'KP-86',
    specification: '86型',
    unit: '个',
    createTime: '2023-01-15'
  },
  {
    id: 3,
    materialCode: 'WL003',
    partyCode: ['JD004'],
    materialCategory: '辅料',
    materialName: '电线',
    model: 'BV-2.5',
    specification: '2.5mm²',
    unit: '米',
    createTime: '2023-01-14'
  },
  {
    id: 4,
    materialCode: 'WL004',
    partyCode: ['JD005'],
    materialCategory: '甲料',
    materialName: '水管',
    model: 'PPR-20',
    specification: '20mm',
    unit: '米',
    createTime: '2023-01-14'
  },
  {
    id: 5,
    materialCode: 'WL005',
    partyCode: ['JD006'],
    materialCategory: '商品',
    materialName: '灯具',
    model: 'LED-12W',
    specification: '12W',
    unit: '个',
    createTime: '2023-01-13'
  }
])

// 当前物料
const currentMaterial = ref({
  id: 0,
  materialCode: '',
  partyCode: [] as string[],
  partyCodeInput: '',
  materialCategory: '',
  materialName: '',
  model: '',
  specification: '',
  unit: '',
  createTime: '',
  updateTime: '',
  remarks: ''
})

// 当前甲方编码
const currentPartyCodes = ref([] as string[])

// 文件名
const fileName = ref('')

// 弹窗控制
const materialDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const partyCodeDialogVisible = ref(false)
const importDialogVisible = ref(false)

// 表单引用
const materialFormRef = ref()

// 对话框标题
const dialogTitle = computed(() => {
  return currentMaterial.value.id ? '修改物料' : '新增物料'
})

// 表单验证规则
const materialRules = {
  materialCode: [
    { required: true, message: '请输入公司物料编码', trigger: 'blur' }
  ],
  materialCategory: [
    { required: true, message: '请选择物料分类', trigger: 'blur' }
  ],
  materialName: [
    { required: true, message: '请输入物料名称', trigger: 'blur' }
  ],
  unit: [
    { required: true, message: '请输入单位', trigger: 'blur' }
  ]
}

// 搜索
const handleSearch = () => {
  ElMessage.success('搜索成功')
  console.log('搜索条件:', searchForm)
}

// 重置
const handleReset = () => {
  searchForm.materialCode = ''
  searchForm.materialName = ''
  searchForm.category = ''
}

// 分页变化
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  console.log('每页条数:', val)
}

const handleCurrentChange = (val: number) => {
  pagination.currentPage = val
  console.log('当前页:', val)
}

// 显示甲方编码
const showPartyCodes = (row: any) => {
  currentPartyCodes.value = row.partyCode
  partyCodeDialogVisible.value = true
}

// 新增物料
const addMaterial = () => {
  // 重置表单
  currentMaterial.value = {
    id: 0,
    materialCode: '',
    partyCode: [],
    partyCodeInput: '',
    materialCategory: '',
    materialName: '',
    model: '',
    specification: '',
    unit: '',
    createTime: '',
    updateTime: '',
    remarks: ''
  }
  materialDialogVisible.value = true
}

// 修改物料
const editMaterial = (row: any) => {
  currentMaterial.value = { ...row }
  currentMaterial.value.partyCodeInput = row.partyCode.join(',')
  materialDialogVisible.value = true
}

// 查看详情
const viewDetail = (row: any) => {
  currentMaterial.value = { ...row }
  currentMaterial.value.createTime = '2023-01-15'
  currentMaterial.value.updateTime = '2024-01-15'
  detailDialogVisible.value = true
}

// 删除物料
const deleteMaterial = (row: any) => {
  ElMessageBox.confirm(
    `确定要删除物料 "${row.materialName}" 吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success('删除成功')
    console.log('删除物料:', row)
  }).catch(() => {
    // 用户取消删除
  })
}

// 保存物料
const saveMaterial = () => {
  if (!materialFormRef.value) return
  
  materialFormRef.value.validate((valid: boolean) => {
    if (valid) {
      ElMessage.success('保存成功')
      materialDialogVisible.value = false
      console.log('保存物料:', currentMaterial.value)
    }
  })
}

// 对话框关闭
const handleDialogClose = () => {
  if (materialFormRef.value) {
    materialFormRef.value.resetFields()
  }
}

// 批量导入
const batchImport = () => {
  importDialogVisible.value = true
}

// 文件变更
const handleFileChange = (file: any) => {
  fileName.value = file.name
}

// 下载模板
const downloadTemplate = () => {
  ElMessage.success('下载模板')
}

// 导入物料
const importMaterials = () => {
  ElMessage.success('导入成功')
  importDialogVisible.value = false
}

// 导出Excel
const exportExcel = () => {
  ElMessage.success('导出Excel')
}

// 统计分析
const showStatistics = () => {
  ElMessage.success('显示统计分析')
}
</script>

<style lang="scss" scoped>
.material-base-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
  
  .main-card {
    margin-bottom: 20px;
    
    .card-header {
      font-size: 18px;
      font-weight: bold;
      color: #303133;
    }
  }
  
  .search-section {
    margin-bottom: 15px;
    
    .search-buttons {
      display: flex;
      
      .el-button {
        margin-right: 10px;
      }
    }
  }
  
  .material-table {
    margin-top: 20px;
  }
  
  .pagination {
    margin-top: 20px;
    text-align: right;
  }
  
  .form-actions {
    margin-top: 20px;
    text-align: left;
    
    .el-button {
      margin-right: 10px;
    }
  }
  
  .party-code-explanation {
    color: #909399;
    font-size: 12px;
    margin-top: 5px;
  }
  
  .party-code-item {
    margin-bottom: 5px;
    padding: 3px 0;
    border-bottom: 1px solid #ebeef5;
  }
  
  .import-instructions {
    p {
      margin: 5px 0;
      color: #606266;
    }
  }
}
</style>