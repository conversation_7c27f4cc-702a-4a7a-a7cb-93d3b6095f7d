var e=Object.defineProperty,l=Object.getOwnPropertySymbols,a=Object.prototype.hasOwnProperty,t=Object.prototype.propertyIsEnumerable,o=(l,a,t)=>a in l?e(l,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):l[a]=t;import{E as u}from"./element-plus-7917fd46.js";import{l as s,r as d,c as n,_ as r,y as p,R as i,J as c,av as m,x as _,z as f,Q as v,aa as b,I as y,P as V,O as h,M as g}from"./vue-vendor-fc5a6493.js";import{_ as w}from"./_plugin-vue_export-helper-1b428a4d.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const N={class:"external-cost-container"},D={style:{float:"right",color:"#8492a6","font-size":"13px"}},j={style:{"margin-top":"15px"}},Y={class:"form-actions"},C=w(s({__name:"ExternalCost",setup(e){const s=[{value:"1",label:"阳光小区A栋",orderNo:"GC202401001"},{value:"2",label:"花园广场项目",orderNo:"GC202401002"},{value:"3",label:"商业中心B区",orderNo:"GC202401003"},{value:"4",label:"住宅楼C座",orderNo:"GC202401004"}],w=d("1"),C=n(()=>({1:{partyOrderNo:"GC202401001",projectName:"阳光小区A栋",projectAddress:"阳光小区A栋",projectStatus:"building",progress:"45%"},2:{partyOrderNo:"GC202401002",projectName:"花园广场项目",projectAddress:"花园广场B区",projectStatus:"notStarted",progress:"0%"},3:{partyOrderNo:"GC202401003",projectName:"商业中心B区",projectAddress:"商业街88号",projectStatus:"paused",progress:"65%"},4:{partyOrderNo:"GC202401004",projectName:"住宅楼C座",projectAddress:"住宅区C栋",projectStatus:"completed",progress:"100%"}}[w.value])),A=e=>{switch(e){case"notStarted":default:return"info";case"building":return"primary";case"paused":return"warning";case"completed":return"success"}},U=e=>{switch(e){case"notStarted":return"未开始";case"building":return"在建";case"paused":return"暂停";case"completed":return"完成";default:return""}},k=r({costDate:"",costName:"",costType:"",quantity:1,unitPrice:"",totalAmount:"",supplier:"",costDescription:"",invoiceNumber:"",invoiceDate:"",invoiceAmount:""}),P=d([{id:1,costDate:"2024-01-10",costName:"吊车租赁",costType:"equipmentRental",quantity:1,unitPrice:"¥2,500",totalAmount:"¥2,500",supplier:"蓝天设备"},{id:2,costDate:"2024-01-08",costName:"电力检测",costType:"inspection",quantity:1,unitPrice:"¥1,200",totalAmount:"¥1,200",supplier:"电力检测"},{id:3,costDate:"2024-01-05",costName:"防水工程",costType:"outsourcing",quantity:1,unitPrice:"¥8,000",totalAmount:"¥8,000",supplier:"防水公司"},{id:4,costDate:"2024-01-03",costName:"材料运输",costType:"transportation",quantity:1,unitPrice:"¥600",totalAmount:"¥600",supplier:"快捷物流"}]),q=d({id:0,costDate:"",costName:"",costType:"",quantity:1,unitPrice:"",totalAmount:"",supplier:"",remarks:""}),x=d(!1),M=()=>{u.success("添加成本项")},S=e=>{q.value=((e,u)=>{for(var s in u||(u={}))a.call(u,s)&&o(e,s,u[s]);if(l)for(var s of l(u))t.call(u,s)&&o(e,s,u[s]);return e})({},e),x.value=!0},O=()=>{u.success("保存成功"),x.value=!1},T=()=>{u.success("保存成功")},G=()=>{u.success("提交成功")},R=()=>{u.success("开始打印")},B=()=>{u.info("已取消")};return(e,l)=>{const a=m("el-col"),t=m("el-option"),o=m("el-select"),d=m("el-row"),n=m("el-form-item"),r=m("el-tag"),E=m("el-date-picker"),I=m("el-input"),z=m("el-input-number"),J=m("el-card"),Q=m("el-table-column"),$=m("el-button"),F=m("el-table"),H=m("el-form"),K=m("el-dialog");return _(),p("div",N,[i(J,{class:"main-card"},{header:c(()=>l[22]||(l[22]=[f("div",{class:"card-header"},[f("span",null,"外部成本")],-1)])),default:c(()=>[i(d,{gutter:20,class:"form-section"},{default:c(()=>[i(a,{span:24},{default:c(()=>l[23]||(l[23]=[f("div",{class:"section-title"},"工程选择",-1)])),_:1,__:[23]}),i(a,{span:24},{default:c(()=>[i(o,{modelValue:w.value,"onUpdate:modelValue":l[0]||(l[0]=e=>w.value=e),placeholder:"请选择工程",style:{width:"100%"}},{default:c(()=>[(_(),p(v,null,b(s,e=>i(t,{key:e.value,label:e.label,value:e.value},{default:c(()=>[f("span",null,V(e.label),1),f("span",D,V(e.orderNo),1)]),_:2},1032,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1})]),_:1}),C.value?(_(),y(d,{key:0,gutter:20,class:"form-section"},{default:c(()=>[i(a,{span:24},{default:c(()=>l[24]||(l[24]=[f("div",{class:"section-title"},"工程信息",-1)])),_:1,__:[24]}),i(a,{span:12},{default:c(()=>[i(n,{label:"甲方订单号:"},{default:c(()=>[f("span",null,V(C.value.partyOrderNo),1)]),_:1})]),_:1}),i(a,{span:12},{default:c(()=>[i(n,{label:"工程名称:"},{default:c(()=>[f("span",null,V(C.value.projectName),1)]),_:1})]),_:1}),i(a,{span:12},{default:c(()=>[i(n,{label:"工程地址:"},{default:c(()=>[f("span",null,V(C.value.projectAddress),1)]),_:1})]),_:1}),i(a,{span:12},{default:c(()=>[i(n,{label:"工程状态:"},{default:c(()=>[i(r,{type:A(C.value.projectStatus)},{default:c(()=>[h(V(U(C.value.projectStatus)),1)]),_:1},8,["type"])]),_:1})]),_:1}),i(a,{span:12},{default:c(()=>[i(n,{label:"当前进度:"},{default:c(()=>[f("span",null,V(C.value.progress),1)]),_:1})]),_:1})]),_:1})):g("",!0),i(d,{gutter:20,class:"form-section"},{default:c(()=>[i(a,{span:24},{default:c(()=>l[25]||(l[25]=[f("div",{class:"section-title"},"外部成本录入",-1)])),_:1,__:[25]}),i(a,{span:12},{default:c(()=>[i(n,{label:"成本日期:"},{default:c(()=>[i(E,{modelValue:k.costDate,"onUpdate:modelValue":l[1]||(l[1]=e=>k.costDate=e),type:"date",placeholder:"请选择成本日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),i(a,{span:12},{default:c(()=>[i(n,{label:"成本名称:"},{default:c(()=>[i(I,{modelValue:k.costName,"onUpdate:modelValue":l[2]||(l[2]=e=>k.costName=e),placeholder:"请输入成本名称"},null,8,["modelValue"])]),_:1})]),_:1}),i(a,{span:12},{default:c(()=>[i(n,{label:"成本类型:"},{default:c(()=>[i(o,{modelValue:k.costType,"onUpdate:modelValue":l[3]||(l[3]=e=>k.costType=e),placeholder:"请选择成本类型",style:{width:"100%"}},{default:c(()=>[i(t,{label:"设备租赁",value:"equipmentRental"}),i(t,{label:"专业服务",value:"professionalService"}),i(t,{label:"外包工程",value:"outsourcing"}),i(t,{label:"运输费用",value:"transportation"}),i(t,{label:"检测费用",value:"inspection"}),i(t,{label:"其他",value:"other"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),i(a,{span:12},{default:c(()=>[i(n,{label:"数量:"},{default:c(()=>[i(z,{modelValue:k.quantity,"onUpdate:modelValue":l[4]||(l[4]=e=>k.quantity=e),min:0,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),i(a,{span:12},{default:c(()=>[i(n,{label:"单价:"},{default:c(()=>[i(I,{modelValue:k.unitPrice,"onUpdate:modelValue":l[5]||(l[5]=e=>k.unitPrice=e),placeholder:"请输入单价"},{append:c(()=>l[26]||(l[26]=[h("元",-1)])),_:1},8,["modelValue"])]),_:1})]),_:1}),i(a,{span:12},{default:c(()=>[i(n,{label:"总金额:"},{default:c(()=>[i(I,{modelValue:k.totalAmount,"onUpdate:modelValue":l[6]||(l[6]=e=>k.totalAmount=e),placeholder:"请输入总金额"},{append:c(()=>l[27]||(l[27]=[h("元",-1)])),_:1},8,["modelValue"])]),_:1})]),_:1}),i(a,{span:12},{default:c(()=>[i(n,{label:"供应商:"},{default:c(()=>[i(I,{modelValue:k.supplier,"onUpdate:modelValue":l[7]||(l[7]=e=>k.supplier=e),placeholder:"请输入供应商"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),i(d,{gutter:20,class:"form-section"},{default:c(()=>[i(a,{span:24},{default:c(()=>[i(J,{class:"info-card"},{default:c(()=>l[28]||(l[28]=[f("div",{class:"info-title"},"成本类型说明:",-1),f("div",{class:"info-content"},[f("p",null,"1. 设备租赁: 大型设备或特殊工具的租赁费用"),f("p",null,"2. 专业服务: 需要专业资质的服务费用"),f("p",null,"3. 外包工程: 分包给其他公司的工程费用"),f("p",null,"4. 运输费用: 材料或设备运输相关费用"),f("p",null,"5. 检测费用: 工程质量检测、安全检测等费用"),f("p",null,"6. 其他: 未包含在上述分类中的其他外部成本")],-1)])),_:1,__:[28]})]),_:1})]),_:1}),i(d,{gutter:20,class:"form-section"},{default:c(()=>[i(a,{span:24},{default:c(()=>l[29]||(l[29]=[f("div",{class:"section-title"},"成本明细",-1)])),_:1,__:[29]}),i(a,{span:24},{default:c(()=>[i(F,{data:P.value,border:"",class:"cost-table"},{default:c(()=>[i(Q,{type:"index",label:"序号",width:"60"}),i(Q,{prop:"costDate",label:"成本日期",width:"120"}),i(Q,{prop:"costName",label:"成本名称",width:"120"}),i(Q,{prop:"costType",label:"成本类型",width:"120"},{default:c(e=>{return[h(V((l=e.row.costType,{equipmentRental:"设备租赁",professionalService:"专业服务",outsourcing:"外包工程",transportation:"运输费用",inspection:"检测费用",other:"其他"}[l]||"")),1)];var l}),_:1}),i(Q,{prop:"quantity",label:"数量",width:"80"}),i(Q,{prop:"unitPrice",label:"单价",width:"100"}),i(Q,{prop:"totalAmount",label:"总金额",width:"100"}),i(Q,{prop:"supplier",label:"供应商",width:"120"}),i(Q,{label:"操作",width:"120",fixed:"right"},{default:c(e=>[i($,{type:"primary",link:"",onClick:l=>S(e.row)},{default:c(()=>l[30]||(l[30]=[h("编辑",-1)])),_:2,__:[30]},1032,["onClick"]),i($,{type:"danger",link:"",onClick:l=>{return a=e.$index,P.value.splice(a,1),void u.success("删除成功");var a}},{default:c(()=>l[31]||(l[31]=[h("删除",-1)])),_:2,__:[31]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),f("div",j,[i($,{type:"primary",icon:"Plus",onClick:M},{default:c(()=>l[32]||(l[32]=[h("添加成本项",-1)])),_:1,__:[32]})])]),_:1})]),_:1}),i(d,{gutter:20,class:"form-section"},{default:c(()=>[i(a,{span:24},{default:c(()=>l[33]||(l[33]=[f("div",{class:"section-title"},"成本统计",-1)])),_:1,__:[33]}),i(a,{span:24},{default:c(()=>[i(J,{class:"statistics-card"},{default:c(()=>[i(d,{gutter:20},{default:c(()=>[i(a,{span:12},{default:c(()=>l[34]||(l[34]=[f("div",{class:"statistic-item"},[f("span",{class:"label"},"本月外部成本:"),f("span",null,"¥12,300")],-1)])),_:1,__:[34]}),i(a,{span:12},{default:c(()=>l[35]||(l[35]=[f("div",{class:"statistic-item"},[f("span",{class:"label"},"累计外部成本:"),f("span",null,"¥25,680")],-1)])),_:1,__:[35]}),i(a,{span:12},{default:c(()=>l[36]||(l[36]=[f("div",{class:"statistic-item"},[f("span",{class:"label"},"占总成本比例:"),f("span",null,"18.5%")],-1)])),_:1,__:[36]})]),_:1})]),_:1})]),_:1})]),_:1}),i(d,{gutter:20,class:"form-section"},{default:c(()=>[i(a,{span:24},{default:c(()=>l[37]||(l[37]=[f("div",{class:"section-title"},"备注信息",-1)])),_:1,__:[37]}),i(a,{span:24},{default:c(()=>[i(n,{label:"成本说明:"},{default:c(()=>[i(I,{modelValue:k.costDescription,"onUpdate:modelValue":l[8]||(l[8]=e=>k.costDescription=e),type:"textarea",rows:3,placeholder:"请输入成本说明"},null,8,["modelValue"])]),_:1})]),_:1}),i(a,{span:24},{default:c(()=>[i(d,{gutter:20},{default:c(()=>[i(a,{span:8},{default:c(()=>[i(n,{label:"发票号码:"},{default:c(()=>[i(I,{modelValue:k.invoiceNumber,"onUpdate:modelValue":l[9]||(l[9]=e=>k.invoiceNumber=e),placeholder:"请输入发票号码"},null,8,["modelValue"])]),_:1})]),_:1}),i(a,{span:8},{default:c(()=>[i(n,{label:"开票日期:"},{default:c(()=>[i(E,{modelValue:k.invoiceDate,"onUpdate:modelValue":l[10]||(l[10]=e=>k.invoiceDate=e),type:"date",placeholder:"请选择开票日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),i(a,{span:8},{default:c(()=>[i(n,{label:"发票金额:"},{default:c(()=>[i(I,{modelValue:k.invoiceAmount,"onUpdate:modelValue":l[11]||(l[11]=e=>k.invoiceAmount=e),placeholder:"请输入发票金额"},{append:c(()=>l[38]||(l[38]=[h("元",-1)])),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),f("div",Y,[i($,{type:"primary",onClick:T},{default:c(()=>l[39]||(l[39]=[h("保存",-1)])),_:1,__:[39]}),i($,{type:"success",onClick:G},{default:c(()=>l[40]||(l[40]=[h("提交",-1)])),_:1,__:[40]}),i($,{onClick:R},{default:c(()=>l[41]||(l[41]=[h("打印",-1)])),_:1,__:[41]}),i($,{onClick:B},{default:c(()=>l[42]||(l[42]=[h("取消",-1)])),_:1,__:[42]})]),i(K,{modelValue:x.value,"onUpdate:modelValue":l[21]||(l[21]=e=>x.value=e),title:"编辑外部成本",width:"500"},{footer:c(()=>[i($,{onClick:l[20]||(l[20]=e=>x.value=!1)},{default:c(()=>l[45]||(l[45]=[h("取消",-1)])),_:1,__:[45]}),i($,{type:"primary",onClick:O},{default:c(()=>l[46]||(l[46]=[h("保存",-1)])),_:1,__:[46]})]),default:c(()=>[i(H,{model:q.value,"label-width":"100px"},{default:c(()=>[i(n,{label:"成本日期:"},{default:c(()=>[i(E,{modelValue:q.value.costDate,"onUpdate:modelValue":l[12]||(l[12]=e=>q.value.costDate=e),type:"date",placeholder:"请选择成本日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),i(n,{label:"成本名称:"},{default:c(()=>[i(I,{modelValue:q.value.costName,"onUpdate:modelValue":l[13]||(l[13]=e=>q.value.costName=e),placeholder:"请输入成本名称"},null,8,["modelValue"])]),_:1}),i(n,{label:"成本类型:"},{default:c(()=>[i(o,{modelValue:q.value.costType,"onUpdate:modelValue":l[14]||(l[14]=e=>q.value.costType=e),placeholder:"请选择成本类型",style:{width:"100%"}},{default:c(()=>[i(t,{label:"设备租赁",value:"equipmentRental"}),i(t,{label:"专业服务",value:"professionalService"}),i(t,{label:"外包工程",value:"outsourcing"}),i(t,{label:"运输费用",value:"transportation"}),i(t,{label:"检测费用",value:"inspection"}),i(t,{label:"其他",value:"other"})]),_:1},8,["modelValue"])]),_:1}),i(n,{label:"数量:"},{default:c(()=>[i(z,{modelValue:q.value.quantity,"onUpdate:modelValue":l[15]||(l[15]=e=>q.value.quantity=e),min:0,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),i(n,{label:"单价:"},{default:c(()=>[i(I,{modelValue:q.value.unitPrice,"onUpdate:modelValue":l[16]||(l[16]=e=>q.value.unitPrice=e),placeholder:"请输入单价"},{append:c(()=>l[43]||(l[43]=[h("元",-1)])),_:1},8,["modelValue"])]),_:1}),i(n,{label:"总金额:"},{default:c(()=>[i(I,{modelValue:q.value.totalAmount,"onUpdate:modelValue":l[17]||(l[17]=e=>q.value.totalAmount=e),placeholder:"请输入总金额"},{append:c(()=>l[44]||(l[44]=[h("元",-1)])),_:1},8,["modelValue"])]),_:1}),i(n,{label:"供应商:"},{default:c(()=>[i(I,{modelValue:q.value.supplier,"onUpdate:modelValue":l[18]||(l[18]=e=>q.value.supplier=e),placeholder:"请输入供应商"},null,8,["modelValue"])]),_:1}),i(n,{label:"备注:"},{default:c(()=>[i(I,{modelValue:q.value.remarks,"onUpdate:modelValue":l[19]||(l[19]=e=>q.value.remarks=e),type:"textarea",rows:2,placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])]),_:1})])}}}),[["__scopeId","data-v-dd084f27"]]);export{C as default};
