<template>
  <div class="product-price-container">
    <el-card class="main-card">
      <template #header>
        <div class="card-header">
          <span>商品价格</span>
        </div>
      </template>
      
      <!-- 搜索栏 -->
      <el-row :gutter="20" class="search-section">
        <el-col :span="6">
          <el-input v-model="searchForm.materialCode" placeholder="公司物料编码" clearable />
        </el-col>
        <el-col :span="6">
          <el-input v-model="searchForm.productName" placeholder="商品名称" clearable />
        </el-col>
        <el-col :span="6">
          <el-select v-model="searchForm.category" placeholder="分类" clearable style="width: 100%">
            <el-option label="甲料" value="material" />
            <el-option label="商品" value="product" />
            <el-option label="辅料" value="auxiliary" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <div class="search-buttons">
            <el-button type="primary" icon="Search" @click="handleSearch">搜索</el-button>
            <el-button icon="Refresh" @click="handleReset">重置</el-button>
          </div>
        </el-col>
      </el-row>
      
      <!-- 商品价格列表 -->
      <el-table :data="productPriceList" border class="product-price-table" style="width: 100%; margin-top: 20px;">
        <el-table-column prop="materialCode" label="公司物料编码" min-width="120" />
        <el-table-column prop="partyCode" label="甲方编码" min-width="100">
          <template #default="scope">
            <el-button type="primary" link @click="showPartyCodes(scope.row)">显示</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="productName" label="商品名称" min-width="120" />
        <el-table-column prop="model" label="型号" min-width="100" />
        <el-table-column prop="specification" label="规格" min-width="100" />
        <el-table-column prop="unit" label="单位" min-width="80" />
        <el-table-column prop="unitPrice" label="单价(元)" min-width="100" />
        <el-table-column prop="updateTime" label="更新时间" min-width="120" />
        <el-table-column label="操作" min-width="150" fixed="right">
          <template #default="scope">
            <el-button type="primary" link @click="modifyPrice(scope.row)">修改价格</el-button>
            <el-button type="primary" link @click="viewDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        class="pagination"
      />
      
      <!-- 操作按钮 -->
      <div class="form-actions">
        <el-button type="primary" icon="Coin" @click="batchAdjustPrice">批量调价</el-button>
        <el-button icon="Upload" @click="importPrice">导入价格</el-button>
        <el-button icon="Download" @click="exportExcel">导出Excel</el-button>
        <el-button icon="DataAnalysis" @click="showPriceHistory">价格历史</el-button>
      </div>
      
      <!-- 修改价格弹窗 -->
      <el-dialog v-model="priceDialogVisible" title="修改价格" width="500">
        <el-form :model="currentProduct" label-width="120px">
          <el-form-item label="公司物料编码:">
            <span>{{ currentProduct.materialCode }}</span>
          </el-form-item>
          <el-form-item label="商品名称:">
            <span>{{ currentProduct.productName }}</span>
          </el-form-item>
          <el-form-item label="型号:">
            <span>{{ currentProduct.model }}</span>
          </el-form-item>
          <el-form-item label="规格:">
            <span>{{ currentProduct.specification }}</span>
          </el-form-item>
          <el-form-item label="单位:">
            <span>{{ currentProduct.unit }}</span>
          </el-form-item>
          <el-form-item label="当前单价:">
            <span>{{ currentProduct.unitPrice }}</span>
          </el-form-item>
          <el-form-item label="新单价:" required>
            <el-input v-model="priceForm.newPrice" placeholder="请输入新单价">
              <template #append>元</template>
            </el-input>
          </el-form-item>
          <el-form-item label="调价原因:">
            <el-input
              v-model="priceForm.adjustReason"
              type="textarea"
              :rows="2"
              placeholder="请输入调价原因"
            />
          </el-form-item>
          <el-form-item label="备注:">
            <el-input
              v-model="priceForm.remarks"
              type="textarea"
              :rows="2"
              placeholder="请输入备注"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="priceDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="savePrice">保存</el-button>
        </template>
      </el-dialog>
      
      <!-- 批量调价弹窗 -->
      <el-dialog v-model="batchDialogVisible" title="批量调价" width="600">
        <el-form :model="batchForm" label-width="120px">
          <el-form-item label="调价方式:">
            <el-radio-group v-model="batchForm.adjustType">
              <el-radio-button label="percentage">百分比调价</el-radio-button>
              <el-radio-button label="fixed">固定金额调价</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="调价幅度:">
            <el-input v-model="batchForm.adjustValue" placeholder="请输入调价幅度">
              <template #append>{{ batchForm.adjustType === 'percentage' ? '%' : '元' }}</template>
            </el-input>
          </el-form-item>
          <el-form-item label="调价方向:">
            <el-radio-group v-model="batchForm.adjustDirection">
              <el-radio-button label="up">上涨</el-radio-button>
              <el-radio-button label="down">下跌</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="调价原因:">
            <el-input
              v-model="batchForm.adjustReason"
              type="textarea"
              :rows="2"
              placeholder="请输入调价原因"
            />
          </el-form-item>
          <el-divider />
          <el-form-item label="选择商品:">
            <el-card class="product-selection-card">
              <el-checkbox-group v-model="batchForm.selectedProducts">
                <div 
                  v-for="product in productPriceList" 
                  :key="product.id" 
                  class="product-checkbox-item"
                >
                  <el-checkbox :label="product.id">
                    {{ product.productName }} ({{ product.materialCode }})
                  </el-checkbox>
                </div>
              </el-checkbox-group>
            </el-card>
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="selectAllProducts">全选</el-button>
          <el-button @click="deselectAllProducts">全不选</el-button>
          <el-button @click="previewAdjustment">预览</el-button>
          <el-button type="primary" @click="confirmBatchAdjust">确认调价</el-button>
          <el-button @click="batchDialogVisible = false">取消</el-button>
        </template>
      </el-dialog>
      
      <!-- 价格历史弹窗 -->
      <el-dialog v-model="historyDialogVisible" title="价格历史" width="700">
        <el-form :model="currentProduct" label-width="150px">
          <el-form-item label="商品名称:">
            <span>{{ currentProduct.productName }} ({{ currentProduct.materialCode }})</span>
          </el-form-item>
          <el-form-item label="价格变更记录:">
            <el-table :data="priceHistoryList" border height="300">
              <el-table-column prop="changeTime" label="时间" width="150" />
              <el-table-column prop="oldPrice" label="原价格" width="100" />
              <el-table-column prop="newPrice" label="新价格" width="100" />
              <el-table-column prop="changeRatio" label="变更幅度" width="100" />
              <el-table-column prop="operator" label="操作人" width="100" />
            </el-table>
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="historyDialogVisible = false">关闭</el-button>
        </template>
      </el-dialog>
      
      <!-- 甲方编码显示弹窗 -->
      <el-dialog v-model="partyCodeDialogVisible" title="甲方编码列表" width="500">
        <div v-for="(code, index) in currentPartyCodes" :key="index" class="party-code-item">
          编码{{ index + 1 }}: {{ code }}
        </div>
        <template #footer>
          <el-button @click="partyCodeDialogVisible = false">关闭</el-button>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

// 搜索表单
const searchForm = reactive({
  materialCode: '',
  productName: '',
  category: ''
})

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 156
})

// 商品价格列表
const productPriceList = ref([
  {
    id: 1,
    materialCode: 'SP001',
    partyCode: ['JD001', 'JD002'],
    productName: '智能开关',
    model: 'KG-86',
    specification: '86型',
    unit: '个',
    unitPrice: '¥45.00',
    updateTime: '2024-01-15'
  },
  {
    id: 2,
    materialCode: 'SP002',
    partyCode: ['JD003'],
    productName: 'LED灯具',
    model: 'LED-12W',
    specification: '12W',
    unit: '个',
    unitPrice: '¥130.00',
    updateTime: '2024-01-15'
  },
  {
    id: 3,
    materialCode: 'SP003',
    partyCode: ['JD004'],
    productName: '插座面板',
    model: 'ZP-86',
    specification: '86型',
    unit: '个',
    unitPrice: '¥60.00',
    updateTime: '2024-01-14'
  },
  {
    id: 4,
    materialCode: 'SP004',
    partyCode: ['JD005'],
    productName: '电线',
    model: 'BV-2.5',
    specification: '2.5mm²',
    unit: '米',
    unitPrice: '¥5.00',
    updateTime: '2024-01-14'
  },
  {
    id: 5,
    materialCode: 'SP005',
    partyCode: ['JD006'],
    productName: '开关面板',
    model: 'KP-86',
    specification: '86型',
    unit: '个',
    unitPrice: '¥250.00',
    updateTime: '2024-01-13'
  },
  {
    id: 6,
    materialCode: 'SP006',
    partyCode: ['JD007'],
    productName: '水管',
    model: 'PPR-20',
    specification: '20mm',
    unit: '米',
    unitPrice: '¥8.00',
    updateTime: '2024-01-12'
  },
  {
    id: 7,
    materialCode: 'SP007',
    partyCode: ['JD008'],
    productName: '水龙头',
    model: 'LT-15',
    specification: '15mm',
    unit: '个',
    unitPrice: '¥35.00',
    updateTime: '2024-01-12'
  }
])

// 当前商品
const currentProduct = ref({
  id: 0,
  materialCode: '',
  partyCode: [] as string[],
  productName: '',
  model: '',
  specification: '',
  unit: '',
  unitPrice: '',
  updateTime: ''
})

// 当前甲方编码
const currentPartyCodes = ref([] as string[])

// 价格表单
const priceForm = reactive({
  newPrice: '',
  adjustReason: '',
  remarks: ''
})

// 批量调价表单
const batchForm = reactive({
  adjustType: 'percentage',
  adjustValue: '10',
  adjustDirection: 'up',
  adjustReason: '',
  selectedProducts: [] as number[]
})

// 价格历史列表
const priceHistoryList = ref([
  {
    id: 1,
    changeTime: '2024-01-15',
    oldPrice: '¥45.00',
    newPrice: '¥50.00',
    changeRatio: '+11.11%',
    operator: '张三'
  },
  {
    id: 2,
    changeTime: '2024-01-10',
    oldPrice: '¥40.00',
    newPrice: '¥45.00',
    changeRatio: '+12.50%',
    operator: '李四'
  },
  {
    id: 3,
    changeTime: '2024-01-05',
    oldPrice: '¥35.00',
    newPrice: '¥40.00',
    changeRatio: '+14.29%',
    operator: '王五'
  },
  {
    id: 4,
    changeTime: '2023-12-20',
    oldPrice: '¥45.00',
    newPrice: '¥35.00',
    changeRatio: '-22.22%',
    operator: '赵六'
  }
])

// 弹窗控制
const priceDialogVisible = ref(false)
const batchDialogVisible = ref(false)
const historyDialogVisible = ref(false)
const partyCodeDialogVisible = ref(false)

// 搜索
const handleSearch = () => {
  ElMessage.success('搜索成功')
  console.log('搜索条件:', searchForm)
}

// 重置
const handleReset = () => {
  searchForm.materialCode = ''
  searchForm.productName = ''
  searchForm.category = ''
}

// 分页变化
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  console.log('每页条数:', val)
}

const handleCurrentChange = (val: number) => {
  pagination.currentPage = val
  console.log('当前页:', val)
}

// 显示甲方编码
const showPartyCodes = (row: any) => {
  currentPartyCodes.value = row.partyCode
  partyCodeDialogVisible.value = true
}

// 修改价格
const modifyPrice = (row: any) => {
  currentProduct.value = { ...row }
  priceForm.newPrice = ''
  priceForm.adjustReason = ''
  priceForm.remarks = ''
  priceDialogVisible.value = true
}

// 查看详情
const viewDetail = (row: any) => {
  currentProduct.value = { ...row }
  historyDialogVisible.value = true
}

// 保存价格
const savePrice = () => {
  ElMessage.success('价格修改成功')
  priceDialogVisible.value = false
  console.log('修改价格:', currentProduct.value, priceForm)
}

// 批量调价
const batchAdjustPrice = () => {
  batchDialogVisible.value = true
}

// 全选商品
const selectAllProducts = () => {
  batchForm.selectedProducts = productPriceList.value.map(item => item.id)
}

// 全不选商品
const deselectAllProducts = () => {
  batchForm.selectedProducts = []
}

// 预览调价
const previewAdjustment = () => {
  ElMessage.success('预览调价结果')
}

// 确认批量调价
const confirmBatchAdjust = () => {
  ElMessage.success('批量调价成功')
  batchDialogVisible.value = false
}

// 导入价格
const importPrice = () => {
  ElMessage.success('导入价格')
}

// 导出Excel
const exportExcel = () => {
  ElMessage.success('导出Excel')
}

// 显示价格历史
const showPriceHistory = () => {
  // 选择第一个商品作为示例
  if (productPriceList.value.length > 0) {
    currentProduct.value = { ...productPriceList.value[0] }
    historyDialogVisible.value = true
  }
}
</script>

<style lang="scss" scoped>
.product-price-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
  
  .main-card {
    margin-bottom: 20px;
    
    .card-header {
      font-size: 18px;
      font-weight: bold;
      color: #303133;
    }
  }
  
  .search-section {
    margin-bottom: 15px;
    
    .search-buttons {
      display: flex;
      
      .el-button {
        margin-right: 10px;
      }
    }
  }
  
  .product-price-table {
    margin-top: 20px;
  }
  
  .pagination {
    margin-top: 20px;
    text-align: right;
  }
  
  .form-actions {
    margin-top: 20px;
    text-align: left;
    
    .el-button {
      margin-right: 10px;
    }
  }
  
  .product-selection-card {
    max-height: 300px;
    overflow-y: auto;
    
    .product-checkbox-item {
      margin-bottom: 10px;
    }
  }
  
  .party-code-item {
    margin-bottom: 10px;
    padding: 5px 0;
    border-bottom: 1px solid #ebeef5;
  }
}
</style>