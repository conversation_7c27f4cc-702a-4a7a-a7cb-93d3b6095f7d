<template>
  <div class="external-cost-container">
    <el-card class="main-card">
      <template #header>
        <div class="card-header">
          <span>外部成本</span>
        </div>
      </template>
      
      <!-- 工程选择 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">工程选择</div>
        </el-col>
        <el-col :span="24">
          <el-select v-model="selectedProject" placeholder="请选择工程" style="width: 100%">
            <el-option
              v-for="project in projectOptions"
              :key="project.value"
              :label="project.label"
              :value="project.value"
            >
              <span>{{ project.label }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ project.orderNo }}</span>
            </el-option>
          </el-select>
        </el-col>
      </el-row>
      
      <!-- 工程信息 -->
      <el-row :gutter="20" class="form-section" v-if="currentProject">
        <el-col :span="24">
          <div class="section-title">工程信息</div>
        </el-col>
        <el-col :span="12">
          <el-form-item label="甲方订单号:">
            <span>{{ currentProject.partyOrderNo }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工程名称:">
            <span>{{ currentProject.projectName }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工程地址:">
            <span>{{ currentProject.projectAddress }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工程状态:">
            <el-tag :type="getProjectStatusType(currentProject.projectStatus)">
              {{ getProjectStatusText(currentProject.projectStatus) }}
            </el-tag>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="当前进度:">
            <span>{{ currentProject.progress }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 外部成本录入 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">外部成本录入</div>
        </el-col>
        <el-col :span="12">
          <el-form-item label="成本日期:">
            <el-date-picker
              v-model="costForm.costDate"
              type="date"
              placeholder="请选择成本日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="成本名称:">
            <el-input v-model="costForm.costName" placeholder="请输入成本名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="成本类型:">
            <el-select v-model="costForm.costType" placeholder="请选择成本类型" style="width: 100%">
              <el-option label="设备租赁" value="equipmentRental" />
              <el-option label="专业服务" value="professionalService" />
              <el-option label="外包工程" value="outsourcing" />
              <el-option label="运输费用" value="transportation" />
              <el-option label="检测费用" value="inspection" />
              <el-option label="其他" value="other" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="数量:">
            <el-input-number 
              v-model="costForm.quantity" 
              :min="0" 
              controls-position="right" 
              style="width: 100%" 
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="单价:">
            <el-input v-model="costForm.unitPrice" placeholder="请输入单价">
              <template #append>元</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="总金额:">
            <el-input v-model="costForm.totalAmount" placeholder="请输入总金额">
              <template #append>元</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="供应商:">
            <el-input v-model="costForm.supplier" placeholder="请输入供应商" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 成本类型说明 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <el-card class="info-card">
            <div class="info-title">成本类型说明:</div>
            <div class="info-content">
              <p>1. 设备租赁: 大型设备或特殊工具的租赁费用</p>
              <p>2. 专业服务: 需要专业资质的服务费用</p>
              <p>3. 外包工程: 分包给其他公司的工程费用</p>
              <p>4. 运输费用: 材料或设备运输相关费用</p>
              <p>5. 检测费用: 工程质量检测、安全检测等费用</p>
              <p>6. 其他: 未包含在上述分类中的其他外部成本</p>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 成本明细 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">成本明细</div>
        </el-col>
        <el-col :span="24">
          <el-table :data="costList" border class="cost-table">
            <el-table-column type="index" label="序号" width="60" />
            <el-table-column prop="costDate" label="成本日期" width="120" />
            <el-table-column prop="costName" label="成本名称" width="120" />
            <el-table-column prop="costType" label="成本类型" width="120">
              <template #default="scope">
                {{ getCostTypeText(scope.row.costType) }}
              </template>
            </el-table-column>
            <el-table-column prop="quantity" label="数量" width="80" />
            <el-table-column prop="unitPrice" label="单价" width="100" />
            <el-table-column prop="totalAmount" label="总金额" width="100" />
            <el-table-column prop="supplier" label="供应商" width="120" />
            <el-table-column label="操作" width="120" fixed="right">
              <template #default="scope">
                <el-button type="primary" link @click="editCost(scope.row)">编辑</el-button>
                <el-button type="danger" link @click="removeCost(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div style="margin-top: 15px;">
            <el-button type="primary" icon="Plus" @click="addCost">添加成本项</el-button>
          </div>
        </el-col>
      </el-row>
      
      <!-- 成本统计 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">成本统计</div>
        </el-col>
        <el-col :span="24">
          <el-card class="statistics-card">
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="statistic-item">
                  <span class="label">本月外部成本:</span>
                  <span>¥12,300</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="statistic-item">
                  <span class="label">累计外部成本:</span>
                  <span>¥25,680</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="statistic-item">
                  <span class="label">占总成本比例:</span>
                  <span>18.5%</span>
                </div>
              </el-col>
            </el-row>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 备注信息 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">备注信息</div>
        </el-col>
        <el-col :span="24">
          <el-form-item label="成本说明:">
            <el-input
              v-model="costForm.costDescription"
              type="textarea"
              :rows="3"
              placeholder="请输入成本说明"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="发票号码:">
                <el-input v-model="costForm.invoiceNumber" placeholder="请输入发票号码" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="开票日期:">
                <el-date-picker
                  v-model="costForm.invoiceDate"
                  type="date"
                  placeholder="请选择开票日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="发票金额:">
                <el-input v-model="costForm.invoiceAmount" placeholder="请输入发票金额">
                  <template #append>元</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
      
      <!-- 操作按钮 -->
      <div class="form-actions">
        <el-button type="primary" @click="handleSave">保存</el-button>
        <el-button type="success" @click="handleSubmit">提交</el-button>
        <el-button @click="handlePrint">打印</el-button>
        <el-button @click="handleCancel">取消</el-button>
      </div>
      
      <!-- 编辑成本弹窗 -->
      <el-dialog v-model="editDialogVisible" title="编辑外部成本" width="500">
        <el-form :model="currentCost" label-width="100px">
          <el-form-item label="成本日期:">
            <el-date-picker
              v-model="currentCost.costDate"
              type="date"
              placeholder="请选择成本日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="成本名称:">
            <el-input v-model="currentCost.costName" placeholder="请输入成本名称" />
          </el-form-item>
          <el-form-item label="成本类型:">
            <el-select v-model="currentCost.costType" placeholder="请选择成本类型" style="width: 100%">
              <el-option label="设备租赁" value="equipmentRental" />
              <el-option label="专业服务" value="professionalService" />
              <el-option label="外包工程" value="outsourcing" />
              <el-option label="运输费用" value="transportation" />
              <el-option label="检测费用" value="inspection" />
              <el-option label="其他" value="other" />
            </el-select>
          </el-form-item>
          <el-form-item label="数量:">
            <el-input-number 
              v-model="currentCost.quantity" 
              :min="0" 
              controls-position="right" 
              style="width: 100%" 
            />
          </el-form-item>
          <el-form-item label="单价:">
            <el-input v-model="currentCost.unitPrice" placeholder="请输入单价">
              <template #append>元</template>
            </el-input>
          </el-form-item>
          <el-form-item label="总金额:">
            <el-input v-model="currentCost.totalAmount" placeholder="请输入总金额">
              <template #append>元</template>
            </el-input>
          </el-form-item>
          <el-form-item label="供应商:">
            <el-input v-model="currentCost.supplier" placeholder="请输入供应商" />
          </el-form-item>
          <el-form-item label="备注:">
            <el-input
              v-model="currentCost.remarks"
              type="textarea"
              :rows="2"
              placeholder="请输入备注"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveCost">保存</el-button>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

// 工程选项
const projectOptions = [
  { value: '1', label: '阳光小区A栋', orderNo: 'GC202401001' },
  { value: '2', label: '花园广场项目', orderNo: 'GC202401002' },
  { value: '3', label: '商业中心B区', orderNo: 'GC202401003' },
  { value: '4', label: '住宅楼C座', orderNo: 'GC202401004' }
]

// 选中的工程
const selectedProject = ref('1')

// 当前工程信息
const currentProject = computed(() => {
  const projectMap: any = {
    '1': {
      partyOrderNo: 'GC202401001',
      projectName: '阳光小区A栋',
      projectAddress: '阳光小区A栋',
      projectStatus: 'building',
      progress: '45%'
    },
    '2': {
      partyOrderNo: 'GC202401002',
      projectName: '花园广场项目',
      projectAddress: '花园广场B区',
      projectStatus: 'notStarted',
      progress: '0%'
    },
    '3': {
      partyOrderNo: 'GC202401003',
      projectName: '商业中心B区',
      projectAddress: '商业街88号',
      projectStatus: 'paused',
      progress: '65%'
    },
    '4': {
      partyOrderNo: 'GC202401004',
      projectName: '住宅楼C座',
      projectAddress: '住宅区C栋',
      projectStatus: 'completed',
      progress: '100%'
    }
  }
  
  return projectMap[selectedProject.value]
})

// 获取工程状态类型
const getProjectStatusType = (status: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  switch (status) {
    case 'notStarted':
      return 'info'
    case 'building':
      return 'primary'
    case 'paused':
      return 'warning'
    case 'completed':
      return 'success'
    default:
      return 'info'
  }
}

// 获取工程状态文本
const getProjectStatusText = (status: string) => {
  switch (status) {
    case 'notStarted':
      return '未开始'
    case 'building':
      return '在建'
    case 'paused':
      return '暂停'
    case 'completed':
      return '完成'
    default:
      return ''
  }
}

// 成本表单
const costForm = reactive({
  costDate: '',
  costName: '',
  costType: '',
  quantity: 1,
  unitPrice: '',
  totalAmount: '',
  supplier: '',
  costDescription: '',
  invoiceNumber: '',
  invoiceDate: '',
  invoiceAmount: ''
})

// 成本列表
const costList = ref([
  {
    id: 1,
    costDate: '2024-01-10',
    costName: '吊车租赁',
    costType: 'equipmentRental',
    quantity: 1,
    unitPrice: '¥2,500',
    totalAmount: '¥2,500',
    supplier: '蓝天设备'
  },
  {
    id: 2,
    costDate: '2024-01-08',
    costName: '电力检测',
    costType: 'inspection',
    quantity: 1,
    unitPrice: '¥1,200',
    totalAmount: '¥1,200',
    supplier: '电力检测'
  },
  {
    id: 3,
    costDate: '2024-01-05',
    costName: '防水工程',
    costType: 'outsourcing',
    quantity: 1,
    unitPrice: '¥8,000',
    totalAmount: '¥8,000',
    supplier: '防水公司'
  },
  {
    id: 4,
    costDate: '2024-01-03',
    costName: '材料运输',
    costType: 'transportation',
    quantity: 1,
    unitPrice: '¥600',
    totalAmount: '¥600',
    supplier: '快捷物流'
  }
])

// 当前编辑的成本
const currentCost = ref({
  id: 0,
  costDate: '',
  costName: '',
  costType: '',
  quantity: 1,
  unitPrice: '',
  totalAmount: '',
  supplier: '',
  remarks: ''
})

// 弹窗控制
const editDialogVisible = ref(false)

// 获取成本类型文本
const getCostTypeText = (type: string) => {
  const typeMap: any = {
    equipmentRental: '设备租赁',
    professionalService: '专业服务',
    outsourcing: '外包工程',
    transportation: '运输费用',
    inspection: '检测费用',
    other: '其他'
  }
  
  return typeMap[type] || ''
}

// 添加成本
const addCost = () => {
  // 这里应该将costForm中的数据添加到costList中
  ElMessage.success('添加成本项')
  console.log('添加成本:', costForm)
}

// 编辑成本
const editCost = (row: any) => {
  currentCost.value = { ...row }
  editDialogVisible.value = true
}

// 保存成本
const saveCost = () => {
  ElMessage.success('保存成功')
  editDialogVisible.value = false
  console.log('保存成本:', currentCost.value)
}

// 删除成本
const removeCost = (index: number) => {
  costList.value.splice(index, 1)
  ElMessage.success('删除成功')
}

// 保存
const handleSave = () => {
  ElMessage.success('保存成功')
  console.log('保存数据:', costForm, costList.value)
}

// 提交
const handleSubmit = () => {
  ElMessage.success('提交成功')
  console.log('提交数据:', costForm, costList.value)
}

// 打印
const handlePrint = () => {
  ElMessage.success('开始打印')
}

// 取消
const handleCancel = () => {
  ElMessage.info('已取消')
}
</script>

<style lang="scss" scoped>
.external-cost-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
  
  .main-card {
    margin-bottom: 20px;
    
    .card-header {
      font-size: 18px;
      font-weight: bold;
      color: #303133;
    }
  }
  
  .form-section {
    margin-bottom: 20px;
    
    .section-title {
      font-size: 16px;
      font-weight: bold;
      color: #606266;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ebeef5;
    }
    
    .el-form-item {
      margin-bottom: 18px;
    }
  }
  
  .info-card {
    background-color: #f0f9ff;
    border-color: #b3e0ff;
    
    .info-title {
      font-weight: bold;
      color: #409eff;
      margin-bottom: 10px;
    }
    
    .info-content {
      p {
        margin: 5px 0;
        color: #606266;
      }
    }
  }
  
  .cost-table {
    margin-top: 10px;
  }
  
  .statistics-card {
    background-color: #f0f9ff;
    border-color: #b3e0ff;
    
    .statistic-item {
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;
      
      .label {
        font-weight: bold;
        color: #409eff;
      }
    }
  }
  
  .form-actions {
    text-align: center;
    padding: 20px 0;
    
    .el-button {
      margin: 0 10px;
      padding: 12px 30px;
    }
  }
}
</style>