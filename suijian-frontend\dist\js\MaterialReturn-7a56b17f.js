var e=Object.defineProperty,a=Object.defineProperties,t=Object.getOwnPropertyDescriptors,l=Object.getOwnPropertySymbols,u=Object.prototype.hasOwnProperty,r=Object.prototype.propertyIsEnumerable,i=(a,t,l)=>t in a?e(a,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):a[t]=l;import{E as n}from"./element-plus-7917fd46.js";import{l as o,_ as s,r as d,y as p,R as c,J as m,av as _,x as f,z as y,Q as b,aa as v,O as h,P as w,I as V}from"./vue-vendor-fc5a6493.js";import{_ as k}from"./_plugin-vue_export-helper-1b428a4d.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const Q={class:"material-return-container"},C={style:{"margin-top":"15px"}},g={class:"add-material-actions"},x={class:"statistic-item"},P={class:"statistic-item"},j={class:"statistic-item"},R={class:"total-amount"},U={class:"form-actions"},O=k(o({__name:"MaterialReturn",setup(e){const o=s({returnDate:"2024-01-15",returner:"李四",department:"工程部",returnType:"projectSurplus",relatedProject:1,returnReason:"",remarks:""}),k=d([{id:1,name:"阳光小区A栋"},{id:2,name:"花园广场项目"},{id:3,name:"商业中心B区"},{id:4,name:"住宅楼C座"}]),O=d([{id:1,materialCode:"WL001",materialName:"电缆线",model:"YJV-3*4",specification:"3*4mm²",unit:"米",stockQuantity:200,returnQuantity:50,maxReturnQuantity:50,unitPrice:"30.00",subtotal:"1,500.00"},{id:2,materialCode:"WL002",materialName:"开关面板",model:"KP-86",specification:"86型",unit:"个",stockQuantity:100,returnQuantity:20,maxReturnQuantity:20,unitPrice:"250.00",subtotal:"5,000.00"},{id:3,materialCode:"WL003",materialName:"电线",model:"BV-2.5",specification:"2.5mm²",unit:"米",stockQuantity:500,returnQuantity:100,maxReturnQuantity:100,unitPrice:"5.00",subtotal:"500.00"}]),Y=d([{id:1,materialCode:"WL001",materialName:"电缆线",model:"YJV-3*4",specification:"3*4mm²",unit:"米",stockQuantity:200,maxReturnQuantity:50},{id:2,materialCode:"WL002",materialName:"开关面板",model:"KP-86",specification:"86型",unit:"个",stockQuantity:100,maxReturnQuantity:20},{id:4,materialCode:"WL004",materialName:"水管",model:"PPR-20",specification:"20mm",unit:"米",stockQuantity:300,maxReturnQuantity:50}]),D=s({materialTypes:3,totalQuantity:170,totalAmount:"7,000.00"}),N=d(!1),M=d(""),T=()=>{N.value=!0},L=()=>{n.success("搜索物料")},W=e=>{var o;O.value.some(a=>a.id===e.id)?n.warning("该物料已添加"):(O.value.push((o=((e,a)=>{for(var t in a||(a={}))u.call(a,t)&&i(e,t,a[t]);if(l)for(var t of l(a))r.call(a,t)&&i(e,t,a[t]);return e})({},e),a(o,t({returnQuantity:1,unitPrice:"0.00",subtotal:"0.00"})))),N.value=!1,n.success("添加成功"),S())},I=()=>{n.success("添加物料")},S=()=>{D.materialTypes=O.value.length,D.totalQuantity=O.value.reduce((e,a)=>e+a.returnQuantity,0)},A=()=>{n.success("保存成功")},J=()=>{n.success("开始打印")},B=()=>{n.success("提交成功")},E=()=>{n.info("已取消")};return(e,a)=>{const t=_("el-col"),l=_("el-form-item"),u=_("el-date-picker"),r=_("el-input"),i=_("el-option"),s=_("el-select"),d=_("el-row"),K=_("el-table-column"),q=_("el-input-number"),z=_("el-button"),$=_("el-table"),F=_("el-dialog"),G=_("el-card");return f(),p("div",Q,[c(G,{class:"main-card"},{header:m(()=>a[10]||(a[10]=[y("div",{class:"card-header"},[y("span",null,"物料退仓")],-1)])),default:m(()=>[c(d,{gutter:20,class:"form-section"},{default:m(()=>[c(t,{span:24},{default:m(()=>a[11]||(a[11]=[y("div",{class:"section-title"},"退仓信息",-1)])),_:1,__:[11]}),c(t,{span:12},{default:m(()=>[c(l,{label:"退仓单号:"},{default:m(()=>a[12]||(a[12]=[y("span",null,"TC20240115001",-1)])),_:1,__:[12]})]),_:1}),c(t,{span:12},{default:m(()=>[c(l,{label:"退仓日期:"},{default:m(()=>[c(u,{modelValue:o.returnDate,"onUpdate:modelValue":a[0]||(a[0]=e=>o.returnDate=e),type:"date",placeholder:"请选择退仓日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),c(t,{span:12},{default:m(()=>[c(l,{label:"退仓人:"},{default:m(()=>[c(r,{modelValue:o.returner,"onUpdate:modelValue":a[1]||(a[1]=e=>o.returner=e),placeholder:"请输入退仓人"},null,8,["modelValue"])]),_:1})]),_:1}),c(t,{span:12},{default:m(()=>[c(l,{label:"所属部门:"},{default:m(()=>[c(r,{modelValue:o.department,"onUpdate:modelValue":a[2]||(a[2]=e=>o.department=e),placeholder:"请输入所属部门"},null,8,["modelValue"])]),_:1})]),_:1}),c(t,{span:12},{default:m(()=>[c(l,{label:"退仓类型:"},{default:m(()=>[c(s,{modelValue:o.returnType,"onUpdate:modelValue":a[3]||(a[3]=e=>o.returnType=e),placeholder:"请选择退仓类型",style:{width:"100%"}},{default:m(()=>[c(i,{label:"工程剩余",value:"projectSurplus"}),c(i,{label:"质量问题",value:"qualityIssue"}),c(i,{label:"规格不符",value:"specMismatch"}),c(i,{label:"其他",value:"other"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),c(t,{span:12},{default:m(()=>[c(l,{label:"关联工程:"},{default:m(()=>[c(s,{modelValue:o.relatedProject,"onUpdate:modelValue":a[4]||(a[4]=e=>o.relatedProject=e),placeholder:"请选择关联工程",style:{width:"100%"}},{default:m(()=>[(f(!0),p(b,null,v(k.value,e=>(f(),V(i,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),c(d,{gutter:20,class:"form-section"},{default:m(()=>[c(t,{span:24},{default:m(()=>a[13]||(a[13]=[y("div",{class:"section-title"},"退仓物料",-1)])),_:1,__:[13]}),c(t,{span:24},{default:m(()=>[c($,{data:O.value,border:"",class:"return-table"},{default:m(()=>[c(K,{type:"index",label:"序号",width:"60"}),c(K,{prop:"materialCode",label:"公司物料编码",width:"120"}),c(K,{prop:"materialName",label:"物料名称",width:"120"}),c(K,{prop:"model",label:"型号",width:"100"}),c(K,{prop:"specification",label:"规格",width:"100"}),c(K,{prop:"unit",label:"单位",width:"80"}),c(K,{prop:"stockQuantity",label:"库存数量",width:"100"}),c(K,{prop:"returnQuantity",label:"退仓数量"},{default:m(e=>[c(q,{modelValue:e.row.returnQuantity,"onUpdate:modelValue":a=>e.row.returnQuantity=a,min:1,max:e.row.maxReturnQuantity,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue","max"])]),_:1}),c(K,{prop:"unitPrice",label:"单价(元)",width:"100"}),c(K,{prop:"subtotal",label:"小计(元)",width:"100"}),c(K,{label:"操作",width:"80",fixed:"right"},{default:m(e=>[c(z,{type:"danger",link:"",onClick:a=>{return t=e.$index,O.value.splice(t,1),n.success("删除成功"),void S();var t}},{default:m(()=>a[14]||(a[14]=[h("删除",-1)])),_:2,__:[14]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),y("div",C,[c(z,{type:"primary",icon:"Plus",onClick:T},{default:m(()=>a[15]||(a[15]=[h("添加物料",-1)])),_:1,__:[15]})])]),_:1})]),_:1}),c(d,{gutter:20,class:"form-section"},{default:m(()=>[c(t,{span:24},{default:m(()=>a[16]||(a[16]=[y("div",{class:"section-title"},"添加物料",-1)])),_:1,__:[16]}),c(t,{span:24},{default:m(()=>[y("div",g,[c(z,{type:"primary",onClick:T},{default:m(()=>a[17]||(a[17]=[h("选择物料",-1)])),_:1,__:[17]}),c(z,{type:"success",onClick:I},{default:m(()=>a[18]||(a[18]=[h("添加",-1)])),_:1,__:[18]})])]),_:1})]),_:1}),c(F,{modelValue:N.value,"onUpdate:modelValue":a[7]||(a[7]=e=>N.value=e),title:"选择物料",width:"800"},{footer:m(()=>[c(z,{onClick:a[6]||(a[6]=e=>N.value=!1)},{default:m(()=>a[21]||(a[21]=[h("取消",-1)])),_:1,__:[21]})]),default:m(()=>[c(d,{gutter:20,style:{"margin-bottom":"20px"}},{default:m(()=>[c(t,{span:18},{default:m(()=>[c(r,{modelValue:M.value,"onUpdate:modelValue":a[5]||(a[5]=e=>M.value=e),placeholder:"请输入搜索关键词",clearable:""},null,8,["modelValue"])]),_:1}),c(t,{span:6},{default:m(()=>[c(z,{type:"primary",icon:"Search",onClick:L},{default:m(()=>a[19]||(a[19]=[h("搜索",-1)])),_:1,__:[19]})]),_:1})]),_:1}),c($,{data:Y.value,border:"",height:"400"},{default:m(()=>[c(K,{prop:"materialCode",label:"公司物料编码",width:"120"}),c(K,{prop:"materialName",label:"物料名称",width:"120"}),c(K,{prop:"model",label:"型号",width:"100"}),c(K,{prop:"specification",label:"规格",width:"100"}),c(K,{prop:"unit",label:"单位",width:"80"}),c(K,{prop:"stockQuantity",label:"库存",width:"80"}),c(K,{prop:"maxReturnQuantity",label:"可退数量",width:"100"}),c(K,{label:"操作",width:"80",fixed:"right"},{default:m(e=>[c(z,{type:"primary",link:"",onClick:a=>W(e.row)},{default:m(()=>a[20]||(a[20]=[h("选择",-1)])),_:2,__:[20]},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1},8,["modelValue"]),c(d,{gutter:20,class:"form-section"},{default:m(()=>[c(t,{span:24},{default:m(()=>a[22]||(a[22]=[y("div",{class:"section-title"},"退仓统计",-1)])),_:1,__:[22]}),c(t,{span:24},{default:m(()=>[c(G,{class:"statistics-card"},{default:m(()=>[c(d,{gutter:20},{default:m(()=>[c(t,{span:12},{default:m(()=>[y("div",x,[a[23]||(a[23]=y("span",{class:"label"},"退仓物料种类:",-1)),y("span",null,w(D.materialTypes)+"种",1)])]),_:1}),c(t,{span:12},{default:m(()=>[y("div",P,[a[24]||(a[24]=y("span",{class:"label"},"退仓物料总数:",-1)),y("span",null,w(D.totalQuantity)+"件",1)])]),_:1}),c(t,{span:12},{default:m(()=>[y("div",j,[a[25]||(a[25]=y("span",{class:"label"},"退仓总金额:",-1)),y("span",R,"¥"+w(D.totalAmount),1)])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),c(d,{gutter:20,class:"form-section"},{default:m(()=>[c(t,{span:24},{default:m(()=>a[26]||(a[26]=[y("div",{class:"section-title"},"备注信息",-1)])),_:1,__:[26]}),c(t,{span:24},{default:m(()=>[c(l,{label:"退仓原因:"},{default:m(()=>[c(r,{modelValue:o.returnReason,"onUpdate:modelValue":a[8]||(a[8]=e=>o.returnReason=e),type:"textarea",rows:3,placeholder:"请输入退仓原因"},null,8,["modelValue"])]),_:1})]),_:1}),c(t,{span:24},{default:m(()=>[c(l,{label:"备注:"},{default:m(()=>[c(r,{modelValue:o.remarks,"onUpdate:modelValue":a[9]||(a[9]=e=>o.remarks=e),type:"textarea",rows:2,placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),y("div",U,[c(z,{type:"primary",onClick:A},{default:m(()=>a[27]||(a[27]=[h("保存",-1)])),_:1,__:[27]}),c(z,{onClick:J},{default:m(()=>a[28]||(a[28]=[h("打印",-1)])),_:1,__:[28]}),c(z,{type:"success",onClick:B},{default:m(()=>a[29]||(a[29]=[h("提交",-1)])),_:1,__:[29]}),c(z,{onClick:E},{default:m(()=>a[30]||(a[30]=[h("取消",-1)])),_:1,__:[30]})])]),_:1})])}}}),[["__scopeId","data-v-5490289a"]]);export{O as default};
