import{t as e,d as a,E as l}from"./element-plus-7917fd46.js";import{l as t,r as s,_ as o,y as d,R as u,J as r,av as n,x as i,z as c,u as p,O as m,Q as _,aa as f,I as b,P as y,M as g}from"./vue-vendor-fc5a6493.js";import{_ as w}from"./_plugin-vue_export-helper-1b428a4d.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const V={class:"project-finish-container"},v={class:"section-title"},h={class:"section-title"},k={class:"form-actions"},D=w(t({__name:"ProjectFinish",setup(t){const w=s(),D=s(null),U=s([{id:1,name:"阳光小区A栋",status:"在建",progress:95,startDate:"2024-01-12"},{id:2,name:"花园广场项目",status:"在建",progress:88,startDate:"2024-01-15"},{id:3,name:"商业中心B区",status:"在建",progress:92,startDate:"2024-01-20"}]),j=o({projectId:"",acceptanceResult:"",acceptanceDate:"",acceptancePerson:"",customerSignature:"",acceptanceDescription:"",projectSummary:"",maintenanceSuggestions:""}),C={projectId:[{required:!0,message:"请选择工程",trigger:"change"}]},P=e=>{D.value=U.value.find(a=>a.id===Number(e))},W=o([{id:1,workType:"电工",name:"李师傅",dailyWage:300,workDays:25,totalLaborCost:"¥7,500",remarks:""},{id:2,workType:"水工",name:"王师傅",dailyWage:280,workDays:20,totalLaborCost:"¥5,600",remarks:""},{id:3,workType:"安装工",name:"张师傅",dailyWage:320,workDays:22,totalLaborCost:"¥7,040",remarks:""},{id:4,workType:"维修工",name:"赵师傅",dailyWage:250,workDays:15,totalLaborCost:"¥3,750",remarks:""}]),L=o([{id:1,materialCode:"WL001",materialName:"电缆线",model:"YJV-3*4",specification:"3*4mm²",unit:"米",usageQuantity:300,unitPrice:"¥30.00",subtotal:"¥9,000",remarks:""},{id:2,materialCode:"WL002",materialName:"开关面板",model:"KP-86",specification:"86型",unit:"个",usageQuantity:40,unitPrice:"¥250.00",subtotal:"¥10,000",remarks:""},{id:3,materialCode:"WL003",materialName:"插座",model:"ZP-86",specification:"86型",unit:"个",usageQuantity:60,unitPrice:"¥60.00",subtotal:"¥3,600",remarks:""},{id:4,materialCode:"WL004",materialName:"灯具",model:"LED-12W",specification:"12W",unit:"个",usageQuantity:30,unitPrice:"¥130.00",subtotal:"¥3,900",remarks:""}]),x=()=>{l.success("保存成功")},S=()=>{l.success("提交成功")},Y=()=>{l.success("开始打印报告")},I=()=>{l.info("已取消")};return(l,t)=>{const s=n("el-icon"),o=n("el-col"),N=n("el-option"),Q=n("el-select"),M=n("el-form-item"),R=n("el-row"),T=n("el-input"),q=n("el-tag"),E=n("el-progress"),J=n("el-table-column"),$=n("el-input-number"),z=n("el-table"),A=n("el-card"),B=n("el-date-picker"),F=n("el-button"),K=n("el-form");return i(),d("div",V,[u(A,{class:"main-card"},{default:r(()=>[u(K,{model:j,rules:C,ref_key:"formRef",ref:w,"label-width":"120px"},{default:r(()=>[u(R,{gutter:20,class:"form-section"},{default:r(()=>[u(o,{span:24},{default:r(()=>[c("div",v,[u(s,null,{default:r(()=>[u(p(e))]),_:1}),t[10]||(t[10]=m(" 工程选择 ",-1))])]),_:1}),u(o,{span:24},{default:r(()=>[u(M,{label:"选择工程:",prop:"projectId"},{default:r(()=>[u(Q,{modelValue:j.projectId,"onUpdate:modelValue":t[0]||(t[0]=e=>j.projectId=e),placeholder:"请选择要完成的工程",style:{width:"100%"},onChange:P},{default:r(()=>[(i(!0),d(_,null,f(U.value,e=>(i(),b(N,{key:e.id,label:`${e.name} (${e.status})`,value:e.id,disabled:"在建"!==e.status},null,8,["label","value","disabled"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),D.value?(i(),b(R,{key:0,gutter:20,class:"form-section"},{default:r(()=>[u(o,{span:24},{default:r(()=>[c("div",h,[u(s,null,{default:r(()=>[u(p(a))]),_:1}),t[11]||(t[11]=m(" 当前工程信息 ",-1))])]),_:1}),u(o,{span:12},{default:r(()=>[u(M,{label:"工程名称:"},{default:r(()=>[u(T,{modelValue:D.value.name,"onUpdate:modelValue":t[1]||(t[1]=e=>D.value.name=e),readonly:""},null,8,["modelValue"])]),_:1})]),_:1}),u(o,{span:12},{default:r(()=>[u(M,{label:"当前状态:"},{default:r(()=>{return[u(q,{type:(e=D.value.status,{"未开始":"info","在建":"warning","暂停":"danger","完成":"success"}[e]||"info")},{default:r(()=>[m(y(D.value.status),1)]),_:1},8,["type"])];var e}),_:1})]),_:1}),u(o,{span:12},{default:r(()=>[u(M,{label:"当前进度:"},{default:r(()=>[u(E,{percentage:D.value.progress},null,8,["percentage"])]),_:1})]),_:1}),u(o,{span:12},{default:r(()=>[u(M,{label:"开始时间:"},{default:r(()=>[u(T,{modelValue:D.value.startDate,"onUpdate:modelValue":t[2]||(t[2]=e=>D.value.startDate=e),readonly:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})):g("",!0),u(R,{gutter:20,class:"form-section"},{default:r(()=>[u(o,{span:24},{default:r(()=>t[12]||(t[12]=[c("div",{class:"section-title"},"人员工时统计",-1)])),_:1,__:[12]}),u(o,{span:24},{default:r(()=>[u(z,{data:W,border:"",class:"staff-table"},{default:r(()=>[u(J,{type:"index",label:"序号",width:"60"}),u(J,{prop:"workType",label:"工种",width:"120"}),u(J,{prop:"name",label:"人员姓名",width:"120"}),u(J,{prop:"dailyWage",label:"工价(元/天)",width:"120"}),u(J,{prop:"workDays",label:"工作天数",width:"100"},{default:r(e=>[u($,{modelValue:e.row.workDays,"onUpdate:modelValue":a=>e.row.workDays=a,min:0,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),u(J,{prop:"totalLaborCost",label:"总工时费",width:"120"}),u(J,{prop:"remarks",label:"备注"},{default:r(e=>[u(T,{modelValue:e.row.remarks,"onUpdate:modelValue":a=>e.row.remarks=a,placeholder:"请输入备注"},null,8,["modelValue","onUpdate:modelValue"])]),_:1})]),_:1},8,["data"])]),_:1})]),_:1}),u(R,{gutter:20,class:"form-section"},{default:r(()=>[u(o,{span:24},{default:r(()=>t[13]||(t[13]=[c("div",{class:"section-title"},"物料使用汇总",-1)])),_:1,__:[13]}),u(o,{span:24},{default:r(()=>[u(z,{data:L,border:"",class:"material-table"},{default:r(()=>[u(J,{type:"index",label:"序号",width:"60"}),u(J,{prop:"materialCode",label:"公司物料编码",width:"120"}),u(J,{prop:"materialName",label:"物料名称",width:"120"}),u(J,{prop:"model",label:"型号",width:"100"}),u(J,{prop:"specification",label:"规格",width:"100"}),u(J,{prop:"unit",label:"单位",width:"80"}),u(J,{prop:"usageQuantity",label:"使用数量",width:"100"}),u(J,{prop:"unitPrice",label:"单价",width:"100"}),u(J,{prop:"subtotal",label:"小计",width:"100"}),u(J,{prop:"remarks",label:"备注"},{default:r(e=>[u(T,{modelValue:e.row.remarks,"onUpdate:modelValue":a=>e.row.remarks=a,placeholder:"请输入备注"},null,8,["modelValue","onUpdate:modelValue"])]),_:1})]),_:1},8,["data"])]),_:1})]),_:1}),u(R,{gutter:20,class:"form-section"},{default:r(()=>[u(o,{span:24},{default:r(()=>t[14]||(t[14]=[c("div",{class:"section-title"},"成本汇总",-1)])),_:1,__:[14]}),u(o,{span:24},{default:r(()=>[u(A,{class:"cost-summary-card"},{default:r(()=>[u(R,{gutter:20},{default:r(()=>[u(o,{span:12},{default:r(()=>t[15]||(t[15]=[c("div",{class:"cost-item"},[c("span",{class:"label"},"人工成本:"),c("span",null,"¥23,890")],-1)])),_:1,__:[15]}),u(o,{span:12},{default:r(()=>t[16]||(t[16]=[c("div",{class:"cost-item"},[c("span",{class:"label"},"物料成本:"),c("span",null,"¥26,500")],-1)])),_:1,__:[16]}),u(o,{span:12},{default:r(()=>t[17]||(t[17]=[c("div",{class:"cost-item"},[c("span",{class:"label"},"其他费用:"),c("span",null,"¥2,300")],-1)])),_:1,__:[17]}),u(o,{span:12},{default:r(()=>t[18]||(t[18]=[c("div",{class:"cost-item"},[c("span",{class:"label"},"总成本:"),c("span",{class:"total-cost"},"¥52,690")],-1)])),_:1,__:[18]}),u(o,{span:12},{default:r(()=>t[19]||(t[19]=[c("div",{class:"cost-item"},[c("span",{class:"label"},"预算成本:"),c("span",null,"¥55,000")],-1)])),_:1,__:[19]}),u(o,{span:12},{default:r(()=>t[20]||(t[20]=[c("div",{class:"cost-item"},[c("span",{class:"label"},"成本差异:"),c("span",{class:"saving"},"¥2,310 (节约)")],-1)])),_:1,__:[20]})]),_:1})]),_:1})]),_:1})]),_:1}),u(R,{gutter:20,class:"form-section"},{default:r(()=>[u(o,{span:24},{default:r(()=>t[21]||(t[21]=[c("div",{class:"section-title"},"验收信息",-1)])),_:1,__:[21]}),u(o,{span:12},{default:r(()=>[u(M,{label:"验收结果:"},{default:r(()=>[u(Q,{modelValue:j.acceptanceResult,"onUpdate:modelValue":t[3]||(t[3]=e=>j.acceptanceResult=e),placeholder:"请选择验收结果",style:{width:"100%"}},{default:r(()=>[u(N,{label:"合格",value:"qualified"}),u(N,{label:"不合格",value:"unqualified"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),u(o,{span:12},{default:r(()=>[u(M,{label:"验收日期:"},{default:r(()=>[u(B,{modelValue:j.acceptanceDate,"onUpdate:modelValue":t[4]||(t[4]=e=>j.acceptanceDate=e),type:"date",placeholder:"请选择验收日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),u(o,{span:12},{default:r(()=>[u(M,{label:"验收人员:"},{default:r(()=>[u(T,{modelValue:j.acceptancePerson,"onUpdate:modelValue":t[5]||(t[5]=e=>j.acceptancePerson=e),placeholder:"请输入验收人员"},null,8,["modelValue"])]),_:1})]),_:1}),u(o,{span:12},{default:r(()=>[u(M,{label:"客户签字:"},{default:r(()=>[u(T,{modelValue:j.customerSignature,"onUpdate:modelValue":t[6]||(t[6]=e=>j.customerSignature=e),placeholder:"请输入客户签字"},null,8,["modelValue"])]),_:1})]),_:1}),u(o,{span:24},{default:r(()=>[u(M,{label:"验收说明:"},{default:r(()=>[u(T,{modelValue:j.acceptanceDescription,"onUpdate:modelValue":t[7]||(t[7]=e=>j.acceptanceDescription=e),type:"textarea",rows:3,placeholder:"请输入验收说明"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),u(R,{gutter:20,class:"form-section"},{default:r(()=>[u(o,{span:24},{default:r(()=>t[22]||(t[22]=[c("div",{class:"section-title"},"备注信息",-1)])),_:1,__:[22]}),u(o,{span:24},{default:r(()=>[u(M,{label:"工程总结:"},{default:r(()=>[u(T,{modelValue:j.projectSummary,"onUpdate:modelValue":t[8]||(t[8]=e=>j.projectSummary=e),type:"textarea",rows:3,placeholder:"请输入工程总结"},null,8,["modelValue"])]),_:1})]),_:1}),u(o,{span:24},{default:r(()=>[u(M,{label:"后续维护建议:"},{default:r(()=>[u(T,{modelValue:j.maintenanceSuggestions,"onUpdate:modelValue":t[9]||(t[9]=e=>j.maintenanceSuggestions=e),type:"textarea",rows:3,placeholder:"请输入后续维护建议"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),c("div",k,[u(F,{type:"primary",onClick:x},{default:r(()=>t[23]||(t[23]=[m("保存",-1)])),_:1,__:[23]}),u(F,{type:"success",onClick:S},{default:r(()=>t[24]||(t[24]=[m("提交",-1)])),_:1,__:[24]}),u(F,{onClick:Y},{default:r(()=>t[25]||(t[25]=[m("打印报告",-1)])),_:1,__:[25]}),u(F,{onClick:I},{default:r(()=>t[26]||(t[26]=[m("取消",-1)])),_:1,__:[26]})])]),_:1},8,["model"])]),_:1})])}}}),[["__scopeId","data-v-6c21951c"]]);export{D as default};
