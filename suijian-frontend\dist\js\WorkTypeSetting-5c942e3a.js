var e=Object.defineProperty,a=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty,t=Object.prototype.propertyIsEnumerable,u=(a,l,t)=>l in a?e(a,l,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[l]=t,r=(e,r)=>{for(var o in r||(r={}))l.call(r,o)&&u(e,o,r[o]);if(a)for(var o of a(r))t.call(r,o)&&u(e,o,r[o]);return e};import{E as o}from"./element-plus-ad78a7bf.js";import{l as d,_ as s,r as i,c as p,y as n,R as m,J as c,av as y,x as _,z as v,O as f,P as b,M as w}from"./vue-vendor-fc5a6493.js";import{_ as k}from"./_plugin-vue_export-helper-1b428a4d.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const g={class:"work-type-setting-container"},T={class:"search-buttons"},h={class:"form-actions"},D={key:0},C=k(d({__name:"WorkTypeSetting",setup(e){const a=s({workTypeName:""}),l=s({currentPage:1,pageSize:10,total:15}),t=i([{id:1,workTypeName:"电工",workTypeDesc:"电气安装维修",standardDailyWage:300,employeeCount:5,status:"enabled",createTime:"2023-01-01"},{id:2,workTypeName:"水工",workTypeDesc:"水管安装维修",standardDailyWage:280,employeeCount:4,status:"enabled",createTime:"2023-01-01"},{id:3,workTypeName:"安装工",workTypeDesc:"设备安装调试",standardDailyWage:320,employeeCount:6,status:"enabled",createTime:"2023-01-01"},{id:4,workTypeName:"维修工",workTypeDesc:"设备维修保养",standardDailyWage:250,employeeCount:3,status:"enabled",createTime:"2023-01-01"},{id:5,workTypeName:"油漆工",workTypeDesc:"墙面涂刷处理",standardDailyWage:260,employeeCount:2,status:"enabled",createTime:"2023-01-01"}]),u=i({id:0,workTypeName:"",workTypeDesc:"",standardDailyWage:0,status:"enabled",createTime:"",updateTime:"",employeeList:[],projectCount:0,workDays:0,totalOutput:0}),d=i(!1),k=i(!1),C=i(!1),V=i(""),W=i(),N=p(()=>u.value.id?"修改工种":"新增工种"),j={workTypeName:[{required:!0,message:"请输入工种名称",trigger:"blur"}],workTypeDesc:[{required:!0,message:"请输入工种描述",trigger:"blur"}],standardDailyWage:[{required:!0,message:"请输入标准工价",trigger:"blur"}]},x=()=>{o.success("搜索成功")},O=()=>{a.workTypeName=""},z=e=>{l.pageSize=e},U=e=>{l.currentPage=e},P=()=>{u.value={id:0,workTypeName:"",workTypeDesc:"",standardDailyWage:0,status:"enabled",createTime:"",updateTime:"",employeeList:[],projectCount:0,workDays:0,totalOutput:0},d.value=!0},S=()=>{W.value&&W.value.validate(e=>{e&&(o.success("保存成功"),d.value=!1)})},L=()=>{W.value&&W.value.resetFields()},E=()=>{C.value=!0},q=e=>{V.value=e.name},R=()=>{o.success("下载模板")},F=()=>{o.success("导入成功"),C.value=!1},I=()=>{o.success("导出Excel")};return(e,s)=>{const i=y("el-input"),p=y("el-col"),$=y("el-button"),J=y("el-row"),M=y("el-table-column"),A=y("el-tag"),B=y("el-table"),G=y("el-pagination"),H=y("el-form-item"),K=y("el-input-number"),Q=y("el-radio"),X=y("el-radio-group"),Y=y("el-form"),Z=y("el-dialog"),ee=y("el-divider"),ae=y("el-upload"),le=y("el-card");return _(),n("div",g,[m(le,{class:"main-card"},{header:c(()=>s[13]||(s[13]=[v("div",{class:"card-header"},[v("span",null,"工种设置")],-1)])),default:c(()=>[m(J,{gutter:20,class:"search-section"},{default:c(()=>[m(p,{span:6},{default:c(()=>[m(i,{modelValue:a.workTypeName,"onUpdate:modelValue":s[0]||(s[0]=e=>a.workTypeName=e),placeholder:"工种名称",clearable:""},null,8,["modelValue"])]),_:1}),m(p,{span:12},{default:c(()=>[v("div",T,[m($,{type:"primary",icon:"Search",onClick:x},{default:c(()=>s[14]||(s[14]=[f("搜索",-1)])),_:1,__:[14]}),m($,{icon:"Refresh",onClick:O},{default:c(()=>s[15]||(s[15]=[f("重置",-1)])),_:1,__:[15]})])]),_:1})]),_:1}),m(B,{data:t.value,border:"",class:"work-type-table",style:{width:"100%","margin-top":"20px"}},{default:c(()=>[m(M,{prop:"workTypeName",label:"工种名称","min-width":"120"}),m(M,{prop:"workTypeDesc",label:"工种描述","min-width":"150"}),m(M,{prop:"standardDailyWage",label:"标准工价(元/天)","min-width":"120"}),m(M,{prop:"employeeCount",label:"员工数量","min-width":"80"}),m(M,{prop:"status",label:"状态","min-width":"80"},{default:c(e=>[m(A,{type:"enabled"===e.row.status?"success":"info"},{default:c(()=>[f(b("enabled"===e.row.status?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),m(M,{prop:"createTime",label:"创建时间","min-width":"120"}),m(M,{label:"操作","min-width":"150",fixed:"right"},{default:c(e=>[m($,{type:"primary",link:"",onClick:a=>{return l=e.row,u.value=r({},l),u.value.createTime="2023-01-01",u.value.updateTime="2024-01-01",u.value.employeeList=[{name:"李师傅",dailyWage:300,status:"active",hireDate:"2023-01-15",remarks:"技术熟练"},{name:"王师傅",dailyWage:320,status:"active",hireDate:"2023-03-20",remarks:"高级技工"},{name:"张师傅",dailyWage:280,status:"active",hireDate:"2023-05-10",remarks:"初级技工"}],u.value.projectCount=5,u.value.workDays=22,u.value.totalOutput=6600,void(d.value=!0);var l}},{default:c(()=>s[16]||(s[16]=[f("修改",-1)])),_:2,__:[16]},1032,["onClick"]),m($,{type:"primary",link:"",onClick:a=>{return l=e.row,u.value=r({},l),u.value.createTime="2023-01-01",u.value.updateTime="2024-01-01",u.value.employeeList=[{name:"李师傅",dailyWage:300,status:"active",hireDate:"2023-01-15",remarks:"技术熟练"},{name:"王师傅",dailyWage:320,status:"active",hireDate:"2023-03-20",remarks:"高级技工"},{name:"张师傅",dailyWage:280,status:"active",hireDate:"2023-05-10",remarks:"初级技工"}],u.value.projectCount=5,u.value.workDays=22,u.value.totalOutput=6600,void(k.value=!0);var l}},{default:c(()=>s[17]||(s[17]=[f("详情",-1)])),_:2,__:[17]},1032,["onClick"]),m($,{type:"primary",link:"",onClick:a=>(e=>{const a="enabled"===e.status?"disabled":"enabled";o.success(`已${"enabled"===a?"启用":"禁用"}工种: ${e.workTypeName}`),e.status=a})(e.row)},{default:c(()=>[f(b("enabled"===e.row.status?"禁用":"启用"),1)]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),m(G,{"current-page":l.currentPage,"onUpdate:currentPage":s[1]||(s[1]=e=>l.currentPage=e),"page-size":l.pageSize,"onUpdate:pageSize":s[2]||(s[2]=e=>l.pageSize=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:l.total,onSizeChange:z,onCurrentChange:U,class:"pagination"},null,8,["current-page","page-size","total"]),v("div",h,[m($,{type:"primary",icon:"Plus",onClick:P},{default:c(()=>s[18]||(s[18]=[f("新增工种",-1)])),_:1,__:[18]}),m($,{icon:"Upload",onClick:E},{default:c(()=>s[19]||(s[19]=[f("批量导入",-1)])),_:1,__:[19]}),m($,{icon:"Download",onClick:I},{default:c(()=>s[20]||(s[20]=[f("导出Excel",-1)])),_:1,__:[20]})]),m(Z,{modelValue:d.value,"onUpdate:modelValue":s[8]||(s[8]=e=>d.value=e),title:N.value,width:"500",onClose:L},{footer:c(()=>[m($,{onClick:s[7]||(s[7]=e=>d.value=!1)},{default:c(()=>s[23]||(s[23]=[f("取消",-1)])),_:1,__:[23]}),m($,{type:"primary",onClick:S},{default:c(()=>s[24]||(s[24]=[f("保存",-1)])),_:1,__:[24]})]),default:c(()=>[m(Y,{ref_key:"workTypeFormRef",ref:W,model:u.value,rules:j,"label-width":"120px"},{default:c(()=>[m(H,{label:"工种名称:",prop:"workTypeName"},{default:c(()=>[m(i,{modelValue:u.value.workTypeName,"onUpdate:modelValue":s[3]||(s[3]=e=>u.value.workTypeName=e),placeholder:"请输入工种名称"},null,8,["modelValue"])]),_:1}),m(H,{label:"工种描述:",prop:"workTypeDesc"},{default:c(()=>[m(i,{modelValue:u.value.workTypeDesc,"onUpdate:modelValue":s[4]||(s[4]=e=>u.value.workTypeDesc=e),type:"textarea",placeholder:"请输入工种描述"},null,8,["modelValue"])]),_:1}),m(H,{label:"标准工价(元/天):",prop:"standardDailyWage"},{default:c(()=>[m(K,{modelValue:u.value.standardDailyWage,"onUpdate:modelValue":s[5]||(s[5]=e=>u.value.standardDailyWage=e),min:0,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),m(H,{label:"状态:",prop:"status"},{default:c(()=>[m(X,{modelValue:u.value.status,"onUpdate:modelValue":s[6]||(s[6]=e=>u.value.status=e)},{default:c(()=>[m(Q,{label:"enabled"},{default:c(()=>s[21]||(s[21]=[f("启用",-1)])),_:1,__:[21]}),m(Q,{label:"disabled"},{default:c(()=>s[22]||(s[22]=[f("禁用",-1)])),_:1,__:[22]})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),m(Z,{modelValue:k.value,"onUpdate:modelValue":s[10]||(s[10]=e=>k.value=e),title:"工种详情",width:"600"},{footer:c(()=>[m($,{onClick:s[9]||(s[9]=e=>k.value=!1)},{default:c(()=>s[25]||(s[25]=[f("关闭",-1)])),_:1,__:[25]})]),default:c(()=>[m(Y,{model:u.value,"label-width":"120px"},{default:c(()=>[m(H,{label:"工种名称:"},{default:c(()=>[v("span",null,b(u.value.workTypeName),1)]),_:1}),m(H,{label:"工种描述:"},{default:c(()=>[v("span",null,b(u.value.workTypeDesc),1)]),_:1}),m(H,{label:"标准工价:"},{default:c(()=>[v("span",null,b(u.value.standardDailyWage)+"元/天",1)]),_:1}),m(H,{label:"状态:"},{default:c(()=>[m(A,{type:"enabled"===u.value.status?"success":"info"},{default:c(()=>[f(b("enabled"===u.value.status?"启用":"禁用"),1)]),_:1},8,["type"])]),_:1}),m(H,{label:"创建时间:"},{default:c(()=>[v("span",null,b(u.value.createTime),1)]),_:1}),m(H,{label:"更新时间:"},{default:c(()=>[v("span",null,b(u.value.updateTime),1)]),_:1}),m(ee),m(H,{label:"关联员工列表:"},{default:c(()=>[m(B,{data:u.value.employeeList,border:"",style:{width:"100%"}},{default:c(()=>[m(M,{prop:"name",label:"姓名",width:"80"}),m(M,{prop:"dailyWage",label:"工价(元/天)",width:"100"}),m(M,{prop:"status",label:"状态",width:"80"},{default:c(e=>[m(A,{type:"active"===e.row.status?"success":"info"},{default:c(()=>[f(b("active"===e.row.status?"在职":"离职"),1)]),_:2},1032,["type"])]),_:1}),m(M,{prop:"hireDate",label:"入职时间",width:"120"}),m(M,{prop:"remarks",label:"备注"})]),_:1},8,["data"])]),_:1}),m(H,{label:"工作统计:"},{default:c(()=>[v("div",null,"- 本月参与项目: "+b(u.value.projectCount)+"个",1),v("div",null,"- 本月工作天数: "+b(u.value.workDays)+"天",1),v("div",null,"- 本月总产值: ¥"+b(u.value.totalOutput),1)]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),m(Z,{modelValue:C.value,"onUpdate:modelValue":s[12]||(s[12]=e=>C.value=e),title:"批量导入",width:"500"},{footer:c(()=>[m($,{onClick:s[11]||(s[11]=e=>C.value=!1)},{default:c(()=>s[30]||(s[30]=[f("取消",-1)])),_:1,__:[30]}),m($,{type:"primary",onClick:F},{default:c(()=>s[31]||(s[31]=[f("导入",-1)])),_:1,__:[31]})]),default:c(()=>[m(Y,{"label-width":"100px"},{default:c(()=>[m(H,{label:"导入类型:"},{default:c(()=>s[26]||(s[26]=[v("span",null,"工种设置",-1)])),_:1,__:[26]}),m(H,{label:"选择文件:"},{default:c(()=>[m(ae,{class:"upload-demo",action:"","auto-upload":!1,"on-change":q},{default:c(()=>[m($,{type:"primary"},{default:c(()=>s[27]||(s[27]=[f("选择文件",-1)])),_:1,__:[27]})]),_:1}),V.value?(_(),n("div",D,"文件名: "+b(V.value),1)):w("",!0)]),_:1}),m(H,{label:"模板下载:"},{default:c(()=>[m($,{type:"primary",link:"",onClick:R},{default:c(()=>s[28]||(s[28]=[f("下载模板",-1)])),_:1,__:[28]})]),_:1}),m(H,{label:"导入说明:"},{default:c(()=>s[29]||(s[29]=[v("div",{class:"import-instructions"},[v("p",null,"1. 请按照模板格式填写工种信息"),v("p",null,"2. 工种名称为必填项且不能重复"),v("p",null,"3. 标准工价为数字，单位为元/天"),v("p",null,"4. 状态只能填写: 启用、禁用"),v("p",null,"5. 支持.xls、.xlsx格式文件")],-1)])),_:1,__:[29]})]),_:1})]),_:1},8,["modelValue"])]),_:1})])}}}),[["__scopeId","data-v-212b5f7a"]]);export{C as default};
