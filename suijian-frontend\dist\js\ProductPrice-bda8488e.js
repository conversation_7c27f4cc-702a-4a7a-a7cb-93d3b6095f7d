var e=Object.defineProperty,a=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty,t=Object.prototype.propertyIsEnumerable,d=(a,l,t)=>l in a?e(a,l,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[l]=t,o=(e,o)=>{for(var u in o||(o={}))l.call(o,u)&&d(e,u,o[u]);if(a)for(var u of a(o))t.call(o,u)&&d(e,u,o[u]);return e};import{E as u}from"./element-plus-7917fd46.js";import{l as i,_ as r,r as n,y as c,R as p,J as m,av as s,x as _,z as f,O as v,P as b,Q as h,aa as y}from"./vue-vendor-fc5a6493.js";import{_ as C}from"./_plugin-vue_export-helper-1b428a4d.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const P={class:"product-price-container"},V={class:"search-buttons"},g={class:"form-actions"},w=C(i({__name:"ProductPrice",setup(e){const a=r({materialCode:"",productName:"",category:""}),l=r({currentPage:1,pageSize:10,total:156}),t=n([{id:1,materialCode:"SP001",partyCode:["JD001","JD002"],productName:"智能开关",model:"KG-86",specification:"86型",unit:"个",unitPrice:"¥45.00",updateTime:"2024-01-15"},{id:2,materialCode:"SP002",partyCode:["JD003"],productName:"LED灯具",model:"LED-12W",specification:"12W",unit:"个",unitPrice:"¥130.00",updateTime:"2024-01-15"},{id:3,materialCode:"SP003",partyCode:["JD004"],productName:"插座面板",model:"ZP-86",specification:"86型",unit:"个",unitPrice:"¥60.00",updateTime:"2024-01-14"},{id:4,materialCode:"SP004",partyCode:["JD005"],productName:"电线",model:"BV-2.5",specification:"2.5mm²",unit:"米",unitPrice:"¥5.00",updateTime:"2024-01-14"},{id:5,materialCode:"SP005",partyCode:["JD006"],productName:"开关面板",model:"KP-86",specification:"86型",unit:"个",unitPrice:"¥250.00",updateTime:"2024-01-13"},{id:6,materialCode:"SP006",partyCode:["JD007"],productName:"水管",model:"PPR-20",specification:"20mm",unit:"米",unitPrice:"¥8.00",updateTime:"2024-01-12"},{id:7,materialCode:"SP007",partyCode:["JD008"],productName:"水龙头",model:"LT-15",specification:"15mm",unit:"个",unitPrice:"¥35.00",updateTime:"2024-01-12"}]),d=n({id:0,materialCode:"",partyCode:[],productName:"",model:"",specification:"",unit:"",unitPrice:"",updateTime:""}),i=n([]),C=r({newPrice:"",adjustReason:"",remarks:""}),w=r({adjustType:"percentage",adjustValue:"10",adjustDirection:"up",adjustReason:"",selectedProducts:[]}),k=n([{id:1,changeTime:"2024-01-15",oldPrice:"¥45.00",newPrice:"¥50.00",changeRatio:"+11.11%",operator:"张三"},{id:2,changeTime:"2024-01-10",oldPrice:"¥40.00",newPrice:"¥45.00",changeRatio:"+12.50%",operator:"李四"},{id:3,changeTime:"2024-01-05",oldPrice:"¥35.00",newPrice:"¥40.00",changeRatio:"+14.29%",operator:"王五"},{id:4,changeTime:"2023-12-20",oldPrice:"¥45.00",newPrice:"¥35.00",changeRatio:"-22.22%",operator:"赵六"}]),j=n(!1),x=n(!1),T=n(!1),U=n(!1),N=()=>{u.success("搜索成功")},D=()=>{a.materialCode="",a.productName="",a.category=""},R=e=>{l.pageSize=e},S=e=>{l.currentPage=e},z=()=>{u.success("价格修改成功"),j.value=!1},J=()=>{x.value=!0},O=()=>{w.selectedProducts=t.value.map(e=>e.id)},E=()=>{w.selectedProducts=[]},L=()=>{u.success("预览调价结果")},I=()=>{u.success("批量调价成功"),x.value=!1},K=()=>{u.success("导入价格")},W=()=>{u.success("导出Excel")},q=()=>{t.value.length>0&&(d.value=o({},t.value[0]),T.value=!0)};return(e,u)=>{const r=s("el-input"),n=s("el-col"),A=s("el-option"),B=s("el-select"),G=s("el-button"),Q=s("el-row"),Z=s("el-table-column"),F=s("el-table"),H=s("el-pagination"),M=s("el-form-item"),X=s("el-form"),Y=s("el-dialog"),$=s("el-radio-button"),ee=s("el-radio-group"),ae=s("el-divider"),le=s("el-checkbox"),te=s("el-checkbox-group"),de=s("el-card");return _(),c("div",P,[p(de,{class:"main-card"},{header:m(()=>u[21]||(u[21]=[f("div",{class:"card-header"},[f("span",null,"商品价格")],-1)])),default:m(()=>[p(Q,{gutter:20,class:"search-section"},{default:m(()=>[p(n,{span:6},{default:m(()=>[p(r,{modelValue:a.materialCode,"onUpdate:modelValue":u[0]||(u[0]=e=>a.materialCode=e),placeholder:"公司物料编码",clearable:""},null,8,["modelValue"])]),_:1}),p(n,{span:6},{default:m(()=>[p(r,{modelValue:a.productName,"onUpdate:modelValue":u[1]||(u[1]=e=>a.productName=e),placeholder:"商品名称",clearable:""},null,8,["modelValue"])]),_:1}),p(n,{span:6},{default:m(()=>[p(B,{modelValue:a.category,"onUpdate:modelValue":u[2]||(u[2]=e=>a.category=e),placeholder:"分类",clearable:"",style:{width:"100%"}},{default:m(()=>[p(A,{label:"甲料",value:"material"}),p(A,{label:"商品",value:"product"}),p(A,{label:"辅料",value:"auxiliary"})]),_:1},8,["modelValue"])]),_:1}),p(n,{span:6},{default:m(()=>[f("div",V,[p(G,{type:"primary",icon:"Search",onClick:N},{default:m(()=>u[22]||(u[22]=[v("搜索",-1)])),_:1,__:[22]}),p(G,{icon:"Refresh",onClick:D},{default:m(()=>u[23]||(u[23]=[v("重置",-1)])),_:1,__:[23]})])]),_:1})]),_:1}),p(F,{data:t.value,border:"",class:"product-price-table",style:{width:"100%","margin-top":"20px"}},{default:m(()=>[p(Z,{prop:"materialCode",label:"公司物料编码","min-width":"120"}),p(Z,{prop:"partyCode",label:"甲方编码","min-width":"100"},{default:m(e=>[p(G,{type:"primary",link:"",onClick:a=>{return l=e.row,i.value=l.partyCode,void(U.value=!0);var l}},{default:m(()=>u[24]||(u[24]=[v("显示",-1)])),_:2,__:[24]},1032,["onClick"])]),_:1}),p(Z,{prop:"productName",label:"商品名称","min-width":"120"}),p(Z,{prop:"model",label:"型号","min-width":"100"}),p(Z,{prop:"specification",label:"规格","min-width":"100"}),p(Z,{prop:"unit",label:"单位","min-width":"80"}),p(Z,{prop:"unitPrice",label:"单价(元)","min-width":"100"}),p(Z,{prop:"updateTime",label:"更新时间","min-width":"120"}),p(Z,{label:"操作","min-width":"150",fixed:"right"},{default:m(e=>[p(G,{type:"primary",link:"",onClick:a=>{return l=e.row,d.value=o({},l),C.newPrice="",C.adjustReason="",C.remarks="",void(j.value=!0);var l}},{default:m(()=>u[25]||(u[25]=[v("修改价格",-1)])),_:2,__:[25]},1032,["onClick"]),p(G,{type:"primary",link:"",onClick:a=>{return l=e.row,d.value=o({},l),void(T.value=!0);var l}},{default:m(()=>u[26]||(u[26]=[v("详情",-1)])),_:2,__:[26]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),p(H,{"current-page":l.currentPage,"onUpdate:currentPage":u[3]||(u[3]=e=>l.currentPage=e),"page-size":l.pageSize,"onUpdate:pageSize":u[4]||(u[4]=e=>l.pageSize=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:l.total,onSizeChange:R,onCurrentChange:S,class:"pagination"},null,8,["current-page","page-size","total"]),f("div",g,[p(G,{type:"primary",icon:"Coin",onClick:J},{default:m(()=>u[27]||(u[27]=[v("批量调价",-1)])),_:1,__:[27]}),p(G,{icon:"Upload",onClick:K},{default:m(()=>u[28]||(u[28]=[v("导入价格",-1)])),_:1,__:[28]}),p(G,{icon:"Download",onClick:W},{default:m(()=>u[29]||(u[29]=[v("导出Excel",-1)])),_:1,__:[29]}),p(G,{icon:"DataAnalysis",onClick:q},{default:m(()=>u[30]||(u[30]=[v("价格历史",-1)])),_:1,__:[30]})]),p(Y,{modelValue:j.value,"onUpdate:modelValue":u[9]||(u[9]=e=>j.value=e),title:"修改价格",width:"500"},{footer:m(()=>[p(G,{onClick:u[8]||(u[8]=e=>j.value=!1)},{default:m(()=>u[32]||(u[32]=[v("取消",-1)])),_:1,__:[32]}),p(G,{type:"primary",onClick:z},{default:m(()=>u[33]||(u[33]=[v("保存",-1)])),_:1,__:[33]})]),default:m(()=>[p(X,{model:d.value,"label-width":"120px"},{default:m(()=>[p(M,{label:"公司物料编码:"},{default:m(()=>[f("span",null,b(d.value.materialCode),1)]),_:1}),p(M,{label:"商品名称:"},{default:m(()=>[f("span",null,b(d.value.productName),1)]),_:1}),p(M,{label:"型号:"},{default:m(()=>[f("span",null,b(d.value.model),1)]),_:1}),p(M,{label:"规格:"},{default:m(()=>[f("span",null,b(d.value.specification),1)]),_:1}),p(M,{label:"单位:"},{default:m(()=>[f("span",null,b(d.value.unit),1)]),_:1}),p(M,{label:"当前单价:"},{default:m(()=>[f("span",null,b(d.value.unitPrice),1)]),_:1}),p(M,{label:"新单价:",required:""},{default:m(()=>[p(r,{modelValue:C.newPrice,"onUpdate:modelValue":u[5]||(u[5]=e=>C.newPrice=e),placeholder:"请输入新单价"},{append:m(()=>u[31]||(u[31]=[v("元",-1)])),_:1},8,["modelValue"])]),_:1}),p(M,{label:"调价原因:"},{default:m(()=>[p(r,{modelValue:C.adjustReason,"onUpdate:modelValue":u[6]||(u[6]=e=>C.adjustReason=e),type:"textarea",rows:2,placeholder:"请输入调价原因"},null,8,["modelValue"])]),_:1}),p(M,{label:"备注:"},{default:m(()=>[p(r,{modelValue:C.remarks,"onUpdate:modelValue":u[7]||(u[7]=e=>C.remarks=e),type:"textarea",rows:2,placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),p(Y,{modelValue:x.value,"onUpdate:modelValue":u[16]||(u[16]=e=>x.value=e),title:"批量调价",width:"600"},{footer:m(()=>[p(G,{onClick:O},{default:m(()=>u[38]||(u[38]=[v("全选",-1)])),_:1,__:[38]}),p(G,{onClick:E},{default:m(()=>u[39]||(u[39]=[v("全不选",-1)])),_:1,__:[39]}),p(G,{onClick:L},{default:m(()=>u[40]||(u[40]=[v("预览",-1)])),_:1,__:[40]}),p(G,{type:"primary",onClick:I},{default:m(()=>u[41]||(u[41]=[v("确认调价",-1)])),_:1,__:[41]}),p(G,{onClick:u[15]||(u[15]=e=>x.value=!1)},{default:m(()=>u[42]||(u[42]=[v("取消",-1)])),_:1,__:[42]})]),default:m(()=>[p(X,{model:w,"label-width":"120px"},{default:m(()=>[p(M,{label:"调价方式:"},{default:m(()=>[p(ee,{modelValue:w.adjustType,"onUpdate:modelValue":u[10]||(u[10]=e=>w.adjustType=e)},{default:m(()=>[p($,{label:"percentage"},{default:m(()=>u[34]||(u[34]=[v("百分比调价",-1)])),_:1,__:[34]}),p($,{label:"fixed"},{default:m(()=>u[35]||(u[35]=[v("固定金额调价",-1)])),_:1,__:[35]})]),_:1},8,["modelValue"])]),_:1}),p(M,{label:"调价幅度:"},{default:m(()=>[p(r,{modelValue:w.adjustValue,"onUpdate:modelValue":u[11]||(u[11]=e=>w.adjustValue=e),placeholder:"请输入调价幅度"},{append:m(()=>[v(b("percentage"===w.adjustType?"%":"元"),1)]),_:1},8,["modelValue"])]),_:1}),p(M,{label:"调价方向:"},{default:m(()=>[p(ee,{modelValue:w.adjustDirection,"onUpdate:modelValue":u[12]||(u[12]=e=>w.adjustDirection=e)},{default:m(()=>[p($,{label:"up"},{default:m(()=>u[36]||(u[36]=[v("上涨",-1)])),_:1,__:[36]}),p($,{label:"down"},{default:m(()=>u[37]||(u[37]=[v("下跌",-1)])),_:1,__:[37]})]),_:1},8,["modelValue"])]),_:1}),p(M,{label:"调价原因:"},{default:m(()=>[p(r,{modelValue:w.adjustReason,"onUpdate:modelValue":u[13]||(u[13]=e=>w.adjustReason=e),type:"textarea",rows:2,placeholder:"请输入调价原因"},null,8,["modelValue"])]),_:1}),p(ae),p(M,{label:"选择商品:"},{default:m(()=>[p(de,{class:"product-selection-card"},{default:m(()=>[p(te,{modelValue:w.selectedProducts,"onUpdate:modelValue":u[14]||(u[14]=e=>w.selectedProducts=e)},{default:m(()=>[(_(!0),c(h,null,y(t.value,e=>(_(),c("div",{key:e.id,class:"product-checkbox-item"},[p(le,{label:e.id},{default:m(()=>[v(b(e.productName)+" ("+b(e.materialCode)+") ",1)]),_:2},1032,["label"])]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),p(Y,{modelValue:T.value,"onUpdate:modelValue":u[18]||(u[18]=e=>T.value=e),title:"价格历史",width:"700"},{footer:m(()=>[p(G,{onClick:u[17]||(u[17]=e=>T.value=!1)},{default:m(()=>u[43]||(u[43]=[v("关闭",-1)])),_:1,__:[43]})]),default:m(()=>[p(X,{model:d.value,"label-width":"150px"},{default:m(()=>[p(M,{label:"商品名称:"},{default:m(()=>[f("span",null,b(d.value.productName)+" ("+b(d.value.materialCode)+")",1)]),_:1}),p(M,{label:"价格变更记录:"},{default:m(()=>[p(F,{data:k.value,border:"",height:"300"},{default:m(()=>[p(Z,{prop:"changeTime",label:"时间",width:"150"}),p(Z,{prop:"oldPrice",label:"原价格",width:"100"}),p(Z,{prop:"newPrice",label:"新价格",width:"100"}),p(Z,{prop:"changeRatio",label:"变更幅度",width:"100"}),p(Z,{prop:"operator",label:"操作人",width:"100"})]),_:1},8,["data"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),p(Y,{modelValue:U.value,"onUpdate:modelValue":u[20]||(u[20]=e=>U.value=e),title:"甲方编码列表",width:"500"},{footer:m(()=>[p(G,{onClick:u[19]||(u[19]=e=>U.value=!1)},{default:m(()=>u[44]||(u[44]=[v("关闭",-1)])),_:1,__:[44]})]),default:m(()=>[(_(!0),c(h,null,y(i.value,(e,a)=>(_(),c("div",{key:a,class:"party-code-item"}," 编码"+b(a+1)+": "+b(e),1))),128))]),_:1},8,["modelValue"])]),_:1})])}}}),[["__scopeId","data-v-cd138146"]]);export{w as default};
