import{l as s,y as r,z as a,R as e,J as o,aT as l,av as t,x as i,u as c,O as d}from"./vue-vendor-fc5a6493.js";import{h as n,y as u}from"./element-plus-ad78a7bf.js";import{_ as p}from"./_plugin-vue_export-helper-1b428a4d.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const v={class:"error-404"},_={class:"error-container"},m={class:"error-content"},f={class:"error-actions"},h=p(s({__name:"404",setup(s){const p=l(),h=()=>{p.push("/dashboard")},j=()=>{p.go(-1)};return(s,l)=>{const p=t("el-icon"),g=t("el-button");return i(),r("div",v,[a("div",_,[a("div",m,[l[2]||(l[2]=a("div",{class:"error-code"},"404",-1)),l[3]||(l[3]=a("div",{class:"error-title"},"页面未找到",-1)),l[4]||(l[4]=a("div",{class:"error-description"}," 抱歉，您访问的页面不存在或已被删除。 ",-1)),a("div",f,[e(g,{type:"primary",onClick:h},{default:o(()=>[e(p,null,{default:o(()=>[e(c(n))]),_:1}),l[0]||(l[0]=d(" 返回首页 ",-1))]),_:1,__:[0]}),e(g,{onClick:j},{default:o(()=>[e(p,null,{default:o(()=>[e(c(u))]),_:1}),l[1]||(l[1]=d(" 返回上页 ",-1))]),_:1,__:[1]})])]),l[5]||(l[5]=a("div",{class:"error-image"},[a("div",{class:"image-placeholder"}," 🔍 ")],-1))])])}}}),[["__scopeId","data-v-a76944c9"]]);export{h as default};
