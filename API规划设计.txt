工程管理系统 - API 规划设计
==========================================

1. 首页相关
- GET    /api/dashboard/materials/unused         获取未使用物料统计（项数、总价、明细）
- GET    /api/dashboard/materials/used           获取已使用物料统计（项数、总价、明细）
- GET    /api/dashboard/materials/issued         获取已领出物料统计（项数、总价）
- GET    /api/dashboard/projects/status          获取工程进度统计（未开始/在建/暂停/完成数量及明细）
- GET    /api/dashboard/loose-materials          获取散单物料统计（平账时间、甲料分类统计、库存分布）
- GET    /api/dashboard/products/sales           获取商品售卖统计（本月售卖、销售排行榜、畅销排行榜）

2. 仓库管理
- GET    /api/materials                          获取物料列表（支持搜索）
- POST   /api/materials/apply                    提交领料申请
- POST   /api/materials/apply/save-print         保存并打印领料申请
- POST   /api/materials/inbound                  甲料入库
- POST   /api/materials/inbound/save-print       保存并打印甲料入库
- POST   /api/materials/return                   物料退仓
- POST   /api/materials/return/save-print        保存并打印物料退仓
- POST   /api/materials/auxiliary/purchase       辅料采购
- POST   /api/materials/auxiliary/save-print     保存并打印辅料采购
- POST   /api/products/inbound                   商品入库
- POST   /api/products/inbound/save-print        保存并打印商品入库
- GET    /api/materials/records                  获取进出记录
- POST   /api/products/outbound                  商品售卖出库
- GET    /api/materials/prices                   获取物料价格列表
- PUT    /api/materials/prices/{id}              修改物料价格
- GET    /api/products/prices                    获取商品价格列表
- PUT    /api/products/prices/{id}               修改商品价格
- GET    /api/materials/base                     获取物料基础库
- POST   /api/materials/base/import              导入物料基础库（excel）
- POST   /api/materials/base                     新增物料基础信息
- PUT    /api/materials/base/{id}                编辑物料基础信息
- GET    /api/materials/warning                  获取库存预警列表
- PUT    /api/materials/warning/{id}             修改预警数量

3. 散户订单
- GET    /api/loose-orders                       获取散户订单列表（支持多条件搜索）
- POST   /api/loose-orders                       新增甲方派单
- POST   /api/loose-orders/assign                订单分派（分派师傅、物料、用时）
- GET    /api/loose-orders/balance/month         获取月度平账信息
- POST   /api/loose-orders/balance/confirm       确认平账完成
- GET    /api/loose-orders/balance/records       获取平账记录列表
- GET    /api/loose-orders/balance/{id}/detail   获取平账详情
- POST   /api/loose-orders/execute                散户订单执行（提交完成时间、实际用时、物料消耗明细、备注）
- GET    /api/loose-orders/{id}/execution         获取散户订单执行详情
- PUT    /api/loose-orders/{id}/execution         编辑散户订单执行信息

4. 工程订单
- GET    /api/projects                           获取工程订单列表（支持多条件搜索）
- POST   /api/projects                           新增甲方派单
- GET    /api/projects/types                     获取工种设置列表
- POST   /api/projects/types                     新增工种
- PUT    /api/projects/types/{id}                编辑工种
- POST   /api/projects/start                     工程开始
- POST   /api/projects/progress                  工程推进（进度、人员、物料）
- POST   /api/projects/pause                     工程暂停
- POST   /api/projects/finish                    工程完成
- POST   /api/projects/costs/external            外部成本录入

5. 员工管理
- GET    /api/employees                          获取员工列表
- PUT    /api/employees/{id}                     修改员工工种、工价
- GET    /api/employees/types                    获取工种设置列表
- POST   /api/employees/types                    新增工种
- PUT    /api/employees/types/{id}               编辑工种
- POST   /api/employees/types/import             导入工种（excel）
- GET    /api/employees/performance              获取绩效参数列表
- PUT    /api/employees/performance/{id}         修改绩效参数

6. 系统设置
- GET    /api/users                              获取用户列表
- PUT    /api/users/{id}                         修改用户信息
- POST   /api/users                              新增用户
- GET    /api/permissions                        获取权限列表
- POST   /api/permissions                        新增权限
- PUT    /api/permissions/{id}                   修改权限
- GET    /api/roles                              获取角色列表
- POST   /api/roles                              新增角色
- PUT    /api/roles/{id}                         修改角色
- POST   /api/roles/{id}/permissions             分配角色权限
- GET    /api/logs                               获取系统日志（支持多条件搜索）
- POST   /api/data/import                        基础数据导入（物料）

API 设计说明：
1. 所有API采用RESTful风格设计
2. 使用HTTP标准方法：GET（查询）、POST（创建）、PUT（更新）、DELETE（删除）
3. 统一使用JSON格式进行数据交换
4. 支持分页查询的接口统一使用page、pageSize参数
5. 支持搜索的接口统一使用search参数
6. 文件上传接口使用multipart/form-data格式
7. 所有接口都需要进行身份验证和权限校验
8. 错误响应统一使用HTTP状态码和错误信息 