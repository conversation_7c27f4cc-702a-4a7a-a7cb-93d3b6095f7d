import{l as e,c as a,r as t,y as l,R as s,J as d,aU as r,aT as o,av as u,x as _,z as n,u as i,O as f,P as c}from"./vue-vendor-fc5a6493.js";import{h as p,b as m,d as x,t as h,u as y,s as b,c as v,e as w}from"./element-plus-7917fd46.js";import{u as j}from"./index-8b6647d3.js";import{_ as g}from"./_plugin-vue_export-helper-1b428a4d.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const k={class:"layout-container"},P={class:"header-left"},T={class:"header-right"},z={class:"user-info"},B={class:"username"},C=g(e({__name:"index",setup(e){const g=r(),C=o(),I=j(),J=a(()=>g.path),N=a(()=>g.meta.title||"页面"),O=a(()=>I.user||{username:"用户"}),R=t(["/dashboard","/warehouse","/loose-orders","/projects","/employees","/system"]),U=e=>{return a=this,t=null,l=function*(){if("logout"===e)try{yield w.confirm("确定要退出登录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),I.logout(),C.push("/login")}catch(a){}else"profile"===e&&C.push("/system/profile")},new Promise((e,s)=>{var d=e=>{try{o(l.next(e))}catch(a){s(a)}},r=e=>{try{o(l.throw(e))}catch(a){s(a)}},o=a=>a.done?e(a.value):Promise.resolve(a.value).then(d,r);o((l=l.apply(a,t)).next())});var a,t,l};return(e,a)=>{const t=u("el-icon"),r=u("el-menu-item"),o=u("el-sub-menu"),w=u("el-menu"),j=u("el-aside"),g=u("el-breadcrumb-item"),C=u("el-breadcrumb"),I=u("el-avatar"),q=u("el-dropdown-item"),A=u("el-dropdown-menu"),D=u("el-dropdown"),E=u("el-header"),F=u("router-view"),G=u("el-main"),H=u("el-container");return _(),l("div",k,[s(H,null,{default:d(()=>[s(j,{width:"250px",class:"sidebar"},{default:d(()=>[a[45]||(a[45]=n("div",{class:"logo"},[n("h2",null,"工程管理系统")],-1)),s(w,{"default-active":J.value,"default-expanded-keys":R.value,class:"sidebar-menu","background-color":"#304156","text-color":"#bfcbd9","active-text-color":"#409eff",router:""},{default:d(()=>[s(o,{index:"/dashboard"},{title:d(()=>[s(t,null,{default:d(()=>[s(i(p))]),_:1}),a[0]||(a[0]=n("span",null,"首页",-1))]),default:d(()=>[s(r,{index:"/dashboard"},{default:d(()=>a[1]||(a[1]=[f("首页概览",-1)])),_:1,__:[1]}),s(r,{index:"/dashboard/material-statistics"},{default:d(()=>a[2]||(a[2]=[f("工程物料统计",-1)])),_:1,__:[2]}),s(r,{index:"/dashboard/project-progress"},{default:d(()=>a[3]||(a[3]=[f("工程进度统计",-1)])),_:1,__:[3]}),s(r,{index:"/dashboard/loose-material-statistics"},{default:d(()=>a[4]||(a[4]=[f("散单物料统计",-1)])),_:1,__:[4]}),s(r,{index:"/dashboard/loose-order-statistics"},{default:d(()=>a[5]||(a[5]=[f("散单情况统计",-1)])),_:1,__:[5]}),s(r,{index:"/dashboard/sales-statistics"},{default:d(()=>a[6]||(a[6]=[f("商品售卖统计",-1)])),_:1,__:[6]})]),_:1}),s(o,{index:"/warehouse"},{title:d(()=>[s(t,null,{default:d(()=>[s(i(m))]),_:1}),a[7]||(a[7]=n("span",null,"仓库管理",-1))]),default:d(()=>[s(r,{index:"/warehouse/material-list"},{default:d(()=>a[8]||(a[8]=[f("物料列表",-1)])),_:1,__:[8]}),s(r,{index:"/warehouse/material-apply"},{default:d(()=>a[9]||(a[9]=[f("领料申请",-1)])),_:1,__:[9]}),s(r,{index:"/warehouse/material-inbound"},{default:d(()=>a[10]||(a[10]=[f("甲料入库",-1)])),_:1,__:[10]}),s(r,{index:"/warehouse/material-return"},{default:d(()=>a[11]||(a[11]=[f("物料退仓",-1)])),_:1,__:[11]}),s(r,{index:"/warehouse/auxiliary-purchase"},{default:d(()=>a[12]||(a[12]=[f("辅料采购",-1)])),_:1,__:[12]}),s(r,{index:"/warehouse/product-inbound"},{default:d(()=>a[13]||(a[13]=[f("商品入库",-1)])),_:1,__:[13]}),s(r,{index:"/warehouse/material-records"},{default:d(()=>a[14]||(a[14]=[f("进出记录",-1)])),_:1,__:[14]}),s(r,{index:"/warehouse/product-outbound"},{default:d(()=>a[15]||(a[15]=[f("商品售卖出库",-1)])),_:1,__:[15]}),s(r,{index:"/warehouse/material-price"},{default:d(()=>a[16]||(a[16]=[f("物料价格",-1)])),_:1,__:[16]}),s(r,{index:"/warehouse/product-price"},{default:d(()=>a[17]||(a[17]=[f("商品价格",-1)])),_:1,__:[17]}),s(r,{index:"/warehouse/material-base"},{default:d(()=>a[18]||(a[18]=[f("物料基础库",-1)])),_:1,__:[18]}),s(r,{index:"/warehouse/stock-warning"},{default:d(()=>a[19]||(a[19]=[f("库存预警",-1)])),_:1,__:[19]})]),_:1}),s(o,{index:"/loose-orders"},{title:d(()=>[s(t,null,{default:d(()=>[s(i(x))]),_:1}),a[20]||(a[20]=n("span",null,"散户订单",-1))]),default:d(()=>[s(r,{index:"/loose-orders/order-list"},{default:d(()=>a[21]||(a[21]=[f("订单列表",-1)])),_:1,__:[21]}),s(r,{index:"/loose-orders/order-assign"},{default:d(()=>a[22]||(a[22]=[f("甲方派单",-1)])),_:1,__:[22]}),s(r,{index:"/loose-orders/order-execute"},{default:d(()=>a[23]||(a[23]=[f("订单执行",-1)])),_:1,__:[23]}),s(r,{index:"/loose-orders/monthly-balance"},{default:d(()=>a[24]||(a[24]=[f("月度平账",-1)])),_:1,__:[24]}),s(r,{index:"/loose-orders/balance-records"},{default:d(()=>a[25]||(a[25]=[f("平账记录",-1)])),_:1,__:[25]})]),_:1}),s(o,{index:"/projects"},{title:d(()=>[s(t,null,{default:d(()=>[s(i(h))]),_:1}),a[26]||(a[26]=n("span",null,"工程订单",-1))]),default:d(()=>[s(r,{index:"/projects/project-list"},{default:d(()=>a[27]||(a[27]=[f("工程订单列表",-1)])),_:1,__:[27]}),s(r,{index:"/projects/project-assign"},{default:d(()=>a[28]||(a[28]=[f("甲方派单",-1)])),_:1,__:[28]}),s(r,{index:"/projects/work-type-setting"},{default:d(()=>a[29]||(a[29]=[f("工种设置",-1)])),_:1,__:[29]}),s(r,{index:"/projects/project-start"},{default:d(()=>a[30]||(a[30]=[f("工程开始",-1)])),_:1,__:[30]}),s(r,{index:"/projects/project-progress"},{default:d(()=>a[31]||(a[31]=[f("工程推进",-1)])),_:1,__:[31]}),s(r,{index:"/projects/project-pause"},{default:d(()=>a[32]||(a[32]=[f("工程暂停",-1)])),_:1,__:[32]}),s(r,{index:"/projects/project-finish"},{default:d(()=>a[33]||(a[33]=[f("工程完成",-1)])),_:1,__:[33]}),s(r,{index:"/projects/external-cost"},{default:d(()=>a[34]||(a[34]=[f("外部成本",-1)])),_:1,__:[34]})]),_:1}),s(o,{index:"/employees"},{title:d(()=>[s(t,null,{default:d(()=>[s(i(y))]),_:1}),a[35]||(a[35]=n("span",null,"员工管理",-1))]),default:d(()=>[s(r,{index:"/employees/employee-list"},{default:d(()=>a[36]||(a[36]=[f("员工列表",-1)])),_:1,__:[36]}),s(r,{index:"/employees/work-type-setting"},{default:d(()=>a[37]||(a[37]=[f("工种设置",-1)])),_:1,__:[37]}),s(r,{index:"/employees/performance-setting"},{default:d(()=>a[38]||(a[38]=[f("绩效设置",-1)])),_:1,__:[38]})]),_:1}),s(o,{index:"/system"},{title:d(()=>[s(t,null,{default:d(()=>[s(i(b))]),_:1}),a[39]||(a[39]=n("span",null,"系统设置",-1))]),default:d(()=>[s(r,{index:"/system/user-management"},{default:d(()=>a[40]||(a[40]=[f("用户管理",-1)])),_:1,__:[40]}),s(r,{index:"/system/add-user"},{default:d(()=>a[41]||(a[41]=[f("增加用户",-1)])),_:1,__:[41]}),s(r,{index:"/system/permission-management"},{default:d(()=>a[42]||(a[42]=[f("权限管理",-1)])),_:1,__:[42]}),s(r,{index:"/system/system-log"},{default:d(()=>a[43]||(a[43]=[f("系统日志",-1)])),_:1,__:[43]}),s(r,{index:"/system/data-import"},{default:d(()=>a[44]||(a[44]=[f("基础数据导入",-1)])),_:1,__:[44]})]),_:1})]),_:1},8,["default-active","default-expanded-keys"])]),_:1,__:[45]}),s(H,null,{default:d(()=>[s(E,{class:"header"},{default:d(()=>[n("div",P,[s(C,{separator:"/"},{default:d(()=>[s(g,null,{default:d(()=>a[46]||(a[46]=[f("首页",-1)])),_:1,__:[46]}),s(g,null,{default:d(()=>[f(c(N.value),1)]),_:1})]),_:1})]),n("div",T,[s(D,{onCommand:U},{dropdown:d(()=>[s(A,null,{default:d(()=>[s(q,{command:"profile"},{default:d(()=>a[47]||(a[47]=[f("个人信息",-1)])),_:1,__:[47]}),s(q,{command:"logout",divided:""},{default:d(()=>a[48]||(a[48]=[f("退出登录",-1)])),_:1,__:[48]})]),_:1})]),default:d(()=>[n("span",z,[s(I,{size:32,src:"/avatar.png"}),n("span",B,c(O.value.username),1),s(t,null,{default:d(()=>[s(i(v))]),_:1})])]),_:1})])]),_:1}),s(G,{class:"main"},{default:d(()=>[s(F)]),_:1})]),_:1})]),_:1})])}}}),[["__scopeId","data-v-3c6b1d13"]]);export{C as default};
