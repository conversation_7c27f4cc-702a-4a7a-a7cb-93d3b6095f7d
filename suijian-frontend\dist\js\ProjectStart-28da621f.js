var e=Object.defineProperty,a=Object.defineProperties,l=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,d=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable,r=(a,l,t)=>l in a?e(a,l,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[l]=t;import{E as s}from"./element-plus-ad78a7bf.js";import{l as i,_ as n,r as u,c as p,y as c,R as m,J as f,av as _,x as b,z as v,Q as y,aa as h,P as g,O as w,M as j,I as V}from"./vue-vendor-fc5a6493.js";import{_ as k}from"./_plugin-vue_export-helper-1b428a4d.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const C={class:"project-start-container"},P={style:{"margin-top":"15px"}},N={class:"form-actions"},U=k(i({__name:"ProjectStart",setup(e){const i=n({selectedProject:"",projectManager:"",managerPhone:"",startInstructions:"",remarks:""}),k=u([{id:"P001",partyOrderNo:"GC202401001",projectName:"阳光小区A栋",projectAddress:"阳光小区A栋1-6层",status:"未开始",estimatedStartTime:"2024-02-01",estimatedEndTime:"2024-03-15"},{id:"P002",partyOrderNo:"GC202401002",projectName:"花园广场项目",projectAddress:"花园广场B区",status:"未开始",estimatedStartTime:"2024-02-01",estimatedEndTime:"2024-04-30"},{id:"P003",partyOrderNo:"GC202401003",projectName:"商业中心B区",projectAddress:"商业中心B区地下停车场",status:"未开始",estimatedStartTime:"2024-02-15",estimatedEndTime:"2024-05-01"}]),U=p(()=>i.selectedProject?k.value.find(e=>e.id===i.selectedProject):null),O=u([{id:1,materialCode:"WL001",materialName:"电缆线",model:"YJV-3*4",specification:"3*4mm²",unit:"米",stockQuantity:500,plannedUsage:200},{id:2,materialCode:"WL002",materialName:"开关面板",model:"KP-86",specification:"86型",unit:"个",stockQuantity:100,plannedUsage:30}]),x=u([{id:1,materialCode:"WL001",materialName:"电缆线",model:"YJV-3*4",specification:"3*4mm²",unit:"米",stockQuantity:500},{id:2,materialCode:"WL002",materialName:"开关面板",model:"KP-86",specification:"86型",unit:"个",stockQuantity:100},{id:3,materialCode:"WL003",materialName:"插座",model:"ZP-86",specification:"86型",unit:"个",stockQuantity:150},{id:4,materialCode:"WL004",materialName:"灯具",model:"LED-12W",specification:"12W",unit:"个",stockQuantity:200}]),Q=u(!1),T=u(""),W=e=>{},E=e=>{switch(e){case"未开始":default:return"info";case"进行中":return"primary";case"暂停":return"warning";case"已完成":return"success"}},L=()=>{Q.value=!0},S=()=>{s.success("搜索物料")},A=e=>{var i;O.value.some(a=>a.id===e.id)?s.warning("该物料已添加"):(O.value.push((i=((e,a)=>{for(var l in a||(a={}))d.call(a,l)&&r(e,l,a[l]);if(t)for(var l of t(a))o.call(a,l)&&r(e,l,a[l]);return e})({},e),a(i,l({plannedUsage:0})))),Q.value=!1,s.success("添加成功"))},I=()=>{s.success("保存成功")},M=()=>{s.success("提交成功")},B=()=>{s.info("已取消")};return(e,a)=>{const l=_("el-col"),t=_("el-option"),d=_("el-select"),o=_("el-form-item"),r=_("el-tag"),s=_("el-row"),n=_("el-input"),u=_("el-table-column"),p=_("el-input-number"),G=_("el-button"),J=_("el-table"),D=_("el-dialog"),K=_("el-card");return b(),c("div",C,[m(K,{class:"main-card"},{default:f(()=>[m(s,{gutter:20,class:"form-section"},{default:f(()=>[m(l,{span:24},{default:f(()=>a[8]||(a[8]=[v("div",{class:"section-title"},"工程信息",-1)])),_:1,__:[8]}),m(l,{span:12},{default:f(()=>[m(o,{label:"选择工程:",prop:"selectedProject"},{default:f(()=>[m(d,{modelValue:i.selectedProject,"onUpdate:modelValue":a[0]||(a[0]=e=>i.selectedProject=e),placeholder:"请选择工程",style:{width:"100%"},onChange:W},{default:f(()=>[(b(!0),c(y,null,h(k.value,e=>(b(),V(t,{key:e.id,label:e.projectName,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),U.value?(b(),c(y,{key:0},[m(l,{span:12},{default:f(()=>[m(o,{label:"甲方订单号:"},{default:f(()=>[v("span",null,g(U.value.partyOrderNo),1)]),_:1})]),_:1}),m(l,{span:12},{default:f(()=>[m(o,{label:"工程名称:"},{default:f(()=>[v("span",null,g(U.value.projectName),1)]),_:1})]),_:1}),m(l,{span:12},{default:f(()=>[m(o,{label:"工程地址:"},{default:f(()=>[v("span",null,g(U.value.projectAddress),1)]),_:1})]),_:1}),m(l,{span:12},{default:f(()=>[m(o,{label:"工程状态:"},{default:f(()=>[m(r,{type:E(U.value.status)},{default:f(()=>[w(g(U.value.status),1)]),_:1},8,["type"])]),_:1})]),_:1}),m(l,{span:12},{default:f(()=>[m(o,{label:"预估开始时间:"},{default:f(()=>[v("span",null,g(U.value.estimatedStartTime),1)]),_:1})]),_:1}),m(l,{span:12},{default:f(()=>[m(o,{label:"预估结束时间:"},{default:f(()=>[v("span",null,g(U.value.estimatedEndTime),1)]),_:1})]),_:1})],64)):j("",!0)]),_:1}),m(s,{gutter:20,class:"form-section"},{default:f(()=>[m(l,{span:24},{default:f(()=>a[9]||(a[9]=[v("div",{class:"section-title"},"开始信息",-1)])),_:1,__:[9]}),m(l,{span:12},{default:f(()=>[m(o,{label:"工程负责人:"},{default:f(()=>[m(n,{modelValue:i.projectManager,"onUpdate:modelValue":a[1]||(a[1]=e=>i.projectManager=e),placeholder:"请输入工程负责人"},null,8,["modelValue"])]),_:1})]),_:1}),m(l,{span:12},{default:f(()=>[m(o,{label:"负责人电话:"},{default:f(()=>[m(n,{modelValue:i.managerPhone,"onUpdate:modelValue":a[2]||(a[2]=e=>i.managerPhone=e),placeholder:"请输入负责人电话"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),m(s,{gutter:20,class:"form-section"},{default:f(()=>[m(l,{span:24},{default:f(()=>a[10]||(a[10]=[v("div",{class:"section-title"},"物料准备",-1)])),_:1,__:[10]}),m(l,{span:24},{default:f(()=>[m(J,{data:O.value,border:"",class:"material-table"},{default:f(()=>[m(u,{type:"index",label:"序号",width:"60"}),m(u,{prop:"materialCode",label:"公司物料编码",width:"120"},{default:f(e=>[v("span",null,g(e.row.materialCode),1)]),_:1}),m(u,{prop:"materialName",label:"名称",width:"120"}),m(u,{prop:"model",label:"型号",width:"100"}),m(u,{prop:"specification",label:"规格",width:"100"}),m(u,{prop:"unit",label:"单位",width:"80"}),m(u,{prop:"stockQuantity",label:"库存数量",width:"100"}),m(u,{prop:"plannedUsage",label:"计划用量"},{default:f(e=>[m(p,{modelValue:e.row.plannedUsage,"onUpdate:modelValue":a=>e.row.plannedUsage=a,min:0,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),m(u,{label:"操作",width:"80",fixed:"right"},{default:f(e=>[m(G,{type:"danger",link:"",onClick:a=>{return l=e.$index,void O.value.splice(l,1);var l}},{default:f(()=>a[11]||(a[11]=[w("删除",-1)])),_:2,__:[11]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),v("div",P,[m(G,{type:"primary",icon:"Plus",onClick:L},{default:f(()=>a[12]||(a[12]=[w("添加物料",-1)])),_:1,__:[12]})])]),_:1})]),_:1}),m(s,{gutter:20,class:"form-section"},{default:f(()=>[m(l,{span:24},{default:f(()=>a[13]||(a[13]=[v("div",{class:"section-title"},"备注信息",-1)])),_:1,__:[13]}),m(l,{span:24},{default:f(()=>[m(o,{label:"开始说明:"},{default:f(()=>[m(n,{modelValue:i.startInstructions,"onUpdate:modelValue":a[3]||(a[3]=e=>i.startInstructions=e),type:"textarea",rows:3,placeholder:"请输入开始说明"},null,8,["modelValue"])]),_:1})]),_:1}),m(l,{span:24},{default:f(()=>[m(o,{label:"备注:"},{default:f(()=>[m(n,{modelValue:i.remarks,"onUpdate:modelValue":a[4]||(a[4]=e=>i.remarks=e),type:"textarea",rows:2,placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),v("div",N,[m(G,{type:"primary",onClick:I},{default:f(()=>a[14]||(a[14]=[w("保存",-1)])),_:1,__:[14]}),m(G,{type:"success",onClick:M},{default:f(()=>a[15]||(a[15]=[w("提交",-1)])),_:1,__:[15]}),m(G,{onClick:B},{default:f(()=>a[16]||(a[16]=[w("取消",-1)])),_:1,__:[16]})]),m(D,{modelValue:Q.value,"onUpdate:modelValue":a[7]||(a[7]=e=>Q.value=e),title:"选择物料",width:"800"},{footer:f(()=>[m(G,{onClick:a[6]||(a[6]=e=>Q.value=!1)},{default:f(()=>a[19]||(a[19]=[w("取消",-1)])),_:1,__:[19]})]),default:f(()=>[m(s,{gutter:20,style:{"margin-bottom":"20px"}},{default:f(()=>[m(l,{span:18},{default:f(()=>[m(n,{modelValue:T.value,"onUpdate:modelValue":a[5]||(a[5]=e=>T.value=e),placeholder:"请输入搜索关键词",clearable:""},null,8,["modelValue"])]),_:1}),m(l,{span:6},{default:f(()=>[m(G,{type:"primary",icon:"Search",onClick:S},{default:f(()=>a[17]||(a[17]=[w("搜索",-1)])),_:1,__:[17]})]),_:1})]),_:1}),m(J,{data:x.value,border:"",height:"400"},{default:f(()=>[m(u,{prop:"materialCode",label:"公司物料编码",width:"120"}),m(u,{prop:"materialName",label:"物料名称",width:"120"}),m(u,{prop:"model",label:"型号",width:"100"}),m(u,{prop:"specification",label:"规格",width:"100"}),m(u,{prop:"unit",label:"单位",width:"80"}),m(u,{prop:"stockQuantity",label:"库存",width:"80"}),m(u,{label:"操作",width:"80",fixed:"right"},{default:f(e=>[m(G,{type:"primary",link:"",onClick:a=>A(e.row)},{default:f(()=>a[18]||(a[18]=[w("选择",-1)])),_:2,__:[18]},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1},8,["modelValue"])]),_:1})])}}}),[["__scopeId","data-v-c5b5fae7"]]);export{U as default};
