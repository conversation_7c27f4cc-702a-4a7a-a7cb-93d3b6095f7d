import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'

// 导入插件
import { setupElementPlus } from '@/plugins/element-plus'

// 导入样式
import '@/assets/styles/common.scss'

// 导入Mock数据（仅在开发环境）
if (import.meta.env.VITE_APP_MOCK_ENABLED === 'true') {
  import('@/utils/mock')
}

const app = createApp(App)

// 安装插件
app.use(createPinia())
app.use(router)
setupElementPlus(app)

app.mount('#app')