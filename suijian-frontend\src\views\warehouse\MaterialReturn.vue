<template>
  <div class="material-return-container">
    <el-card class="main-card">
      <template #header>
        <div class="card-header">
          <span>物料退仓</span>
        </div>
      </template>
      
      <!-- 退仓信息 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">退仓信息</div>
        </el-col>
        <el-col :span="12">
          <el-form-item label="退仓单号:">
            <span>TC20240115001</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="退仓日期:">
            <el-date-picker
              v-model="returnForm.returnDate"
              type="date"
              placeholder="请选择退仓日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="退仓人:">
            <el-input v-model="returnForm.returner" placeholder="请输入退仓人" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属部门:">
            <el-input v-model="returnForm.department" placeholder="请输入所属部门" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="退仓类型:">
            <el-select v-model="returnForm.returnType" placeholder="请选择退仓类型" style="width: 100%">
              <el-option label="工程剩余" value="projectSurplus" />
              <el-option label="质量问题" value="qualityIssue" />
              <el-option label="规格不符" value="specMismatch" />
              <el-option label="其他" value="other" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="关联工程:">
            <el-select v-model="returnForm.relatedProject" placeholder="请选择关联工程" style="width: 100%">
              <el-option
                v-for="project in projectList"
                :key="project.id"
                :label="project.name"
                :value="project.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 退仓物料 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">退仓物料</div>
        </el-col>
        <el-col :span="24">
          <el-table :data="returnItems" border class="return-table">
            <el-table-column type="index" label="序号" width="60" />
            <el-table-column prop="materialCode" label="公司物料编码" width="120" />
            <el-table-column prop="materialName" label="物料名称" width="120" />
            <el-table-column prop="model" label="型号" width="100" />
            <el-table-column prop="specification" label="规格" width="100" />
            <el-table-column prop="unit" label="单位" width="80" />
            <el-table-column prop="stockQuantity" label="库存数量" width="100" />
            <el-table-column prop="returnQuantity" label="退仓数量">
              <template #default="scope">
                <el-input-number
                  v-model="scope.row.returnQuantity"
                  :min="1"
                  :max="scope.row.maxReturnQuantity"
                  controls-position="right"
                  style="width: 100%"
                />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80" fixed="right">
              <template #default="scope">
                <el-button type="danger" link @click="removeItem(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div style="margin-top: 15px;">
            <el-button type="primary" icon="Plus" @click="selectMaterial">添加物料</el-button>
          </div>
        </el-col>
      </el-row>
      
      <!-- 添加物料 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">添加物料</div>
        </el-col>
        <el-col :span="24">
          <div class="add-material-actions">
            <el-button type="primary" @click="selectMaterial">选择物料</el-button>
            <el-button type="success" @click="addMaterial">添加</el-button>
          </div>
        </el-col>
      </el-row>
      
      <!-- 物料选择弹窗 -->
      <el-dialog v-model="materialDialogVisible" title="选择物料" width="800">
        <el-row :gutter="20" style="margin-bottom: 20px;">
          <el-col :span="18">
            <el-input v-model="materialSearch" placeholder="请输入搜索关键词" clearable />
          </el-col>
          <el-col :span="6">
            <el-button type="primary" icon="Search" @click="searchMaterials">搜索</el-button>
          </el-col>
        </el-row>
        
        <el-table :data="materialOptions" border height="400">
          <el-table-column prop="materialCode" label="公司物料编码" width="120" />
          <el-table-column prop="materialName" label="物料名称" width="120" />
          <el-table-column prop="model" label="型号" width="100" />
          <el-table-column prop="specification" label="规格" width="100" />
          <el-table-column prop="unit" label="单位" width="80" />
          <el-table-column prop="stockQuantity" label="库存" width="80" />
          <el-table-column prop="maxReturnQuantity" label="可退数量" width="100" />
          <el-table-column label="操作" width="80" fixed="right">
            <template #default="scope">
              <el-button type="primary" link @click="selectMaterialItem(scope.row)">选择</el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <template #footer>
          <el-button @click="materialDialogVisible = false">取消</el-button>
        </template>
      </el-dialog>
      
      <!-- 退仓统计 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">退仓统计</div>
        </el-col>
        <el-col :span="24">
          <el-card class="statistics-card">
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="statistic-item">
                  <span class="label">退仓物料种类:</span>
                  <span>{{ statistics.materialTypes }}种</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="statistic-item">
                  <span class="label">退仓物料总数:</span>
                  <span>{{ statistics.totalQuantity }}件</span>
                </div>
              </el-col>
            </el-row>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 备注信息 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">备注信息</div>
        </el-col>
        <el-col :span="24">
          <el-form-item label="退仓原因:">
            <el-input
              v-model="returnForm.returnReason"
              type="textarea"
              :rows="3"
              placeholder="请输入退仓原因"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注:">
            <el-input
              v-model="returnForm.remarks"
              type="textarea"
              :rows="2"
              placeholder="请输入备注"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 操作按钮 -->
      <div class="form-actions">
        <el-button type="primary" @click="handleSave">保存</el-button>
        <el-button @click="handlePrint">打印</el-button>
        <el-button type="success" @click="handleSubmit">提交</el-button>
        <el-button @click="handleCancel">取消</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

// 退仓表单
const returnForm = reactive({
  returnDate: '2024-01-15',
  returner: '李四',
  department: '工程部',
  returnType: 'projectSurplus',
  relatedProject: 1,
  returnReason: '',
  remarks: ''
})

// 工程列表
const projectList = ref([
  { id: 1, name: '阳光小区A栋' },
  { id: 2, name: '花园广场项目' },
  { id: 3, name: '商业中心B区' },
  { id: 4, name: '住宅楼C座' }
])

// 退仓物料
const returnItems = ref([
  {
    id: 1,
    materialCode: 'WL001',
    materialName: '电缆线',
    model: 'YJV-3*4',
    specification: '3*4mm²',
    unit: '米',
    stockQuantity: 200,
    returnQuantity: 50,
    maxReturnQuantity: 50
  },
  {
    id: 2,
    materialCode: 'WL002',
    materialName: '开关面板',
    model: 'KP-86',
    specification: '86型',
    unit: '个',
    stockQuantity: 100,
    returnQuantity: 20,
    maxReturnQuantity: 20
  },
  {
    id: 3,
    materialCode: 'WL003',
    materialName: '电线',
    model: 'BV-2.5',
    specification: '2.5mm²',
    unit: '米',
    stockQuantity: 500,
    returnQuantity: 100,
    maxReturnQuantity: 100
  }
])

// 物料选项
const materialOptions = ref([
  {
    id: 1,
    materialCode: 'WL001',
    materialName: '电缆线',
    model: 'YJV-3*4',
    specification: '3*4mm²',
    unit: '米',
    stockQuantity: 200,
    maxReturnQuantity: 50
  },
  {
    id: 2,
    materialCode: 'WL002',
    materialName: '开关面板',
    model: 'KP-86',
    specification: '86型',
    unit: '个',
    stockQuantity: 100,
    maxReturnQuantity: 20
  },
  {
    id: 4,
    materialCode: 'WL004',
    materialName: '水管',
    model: 'PPR-20',
    specification: '20mm',
    unit: '米',
    stockQuantity: 300,
    maxReturnQuantity: 50
  }
])

// 统计信息
const statistics = reactive({
  materialTypes: 3,
  totalQuantity: 170
})

// 弹窗控制
const materialDialogVisible = ref(false)

// 物料搜索
const materialSearch = ref('')

// 选择物料
const selectMaterial = () => {
  materialDialogVisible.value = true
}

// 搜索物料
const searchMaterials = () => {
  ElMessage.success('搜索物料')
  console.log('搜索关键词:', materialSearch.value)
}

// 选择物料项
const selectMaterialItem = (row: any) => {
  // 检查是否已添加
  const exists = returnItems.value.some(item => item.id === row.id)
  if (exists) {
    ElMessage.warning('该物料已添加')
    return
  }
  
  returnItems.value.push({
    ...row,
    returnQuantity: 1
  })
  
  materialDialogVisible.value = false
  ElMessage.success('添加成功')
  updateStatistics()
}

// 删除物料
const removeItem = (index: number) => {
  returnItems.value.splice(index, 1)
  ElMessage.success('删除成功')
  updateStatistics()
}

// 添加物料
const addMaterial = () => {
  ElMessage.success('添加物料')
}

// 更新统计信息
const updateStatistics = () => {
  statistics.materialTypes = returnItems.value.length
  statistics.totalQuantity = returnItems.value.reduce((total, item) => total + item.returnQuantity, 0)
}

// 保存
const handleSave = () => {
  ElMessage.success('保存成功')
  console.log('保存数据:', returnForm, returnItems.value)
}

// 打印
const handlePrint = () => {
  ElMessage.success('开始打印')
}

// 提交
const handleSubmit = () => {
  ElMessage.success('提交成功')
  console.log('提交数据:', returnForm, returnItems.value)
}

// 取消
const handleCancel = () => {
  ElMessage.info('已取消')
}
</script>

<style lang="scss" scoped>
.material-return-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
  
  .main-card {
    margin-bottom: 20px;
    
    .card-header {
      font-size: 18px;
      font-weight: bold;
      color: #303133;
    }
  }
  
  .form-section {
    margin-bottom: 20px;
    
    .section-title {
      font-size: 16px;
      font-weight: bold;
      color: #606266;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ebeef5;
    }
    
    .el-form-item {
      margin-bottom: 18px;
    }
  }
  
  .return-table {
    margin-top: 10px;
  }
  
  .add-material-actions {
    .el-button {
      margin-right: 10px;
    }
  }
  
  .statistics-card {
    background-color: #f0f9ff;
    border-color: #b3e0ff;
    
    .statistic-item {
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;
      
      .label {
        font-weight: bold;
        color: #409eff;
      }
      
      .total-amount {
        font-weight: bold;
        color: #303133;
      }
    }
  }
  
  .form-actions {
    text-align: center;
    padding: 20px 0;
    
    .el-button {
      margin: 0 10px;
      padding: 12px 30px;
    }
  }
}
</style>