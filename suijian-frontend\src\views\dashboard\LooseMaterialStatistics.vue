<template>
  <div class="loose-material-statistics">

    <!-- 平账信息 -->
    <el-card class="balance-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>📅 平账信息</span>
        </div>
      </template>
      
      <div class="balance-info">
        <div class="balance-item">
          <span class="icon">📋</span>
          <span class="label">上次平账时间:</span>
          <span class="value">{{ lastBalanceTime }}</span>
        </div>
        <div class="balance-item">
          <span class="icon">⏰</span>
          <span class="label">距离上次平账:</span>
          <span class="value days">{{ daysSinceLastBalance }}天</span>
        </div>
        <div class="balance-item warning">
          <span class="icon">⚠️</span>
          <span class="label">提醒:</span>
          <span class="value">建议每月进行平账操作</span>
        </div>
      </div>
    </el-card>

    <!-- 甲料分类统计 -->
    <el-card class="material-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>🏷️ 甲料分类统计</span>
        </div>
      </template>
      
      <div class="material-summary">
        <div class="summary-row">
          <div class="summary-item unused">
            <span class="icon">🔴</span>
            <span class="label">未使用甲料:</span>
            <span class="count">{{ unusedMaterialCount }}项</span>
            <span class="quantity">数量: {{ unusedQuantity }}件</span>
            <span class="amount">总价: ¥{{ formatNumber(unusedAmount) }}</span>
          </div>
        </div>
        <div class="summary-row">
          <div class="summary-item used">
            <span class="icon">✅</span>
            <span class="label">已使用甲料:</span>
            <span class="count">{{ usedMaterialCount }}项</span>
            <span class="quantity">数量: {{ usedQuantity }}件</span>
            <span class="amount">总价: ¥{{ formatNumber(usedAmount) }}</span>
          </div>
        </div>
      </div>

      <div class="stock-distribution">
        <h4>📦 甲料库存分布:</h4>
        <div class="distribution-items">
          <div class="distribution-item">
            <span class="icon">🏪</span>
            <span class="label">仓库库存:</span>
            <span class="value">{{ warehouseStock }}件</span>
          </div>
          <div class="distribution-item">
            <span class="icon">👷</span>
            <span class="label">工人师傅:</span>
            <span class="value">{{ workerStock }}件</span>
          </div>
          <div class="distribution-item total">
            <span class="icon">📊</span>
            <span class="label">库存总计:</span>
            <span class="value">{{ totalStock }}件</span>
            <span class="amount">💰 总价值: ¥{{ formatNumber(totalStockValue) }}</span>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 甲料使用趋势 -->
    <el-card class="trend-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>📈 甲料使用趋势</span>
        </div>
      </template>
      
      <div class="trend-summary">
        <div class="trend-item">
          <span class="label">本月使用:</span>
          <span class="value">{{ currentMonthUsage }}件</span>
        </div>
        <div class="trend-item">
          <span class="label">上月使用:</span>
          <span class="value">{{ lastMonthUsage }}件</span>
        </div>
        <div class="trend-item">
          <span class="label">环比:</span>
          <span class="value" :class="{ positive: trendPercentage > 0, negative: trendPercentage < 0 }">
            {{ trendPercentage > 0 ? '+' : '' }}{{ trendPercentage }}%
          </span>
        </div>
        <div class="trend-item">
          <span class="label">平均日耗:</span>
          <span class="value">{{ averageDailyUsage }}件</span>
        </div>
        <div class="trend-item">
          <span class="label">预计月底库存:</span>
          <span class="value">{{ estimatedEndStock }}件</span>
        </div>
      </div>
    </el-card>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <el-button type="success" size="large" @click="viewDetails">
        📋 查看详情
      </el-button>
      <el-button type="default" size="large" @click="refreshData">
        🔄 刷新
      </el-button>
      <el-button type="warning" size="large" @click="usageReport">
        📈 使用报表
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const lastBalanceTime = ref('2024-01-01')
const unusedMaterialCount = ref(8)
const unusedQuantity = ref(150)
const unusedAmount = ref(25000)
const usedMaterialCount = ref(12)
const usedQuantity = ref(280)
const usedAmount = ref(45000)
const warehouseStock = ref(200)
const workerStock = ref(230)
const currentMonthUsage = ref(85)
const lastMonthUsage = ref(92)
const averageDailyUsage = ref(2.8)

// 计算属性
const daysSinceLastBalance = computed(() => {
  const lastDate = new Date(lastBalanceTime.value)
  const today = new Date()
  const diffTime = Math.abs(today.getTime() - lastDate.getTime())
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
})

const totalStock = computed(() => warehouseStock.value + workerStock.value)
const totalStockValue = computed(() => 70000) // 示例值
const trendPercentage = computed(() => {
  if (lastMonthUsage.value === 0) return 0
  return Math.round(((currentMonthUsage.value - lastMonthUsage.value) / lastMonthUsage.value) * 100 * 10) / 10
})

const estimatedEndStock = computed(() => {
  const remainingDays = 31 - new Date().getDate()
  return totalStock.value - (averageDailyUsage.value * remainingDays)
})

// 方法
const formatNumber = (num: number): string => {
  return num.toLocaleString()
}

const viewDetails = () => {
  ElMessage.info('查看甲料详细信息')
  // 这里可以打开详情弹窗或跳转到详情页面
}

const refreshData = () => {
  ElMessage.success('数据已刷新')
  // 这里可以调用API刷新数据
}

const usageReport = () => {
  ElMessage.info('生成甲料使用报表')
  // 这里可以跳转到使用报表页面
}

// 生命周期
onMounted(() => {
  // 页面加载时获取数据
})
</script>

<style scoped>
.loose-material-statistics {
  padding: 20px;
}

.breadcrumb {
  margin-bottom: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: bold;
  margin: 0 0 8px 0;
  color: #303133;
}

.page-description {
  color: #606266;
  margin: 0;
}

.balance-card, .material-card, .trend-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.balance-info {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.balance-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.balance-item.warning {
  background-color: #fef0e6;
  border-left: 4px solid #e6a23c;
}

.balance-item .icon {
  font-size: 16px;
}

.balance-item .label {
  color: #606266;
  min-width: 120px;
}

.balance-item .value {
  font-weight: bold;
  color: #303133;
}

.balance-item .days {
  color: #e6a23c;
}

.material-summary {
  margin-bottom: 20px;
}

.summary-row {
  margin-bottom: 15px;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  border-radius: 8px;
}

.summary-item.unused {
  background-color: #fef2f2;
  border-left: 4px solid #f56565;
}

.summary-item.used {
  background-color: #f0fff4;
  border-left: 4px solid #48bb78;
}

.summary-item .icon {
  font-size: 16px;
}

.summary-item .label {
  color: #606266;
  min-width: 100px;
}

.summary-item .count,
.summary-item .quantity,
.summary-item .amount {
  font-weight: bold;
  color: #303133;
}

.stock-distribution h4 {
  margin: 0 0 15px 0;
  color: #303133;
}

.distribution-items {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.distribution-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.distribution-item.total {
  background-color: #ecf5ff;
  border-left: 4px solid #409eff;
  font-weight: bold;
}

.distribution-item .icon {
  font-size: 16px;
}

.distribution-item .label {
  color: #606266;
  min-width: 100px;
}

.distribution-item .value {
  font-weight: bold;
  color: #409eff;
}

.distribution-item .amount {
  margin-left: 15px;
  color: #67c23a;
}

.trend-summary {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 8px;
}

.trend-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
  min-width: 120px;
}

.trend-item .label {
  color: #606266;
  font-size: 14px;
}

.trend-item .value {
  font-weight: bold;
  font-size: 16px;
  color: #303133;
}

.trend-item .value.positive {
  color: #67c23a;
}

.trend-item .value.negative {
  color: #f56c6c;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 30px;
}
</style>
