<template>
  <div class="layout-container">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside width="250px" class="sidebar">
        <div class="logo">
          <h2>工程管理系统</h2>
        </div>
        
        <el-menu
          :default-active="activeMenu"
          :default-expanded-keys="expandedKeys"
          class="sidebar-menu"
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409eff"
          router
        >
          <!-- 首页 -->
          <el-sub-menu index="/dashboard">
            <template #title>
              <el-icon><House /></el-icon>
              <span>首页</span>
            </template>
            <el-menu-item index="/dashboard">首页概览</el-menu-item>
            <el-menu-item index="/dashboard/material-statistics">工程物料统计</el-menu-item>
            <el-menu-item index="/dashboard/project-progress">工程进度统计</el-menu-item>
            <el-menu-item index="/dashboard/loose-material-statistics">散单物料统计</el-menu-item>
            <el-menu-item index="/dashboard/loose-order-statistics">散单情况统计</el-menu-item>
            <el-menu-item index="/dashboard/sales-statistics">商品售卖统计</el-menu-item>
          </el-sub-menu>
          
          <!-- 仓库管理 -->
          <el-sub-menu index="/warehouse">
            <template #title>
              <el-icon><Box /></el-icon>
              <span>仓库管理</span>
            </template>
            <el-menu-item index="/warehouse/material-list">物料列表</el-menu-item>
            <el-menu-item index="/warehouse/material-apply">领料申请</el-menu-item>
            <el-menu-item index="/warehouse/material-inbound">甲料入库</el-menu-item>
            <el-menu-item index="/warehouse/material-return">物料退仓</el-menu-item>
            <el-menu-item index="/warehouse/auxiliary-purchase">辅料采购</el-menu-item>
            <el-menu-item index="/warehouse/product-inbound">商品入库</el-menu-item>
            <el-menu-item index="/warehouse/material-records">进出记录</el-menu-item>
            <el-menu-item index="/warehouse/product-outbound">商品售卖出库</el-menu-item>
            <el-menu-item index="/warehouse/material-price">物料价格</el-menu-item>
            <el-menu-item index="/warehouse/product-price">商品价格</el-menu-item>
            <el-menu-item index="/warehouse/material-base">物料基础库</el-menu-item>
            <el-menu-item index="/warehouse/stock-warning">库存预警</el-menu-item>
          </el-sub-menu>
          
          <!-- 散户订单 -->
          <el-sub-menu index="/loose-orders">
            <template #title>
              <el-icon><Document /></el-icon>
              <span>散户订单</span>
            </template>
            <el-menu-item index="/loose-orders/order-list">订单列表</el-menu-item>
            <el-menu-item index="/loose-orders/order-assign">甲方派单</el-menu-item>
            <el-menu-item index="/loose-orders/order-execute">订单执行</el-menu-item>
            <el-menu-item index="/loose-orders/monthly-balance">月度平账</el-menu-item>
            <el-menu-item index="/loose-orders/balance-records">平账记录</el-menu-item>
          </el-sub-menu>
          
          <!-- 工程订单 -->
          <el-sub-menu index="/projects">
            <template #title>
              <el-icon><Tools /></el-icon>
              <span>工程订单</span>
            </template>
            <el-menu-item index="/projects/project-list">工程订单列表</el-menu-item>
            <el-menu-item index="/projects/project-assign">甲方派单</el-menu-item>
            <el-menu-item index="/projects/work-type-setting">工种设置</el-menu-item>
            <el-menu-item index="/projects/project-start">工程开始</el-menu-item>
            <el-menu-item index="/projects/project-progress">工程推进</el-menu-item>
            <el-menu-item index="/projects/project-pause">工程暂停</el-menu-item>
            <el-menu-item index="/projects/project-finish">工程完成</el-menu-item>
            <el-menu-item index="/projects/external-cost">外部成本</el-menu-item>
          </el-sub-menu>
          
          <!-- 员工管理 -->
          <el-sub-menu index="/employees">
            <template #title>
              <el-icon><User /></el-icon>
              <span>员工管理</span>
            </template>
            <el-menu-item index="/employees/employee-list">员工列表</el-menu-item>
            <el-menu-item index="/employees/work-type-setting">工种设置</el-menu-item>
            <el-menu-item index="/employees/performance-setting">绩效设置</el-menu-item>
          </el-sub-menu>
          
          <!-- 系统设置 -->
          <el-sub-menu index="/system">
            <template #title>
              <el-icon><Setting /></el-icon>
              <span>系统设置</span>
            </template>
            <el-menu-item index="/system/user-management">用户管理</el-menu-item>
            <el-menu-item index="/system/add-user">增加用户</el-menu-item>
            <el-menu-item index="/system/permission-management">权限管理</el-menu-item>
            <el-menu-item index="/system/system-log">系统日志</el-menu-item>
            <el-menu-item index="/system/data-import">基础数据导入</el-menu-item>
          </el-sub-menu>
        </el-menu>
      </el-aside>
      
      <!-- 主内容区 -->
      <el-container>
        <!-- 头部 -->
        <el-header class="header">
          <div class="header-left">
            <el-breadcrumb separator="/">
              <el-breadcrumb-item>首页</el-breadcrumb-item>
              <el-breadcrumb-item>{{ currentPageTitle }}</el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          
          <div class="header-right">
            <el-dropdown @command="handleCommand">
              <span class="user-info">
                <el-avatar :size="32" src="/avatar.png" />
                <span class="username">{{ userInfo.username }}</span>
                <el-icon><ArrowDown /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">个人信息</el-dropdown-item>
                  <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>
        
        <!-- 内容区 -->
        <el-main class="main">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import { 
  House, 
  Box, 
  Document, 
  Tools, 
  User, 
  Setting, 
  ArrowDown 
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

const activeMenu = computed(() => route.path)
const currentPageTitle = computed(() => route.meta.title || '页面')
const userInfo = computed(() => authStore.user || { username: '用户' })

// 默认展开的菜单项
const expandedKeys = ref(['/dashboard', '/warehouse', '/loose-orders', '/projects', '/employees', '/system'])

const handleCommand = async (command: string) => {
  if (command === 'logout') {
    try {
      await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      
      authStore.logout()
      router.push('/login')
    } catch {
      // 用户取消
    }
  } else if (command === 'profile') {
    // 跳转到个人信息页面
    router.push('/system/profile')
  }
}
</script>

<style lang="scss" scoped>
.layout-container {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

.sidebar {
  background-color: #304156;
  height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  
  .logo {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #2b2f3a;
    
    h2 {
      color: #fff;
      font-size: 16px;
      margin: 0;
    }
  }
  
  .sidebar-menu {
    border: none;
    
    .el-menu-item {
      height: 50px;
      line-height: 50px;
      
      .el-icon {
        margin-right: 10px;
      }
    }
    
    .el-sub-menu {
      .el-sub-menu__title {
        height: 50px;
        line-height: 50px;
        
        .el-icon {
          margin-right: 10px;
        }
      }
      
      .el-menu-item {
        padding-left: 50px !important;
        height: 45px;
        line-height: 45px;
        font-size: 13px;
      }
    }
  }
}

.header {
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  height: 60px;
  flex-shrink: 0;
  
  .header-left {
    .el-breadcrumb {
      font-size: 14px;
    }
  }
  
  .header-right {
    .user-info {
      display: flex;
      align-items: center;
      cursor: pointer;
      
      .username {
        margin: 0 8px;
        color: #606266;
      }
    }
  }
}

.main {
  background-color: #f5f7fa;
  padding: 20px;
  overflow-y: auto;
  overflow-x: hidden;
  height: calc(100vh - 60px);
}
</style> 