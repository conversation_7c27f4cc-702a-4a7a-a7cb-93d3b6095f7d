<template>
  <div class="auxiliary-purchase-container">
    <el-card class="main-card">
      <template #header>
        <div class="card-header">
          <span>辅料采购</span>
        </div>
      </template>
      
      <!-- 采购信息 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">采购信息</div>
        </el-col>
        <el-col :span="12">
          <el-form-item label="采购单号:">
            <span>CG20240115001</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="采购日期:">
            <el-date-picker
              v-model="purchaseForm.purchaseDate"
              type="date"
              placeholder="请选择采购日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="申请人:">
            <el-input v-model="purchaseForm.applicant" placeholder="请输入申请人" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="申请部门:">
            <el-input v-model="purchaseForm.department" placeholder="请输入申请部门" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="供应商:">
            <el-select v-model="purchaseForm.supplier" placeholder="请选择供应商" style="width: 100%">
              <el-option
                v-for="supplier in supplierList"
                :key="supplier.id"
                :label="supplier.name"
                :value="supplier.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="预计到货日期:">
            <el-date-picker
              v-model="purchaseForm.estimatedArrivalDate"
              type="date"
              placeholder="请选择预计到货日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 采购明细 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">采购明细</div>
        </el-col>
        <el-col :span="24">
          <el-table :data="purchaseItems" border class="purchase-table">
            <el-table-column type="index" label="序号" width="60" />
            <el-table-column prop="materialCode" label="公司物料编码" width="120" />
            <el-table-column prop="materialName" label="物料名称" width="120" />
            <el-table-column prop="model" label="型号" width="100" />
            <el-table-column prop="specification" label="规格" width="100" />
            <el-table-column prop="unit" label="单位" width="80" />
            <el-table-column prop="quantity" label="采购数量">
              <template #default="scope">
                <el-input-number 
                  v-model="scope.row.quantity" 
                  :min="1" 
                  controls-position="right" 
                  style="width: 100%" 
                />
              </template>
            </el-table-column>
            <el-table-column prop="unitPrice" label="单价(元)">
              <template #default="scope">
                <el-input 
                  v-model="scope.row.unitPrice" 
                  placeholder="请输入单价" 
                />
              </template>
            </el-table-column>
            <el-table-column prop="subtotal" label="小计(元)" width="100" />
            <el-table-column label="操作" width="80" fixed="right">
              <template #default="scope">
                <el-button type="danger" link @click="removeItem(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div style="margin-top: 15px;">
            <el-button type="primary" icon="Plus" @click="selectMaterial">添加物料</el-button>
          </div>
        </el-col>
      </el-row>
      
      <!-- 添加物料 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">添加物料</div>
        </el-col>
        <el-col :span="24">
          <div class="add-material-actions">
            <el-button type="primary" @click="selectMaterial">选择物料</el-button>
            <el-button type="success" @click="addMaterial">添加</el-button>
          </div>
        </el-col>
      </el-row>
      
      <!-- 物料选择弹窗 -->
      <el-dialog v-model="materialDialogVisible" title="选择物料" width="800">
        <el-row :gutter="20" style="margin-bottom: 20px;">
          <el-col :span="18">
            <el-input v-model="materialSearch" placeholder="请输入搜索关键词" clearable />
          </el-col>
          <el-col :span="6">
            <el-button type="primary" icon="Search" @click="searchMaterials">搜索</el-button>
          </el-col>
        </el-row>
        
        <el-table :data="materialOptions" border height="400">
          <el-table-column prop="materialCode" label="公司物料编码" width="120" />
          <el-table-column prop="materialName" label="物料名称" width="120" />
          <el-table-column prop="model" label="型号" width="100" />
          <el-table-column prop="specification" label="规格" width="100" />
          <el-table-column prop="unit" label="单位" width="80" />
          <el-table-column prop="stockQuantity" label="库存" width="80" />
          <el-table-column label="操作" width="80" fixed="right">
            <template #default="scope">
              <el-button type="primary" link @click="selectMaterialItem(scope.row)">选择</el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <template #footer>
          <el-button @click="materialDialogVisible = false">取消</el-button>
        </template>
      </el-dialog>
      
      <!-- 采购统计 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">采购统计</div>
        </el-col>
        <el-col :span="24">
          <el-card class="statistics-card">
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="statistic-item">
                  <span class="label">采购物料种类:</span>
                  <span>{{ statistics.materialTypes }}种</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="statistic-item">
                  <span class="label">采购物料总数:</span>
                  <span>{{ statistics.totalQuantity }}件</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="statistic-item">
                  <span class="label">采购总金额:</span>
                  <span class="total-amount">¥{{ statistics.totalAmount }}</span>
                </div>
              </el-col>
            </el-row>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 备注信息 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">备注信息</div>
        </el-col>
        <el-col :span="24">
          <el-form-item label="采购说明:">
            <el-input
              v-model="purchaseForm.purchaseDescription"
              type="textarea"
              :rows="3"
              placeholder="请输入采购说明"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注:">
            <el-input
              v-model="purchaseForm.remarks"
              type="textarea"
              :rows="2"
              placeholder="请输入备注"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 操作按钮 -->
      <div class="form-actions">
        <el-button type="primary" @click="handleSave">保存</el-button>
        <el-button @click="handlePrint">打印</el-button>
        <el-button type="success" @click="handleSubmit">提交</el-button>
        <el-button @click="handleCancel">取消</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

// 采购表单
const purchaseForm = reactive({
  purchaseDate: '2024-01-15',
  applicant: '张三',
  department: '仓库部',
  supplier: 1,
  estimatedArrivalDate: '2024-01-20',
  purchaseDescription: '',
  remarks: ''
})

// 供应商列表
const supplierList = ref([
  { id: 1, name: '华强电子' },
  { id: 2, name: '立创电子' },
  { id: 3, name: '得捷电子' },
  { id: 4, name: '贸泽电子' }
])

// 采购明细
const purchaseItems = ref([
  {
    id: 1,
    materialCode: 'FL001',
    materialName: '螺丝',
    model: 'M3',
    specification: 'M3*10',
    unit: '个',
    quantity: 100,
    unitPrice: '0.50',
    subtotal: '50.00'
  },
  {
    id: 2,
    materialCode: 'FL002',
    materialName: '螺母',
    model: 'M3',
    specification: 'M3',
    unit: '个',
    quantity: 100,
    unitPrice: '0.30',
    subtotal: '30.00'
  },
  {
    id: 3,
    materialCode: 'FL003',
    materialName: '垫片',
    model: 'M3',
    specification: 'M3',
    unit: '个',
    quantity: 100,
    unitPrice: '0.10',
    subtotal: '10.00'
  },
  {
    id: 4,
    materialCode: 'FL004',
    materialName: '胶带',
    model: 'T-10',
    specification: '10mm*10m',
    unit: '卷',
    quantity: 20,
    unitPrice: '5.00',
    subtotal: '100.00'
  },
  {
    id: 5,
    materialCode: 'FL005',
    materialName: '扎带',
    model: 'ZD-100',
    specification: '100mm',
    unit: '包',
    quantity: 10,
    unitPrice: '15.00',
    subtotal: '150.00'
  }
])

// 物料选项
const materialOptions = ref([
  {
    id: 1,
    materialCode: 'FL001',
    materialName: '螺丝',
    model: 'M3',
    specification: 'M3*10',
    unit: '个',
    stockQuantity: 500
  },
  {
    id: 2,
    materialCode: 'FL002',
    materialName: '螺母',
    model: 'M3',
    specification: 'M3',
    unit: '个',
    stockQuantity: 400
  },
  {
    id: 3,
    materialCode: 'FL003',
    materialName: '垫片',
    model: 'M3',
    specification: 'M3',
    unit: '个',
    stockQuantity: 600
  },
  {
    id: 4,
    materialCode: 'FL006',
    materialName: '标签',
    model: 'BQ-50',
    specification: '50*30mm',
    unit: '张',
    stockQuantity: 1000
  }
])

// 统计信息
const statistics = reactive({
  materialTypes: 5,
  totalQuantity: 330,
  totalAmount: '340.00'
})

// 弹窗控制
const materialDialogVisible = ref(false)

// 物料搜索
const materialSearch = ref('')

// 选择物料
const selectMaterial = () => {
  materialDialogVisible.value = true
}

// 搜索物料
const searchMaterials = () => {
  ElMessage.success('搜索物料')
  console.log('搜索关键词:', materialSearch.value)
}

// 选择物料项
const selectMaterialItem = (row: any) => {
  // 检查是否已添加
  const exists = purchaseItems.value.some(item => item.id === row.id)
  if (exists) {
    ElMessage.warning('该物料已添加')
    return
  }
  
  purchaseItems.value.push({
    ...row,
    quantity: 1,
    unitPrice: '0.00',
    subtotal: '0.00'
  })
  
  materialDialogVisible.value = false
  ElMessage.success('添加成功')
  updateStatistics()
}

// 删除物料
const removeItem = (index: number) => {
  purchaseItems.value.splice(index, 1)
  ElMessage.success('删除成功')
  updateStatistics()
}

// 添加物料
const addMaterial = () => {
  ElMessage.success('添加物料')
}

// 更新统计信息
const updateStatistics = () => {
  statistics.materialTypes = purchaseItems.value.length
  statistics.totalQuantity = purchaseItems.value.reduce((total, item) => total + item.quantity, 0)
  // 实际项目中需要计算总金额
}

// 保存
const handleSave = () => {
  ElMessage.success('保存成功')
  console.log('保存数据:', purchaseForm, purchaseItems.value)
}

// 打印
const handlePrint = () => {
  ElMessage.success('开始打印')
}

// 提交
const handleSubmit = () => {
  ElMessage.success('提交成功')
  console.log('提交数据:', purchaseForm, purchaseItems.value)
}

// 取消
const handleCancel = () => {
  ElMessage.info('已取消')
}
</script>

<style lang="scss" scoped>
.auxiliary-purchase-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
  
  .main-card {
    margin-bottom: 20px;
    
    .card-header {
      font-size: 18px;
      font-weight: bold;
      color: #303133;
    }
  }
  
  .form-section {
    margin-bottom: 20px;
    
    .section-title {
      font-size: 16px;
      font-weight: bold;
      color: #606266;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ebeef5;
    }
    
    .el-form-item {
      margin-bottom: 18px;
    }
  }
  
  .purchase-table {
    margin-top: 10px;
  }
  
  .add-material-actions {
    .el-button {
      margin-right: 10px;
    }
  }
  
  .statistics-card {
    background-color: #f0f9ff;
    border-color: #b3e0ff;
    
    .statistic-item {
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;
      
      .label {
        font-weight: bold;
        color: #409eff;
      }
      
      .total-amount {
        font-weight: bold;
        color: #303133;
      }
    }
  }
  
  .form-actions {
    text-align: center;
    padding: 20px 0;
    
    .el-button {
      margin: 0 10px;
      padding: 12px 30px;
    }
  }
}
</style>