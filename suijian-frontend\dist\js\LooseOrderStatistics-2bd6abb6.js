import{r as e,g as a,E as t}from"./element-plus-7917fd46.js";import{l,_ as o,r as s,c as r,q as n,y as i,R as d,J as u,av as c,x as p,z as v,u as g,O as C,P as m,M as _}from"./vue-vendor-fc5a6493.js";import{_ as f}from"./_plugin-vue_export-helper-1b428a4d.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const b={class:"loose-order-statistics"},h={class:"card-header"},w={class:"header-actions"},y={class:"overview-content"},I={class:"overview-item"},z={class:"value"},L={class:"overview-item"},S={class:"amount"},j={class:"overview-item"},N={class:"value"},D={class:"card-header"},M={class:"header-actions"},F={class:"total-cost"},P={class:"pagination-section"},x={key:0,class:"order-detail"},k={class:"total-amount"},U={class:"dialog-footer"},V=f(l({__name:"LooseOrderStatistics",setup(l){const f=o({currentPage:1,pageSize:10}),V=s(!1),O=s(null),q=s([{id:1,serialNo:"LS001",projectName:"阳光小区1号楼",preMeterLaborCost:150,indoorLaborCost:200,meterInstallationCost:100,minorInstallationCost:80,secondaryInstallationCost:50,indoorFittingsCost:120,totalCost:700,orderDate:"2024-01-15",status:"已完成"},{id:2,serialNo:"LS002",projectName:"花园广场2号楼",preMeterLaborCost:180,indoorLaborCost:220,meterInstallationCost:100,minorInstallationCost:90,secondaryInstallationCost:60,indoorFittingsCost:140,totalCost:790,orderDate:"2024-01-14",status:"进行中"},{id:3,serialNo:"LS003",projectName:"商业中心A座",preMeterLaborCost:200,indoorLaborCost:250,meterInstallationCost:120,minorInstallationCost:100,secondaryInstallationCost:70,indoorFittingsCost:160,totalCost:900,orderDate:"2024-01-13",status:"待开始"}]),A=r(()=>q.value.length),E=r(()=>q.value.reduce((e,a)=>e+a.totalCost,0)),J=r(()=>{const e=(new Date).getMonth()+1;return q.value.filter(a=>new Date(a.orderDate).getMonth()+1===e).length}),R=r(()=>{const e=(f.currentPage-1)*f.pageSize,a=e+f.pageSize;return q.value.slice(e,a)}),B=e=>e.toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2}),G=e=>{switch(e){case"已完成":return"success";case"进行中":return"primary";case"待开始":return"warning";default:return"info"}},H=()=>{t.success("数据已刷新")},K=()=>{t.info("导出功能开发中...")},Q=e=>{f.pageSize=e,f.currentPage=1},T=e=>{f.currentPage=e};return n(()=>{}),(t,l)=>{const o=c("el-icon"),s=c("el-button"),r=c("el-card"),n=c("el-table-column"),W=c("el-tag"),X=c("el-table"),Y=c("el-pagination"),Z=c("el-descriptions-item"),$=c("el-descriptions"),ee=c("el-dialog");return p(),i("div",b,[d(r,{class:"overview-card",shadow:"hover"},{header:u(()=>[v("div",h,[l[5]||(l[5]=v("span",null,"📊 散单情况概览",-1)),v("div",w,[d(s,{type:"primary",size:"small",onClick:H},{default:u(()=>[d(o,null,{default:u(()=>[d(g(e))]),_:1}),l[4]||(l[4]=C(" 刷新 ",-1))]),_:1,__:[4]})])])]),default:u(()=>[v("div",y,[v("div",I,[l[6]||(l[6]=v("span",{class:"label"},"📋 总散单数:",-1)),v("span",z,m(A.value)+"单",1)]),v("div",L,[l[7]||(l[7]=v("span",{class:"label"},"💰 总金额:",-1)),v("span",S,"¥"+m(B(E.value)),1)]),v("div",j,[l[8]||(l[8]=v("span",{class:"label"},"⏱️ 本月新增:",-1)),v("span",N,m(J.value)+"单",1)])])]),_:1}),d(r,{class:"order-list-card",shadow:"hover"},{header:u(()=>[v("div",D,[l[10]||(l[10]=v("span",null,"📋 散单列表",-1)),v("div",M,[d(s,{type:"success",size:"small",onClick:K},{default:u(()=>[d(o,null,{default:u(()=>[d(g(a))]),_:1}),l[9]||(l[9]=C(" 导出 ",-1))]),_:1,__:[9]})])])]),default:u(()=>[d(X,{data:R.value,style:{width:"100%"},border:""},{default:u(()=>[d(n,{prop:"serialNo",label:"序号",width:"80",align:"center"}),d(n,{prop:"projectName",label:"单项工程名称",width:"200"}),d(n,{prop:"preMeterLaborCost",label:"表前安装人工费",width:"120",align:"center"},{default:u(({row:e})=>[C(" ¥"+m(B(e.preMeterLaborCost)),1)]),_:1}),d(n,{prop:"indoorLaborCost",label:"户内安装人工费",width:"120",align:"center"},{default:u(({row:e})=>[C(" ¥"+m(B(e.indoorLaborCost)),1)]),_:1}),d(n,{prop:"meterInstallationCost",label:"燃气表安装费",width:"120",align:"center"},{default:u(({row:e})=>[C(" ¥"+m(B(e.meterInstallationCost)),1)]),_:1}),d(n,{prop:"minorInstallationCost",label:"燃气小件安装费",width:"130",align:"center"},{default:u(({row:e})=>[C(" ¥"+m(B(e.minorInstallationCost)),1)]),_:1}),d(n,{prop:"secondaryInstallationCost",label:"二次安装费",width:"110",align:"center"},{default:u(({row:e})=>[C(" ¥"+m(B(e.secondaryInstallationCost)),1)]),_:1}),d(n,{prop:"indoorFittingsCost",label:"户内配件费",width:"110",align:"center"},{default:u(({row:e})=>[C(" ¥"+m(B(e.indoorFittingsCost)),1)]),_:1}),d(n,{prop:"totalCost",label:"合计",width:"120",align:"center"},{default:u(({row:e})=>[v("span",F,"¥"+m(B(e.totalCost)),1)]),_:1}),d(n,{prop:"orderDate",label:"订单日期",width:"110",align:"center"}),d(n,{prop:"status",label:"状态",width:"100",align:"center"},{default:u(({row:e})=>[d(W,{type:G(e.status)},{default:u(()=>[C(m(e.status),1)]),_:2},1032,["type"])]),_:1}),d(n,{label:"操作",width:"120",align:"center",fixed:"right"},{default:u(({row:e})=>[d(s,{type:"primary",size:"small",onClick:a=>{return t=e,O.value=t,void(V.value=!0);var t}},{default:u(()=>l[11]||(l[11]=[C(" 详情 ",-1)])),_:2,__:[11]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),v("div",P,[d(Y,{"current-page":f.currentPage,"onUpdate:currentPage":l[0]||(l[0]=e=>f.currentPage=e),"page-size":f.pageSize,"onUpdate:pageSize":l[1]||(l[1]=e=>f.pageSize=e),"page-sizes":[10,20,50,100],total:q.value.length,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Q,onCurrentChange:T},null,8,["current-page","page-size","total"])])]),_:1}),d(ee,{modelValue:V.value,"onUpdate:modelValue":l[3]||(l[3]=e=>V.value=e),title:"散单详情",width:"800px"},{footer:u(()=>[v("div",U,[d(s,{onClick:l[2]||(l[2]=e=>V.value=!1)},{default:u(()=>l[12]||(l[12]=[C("关闭",-1)])),_:1,__:[12]})])]),default:u(()=>[O.value?(p(),i("div",x,[d($,{column:2,border:""},{default:u(()=>[d(Z,{label:"工程名称"},{default:u(()=>[C(m(O.value.projectName),1)]),_:1}),d(Z,{label:"订单日期"},{default:u(()=>[C(m(O.value.orderDate),1)]),_:1}),d(Z,{label:"表前安装人工费"},{default:u(()=>[C("¥"+m(B(O.value.preMeterLaborCost)),1)]),_:1}),d(Z,{label:"户内安装人工费"},{default:u(()=>[C("¥"+m(B(O.value.indoorLaborCost)),1)]),_:1}),d(Z,{label:"燃气表安装费"},{default:u(()=>[C("¥"+m(B(O.value.meterInstallationCost)),1)]),_:1}),d(Z,{label:"燃气小件安装费"},{default:u(()=>[C("¥"+m(B(O.value.minorInstallationCost)),1)]),_:1}),d(Z,{label:"二次安装费"},{default:u(()=>[C("¥"+m(B(O.value.secondaryInstallationCost)),1)]),_:1}),d(Z,{label:"户内配件费"},{default:u(()=>[C("¥"+m(B(O.value.indoorFittingsCost)),1)]),_:1}),d(Z,{label:"合计金额"},{default:u(()=>[v("span",k,"¥"+m(B(O.value.totalCost)),1)]),_:1}),d(Z,{label:"状态"},{default:u(()=>[d(W,{type:G(O.value.status)},{default:u(()=>[C(m(O.value.status),1)]),_:1},8,["type"])]),_:1})]),_:1})])):_("",!0)]),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-f621a7cf"]]);export{V as default};
