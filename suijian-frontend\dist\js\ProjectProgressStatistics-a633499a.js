import{E as s,r as e}from"./element-plus-7917fd46.js";import{l as a,_ as t,r as l,c as r,q as n,y as i,R as u,J as c,z as o,av as p,x as d,u as g,O as m,P as v}from"./vue-vendor-fc5a6493.js";import{_}from"./_plugin-vue_export-helper-1b428a4d.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const f={class:"project-progress-statistics"},h={class:"card-header"},b={class:"header-actions"},z={class:"status-summary"},w={class:"status-item"},y={class:"status-count"},P={class:"status-item"},D={class:"status-count"},j={class:"status-item"},C={class:"status-count"},k={class:"status-item"},S={class:"status-count"},U={class:"progress-summary"},E={class:"progress-item"},T={class:"value"},x={class:"progress-item"},M={class:"value completion-rate"},q={class:"project-icon"},A={key:0,class:"issues-text"},B={key:1,class:"no-issues"},I={class:"pagination-section"},J={class:"action-buttons"},O=_(a({__name:"ProjectProgressStatistics",setup(a){const _=t({currentPage:1,pageSize:10}),O=l([{id:1,name:"阳光小区A栋",icon:"🏢",status:"在建",lastUpdateTime:"2024-01-15 14:30",progress:65,startDate:"2024-01-01",estimatedEndDate:"2024-03-01",currentProgress:"基础施工完成，正在进行主体结构",issues:"材料供应延迟，预计影响工期3天"},{id:2,name:"花园广场项目",icon:"🏬",status:"在建",lastUpdateTime:"2024-01-15 10:20",progress:45,startDate:"2024-01-05",estimatedEndDate:"2024-04-01",currentProgress:"地基开挖完成，准备浇筑混凝土",issues:""},{id:3,name:"商业中心B区",icon:"🏪",status:"暂停",lastUpdateTime:"2024-01-14 16:45",progress:30,startDate:"2023-12-15",estimatedEndDate:"2024-05-01",currentProgress:"主体结构30%，因资金问题暂停",issues:"资金链紧张，等待甲方追加投资"},{id:4,name:"住宅楼C座",icon:"🏠",status:"完成",lastUpdateTime:"2024-01-14 09:15",progress:100,startDate:"2023-11-01",estimatedEndDate:"2024-01-10",currentProgress:"工程已完工，正在进行验收",issues:""},{id:5,name:"绿城花园D区",icon:"🏘️",status:"在建",lastUpdateTime:"2024-01-14 08:30",progress:75,startDate:"2023-12-01",estimatedEndDate:"2024-02-15",currentProgress:"装修阶段，预计本月底完工",issues:"部分装修材料质量不达标，需要更换"},{id:6,name:"工业园E栋",icon:"🏭",status:"未开始",lastUpdateTime:"2024-01-13 17:00",progress:0,startDate:"2024-02-01",estimatedEndDate:"2024-06-01",currentProgress:"等待开工许可证",issues:"环评手续尚未完成"}]),R=r(()=>O.value.length),$=r(()=>O.value.filter(s=>"未开始"===s.status).length),F=r(()=>O.value.filter(s=>"在建"===s.status).length),G=r(()=>O.value.filter(s=>"暂停"===s.status).length),H=r(()=>O.value.filter(s=>"完成"===s.status).length),K=r(()=>0===R.value?0:Math.round(H.value/R.value*100*10)/10),L=r(()=>{const s=(_.currentPage-1)*_.pageSize,e=s+_.pageSize;return O.value.slice(s,e)}),N=s=>({"未开始":"🔵","在建":"🟡","暂停":"🔴","完成":"✅"}[s]||"🔵"),Q=()=>{s.success("数据已刷新")},V=e=>{e?s.info(`查看 ${e.name} 的详细信息`):s.info("查看所有工程详情")},W=()=>{s.info("生成工程进度报表")},X=s=>{_.pageSize=s,_.currentPage=1},Y=s=>{_.currentPage=s};return n(()=>{}),(s,a)=>{const t=p("el-icon"),l=p("el-button"),r=p("el-card"),n=p("el-table-column"),Z=p("el-tag"),ss=p("el-progress"),es=p("el-table"),as=p("el-pagination");return d(),i("div",f,[u(r,{class:"status-card",shadow:"hover"},{header:c(()=>[o("div",h,[a[4]||(a[4]=o("span",null,"📊 工程状态统计",-1)),o("div",b,[u(l,{type:"primary",size:"small",onClick:Q},{default:c(()=>[u(t,null,{default:c(()=>[u(g(e))]),_:1}),a[3]||(a[3]=m(" 刷新 ",-1))]),_:1,__:[3]})])])]),default:c(()=>[o("div",z,[o("div",w,[a[5]||(a[5]=o("span",{class:"status-icon"},"🔵",-1)),a[6]||(a[6]=o("span",{class:"status-label"},"未开始:",-1)),o("span",y,v($.value)+"个",1)]),o("div",P,[a[7]||(a[7]=o("span",{class:"status-icon"},"🟡",-1)),a[8]||(a[8]=o("span",{class:"status-label"},"在建:",-1)),o("span",D,v(F.value)+"个",1)]),o("div",j,[a[9]||(a[9]=o("span",{class:"status-icon"},"🔴",-1)),a[10]||(a[10]=o("span",{class:"status-label"},"暂停:",-1)),o("span",C,v(G.value)+"个",1)]),o("div",k,[a[11]||(a[11]=o("span",{class:"status-icon"},"✅",-1)),a[12]||(a[12]=o("span",{class:"status-label"},"完成:",-1)),o("span",S,v(H.value)+"个",1)])]),o("div",U,[o("div",E,[a[13]||(a[13]=o("span",{class:"label"},"📈 总工程数:",-1)),o("span",T,v(R.value)+"个",1)]),o("div",x,[a[14]||(a[14]=o("span",{class:"label"},"完成率:",-1)),o("span",M,v(K.value)+"%",1)])])]),_:1}),u(r,{class:"project-list-card",shadow:"hover"},{header:c(()=>a[15]||(a[15]=[o("div",{class:"card-header"},[o("span",null,"🏗️ 工程列表 (按最新进度排序)")],-1)])),default:c(()=>[u(es,{data:L.value,style:{width:"100%"},border:""},{default:c(()=>[u(n,{prop:"icon",label:"",width:"50",align:"center"},{default:c(({row:s})=>[o("span",q,v(s.icon),1)]),_:1}),u(n,{prop:"name",label:"工程名称",width:"180"}),u(n,{prop:"status",label:"状态",width:"100",align:"center"},{default:c(({row:s})=>{return[u(Z,{type:(e=s.status,{"未开始":"info","在建":"warning","暂停":"danger","完成":"success"}[e]||"info"),size:"small"},{default:c(()=>[m(v(N(s.status))+" "+v(s.status),1)]),_:2},1032,["type"])];var e}),_:1}),u(n,{prop:"lastUpdateTime",label:"最新进度时间",width:"150",align:"center"}),u(n,{prop:"progress",label:"进度",width:"100",align:"center"},{default:c(({row:s})=>[u(ss,{percentage:s.progress,"stroke-width":8},null,8,["percentage"])]),_:1}),u(n,{prop:"currentProgress",label:"当前进度",width:"150"}),u(n,{prop:"issues",label:"存在问题",width:"200"},{default:c(({row:s})=>[s.issues?(d(),i("span",A,v(s.issues),1)):(d(),i("span",B,"无"))]),_:1}),u(n,{label:"操作",width:"120",align:"center"},{default:c(({row:s})=>[u(l,{type:"primary",size:"small",onClick:e=>V(s)},{default:c(()=>a[16]||(a[16]=[m(" 详情 ",-1)])),_:2,__:[16]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),o("div",I,[u(as,{"current-page":_.currentPage,"onUpdate:currentPage":a[0]||(a[0]=s=>_.currentPage=s),"page-size":_.pageSize,"onUpdate:pageSize":a[1]||(a[1]=s=>_.pageSize=s),"page-sizes":[10,20,50,100],total:O.value.length,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:X,onCurrentChange:Y},null,8,["current-page","page-size","total"])])]),_:1}),o("div",J,[u(l,{type:"primary",size:"large",onClick:a[2]||(a[2]=()=>V())},{default:c(()=>a[17]||(a[17]=[m(" 📊 工程详情 ",-1)])),_:1,__:[17]}),u(l,{type:"success",size:"large",onClick:W},{default:c(()=>a[18]||(a[18]=[m(" 📈 进度报表 ",-1)])),_:1,__:[18]}),u(l,{type:"default",size:"large",onClick:Q},{default:c(()=>a[19]||(a[19]=[m(" 🔄 刷新 ",-1)])),_:1,__:[19]})])])}}}),[["__scopeId","data-v-b031a8a6"]]);export{O as default};
