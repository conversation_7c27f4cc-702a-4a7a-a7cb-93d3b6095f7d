var e=Object.defineProperty,a=Object.defineProperties,l=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,i=Object.prototype.hasOwnProperty,u=Object.prototype.propertyIsEnumerable,d=(a,l,t)=>l in a?e(a,l,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[l]=t;import{E as o}from"./element-plus-7917fd46.js";import{l as n,_ as s,r,y as p,R as c,J as m,av as _,x as f,z as b,Q as y,aa as v,O as h,P as w,I as V}from"./vue-vendor-fc5a6493.js";import{_ as C}from"./_plugin-vue_export-helper-1b428a4d.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const g={class:"product-inbound-container"},Q={style:{"margin-top":"15px"}},k={class:"add-product-actions"},P={class:"statistic-item"},D={class:"statistic-item"},O={class:"statistic-item"},N={class:"total-amount"},j={class:"form-actions"},x=C(n({__name:"ProductInbound",setup(e){const n=s({inboundDate:"2024-01-15",operator:"张三",supplier:1,purchaseOrderNo:"CG2024011001",inboundDescription:"",remarks:""}),C=r([{id:1,name:"华强电子"},{id:2,name:"立创电子"},{id:3,name:"得捷电子"},{id:4,name:"贸泽电子"}]),x=r([{id:1,materialCode:"SP001",materialName:"智能开关",model:"KG-86",specification:"86型",unit:"个",plannedQuantity:100,actualQuantity:100,unitPrice:"45.00",subtotal:"4,500.00"},{id:2,materialCode:"SP002",materialName:"LED灯具",model:"LED-12W",specification:"12W",unit:"个",plannedQuantity:50,actualQuantity:50,unitPrice:"130.00",subtotal:"6,500.00"},{id:3,materialCode:"SP003",materialName:"插座面板",model:"ZP-86",specification:"86型",unit:"个",plannedQuantity:200,actualQuantity:200,unitPrice:"60.00",subtotal:"12,000.00"}]),U=r([{id:1,materialCode:"SP001",materialName:"智能开关",model:"KG-86",specification:"86型",unit:"个",plannedQuantity:100},{id:2,materialCode:"SP002",materialName:"LED灯具",model:"LED-12W",specification:"12W",unit:"个",plannedQuantity:50},{id:4,materialCode:"SP004",materialName:"电线",model:"BV-2.5",specification:"2.5mm²",unit:"米",plannedQuantity:1e3}]),S=s({productTypes:3,totalQuantity:350,totalAmount:"23,000.00"}),Y=r(!1),E=r(""),I=()=>{Y.value=!0},L=()=>{o.success("搜索商品")},M=e=>{var n,s;x.value.some(a=>a.id===e.id)?o.warning("该商品已添加"):(x.value.push((n=((e,a)=>{for(var l in a||(a={}))i.call(a,l)&&d(e,l,a[l]);if(t)for(var l of t(a))u.call(a,l)&&d(e,l,a[l]);return e})({},e),s={actualQuantity:e.plannedQuantity,unitPrice:"0.00",subtotal:"0.00"},a(n,l(s)))),Y.value=!1,o.success("添加成功"),G())},W=()=>{o.success("添加商品")},G=()=>{S.productTypes=x.value.length,S.totalQuantity=x.value.reduce((e,a)=>e+a.actualQuantity,0)},K=()=>{o.success("保存成功")},T=()=>{o.success("开始打印")},A=()=>{o.success("提交成功")},R=()=>{o.info("已取消")};return(e,a)=>{const l=_("el-col"),t=_("el-form-item"),i=_("el-date-picker"),u=_("el-input"),d=_("el-option"),s=_("el-select"),r=_("el-row"),z=_("el-table-column"),B=_("el-input-number"),J=_("el-button"),Z=_("el-table"),$=_("el-dialog"),q=_("el-card");return f(),p("div",g,[c(q,{class:"main-card"},{header:m(()=>a[9]||(a[9]=[b("div",{class:"card-header"},[b("span",null,"商品入库")],-1)])),default:m(()=>[c(r,{gutter:20,class:"form-section"},{default:m(()=>[c(l,{span:24},{default:m(()=>a[10]||(a[10]=[b("div",{class:"section-title"},"入库信息",-1)])),_:1,__:[10]}),c(l,{span:12},{default:m(()=>[c(t,{label:"入库单号:"},{default:m(()=>a[11]||(a[11]=[b("span",null,"RK20240115001",-1)])),_:1,__:[11]})]),_:1}),c(l,{span:12},{default:m(()=>[c(t,{label:"入库日期:"},{default:m(()=>[c(i,{modelValue:n.inboundDate,"onUpdate:modelValue":a[0]||(a[0]=e=>n.inboundDate=e),type:"date",placeholder:"请选择入库日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),c(l,{span:12},{default:m(()=>[c(t,{label:"操作员:"},{default:m(()=>[c(u,{modelValue:n.operator,"onUpdate:modelValue":a[1]||(a[1]=e=>n.operator=e),placeholder:"请输入操作员"},null,8,["modelValue"])]),_:1})]),_:1}),c(l,{span:12},{default:m(()=>[c(t,{label:"供应商:"},{default:m(()=>[c(s,{modelValue:n.supplier,"onUpdate:modelValue":a[2]||(a[2]=e=>n.supplier=e),placeholder:"请选择供应商",style:{width:"100%"}},{default:m(()=>[(f(!0),p(y,null,v(C.value,e=>(f(),V(d,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),c(l,{span:12},{default:m(()=>[c(t,{label:"采购单号:"},{default:m(()=>[c(u,{modelValue:n.purchaseOrderNo,"onUpdate:modelValue":a[3]||(a[3]=e=>n.purchaseOrderNo=e),placeholder:"请输入采购单号"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),c(r,{gutter:20,class:"form-section"},{default:m(()=>[c(l,{span:24},{default:m(()=>a[12]||(a[12]=[b("div",{class:"section-title"},"入库商品",-1)])),_:1,__:[12]}),c(l,{span:24},{default:m(()=>[c(Z,{data:x.value,border:"",class:"inbound-table"},{default:m(()=>[c(z,{type:"index",label:"序号",width:"60"}),c(z,{prop:"materialCode",label:"公司物料编码",width:"120"}),c(z,{prop:"materialName",label:"商品名称",width:"120"}),c(z,{prop:"model",label:"型号",width:"100"}),c(z,{prop:"specification",label:"规格",width:"100"}),c(z,{prop:"unit",label:"单位",width:"80"}),c(z,{prop:"plannedQuantity",label:"计划数量",width:"100"}),c(z,{prop:"actualQuantity",label:"实际数量"},{default:m(e=>[c(B,{modelValue:e.row.actualQuantity,"onUpdate:modelValue":a=>e.row.actualQuantity=a,min:0,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),c(z,{prop:"unitPrice",label:"单价(元)",width:"100"}),c(z,{prop:"subtotal",label:"小计(元)",width:"100"}),c(z,{label:"操作",width:"80",fixed:"right"},{default:m(e=>[c(J,{type:"danger",link:"",onClick:a=>{return l=e.$index,x.value.splice(l,1),o.success("删除成功"),void G();var l}},{default:m(()=>a[13]||(a[13]=[h("删除",-1)])),_:2,__:[13]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),b("div",Q,[c(J,{type:"primary",icon:"Plus",onClick:I},{default:m(()=>a[14]||(a[14]=[h("添加商品",-1)])),_:1,__:[14]})])]),_:1})]),_:1}),c(r,{gutter:20,class:"form-section"},{default:m(()=>[c(l,{span:24},{default:m(()=>a[15]||(a[15]=[b("div",{class:"section-title"},"添加商品",-1)])),_:1,__:[15]}),c(l,{span:24},{default:m(()=>[b("div",k,[c(J,{type:"primary",onClick:I},{default:m(()=>a[16]||(a[16]=[h("选择商品",-1)])),_:1,__:[16]}),c(J,{type:"success",onClick:W},{default:m(()=>a[17]||(a[17]=[h("添加",-1)])),_:1,__:[17]})])]),_:1})]),_:1}),c($,{modelValue:Y.value,"onUpdate:modelValue":a[6]||(a[6]=e=>Y.value=e),title:"选择商品",width:"800"},{footer:m(()=>[c(J,{onClick:a[5]||(a[5]=e=>Y.value=!1)},{default:m(()=>a[20]||(a[20]=[h("取消",-1)])),_:1,__:[20]})]),default:m(()=>[c(r,{gutter:20,style:{"margin-bottom":"20px"}},{default:m(()=>[c(l,{span:18},{default:m(()=>[c(u,{modelValue:E.value,"onUpdate:modelValue":a[4]||(a[4]=e=>E.value=e),placeholder:"请输入搜索关键词",clearable:""},null,8,["modelValue"])]),_:1}),c(l,{span:6},{default:m(()=>[c(J,{type:"primary",icon:"Search",onClick:L},{default:m(()=>a[18]||(a[18]=[h("搜索",-1)])),_:1,__:[18]})]),_:1})]),_:1}),c(Z,{data:U.value,border:"",height:"400"},{default:m(()=>[c(z,{prop:"materialCode",label:"公司物料编码",width:"120"}),c(z,{prop:"materialName",label:"商品名称",width:"120"}),c(z,{prop:"model",label:"型号",width:"100"}),c(z,{prop:"specification",label:"规格",width:"100"}),c(z,{prop:"unit",label:"单位",width:"80"}),c(z,{prop:"plannedQuantity",label:"计划数量",width:"100"}),c(z,{label:"操作",width:"80",fixed:"right"},{default:m(e=>[c(J,{type:"primary",link:"",onClick:a=>M(e.row)},{default:m(()=>a[19]||(a[19]=[h("选择",-1)])),_:2,__:[19]},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1},8,["modelValue"]),c(r,{gutter:20,class:"form-section"},{default:m(()=>[c(l,{span:24},{default:m(()=>a[21]||(a[21]=[b("div",{class:"section-title"},"入库统计",-1)])),_:1,__:[21]}),c(l,{span:24},{default:m(()=>[c(q,{class:"statistics-card"},{default:m(()=>[c(r,{gutter:20},{default:m(()=>[c(l,{span:12},{default:m(()=>[b("div",P,[a[22]||(a[22]=b("span",{class:"label"},"入库商品种类:",-1)),b("span",null,w(S.productTypes)+"种",1)])]),_:1}),c(l,{span:12},{default:m(()=>[b("div",D,[a[23]||(a[23]=b("span",{class:"label"},"入库商品总数:",-1)),b("span",null,w(S.totalQuantity)+"件",1)])]),_:1}),c(l,{span:12},{default:m(()=>[b("div",O,[a[24]||(a[24]=b("span",{class:"label"},"入库总金额:",-1)),b("span",N,"¥"+w(S.totalAmount),1)])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),c(r,{gutter:20,class:"form-section"},{default:m(()=>[c(l,{span:24},{default:m(()=>a[25]||(a[25]=[b("div",{class:"section-title"},"备注信息",-1)])),_:1,__:[25]}),c(l,{span:24},{default:m(()=>[c(t,{label:"入库说明:"},{default:m(()=>[c(u,{modelValue:n.inboundDescription,"onUpdate:modelValue":a[7]||(a[7]=e=>n.inboundDescription=e),type:"textarea",rows:3,placeholder:"请输入入库说明"},null,8,["modelValue"])]),_:1})]),_:1}),c(l,{span:24},{default:m(()=>[c(t,{label:"备注:"},{default:m(()=>[c(u,{modelValue:n.remarks,"onUpdate:modelValue":a[8]||(a[8]=e=>n.remarks=e),type:"textarea",rows:2,placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),b("div",j,[c(J,{type:"primary",onClick:K},{default:m(()=>a[26]||(a[26]=[h("保存",-1)])),_:1,__:[26]}),c(J,{onClick:T},{default:m(()=>a[27]||(a[27]=[h("打印",-1)])),_:1,__:[27]}),c(J,{type:"success",onClick:A},{default:m(()=>a[28]||(a[28]=[h("提交",-1)])),_:1,__:[28]}),c(J,{onClick:R},{default:m(()=>a[29]||(a[29]=[h("取消",-1)])),_:1,__:[29]})])]),_:1})])}}}),[["__scopeId","data-v-2b6f2f92"]]);export{x as default};
