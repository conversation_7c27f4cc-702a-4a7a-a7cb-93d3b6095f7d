import{l as s,r as a,q as l,y as t,z as e,R as i,J as o,aT as c,av as d,x as n,P as r,O as u,u as v}from"./vue-vendor-fc5a6493.js";import{f as p,E as m}from"./element-plus-7917fd46.js";import{_}from"./_plugin-vue_export-helper-1b428a4d.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const f={class:"dashboard"},h={class:"overview-section"},y={class:"card-content"},w={class:"card-info"},k={class:"card-stats"},b={class:"card-content"},g={class:"card-info"},C={class:"card-stats"},z={class:"card-content"},x={class:"card-info"},j={class:"card-stats"},P={class:"card-content"},R={class:"card-info"},$={class:"card-stats"},A={class:"card-content"},I={class:"card-info"},O={class:"card-stats"},q={class:"card-content"},Q={class:"card-info"},W={class:"card-stats"},E={class:"quick-actions-section"},J={class:"statistics-modules-section"},L={class:"module-list"},M={class:"notifications-section"},S={class:"notification-footer"},T=_(s({__name:"index",setup(s){const _=c(),T=a({totalItems:43,totalAmount:215e3}),B=a({inProgress:12,completionRate:28.6}),D=a({totalQuantity:430,totalAmount:7e4}),F=a({monthlyAmount:35680,completionRate:71.4}),G=a({stockWarning:3,balanceReminder:15}),H=a({newOrders:8,completedProjects:2}),K=s=>s.toLocaleString(),N=()=>{_.push("/dashboard/material-statistics")},U=()=>{_.push("/dashboard/project-progress")},V=()=>{_.push("/dashboard/loose-material-statistics")},X=()=>{_.push("/dashboard/sales-statistics")},Y=()=>{m.info("跳转到报表页面")},Z=()=>{m.info("查看全部通知")};return l(()=>{return s=this,a=null,l=function*(){},new Promise((t,e)=>{var i=s=>{try{c(l.next(s))}catch(a){e(a)}},o=s=>{try{c(l.throw(s))}catch(a){e(a)}},c=s=>s.done?t(s.value):Promise.resolve(s.value).then(i,o);c((l=l.apply(s,a)).next())});var s,a,l}),(s,a)=>{const l=d("el-button"),c=d("el-card"),m=d("el-col"),_=d("el-row"),ss=d("el-icon");return n(),t("div",f,[e("div",h,[a[25]||(a[25]=e("h2",{class:"section-title"},"📊 数据概览",-1)),i(_,{gutter:20},{default:o(()=>[i(m,{span:8},{default:o(()=>[i(c,{class:"overview-card",shadow:"hover",onClick:N},{default:o(()=>[e("div",y,[a[9]||(a[9]=e("div",{class:"card-icon"},"📦",-1)),e("div",w,[a[8]||(a[8]=e("div",{class:"card-title"},"工程物料",-1)),e("div",k,[e("span",null,"总计: "+r(T.value.totalItems)+"项",1),e("span",null,"总价: ¥"+r(K(T.value.totalAmount)),1)]),i(l,{type:"primary",size:"small",class:"card-button"},{default:o(()=>a[7]||(a[7]=[u(" 查看详情 → ",-1)])),_:1,__:[7]})])])]),_:1})]),_:1}),i(m,{span:8},{default:o(()=>[i(c,{class:"overview-card",shadow:"hover",onClick:U},{default:o(()=>[e("div",b,[a[12]||(a[12]=e("div",{class:"card-icon"},"🏗️",-1)),e("div",g,[a[11]||(a[11]=e("div",{class:"card-title"},"工程进度",-1)),e("div",C,[e("span",null,"在建: "+r(B.value.inProgress)+"个",1),e("span",null,"完成率: "+r(B.value.completionRate)+"%",1)]),i(l,{type:"primary",size:"small",class:"card-button"},{default:o(()=>a[10]||(a[10]=[u(" 查看详情 → ",-1)])),_:1,__:[10]})])])]),_:1})]),_:1}),i(m,{span:8},{default:o(()=>[i(c,{class:"overview-card",shadow:"hover",onClick:V},{default:o(()=>[e("div",z,[a[15]||(a[15]=e("div",{class:"card-icon"},"🏷️",-1)),e("div",x,[a[14]||(a[14]=e("div",{class:"card-title"},"散单物料",-1)),e("div",j,[e("span",null,"甲料: "+r(D.value.totalQuantity)+"件",1),e("span",null,"总价: ¥"+r(K(D.value.totalAmount)),1)]),i(l,{type:"primary",size:"small",class:"card-button"},{default:o(()=>a[13]||(a[13]=[u(" 查看详情 → ",-1)])),_:1,__:[13]})])])]),_:1})]),_:1})]),_:1}),i(_,{gutter:20,style:{"margin-top":"20px"}},{default:o(()=>[i(m,{span:8},{default:o(()=>[i(c,{class:"overview-card",shadow:"hover",onClick:X},{default:o(()=>[e("div",P,[a[18]||(a[18]=e("div",{class:"card-icon"},"💰",-1)),e("div",R,[a[17]||(a[17]=e("div",{class:"card-title"},"商品售卖",-1)),e("div",$,[e("span",null,"本月: ¥"+r(K(F.value.monthlyAmount)),1),e("span",null,"完成度: "+r(F.value.completionRate)+"%",1)]),i(l,{type:"primary",size:"small",class:"card-button"},{default:o(()=>a[16]||(a[16]=[u(" 查看详情 → ",-1)])),_:1,__:[16]})])])]),_:1})]),_:1}),i(m,{span:8},{default:o(()=>[i(c,{class:"overview-card",shadow:"hover"},{default:o(()=>[e("div",A,[a[21]||(a[21]=e("div",{class:"card-icon"},"⚠️",-1)),e("div",I,[a[20]||(a[20]=e("div",{class:"card-title"},"系统提醒",-1)),e("div",O,[e("span",null,"库存预警: "+r(G.value.stockWarning)+"项",1),e("span",null,"平账提醒: "+r(G.value.balanceReminder)+"天",1)]),i(l,{type:"warning",size:"small",class:"card-button"},{default:o(()=>a[19]||(a[19]=[u(" 查看详情 → ",-1)])),_:1,__:[19]})])])]),_:1})]),_:1}),i(m,{span:8},{default:o(()=>[i(c,{class:"overview-card",shadow:"hover"},{default:o(()=>[e("div",q,[a[24]||(a[24]=e("div",{class:"card-icon"},"📈",-1)),e("div",Q,[a[23]||(a[23]=e("div",{class:"card-title"},"今日数据",-1)),e("div",W,[e("span",null,"新订单: "+r(H.value.newOrders)+"个",1),e("span",null,"完成工程: "+r(H.value.completedProjects)+"个",1)]),i(l,{type:"success",size:"small",class:"card-button"},{default:o(()=>a[22]||(a[22]=[u(" 查看详情 → ",-1)])),_:1,__:[22]})])])]),_:1})]),_:1})]),_:1})]),e("div",E,[a[34]||(a[34]=e("h2",{class:"section-title"},"🚀 快捷操作",-1)),i(c,{shadow:"hover"},{default:o(()=>[i(_,{gutter:15},{default:o(()=>[i(m,{span:6},{default:o(()=>[i(l,{type:"primary",size:"large",onClick:a[0]||(a[0]=a=>s.$router.push("/warehouse/material-inbound")),block:""},{default:o(()=>a[26]||(a[26]=[u(" 📦 物料入库 ",-1)])),_:1,__:[26]})]),_:1}),i(m,{span:6},{default:o(()=>[i(l,{type:"success",size:"large",onClick:a[1]||(a[1]=a=>s.$router.push("/warehouse/material-apply")),block:""},{default:o(()=>a[27]||(a[27]=[u(" 📋 领料申请 ",-1)])),_:1,__:[27]})]),_:1}),i(m,{span:6},{default:o(()=>[i(l,{type:"warning",size:"large",onClick:a[2]||(a[2]=a=>s.$router.push("/projects/project-assign")),block:""},{default:o(()=>a[28]||(a[28]=[u(" 🏗️ 新建工程 ",-1)])),_:1,__:[28]})]),_:1}),i(m,{span:6},{default:o(()=>[i(l,{type:"info",size:"large",onClick:a[3]||(a[3]=a=>s.$router.push("/employees/employee-list")),block:""},{default:o(()=>a[29]||(a[29]=[u(" 👤 添加员工 ",-1)])),_:1,__:[29]})]),_:1})]),_:1}),i(_,{gutter:15,style:{"margin-top":"15px"}},{default:o(()=>[i(m,{span:6},{default:o(()=>[i(l,{type:"danger",size:"large",onClick:a[4]||(a[4]=a=>s.$router.push("/warehouse/product-outbound")),block:""},{default:o(()=>a[30]||(a[30]=[u(" 💰 商品售卖 ",-1)])),_:1,__:[30]})]),_:1}),i(m,{span:6},{default:o(()=>[i(l,{type:"primary",size:"large",onClick:a[5]||(a[5]=a=>s.$router.push("/loose-orders/monthly-balance")),block:""},{default:o(()=>a[31]||(a[31]=[u(" 📊 月度平账 ",-1)])),_:1,__:[31]})]),_:1}),i(m,{span:6},{default:o(()=>[i(l,{type:"success",size:"large",onClick:Y,block:""},{default:o(()=>a[32]||(a[32]=[u(" 📈 查看报表 ",-1)])),_:1,__:[32]})]),_:1}),i(m,{span:6},{default:o(()=>[i(l,{type:"default",size:"large",onClick:a[6]||(a[6]=a=>s.$router.push("/system/user-management")),block:""},{default:o(()=>a[33]||(a[33]=[u(" ⚙️ 系统设置 ",-1)])),_:1,__:[33]})]),_:1})]),_:1})]),_:1})]),e("div",J,[a[43]||(a[43]=e("h2",{class:"section-title"},"📊 统计模块入口",-1)),i(c,{shadow:"hover"},{default:o(()=>[e("div",L,[e("div",{class:"module-item",onClick:N},[a[35]||(a[35]=e("div",{class:"module-icon"},"📦",-1)),a[36]||(a[36]=e("div",{class:"module-info"},[e("div",{class:"module-title"},"工程物料统计"),e("div",{class:"module-description"},"查看工程物料使用情况")],-1)),i(ss,{class:"module-arrow"},{default:o(()=>[i(v(p))]),_:1})]),e("div",{class:"module-item",onClick:U},[a[37]||(a[37]=e("div",{class:"module-icon"},"🏗️",-1)),a[38]||(a[38]=e("div",{class:"module-info"},[e("div",{class:"module-title"},"工程进度统计"),e("div",{class:"module-description"},"查看工程进度和状态")],-1)),i(ss,{class:"module-arrow"},{default:o(()=>[i(v(p))]),_:1})]),e("div",{class:"module-item",onClick:V},[a[39]||(a[39]=e("div",{class:"module-icon"},"🏷️",-1)),a[40]||(a[40]=e("div",{class:"module-info"},[e("div",{class:"module-title"},"散单物料统计"),e("div",{class:"module-description"},"查看散单物料和平账信息")],-1)),i(ss,{class:"module-arrow"},{default:o(()=>[i(v(p))]),_:1})]),e("div",{class:"module-item",onClick:X},[a[41]||(a[41]=e("div",{class:"module-icon"},"💰",-1)),a[42]||(a[42]=e("div",{class:"module-info"},[e("div",{class:"module-title"},"商品售卖统计"),e("div",{class:"module-description"},"查看商品销售和排行榜")],-1)),i(ss,{class:"module-arrow"},{default:o(()=>[i(v(p))]),_:1})])])]),_:1})]),e("div",M,[a[46]||(a[46]=e("h2",{class:"section-title"},"🔔 系统通知",-1)),i(c,{shadow:"hover"},{default:o(()=>[a[45]||(a[45]=e("div",{class:"notification-list"},[e("div",{class:"notification-item warning"},[e("span",{class:"notification-icon"},"⚠️"),e("span",{class:"notification-text"},"库存预警: 智能开关库存不足，建议及时补货")]),e("div",{class:"notification-item info"},[e("span",{class:"notification-icon"},"📅"),e("span",{class:"notification-text"},"提醒: 距离上次平账已15天，建议进行月度平账")]),e("div",{class:"notification-item success"},[e("span",{class:"notification-icon"},"🎉"),e("span",{class:"notification-text"},"恭喜: 张三本月销售业绩突破¥8,000")]),e("div",{class:"notification-item default"},[e("span",{class:"notification-icon"},"🔧"),e("span",{class:"notification-text"},"系统: 定时备份已完成 (2024-01-15 03:00)")])],-1)),e("div",S,[i(l,{type:"primary",size:"small",onClick:Z},{default:o(()=>a[44]||(a[44]=[u(" 查看全部通知 ",-1)])),_:1,__:[44]})])]),_:1,__:[45]})])])}}}),[["__scopeId","data-v-b8abda2c"]]);export{T as default};
