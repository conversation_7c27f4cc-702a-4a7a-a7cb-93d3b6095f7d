# 首页功能说明文档

## 1. 页面整体结构

### 1.1 菜单结构
- **主菜单**：首页（包含下拉二级菜单）
- **二级菜单**：
  - 工程物料统计
  - 工程进度统计
  - 散单物料统计
  - 商品售卖统计

### 1.2 页面布局
- **首页概览**：默认显示页面，包含各模块数据概览
- **独立统计页面**：每个二级菜单对应独立的统计页面
- **面包屑导航**：支持导航路径显示（如：首页 > 工程物料统计）
- **响应式设计**：支持不同屏幕尺寸自适应

### 1.3 导航栏
- 顶部导航包含：首页▼、仓库管理、散户订单、工程订单、员工管理、系统设置
- 首页菜单支持下拉显示二级菜单
- 当前页面高亮显示
- 支持快捷键导航

## 2. 首页概览页面

### 2.1 功能概述
首页概览是用户访问系统时的默认页面，提供各个模块的关键数据概览和快捷操作入口。

### 2.2 主要功能
- **数据概览卡片**：
  - 工程物料概览：显示总项数、总价值
  - 工程进度概览：显示在建工程数、完成率
  - 散单物料概览：显示甲料数量、总价值
  - 商品售卖概览：显示本月销售额、完成度
  - 系统提醒概览：显示库存预警、平账提醒
  - 今日数据概览：显示新订单、完成工程

### 2.3 快捷操作区
- **常用操作**：物料入库、领料申请、新建工程、添加员工
- **业务操作**：商品售卖、月度平账、查看报表、系统设置
- **一键跳转**：直接跳转到对应功能页面

### 2.4 统计模块入口
- **模块导航**：提供四个统计模块的入口
- **功能说明**：每个模块都有简要功能描述
- **快速访问**：点击直接进入对应的统计页面

### 2.5 系统通知
- **实时通知**：显示系统重要通知和提醒
- **分类显示**：库存预警、平账提醒、业绩通知、系统通知
- **操作入口**：提供查看全部通知的入口

## 3. 工程物料统计模块

### 2.1 功能概述
根据需求1-1，显示工程中物料的使用情况统计

### 2.2 主要功能
- **总体统计**：显示已领出物料总计（项数和总价）
- **未使用物料统计**：
  - 显示未使用物料总项数和总价
  - 按总价从多到少排序显示每项物料
  - 包含物料名称、数量、总价
  - 提供详情查看功能
- **已使用物料统计**：
  - 显示已使用物料总项数和总价
  - 按总价从多到少排序显示每项物料
  - 包含物料名称、数量、总价
  - 提供详情查看功能

### 3.3 交互功能
- **[详情]按钮**：查看单个物料的详细信息
- **[查看详细报表]**：跳转到详细的物料统计报表
- **[刷新数据]**：实时更新统计数据
- **[导出统计]**：导出物料统计数据

### 3.4 数据展示
- 使用图标区分不同类型的物料（🔧燃气表、🔩镀锌管件、⚡波纹管等）
- 金额格式化显示（¥符号）
- 支持分页显示更多物料

## 4. 工程进度统计模块

### 4.1 功能概述
根据需求1-2，显示工程进度和状态统计

### 4.2 主要功能
- **工程状态统计**：
  - 显示未开始工程数量
  - 显示在建工程数量
  - 显示暂停工程数量
  - 显示完成工程数量
  - 计算总工程数和完成率
- **工程列表**：
  - 按最新进度时间排序显示工程
  - 显示工程名称、状态、最新进度时间
  - 提供操作按钮

### 4.3 状态标识
- 🔵 未开始：蓝色圆点
- 🟡 在建：黄色圆点
- 🔴 暂停：红色圆点
- ✅ 完成：绿色对勾

### 4.4 交互功能
- **[详情]按钮**：查看工程详细信息
- **[工程详情]**：查看所有工程的详细信息
- **[进度报表]**：生成工程进度报表
- **[新建工程]**：快捷创建新工程

## 5. 散单物料统计模块

### 5.1 功能概述
根据需求1-3，显示散单物料的使用和平账情况

### 5.2 主要功能
- **平账信息**：
  - 显示上次平账时间
  - 计算距离上次平账的天数
  - 提供平账提醒
- **甲料分类统计**：
  - 显示未使用甲料（项数、数量、总价）
  - 显示已使用甲料（项数、数量、总价）
  - 显示甲料库存分布（仓库库存、工人师傅）
- **甲料使用趋势**：
  - 本月使用量统计
  - 与上月对比分析
  - 平均日耗计算
  - 预计月底库存

### 5.3 数据分析
- **环比分析**：与上月数据对比
- **趋势预测**：基于历史数据预测库存
- **库存分布**：区分仓库和工人师傅的库存

### 5.4 交互功能
- **[月度平账]**：执行月度平账操作
- **[查看详情]**：查看甲料详细信息
- **[使用报表]**：生成甲料使用报表

## 6. 商品售卖统计模块

### 6.1 功能概述
根据需求1-4，显示商品售卖情况和排行榜

### 6.2 主要功能
- **本月售卖统计**：
  - 显示本月售卖项数和总价
  - 与上月对比分析
  - 月度目标完成度
  - 剩余天数提醒
- **销售排行榜**：
  - 显示销售人员排名
  - 包含销售数量、销售总价、提成
  - 支持查看详细信息
- **畅销排行榜**：
  - 显示商品销售排名
  - 包含商品名称、销售数量、销售总价
  - 显示库存状态
  - 提供补货提醒

### 6.3 排行榜设计
- **销售人员排行**：🥇🥈🥉 奖牌图标
- **商品排行**：按销售总价排序
- **库存状态**：[补货]/[正常] 状态标识

### 6.4 交互功能
- **[查看]按钮**：查看销售人员详细业绩
- **[补货]按钮**：快捷补货操作
- **[销售报表]**：生成销售统计报表
- **[员工绩效]**：查看员工绩效详情
- **[库存管理]**：跳转到库存管理页面

## 7. 页面底部功能区

### 7.1 操作区
- **最后更新时间**：显示数据最后更新时间
- **[刷新全部]**：刷新所有模块数据
- **[设置]**：页面设置选项
- **数据来源说明**：实时统计
- **移动端适配**：已优化标识

### 7.2 快捷操作面板
提供常用功能的快捷入口：
- **[物料入库]**：快速物料入库
- **[领料申请]**：快速领料申请
- **[新建工程]**：快速创建工程
- **[添加员工]**：快速添加员工
- **[商品售卖]**：快速商品售卖
- **[月度平账]**：快速月度平账
- **[查看报表]**：快速查看报表
- **[系统设置]**：快速系统设置

### 7.3 系统通知区
- **库存预警**：库存不足提醒
- **平账提醒**：平账时间提醒
- **业绩通知**：销售业绩通知
- **系统通知**：系统操作通知
- **[查看全部通知]**：查看所有通知

## 8. 技术实现要点

### 8.1 数据更新
- **实时统计**：数据实时更新
- **定时刷新**：定时自动刷新数据
- **手动刷新**：支持手动刷新

### 8.2 性能优化
- **懒加载**：模块数据按需加载
- **缓存机制**：合理使用数据缓存
- **分页显示**：大量数据分页展示

### 8.3 用户体验
- **响应式设计**：适配不同设备
- **图标化设计**：直观的图标表示
- **颜色编码**：状态颜色区分
- **交互反馈**：操作反馈提示

## 9. 数据来源

### 9.1 工程物料统计
- 数据来源：仓库管理系统
- 更新频率：实时更新
- 统计维度：按物料类型、使用状态

### 9.2 工程进度统计
- 数据来源：工程订单系统
- 更新频率：实时更新
- 统计维度：按工程状态、进度时间

### 9.3 散单物料统计
- 数据来源：散户订单系统
- 更新频率：实时更新
- 统计维度：按甲料分类、使用状态

### 9.4 商品售卖统计
- 数据来源：销售管理系统
- 更新频率：实时更新
- 统计维度：按销售人员、商品类型

## 10. 权限控制

### 10.1 查看权限
- 所有用户可查看基础统计信息
- 详细数据需要相应权限

### 10.2 操作权限
- 快捷操作需要对应模块权限
- 系统设置需要管理员权限

### 10.3 数据权限
- 销售数据按人员权限显示
- 财务数据需要财务权限

## 11. 菜单导航设计

### 11.1 主菜单设计
- **首页菜单**：支持下拉显示二级菜单
- **菜单图标**：▼ 表示有下拉菜单
- **悬停效果**：鼠标悬停显示下拉菜单
- **点击行为**：点击主菜单进入首页概览

### 11.2 二级菜单设计
- **菜单项**：工程物料统计、工程进度统计、散单物料统计、商品售卖统计
- **菜单样式**：垂直排列，左对齐
- **点击行为**：点击进入对应的统计页面
- **当前页标识**：当前页面在菜单中高亮显示

### 11.3 面包屑导航
- **导航路径**：首页 > 具体统计页面
- **点击跳转**：点击面包屑可跳转到对应页面
- **当前页标识**：当前页面不可点击
- **分隔符**：使用 > 符号分隔
