-- 工程管理系统数据库建表语句 (SQLite)
-- 创建时间: 2024-01-15
-- 数据库版本: SQLite 3.x

-- ==========================================
-- 1. 用户管理相关表
-- ==========================================

-- 用户表
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码(加密)',
    role_id INTEGER NOT NULL COMMENT '角色ID',
    phone VARCHAR(20) COMMENT '手机号',
    email VARCHAR(100) COMMENT '邮箱',
    status INTEGER DEFAULT 1 COMMENT '状态(1:正常 0:禁用)',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
);

-- 角色表
CREATE TABLE roles (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(50) NOT NULL UNIQUE COMMENT '角色名称',
    description TEXT COMMENT '角色描述',
    status INTEGER DEFAULT 1 COMMENT '状态(1:启用 0:禁用)',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
);

-- 权限表
CREATE TABLE permissions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '权限名称',
    code VARCHAR(100) NOT NULL UNIQUE COMMENT '权限代码',
    module VARCHAR(50) COMMENT '所属模块',
    status INTEGER DEFAULT 1 COMMENT '状态(1:启用 0:禁用)',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
);

-- 角色权限关联表
CREATE TABLE role_permissions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    role_id INTEGER NOT NULL COMMENT '角色ID',
    permission_id INTEGER NOT NULL COMMENT '权限ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (role_id) REFERENCES roles(id),
    FOREIGN KEY (permission_id) REFERENCES permissions(id),
    UNIQUE(role_id, permission_id)
);

-- ==========================================
-- 2. 仓库管理相关表
-- ==========================================

-- 物料表
CREATE TABLE materials (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code VARCHAR(50) NOT NULL UNIQUE COMMENT '公司物料编码',
    name VARCHAR(100) NOT NULL COMMENT '物料名称',
    model VARCHAR(100) COMMENT '型号',
    specification VARCHAR(200) COMMENT '规格',
    unit VARCHAR(20) COMMENT '单位',
    category VARCHAR(20) NOT NULL COMMENT '分类(甲料/商品/辅料)',
    price DECIMAL(10,2) DEFAULT 0.00 COMMENT '单价',
    quantity INTEGER DEFAULT 0 COMMENT '库存数量',
    warning_quantity INTEGER DEFAULT 0 COMMENT '预警数量',
    status INTEGER DEFAULT 1 COMMENT '状态(1:正常 0:停用)',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
);

-- 甲方编码表
CREATE TABLE customer_codes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    material_id INTEGER NOT NULL COMMENT '物料ID',
    customer_code VARCHAR(50) NOT NULL COMMENT '甲方编码',
    customer_name VARCHAR(100) COMMENT '甲方名称',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (material_id) REFERENCES materials(id),
    UNIQUE(material_id, customer_code)
);

-- 物料价格历史表
CREATE TABLE material_price_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    material_id INTEGER NOT NULL COMMENT '物料ID',
    old_price DECIMAL(10,2) COMMENT '原价格',
    new_price DECIMAL(10,2) NOT NULL COMMENT '新价格',
    change_reason TEXT COMMENT '调价原因',
    operator_id INTEGER NOT NULL COMMENT '操作人ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (material_id) REFERENCES materials(id),
    FOREIGN KEY (operator_id) REFERENCES users(id)
);

-- 物料进出记录表
CREATE TABLE material_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    material_id INTEGER NOT NULL COMMENT '物料ID',
    record_type VARCHAR(20) NOT NULL COMMENT '记录类型(领料/入库/退仓/采购)',
    quantity INTEGER NOT NULL COMMENT '变动数量',
    before_quantity INTEGER NOT NULL COMMENT '变动前数量',
    after_quantity INTEGER NOT NULL COMMENT '变动后数量',
    order_no VARCHAR(50) COMMENT '关联订单号',
    order_type VARCHAR(20) COMMENT '订单类型',
    operator_id INTEGER NOT NULL COMMENT '操作人ID',
    worker_id INTEGER COMMENT '领料人ID',
    remark TEXT COMMENT '备注',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (material_id) REFERENCES materials(id),
    FOREIGN KEY (operator_id) REFERENCES users(id),
    FOREIGN KEY (worker_id) REFERENCES employees(id)
);

-- ==========================================
-- 3. 员工管理相关表
-- ==========================================

-- 工种表
CREATE TABLE work_types (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(50) NOT NULL UNIQUE COMMENT '工种名称',
    description TEXT COMMENT '工种描述',
    standard_wage DECIMAL(8,2) DEFAULT 0.00 COMMENT '标准日工资',
    status INTEGER DEFAULT 1 COMMENT '状态(1:启用 0:禁用)',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
);

-- 员工表
CREATE TABLE employees (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(50) NOT NULL COMMENT '员工姓名',
    work_type_id INTEGER NOT NULL COMMENT '工种ID',
    daily_wage DECIMAL(8,2) NOT NULL COMMENT '日工资',
    phone VARCHAR(20) COMMENT '联系电话',
    id_card VARCHAR(18) COMMENT '身份证号',
    status INTEGER DEFAULT 1 COMMENT '状态(1:在职 0:离职)',
    hire_date DATE COMMENT '入职时间',
    remark TEXT COMMENT '备注',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (work_type_id) REFERENCES work_types(id)
);

-- ==========================================
-- 4. 散户订单相关表
-- ==========================================

-- 散户订单表
CREATE TABLE loose_orders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_no VARCHAR(50) NOT NULL UNIQUE COMMENT '甲方订单号',
    customer_name VARCHAR(100) NOT NULL COMMENT '户名',
    address TEXT NOT NULL COMMENT '地址',
    phone VARCHAR(20) COMMENT '电话',
    contact_person VARCHAR(50) COMMENT '联系人',
    order_type VARCHAR(50) NOT NULL COMMENT '订单分类',
    status VARCHAR(20) DEFAULT '待分派' COMMENT '订单状态',
    assigned_worker_id INTEGER COMMENT '分派师傅ID',
    total_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '总金额',
    material_cost DECIMAL(10,2) DEFAULT 0.00 COMMENT '物料成本',
    labor_cost DECIMAL(10,2) DEFAULT 0.00 COMMENT '人工成本',
    profit DECIMAL(10,2) DEFAULT 0.00 COMMENT '利润',
    estimated_days INTEGER COMMENT '预计用时(天)',
    start_date DATE COMMENT '开始时间',
    end_date DATE COMMENT '完成时间',
    remark TEXT COMMENT '备注',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (assigned_worker_id) REFERENCES employees(id)
);

-- 订单物料清单表
CREATE TABLE order_materials (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_id INTEGER NOT NULL COMMENT '订单ID',
    material_id INTEGER NOT NULL COMMENT '物料ID',
    quantity INTEGER NOT NULL COMMENT '数量',
    unit_price DECIMAL(10,2) NOT NULL COMMENT '单价',
    total_price DECIMAL(10,2) NOT NULL COMMENT '小计',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (order_id) REFERENCES loose_orders(id),
    FOREIGN KEY (material_id) REFERENCES materials(id)
);

-- 订单人员表
CREATE TABLE order_workers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_id INTEGER NOT NULL COMMENT '订单ID',
    worker_id INTEGER NOT NULL COMMENT '师傅ID',
    work_days INTEGER NOT NULL COMMENT '工作天数',
    daily_wage DECIMAL(8,2) NOT NULL COMMENT '日工资',
    total_wage DECIMAL(8,2) NOT NULL COMMENT '总工资',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (order_id) REFERENCES loose_orders(id),
    FOREIGN KEY (worker_id) REFERENCES employees(id)
);

-- 月度平账表
CREATE TABLE monthly_balance (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    balance_month VARCHAR(7) NOT NULL COMMENT '平账月份(YYYY-MM)',
    balance_date DATETIME NOT NULL COMMENT '平账时间',
    operator_id INTEGER NOT NULL COMMENT '操作人ID',
    total_orders INTEGER DEFAULT 0 COMMENT '完成订单数',
    total_amount DECIMAL(12,2) DEFAULT 0.00 COMMENT '订单总金额',
    total_material_cost DECIMAL(12,2) DEFAULT 0.00 COMMENT '物料成本',
    total_labor_cost DECIMAL(12,2) DEFAULT 0.00 COMMENT '人工成本',
    total_profit DECIMAL(12,2) DEFAULT 0.00 COMMENT '总利润',
    profit_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '利润率',
    status INTEGER DEFAULT 0 COMMENT '状态(0:未平账 1:已平账)',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (operator_id) REFERENCES users(id),
    UNIQUE(balance_month)
);

-- ==========================================
-- 5. 工程订单相关表
-- ==========================================

-- 工程表
CREATE TABLE projects (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_no VARCHAR(50) NOT NULL UNIQUE COMMENT '甲方订单号',
    project_name VARCHAR(200) NOT NULL COMMENT '工程名称',
    address TEXT NOT NULL COMMENT '工程地址',
    status VARCHAR(20) DEFAULT '未开始' COMMENT '工程状态',
    estimated_start_date DATE COMMENT '预估开始时间',
    estimated_end_date DATE COMMENT '预估结束时间',
    actual_start_date DATE COMMENT '实际开始时间',
    actual_end_date DATE COMMENT '实际结束时间',
    progress INTEGER DEFAULT 0 COMMENT '进度百分比',
    total_cost DECIMAL(12,2) DEFAULT 0.00 COMMENT '总成本',
    material_cost DECIMAL(12,2) DEFAULT 0.00 COMMENT '物料成本',
    labor_cost DECIMAL(12,2) DEFAULT 0.00 COMMENT '人工成本',
    external_cost DECIMAL(12,2) DEFAULT 0.00 COMMENT '外部成本',
    remark TEXT COMMENT '备注',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
);

-- 工程物料使用表
CREATE TABLE project_materials (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id INTEGER NOT NULL COMMENT '工程ID',
    material_id INTEGER NOT NULL COMMENT '物料ID',
    quantity INTEGER NOT NULL COMMENT '使用数量',
    unit_price DECIMAL(10,2) NOT NULL COMMENT '单价',
    total_price DECIMAL(10,2) NOT NULL COMMENT '小计',
    use_date DATE COMMENT '使用日期',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (project_id) REFERENCES projects(id),
    FOREIGN KEY (material_id) REFERENCES materials(id)
);

-- 工程人员表
CREATE TABLE project_workers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id INTEGER NOT NULL COMMENT '工程ID',
    worker_id INTEGER NOT NULL COMMENT '师傅ID',
    work_type_id INTEGER NOT NULL COMMENT '工种ID',
    work_days INTEGER NOT NULL COMMENT '工作天数',
    daily_wage DECIMAL(8,2) NOT NULL COMMENT '日工资',
    total_wage DECIMAL(8,2) NOT NULL COMMENT '总工资',
    work_date DATE COMMENT '工作日期',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (project_id) REFERENCES projects(id),
    FOREIGN KEY (worker_id) REFERENCES employees(id),
    FOREIGN KEY (work_type_id) REFERENCES work_types(id)
);

-- 工程进度记录表
CREATE TABLE project_progress (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id INTEGER NOT NULL COMMENT '工程ID',
    progress INTEGER NOT NULL COMMENT '进度百分比',
    progress_description TEXT COMMENT '进度描述',
    operator_id INTEGER NOT NULL COMMENT '操作人ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (project_id) REFERENCES projects(id),
    FOREIGN KEY (operator_id) REFERENCES users(id)
);

-- 外部成本表
CREATE TABLE external_costs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id INTEGER NOT NULL COMMENT '工程ID',
    cost_name VARCHAR(200) NOT NULL COMMENT '成本名称',
    cost_amount DECIMAL(10,2) NOT NULL COMMENT '成本金额',
    cost_date DATE COMMENT '成本日期',
    remark TEXT COMMENT '备注',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (project_id) REFERENCES projects(id)
);

-- ==========================================
-- 6. 商品售卖相关表
-- ==========================================

-- 商品售卖记录表
CREATE TABLE product_sales (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    material_id INTEGER NOT NULL COMMENT '商品ID',
    quantity INTEGER NOT NULL COMMENT '销售数量',
    unit_price DECIMAL(10,2) NOT NULL COMMENT '单价',
    total_price DECIMAL(10,2) NOT NULL COMMENT '总价',
    seller_id INTEGER NOT NULL COMMENT '销售人ID',
    customer_name VARCHAR(100) COMMENT '客户姓名',
    customer_phone VARCHAR(20) COMMENT '客户电话',
    sale_date DATE NOT NULL COMMENT '销售日期',
    remark TEXT COMMENT '备注',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (material_id) REFERENCES materials(id),
    FOREIGN KEY (seller_id) REFERENCES users(id)
);

-- ==========================================
-- 7. 系统日志表
-- ==========================================

-- 系统日志表
CREATE TABLE system_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER COMMENT '操作人ID',
    username VARCHAR(50) COMMENT '操作人用户名',
    operation VARCHAR(100) NOT NULL COMMENT '操作内容',
    result VARCHAR(20) NOT NULL COMMENT '操作结果(成功/失败)',
    ip_address VARCHAR(50) COMMENT '操作IP',
    user_agent TEXT COMMENT '用户代理',
    request_url TEXT COMMENT '请求URL',
    request_method VARCHAR(10) COMMENT '请求方法',
    request_params TEXT COMMENT '请求参数',
    error_message TEXT COMMENT '错误信息',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- ==========================================
-- 8. 创建索引
-- ==========================================

-- 用户表索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_role_id ON users(role_id);
CREATE INDEX idx_users_status ON users(status);

-- 物料表索引
CREATE INDEX idx_materials_code ON materials(code);
CREATE INDEX idx_materials_category ON materials(category);
CREATE INDEX idx_materials_status ON materials(status);

-- 订单表索引
CREATE INDEX idx_loose_orders_order_no ON loose_orders(order_no);
CREATE INDEX idx_loose_orders_status ON loose_orders(status);
CREATE INDEX idx_loose_orders_assigned_worker_id ON loose_orders(assigned_worker_id);

-- 工程表索引
CREATE INDEX idx_projects_project_no ON projects(project_no);
CREATE INDEX idx_projects_status ON projects(status);

-- 员工表索引
CREATE INDEX idx_employees_name ON employees(name);
CREATE INDEX idx_employees_work_type_id ON employees(work_type_id);
CREATE INDEX idx_employees_status ON employees(status);

-- 日志表索引
CREATE INDEX idx_system_logs_user_id ON system_logs(user_id);
CREATE INDEX idx_system_logs_created_at ON system_logs(created_at);
CREATE INDEX idx_system_logs_operation ON system_logs(operation);

-- ==========================================
-- 9. 插入初始数据
-- ==========================================

-- 插入默认角色
INSERT INTO roles (name, description) VALUES 
('系统管理员', '系统超级管理员，拥有所有权限'),
('仓库管理员', '管理仓库物料，负责物料入库、出库、价格管理等'),
('订单管理员', '管理散户订单，负责订单分派、跟进等'),
('工程管理员', '管理工程订单，负责工程推进、人员安排等'),
('财务人员', '管理财务信息，负责成本核算、平账等'),
('普通用户', '基础操作权限，查看数据等');

-- 插入默认权限
INSERT INTO permissions (name, code, module) VALUES 
-- 系统设置权限
('用户管理', 'system:user:list', '系统设置'),
('角色管理', 'system:role:list', '系统设置'),
('权限管理', 'system:permission:list', '系统设置'),
('系统日志', 'system:log:list', '系统设置'),

-- 仓库管理权限
('物料列表', 'warehouse:material:list', '仓库管理'),
('物料新增', 'warehouse:material:add', '仓库管理'),
('物料编辑', 'warehouse:material:edit', '仓库管理'),
('物料删除', 'warehouse:material:delete', '仓库管理'),
('领料申请', 'warehouse:apply:list', '仓库管理'),
('甲料入库', 'warehouse:inbound:add', '仓库管理'),
('物料退仓', 'warehouse:return:add', '仓库管理'),
('辅料采购', 'warehouse:purchase:add', '仓库管理'),
('商品入库', 'warehouse:product:inbound', '仓库管理'),
('进出记录', 'warehouse:record:list', '仓库管理'),
('商品售卖出库', 'warehouse:product:outbound', '仓库管理'),
('物料价格', 'warehouse:price:list', '仓库管理'),
('商品价格', 'warehouse:product:price', '仓库管理'),
('物料基础库', 'warehouse:base:list', '仓库管理'),
('库存预警', 'warehouse:warning:list', '仓库管理'),

-- 散户订单权限
('订单列表', 'loose:order:list', '散户订单'),
('订单新增', 'loose:order:add', '散户订单'),
('订单编辑', 'loose:order:edit', '散户订单'),
('订单分派', 'loose:order:assign', '散户订单'),
('月度平账', 'loose:balance:monthly', '散户订单'),
('平账记录', 'loose:balance:record', '散户订单'),

-- 工程订单权限
('工程列表', 'project:list', '工程订单'),
('工程新增', 'project:add', '工程订单'),
('工程编辑', 'project:edit', '工程订单'),
('工种设置', 'project:worktype:list', '工程订单'),
('工程开始', 'project:start', '工程订单'),
('工程推进', 'project:progress', '工程订单'),
('工程暂停', 'project:pause', '工程订单'),
('工程完成', 'project:finish', '工程订单'),
('外部成本', 'project:cost:external', '工程订单'),

-- 员工管理权限
('员工列表', 'employee:list', '员工管理'),
('员工新增', 'employee:add', '员工管理'),
('员工编辑', 'employee:edit', '员工管理'),
('工种设置', 'employee:worktype:list', '员工管理'),
('绩效设置', 'employee:performance:list', '员工管理');

-- 插入默认工种
INSERT INTO work_types (name, description, standard_wage) VALUES 
('电工', '电气安装维修', 300.00),
('水工', '水管安装维修', 280.00),
('安装工', '设备安装调试', 320.00),
('维修工', '设备维修保养', 250.00),
('木工', '木制品制作安装', 260.00),
('油漆工', '墙面涂料施工', 240.00);

-- 插入默认管理员用户 (密码: admin123)
INSERT INTO users (username, password, role_id, phone, email) VALUES 
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDa', 1, '13800000000', '<EMAIL>');

-- 为系统管理员分配所有权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT 1, id FROM permissions;

-- ==========================================
-- 10. 创建触发器
-- ==========================================

-- 更新物料库存的触发器
CREATE TRIGGER update_material_quantity
AFTER INSERT ON material_records
BEGIN
    UPDATE materials 
    SET quantity = NEW.after_quantity,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.material_id;
END;

-- 更新订单总金额的触发器
CREATE TRIGGER update_order_total_amount
AFTER INSERT ON order_materials
BEGIN
    UPDATE loose_orders 
    SET total_amount = (
        SELECT COALESCE(SUM(total_price), 0) 
        FROM order_materials 
        WHERE order_id = NEW.order_id
    ),
    material_cost = (
        SELECT COALESCE(SUM(total_price), 0) 
        FROM order_materials 
        WHERE order_id = NEW.order_id
    ),
    updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.order_id;
END;

-- 更新工程总成本的触发器
CREATE TRIGGER update_project_total_cost
AFTER INSERT ON project_materials
BEGIN
    UPDATE projects 
    SET total_cost = (
        SELECT COALESCE(SUM(total_price), 0) 
        FROM project_materials 
        WHERE project_id = NEW.project_id
    ) + (
        SELECT COALESCE(SUM(total_wage), 0) 
        FROM project_workers 
        WHERE project_id = NEW.project_id
    ) + (
        SELECT COALESCE(SUM(cost_amount), 0) 
        FROM external_costs 
        WHERE project_id = NEW.project_id
    ),
    material_cost = (
        SELECT COALESCE(SUM(total_price), 0) 
        FROM project_materials 
        WHERE project_id = NEW.project_id
    ),
    updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.project_id;
END;

-- 更新时间戳的触发器
CREATE TRIGGER update_users_timestamp
AFTER UPDATE ON users
BEGIN
    UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER update_materials_timestamp
AFTER UPDATE ON materials
BEGIN
    UPDATE materials SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER update_loose_orders_timestamp
AFTER UPDATE ON loose_orders
BEGIN
    UPDATE loose_orders SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER update_projects_timestamp
AFTER UPDATE ON projects
BEGIN
    UPDATE projects SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- ==========================================
-- 数据库建表完成
-- ========================================== 