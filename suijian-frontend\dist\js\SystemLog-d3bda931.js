import{r as e,x as l,E as a,e as t}from"./element-plus-ad78a7bf.js";import{l as r,_ as o,r as n,q as s,y as u,R as d,J as i,av as p,x as c,O as m,z as g,u as f,P as _}from"./vue-vendor-fc5a6493.js";import{_ as b}from"./_plugin-vue_export-helper-1b428a4d.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const h={class:"system-log"},v={class:"card-header"},w={class:"header-actions"},y={class:"search-section"},z={class:"pagination-section"},x=b(r({__name:"SystemLog",setup(r){const b=o({logLevel:"",module:"",operator:"",dateRange:[]}),x=o({currentPage:1,pageSize:20,total:0}),L=n([{logId:"L001",logTime:"2024-01-15 14:30:25",logLevel:"info",module:"user",operator:"张三",operation:"用户登录",ipAddress:"*************",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"},{logId:"L002",logTime:"2024-01-15 14:25:10",logLevel:"warning",module:"warehouse",operator:"李四",operation:"物料库存不足预警",ipAddress:"*************",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"},{logId:"L003",logTime:"2024-01-15 14:20:05",logLevel:"error",module:"project",operator:"王五",operation:"工程数据保存失败",ipAddress:"*************",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}]),V=e=>{switch(e){case"info":return"primary";case"warning":return"warning";case"error":return"danger";default:return"info"}},A=e=>{switch(e){case"info":return"信息";case"warning":return"警告";case"error":return"错误";case"debug":return"调试";default:return"未知"}},j=e=>{switch(e){case"user":return"用户管理";case"warehouse":return"仓库管理";case"project":return"工程订单";case"loose":return"散户订单";case"system":return"系统设置";default:return"未知模块"}},C=()=>{a.success("搜索完成")},P=()=>{Object.keys(b).forEach(e=>{b[e]="dateRange"===e?[]:""}),a.info("搜索条件已重置")},T=()=>{a.success("数据已刷新")},W=()=>{return e=this,l=null,r=function*(){try{yield t.confirm("确定要清空所有日志吗？此操作不可恢复。","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),a.success("日志已清空")}catch(e){}},new Promise((a,t)=>{var o=e=>{try{s(r.next(e))}catch(l){t(l)}},n=e=>{try{s(r.throw(e))}catch(l){t(l)}},s=e=>e.done?a(e.value):Promise.resolve(e.value).then(o,n);s((r=r.apply(e,l)).next())});var e,l,r},k=e=>{x.pageSize=e},I=e=>{x.currentPage=e};return s(()=>{x.total=L.value.length}),(t,r)=>{const o=p("el-breadcrumb-item"),n=p("el-breadcrumb"),s=p("el-icon"),M=p("el-button"),Y=p("el-option"),S=p("el-select"),U=p("el-form-item"),D=p("el-input"),R=p("el-date-picker"),H=p("el-form"),K=p("el-table-column"),N=p("el-tag"),B=p("el-table"),E=p("el-pagination"),O=p("el-card");return c(),u("div",h,[d(n,{class:"breadcrumb",separator:">"},{default:i(()=>[d(o,{to:{path:"/dashboard"}},{default:i(()=>r[6]||(r[6]=[m("首页",-1)])),_:1,__:[6]}),d(o,{to:{path:"/system/user-management"}},{default:i(()=>r[7]||(r[7]=[m("系统设置",-1)])),_:1,__:[7]}),d(o,null,{default:i(()=>r[8]||(r[8]=[m("系统日志",-1)])),_:1,__:[8]})]),_:1}),d(O,{class:"main-card"},{header:i(()=>[g("div",v,[r[11]||(r[11]=g("span",null,"📋 系统日志",-1)),g("div",w,[d(M,{type:"primary",size:"small",onClick:T},{default:i(()=>[d(s,null,{default:i(()=>[d(f(e))]),_:1}),r[9]||(r[9]=m(" 刷新 ",-1))]),_:1,__:[9]}),d(M,{type:"danger",size:"small",onClick:W},{default:i(()=>[d(s,null,{default:i(()=>[d(f(l))]),_:1}),r[10]||(r[10]=m(" 清空日志 ",-1))]),_:1,__:[10]})])])]),default:i(()=>[g("div",y,[d(H,{model:b,inline:""},{default:i(()=>[d(U,{label:"日志级别:"},{default:i(()=>[d(S,{modelValue:b.logLevel,"onUpdate:modelValue":r[0]||(r[0]=e=>b.logLevel=e),placeholder:"请选择日志级别"},{default:i(()=>[d(Y,{label:"全部",value:""}),d(Y,{label:"信息",value:"info"}),d(Y,{label:"警告",value:"warning"}),d(Y,{label:"错误",value:"error"}),d(Y,{label:"调试",value:"debug"})]),_:1},8,["modelValue"])]),_:1}),d(U,{label:"操作模块:"},{default:i(()=>[d(S,{modelValue:b.module,"onUpdate:modelValue":r[1]||(r[1]=e=>b.module=e),placeholder:"请选择操作模块"},{default:i(()=>[d(Y,{label:"全部",value:""}),d(Y,{label:"用户管理",value:"user"}),d(Y,{label:"仓库管理",value:"warehouse"}),d(Y,{label:"工程订单",value:"project"}),d(Y,{label:"散户订单",value:"loose"}),d(Y,{label:"系统设置",value:"system"})]),_:1},8,["modelValue"])]),_:1}),d(U,{label:"操作员:"},{default:i(()=>[d(D,{modelValue:b.operator,"onUpdate:modelValue":r[2]||(r[2]=e=>b.operator=e),placeholder:"请输入操作员"},null,8,["modelValue"])]),_:1}),d(U,{label:"日期范围:"},{default:i(()=>[d(R,{modelValue:b.dateRange,"onUpdate:modelValue":r[3]||(r[3]=e=>b.dateRange=e),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),d(U,null,{default:i(()=>[d(M,{type:"primary",onClick:C},{default:i(()=>r[12]||(r[12]=[m("搜索",-1)])),_:1,__:[12]}),d(M,{onClick:P},{default:i(()=>r[13]||(r[13]=[m("重置",-1)])),_:1,__:[13]})]),_:1})]),_:1},8,["model"])]),d(B,{data:L.value,border:"",style:{width:"100%"}},{default:i(()=>[d(K,{prop:"logId",label:"日志ID",width:"100"}),d(K,{prop:"logTime",label:"操作时间",width:"180"}),d(K,{prop:"logLevel",label:"日志级别",width:"100"},{default:i(({row:e})=>[d(N,{type:V(e.logLevel)},{default:i(()=>[m(_(A(e.logLevel)),1)]),_:2},1032,["type"])]),_:1}),d(K,{prop:"module",label:"操作模块",width:"120"},{default:i(({row:e})=>[m(_(j(e.module)),1)]),_:1}),d(K,{prop:"operator",label:"操作员",width:"100"}),d(K,{prop:"operation",label:"操作内容","min-width":"200"}),d(K,{prop:"ipAddress",label:"IP地址",width:"120"}),d(K,{prop:"userAgent",label:"用户代理","min-width":"150","show-overflow-tooltip":""}),d(K,{label:"操作",width:"100",fixed:"right"},{default:i(({row:e})=>[d(M,{type:"primary",size:"small",onClick:l=>(e=>{a.info(`查看日志 ${e.logId} 的详情`)})(e)},{default:i(()=>r[14]||(r[14]=[m(" 详情 ",-1)])),_:2,__:[14]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),g("div",z,[d(E,{"current-page":x.currentPage,"onUpdate:currentPage":r[4]||(r[4]=e=>x.currentPage=e),"page-size":x.pageSize,"onUpdate:pageSize":r[5]||(r[5]=e=>x.pageSize=e),"page-sizes":[10,20,50,100],total:x.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:k,onCurrentChange:I},null,8,["current-page","page-size","total"])])]),_:1})])}}}),[["__scopeId","data-v-af4b9465"]]);export{x as default};
