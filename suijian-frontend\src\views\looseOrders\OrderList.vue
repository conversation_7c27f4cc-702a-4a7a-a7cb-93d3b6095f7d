<template>
  <div class="page-container">
    <div class="search-form">
      <el-form :model="searchForm" inline>
        <el-form-item label="订单号">
          <el-input
            v-model="searchForm.orderNo"
            placeholder="请输入甲方订单号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="户名">
          <el-input
            v-model="searchForm.customerName"
            placeholder="请输入户名"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="地址">
          <el-input
            v-model="searchForm.address"
            placeholder="请输入地址"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="电话">
          <el-input
            v-model="searchForm.phone"
            placeholder="请输入电话"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="联系人">
          <el-input
            v-model="searchForm.contactPerson"
            placeholder="请输入联系人"
            clearable
            style="width: 120px"
          />
        </el-form-item>
        <el-form-item label="订单分类">
          <el-select v-model="searchForm.orderType" placeholder="请选择分类" clearable>
            <el-option label="安装" value="安装" />
            <el-option label="维修" value="维修" />
            <el-option label="改造" value="改造" />
          </el-select>
        </el-form-item>
        <el-form-item label="订单状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="待分派" value="待分派" />
            <el-option label="进行中" value="进行中" />
            <el-option label="已完成" value="已完成" />
            <el-option label="已取消" value="已取消" />
          </el-select>
        </el-form-item>
        <el-form-item label="跟进师傅">
          <el-input
            v-model="searchForm.assignedWorker"
            placeholder="请输入跟进师傅"
            clearable
            style="width: 120px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="action-buttons">
      <el-button type="primary" @click="handleAdd">新增订单</el-button>
      <el-button type="success" @click="handleAssign">批量分派</el-button>
      <el-button type="warning" @click="handleExport">导出Excel</el-button>
    </div>

    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        stripe
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="orderNo" label="甲方订单号" width="150" />
        <el-table-column prop="customerName" label="户名" width="120" />
        <el-table-column prop="address" label="地址" min-width="200" show-overflow-tooltip />
        <el-table-column prop="phone" label="电话" width="120" />
        <el-table-column prop="contactPerson" label="联系人" width="100" />
        <el-table-column prop="orderType" label="订单分类" width="100">
          <template #default="{ row }">
            <el-tag :type="getOrderTypeTag(row.orderType)">{{ row.orderType }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="订单状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTag(row.status)">{{ row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="assignedWorker" label="跟进师傅" width="100" />
        <el-table-column prop="totalAmount" label="总金额" width="120">
          <template #default="{ row }">
            ¥{{ row.totalAmount }}
          </template>
        </el-table-column>
        <el-table-column prop="estimatedDays" label="预计用时" width="100">
          <template #default="{ row }">
            {{ row.estimatedDays }}天
          </template>
        </el-table-column>
        <el-table-column prop="startDate" label="开始时间" width="120" />
        <el-table-column prop="endDate" label="完成时间" width="120" />
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
            <el-button type="info" size="small" @click="handleView(row)">查看</el-button>
            <el-button type="success" size="small" @click="handleDispatch(row)">分派</el-button>
            <el-button type="warning" size="small" @click="handleExecute(row)">执行</el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 分派对话框 -->
    <el-dialog
      v-model="dispatchDialogVisible"
      title="订单分派"
      width="900px"
      center
    >
      <!-- 订单信息展示 -->
      <div class="order-info">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <span class="label">甲方订单号:</span>
              <span class="value">{{ dispatchForm.orderNo }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">户名:</span>
              <span class="value">{{ dispatchForm.customerName }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">用户编号:</span>
              <span class="value">{{ dispatchForm.userNo }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">小区名称:</span>
              <span class="value">{{ dispatchForm.communityName }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">楼橦:</span>
              <span class="value">{{ dispatchForm.building }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">房号:</span>
              <span class="value">{{ dispatchForm.roomNo }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">电话:</span>
              <span class="value">{{ dispatchForm.phone }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">联系人:</span>
              <span class="value">{{ dispatchForm.contactPerson }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">订单分类:</span>
              <span class="value">{{ dispatchForm.orderType }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">订单状态:</span>
              <span class="value">{{ dispatchForm.status }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 师傅选择区域 -->
      <div class="master-selection">
        <div class="section-title">选择师傅</div>
        
        <!-- 搜索区域 -->
        <el-row :gutter="20" style="margin-bottom: 20px;">
          <el-col :span="18">
            <el-input v-model="masterSearch" placeholder="请输入搜索关键词" clearable />
          </el-col>
          <el-col :span="6">
            <el-button type="primary" icon="Search" @click="searchMasters">搜索</el-button>
          </el-col>
        </el-row>
        
        <!-- 师傅列表 -->
        <el-table :data="masterOptions" border height="300" class="master-table">
          <el-table-column prop="name" label="师傅姓名" width="100" />
          <el-table-column prop="workType" label="工种" width="120" />
          <el-table-column prop="dailyWage" label="工价(元/天)" width="120" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.status === 'active' ? 'success' : 'info'">
                {{ scope.row.status === 'active' ? '在职' : '离职' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="taskCount" label="当前任务数" width="120" />
          <el-table-column label="操作" width="100" fixed="right">
            <template #default="scope">
              <el-button type="primary" @click="selectMaster(scope.row)">选择</el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 备注区域 -->
        <div class="remarks-section">
          <el-input
            v-model="dispatchForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注"
          />
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSave">保存</el-button>
          <el-button type="success" @click="handleDispatchSubmit">提交</el-button>
          <el-button @click="handlePrint">打印</el-button>
          <el-button @click="dispatchDialogVisible = false">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'

const router = useRouter()

// 搜索表单
const searchForm = reactive({
  orderNo: '',
  customerName: '',
  address: '',
  phone: '',
  contactPerson: '',
  orderType: '',
  status: '',
  assignedWorker: ''
})

// 表格数据
const tableData = ref([])
const loading = ref(false)
const selectedRows = ref([])

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 分派对话框
const dispatchDialogVisible = ref(false)
const dispatchForm = reactive({
  orderNo: '',
  customerName: '',
  userNo: '',
  communityName: '',
  building: '',
  roomNo: '',
  phone: '',
  contactPerson: '',
  orderType: '',
  status: '',
  assignedWorker: '',
  estimatedDays: 1,
  startDate: '',
  remark: ''
})

// 师傅搜索
const masterSearch = ref('')

// 师傅选项
const masterOptions = ref([
  {
    id: 1,
    name: '李师傅',
    workType: '电工',
    dailyWage: 300,
    status: 'active',
    taskCount: 2
  },
  {
    id: 2,
    name: '王师傅',
    workType: '水工',
    dailyWage: 280,
    status: 'active',
    taskCount: 1
  },
  {
    id: 3,
    name: '张师傅',
    workType: '安装工',
    dailyWage: 320,
    status: 'active',
    taskCount: 3
  },
  {
    id: 4,
    name: '赵师傅',
    workType: '电工',
    dailyWage: 300,
    status: 'active',
    taskCount: 0
  }
])

const dispatchRules = {
  assignedWorker: [{ required: true, message: '请选择分派师傅', trigger: 'change' }],
  estimatedDays: [{ required: true, message: '请输入预计用时', trigger: 'blur' }],
  startDate: [{ required: true, message: '请选择开始时间', trigger: 'change' }]
}

// 获取订单分类标签类型
const getOrderTypeTag = (type: string) => {
  const types = {
    '安装': 'primary',
    '维修': 'warning',
    '改造': 'success',
    '水电安装': 'primary',
    '一次挂表': 'success',
    '二次安装': 'warning',
    '售后': 'info'
  }
  return types[type] || 'info'
}

// 获取状态标签类型
const getStatusTag = (status: string) => {
  const types = {
    '待分派': 'info',
    '进行中': 'warning',
    '已完成': 'success',
    '已取消': 'danger'
  }
  return types[status] || 'info'
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  loadData()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    orderNo: '',
    customerName: '',
    address: '',
    phone: '',
    contactPerson: '',
    orderType: '',
    status: '',
    assignedWorker: ''
  })
  handleSearch()
}

// 选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

// 新增
const handleAdd = () => {
  ElMessage.info('新增订单功能待实现')
}

// 编辑
const handleEdit = (row: any) => {
  ElMessage.info('编辑订单功能待实现')
}

// 查看
const handleView = (row: any) => {
  ElMessage.info('查看订单详情功能待实现')
}

// 执行
const handleExecute = (row: any) => {
  // 跳转到订单执行页面并传递订单信息
  const orderInfo = {
    orderNo: row.orderNo,
    customerName: row.customerName,
    userNo: row.userNo,
    communityName: row.communityName,
    building: row.building,
    roomNo: row.roomNo,
    address: row.address,
    phone: row.phone,
    contactPerson: row.contactPerson,
    orderType: row.orderType,
    status: row.status,
    assignedWorker: row.assignedWorker,
    totalAmount: row.totalAmount,
    estimatedDays: row.estimatedDays,
    startDate: row.startDate,
    endDate: row.endDate
  }
  
  // 使用路由跳转并传递参数
  router.push({
    name: 'OrderExecute',
    query: { orderInfo: JSON.stringify(orderInfo) }
  })
}

// 分派
const handleDispatch = (row: any) => {
  Object.assign(dispatchForm, {
    orderNo: row.orderNo,
    customerName: row.customerName,
    userNo: row.userNo || 'YH' + row.orderNo.slice(-6),
    communityName: row.communityName || row.address?.split('区')[0] + '区',
    building: row.building || row.address?.split('区')[1]?.split('号')[0] + '栋',
    roomNo: row.roomNo || row.address?.split('号')[1]?.split('号')[0] || '101',
    phone: row.phone,
    contactPerson: row.contactPerson,
    orderType: row.orderType,
    status: row.status,
    assignedWorker: '',
    estimatedDays: 1,
    startDate: '',
    remark: ''
  })
  dispatchDialogVisible.value = true
}

// 搜索师傅
const searchMasters = () => {
  ElMessage.success('搜索师傅')
  console.log('搜索关键词:', masterSearch.value)
}

// 选择师傅
const selectMaster = (row: any) => {
  dispatchForm.assignedWorker = row.id
  ElMessage.success(`已选择师傅: ${row.name}`)
}

// 保存
const handleSave = () => {
  ElMessage.success('保存成功')
  console.log('保存数据:', dispatchForm)
}

// 批量分派
const handleAssign = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要分派的订单')
    return
  }
  ElMessage.info('批量分派功能待实现')
}

// 删除
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要删除该订单吗？', '提示', {
      type: 'warning'
    })
    ElMessage.success('删除成功')
    loadData()
  } catch {
    // 用户取消
  }
}

// 导出
const handleExport = () => {
  ElMessage.info('导出功能待实现')
}

// 分派提交
const handleDispatchSubmit = async () => {
  if (!dispatchForm.assignedWorker) {
    ElMessage.warning('请选择分派师傅')
    return
  }
  
  ElMessage.success('分派成功')
  dispatchDialogVisible.value = false
  loadData()
}

// 打印
const handlePrint = () => {
  ElMessage.success('开始打印')
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  loadData()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadData()
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟数据
    tableData.value = [
      {
        id: 1,
        orderNo: 'JD202401001',
        customerName: '张先生',
        userNo: 'YH001234',
        communityName: '阳光小区',
        building: '1栋',
        roomNo: '101',
        address: '阳光小区1栋101',
        phone: '138****1234',
        contactPerson: '张先生',
        orderType: '水电安装',
        status: '待分派',
        assignedWorker: '',
        totalAmount: 1500.00,
        estimatedDays: 3,
        startDate: '',
        endDate: ''
      },
      {
        id: 2,
        orderNo: 'JD202401002',
        customerName: '李女士',
        userNo: 'YH001235',
        communityName: '花园小区',
        building: '2栋',
        roomNo: '202',
        address: '花园小区2栋202',
        phone: '139****5678',
        contactPerson: '李女士',
        orderType: '一次挂表',
        status: '待分派',
        assignedWorker: '',
        totalAmount: 800.00,
        estimatedDays: 2,
        startDate: '',
        endDate: ''
      },
      {
        id: 3,
        orderNo: 'JD202401003',
        customerName: '王先生',
        userNo: 'YH001236',
        communityName: '商业街',
        building: '3号楼',
        roomNo: '303',
        address: '商业街3号楼303',
        phone: '137****9012',
        contactPerson: '王先生',
        orderType: '二次安装',
        status: '待分派',
        assignedWorker: '',
        totalAmount: 1200.00,
        estimatedDays: 4,
        startDate: '',
        endDate: ''
      },
      {
        id: 4,
        orderNo: 'JD202401004',
        customerName: '赵女士',
        userNo: 'YH001237',
        communityName: '住宅区',
        building: '4栋',
        roomNo: '404',
        address: '住宅区4栋404',
        phone: '136****3456',
        contactPerson: '赵女士',
        orderType: '售后',
        status: '待分派',
        assignedWorker: '',
        totalAmount: 600.00,
        estimatedDays: 1,
        startDate: '',
        endDate: ''
      }
    ]
    pagination.total = 156
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadData()
})
</script> 

<style lang="scss" scoped>
.page-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);

  .search-form {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .action-buttons {
    margin-bottom: 20px;
    
    .el-button {
      margin-right: 10px;
    }
  }

  .table-container {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .pagination-container {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }
  }
}

// 分派弹窗样式
.order-info {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;

  .info-item {
    display: flex;
    margin-bottom: 10px;
    line-height: 1.6;

    .label {
      width: 100px;
      font-weight: bold;
      color: #606266;
    }

    .value {
      flex: 1;
      color: #303133;
    }
  }
}

.master-selection {
  .section-title {
    font-size: 16px;
    font-weight: bold;
    color: #606266;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;
  }

  .master-table {
    margin-bottom: 20px;
  }

  .remarks-section {
    margin-top: 15px;
  }
}

.dialog-footer {
  text-align: center;

  .el-button {
    margin: 0 5px;
  }
}
</style> 