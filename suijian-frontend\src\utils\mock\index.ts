import Mock from 'mockjs'

// 设置Mock延迟
Mock.setup({
  timeout: '200-600'
})

// 登录接口
Mock.mock('/api/auth/login', 'post', {
  code: 200,
  message: '登录成功',
  data: {
    token: '@guid',
    user: {
      id: '@id',
      username: 'admin',
      role: '系统管理员',
      phone: '13800000000',
      email: '<EMAIL>'
    }
  }
})

// 检查认证状态
Mock.mock('/api/auth/check', 'get', {
  code: 200,
  message: '验证成功',
  data: {
    user: {
      id: '@id',
      username: 'admin',
      role: '系统管理员',
      phone: '13800000000',
      email: '<EMAIL>'
    }
  }
})

// 首页统计数据
Mock.mock('/api/dashboard/materials/unused', 'get', {
  code: 200,
  message: '获取成功',
  data: {
    totalItems: 25,
    totalAmount: 125680,
    items: [
      {
        name: '电缆线',
        quantity: 100,
        unit: '米',
        totalPrice: 15000
      },
      {
        name: '电线',
        quantity: 200,
        unit: '米',
        totalPrice: 8000
      }
    ]
  }
})

Mock.mock('/api/dashboard/materials/used', 'get', {
  code: 200,
  message: '获取成功',
  data: {
    totalItems: 18,
    totalAmount: 89320,
    items: [
      {
        name: '开关',
        quantity: 50,
        unit: '个',
        totalPrice: 2500
      }
    ]
  }
})

Mock.mock('/api/dashboard/projects/status', 'get', {
  code: 200,
  message: '获取成功',
  data: {
    notStarted: 5,
    inProgress: 12,
    paused: 3,
    completed: 8,
    projects: [
      {
        name: '阳光小区A栋',
        status: '在建',
        lastUpdateTime: '2024-01-15 14:30'
      }
    ]
  }
})

// 物料列表
Mock.mock(/\/api\/materials(\?.*)?$/, 'get', {
  code: 200,
  message: '获取成功',
  data: {
    list: [
      {
        id: 1,
        code: 'WL001',
        name: '电缆线',
        model: 'YJV-3*4',
        specification: '3*4mm²',
        unit: '米',
        category: '甲料',
        price: 30.00,
        quantity: 500,
        warningQuantity: 100,
        status: 1
      },
      {
        id: 2,
        code: 'WL002',
        name: '电线',
        model: 'BV-2.5',
        specification: '2.5mm²',
        unit: '米',
        category: '甲料',
        price: 15.00,
        quantity: 800,
        warningQuantity: 200,
        status: 1
      }
    ],
    total: 156,
    page: 1,
    pageSize: 20
  }
})

// 月度平账基础信息
Mock.mock('/api/loose-orders/balance/month', 'get', {
  code: 200,
  message: '获取成功',
  data: {
    balanceInfo: {
      month: '2024年1月',
      balanceDate: '2024-01-31T15:30:00',
      laborCost: 54336,
      overIssuedItems: 12,
      overIssuedQuantity: 156,
      unreturnedItems: 8,
      unreturnedQuantity: 89
    }
  }
})

// 工程费用汇总表数据
Mock.mock('/api/loose-orders/balance/project-cost', 'get', {
  code: 200,
  message: '获取成功',
  data: {
    list: [
      {
        serialNo: 1,
        projectName: '阳光小区A栋户内安装',
        preMeterLaborCost: 15000,
        indoorLaborCost: 25000,
        laborSubtotal: 40000,
        installedHouseholds: 50,
        gasMeterCost: 8000,
        postMeterCost: 12000,
        fittingsCost: 6000,
        materialSubtotal: 26000,
        actualReceivedAmount: 28000,
        overReceivedAmount: 2000,
        totalProjectCost: 68000,
        payableAmount: 38000
      },
      {
        serialNo: 2,
        projectName: '绿城花园B区户内安装',
        preMeterLaborCost: 18000,
        indoorLaborCost: 30000,
        laborSubtotal: 48000,
        installedHouseholds: 60,
        gasMeterCost: 9600,
        postMeterCost: 14400,
        fittingsCost: 7200,
        materialSubtotal: 31200,
        actualReceivedAmount: 33600,
        overReceivedAmount: 2400,
        totalProjectCost: 81600,
        payableAmount: 45600
      }
    ],
    summary: {
      preMeterLaborCost: 33000,
      indoorLaborCost: 55000,
      laborSubtotal: 88000,
      installedHouseholds: 110,
      gasMeterCost: 17600,
      postMeterCost: 26400,
      fittingsCost: 13200,
      materialSubtotal: 57200,
      actualReceivedAmount: 61600,
      overReceivedAmount: 4400,
      totalProjectCost: 149600,
      payableAmount: 83600
    }
  }
})

// 表前材料清单表数据
Mock.mock('/api/loose-orders/balance/pre-meter-materials', 'get', {
  code: 200,
  message: '获取成功',
  data: {
    list: [
      {
        serialNo: 1,
        projectName: '绿城花园',
        mechanicalConnectorReceived: 50,
        mechanicalConnectorConsumed: 48,
        mechanicalConnectorPrice: 25.5,
        qianjiaMeterReceived: 30,
        qianjiaMeterConsumed: 29,
        qianjiaMeterPrice: 180.0,
        lowPressureRegulatorReceived: 80,
        lowPressureRegulatorConsumed: 78,
        lowPressureRegulatorPrice: 45.0,
        meterBoxReceived: 60,
        meterBoxConsumed: 58,
        meterBoxPrice: 120.0,
        preMeterValveReceived: 100,
        preMeterValveConsumed: 97,
        preMeterValvePrice: 35.0,
        prefabricatedPipeReceived: 200,
        prefabricatedPipeConsumed: 195,
        prefabricatedPipePrice: 8.5,
        preMeterMaterialsReceivedAmount: 28500,
        preMeterMaterialsConsumedAmount: 27800
      }
    ],
    summary: {
      mechanicalConnectorReceived: 50,
      mechanicalConnectorConsumed: 48,
      qianjiaMeterReceived: 30,
      qianjiaMeterConsumed: 29,
      lowPressureRegulatorReceived: 80,
      lowPressureRegulatorConsumed: 78,
      meterBoxReceived: 60,
      meterBoxConsumed: 58,
      preMeterValveReceived: 100,
      preMeterValveConsumed: 97,
      prefabricatedPipeReceived: 200,
      prefabricatedPipeConsumed: 195,
      preMeterMaterialsReceivedAmount: 28500,
      preMeterMaterialsConsumedAmount: 27800
    }
  }
})

// 户内材料清单表数据
Mock.mock('/api/loose-orders/balance/indoor-materials', 'get', {
  code: 200,
  message: '获取成功',
  data: {
    list: [
      {
        serialNo: 1,
        projectName: '绿城花园',
        stoveFrontValveReceived: 100,
        stoveFrontValveConsumed: 98,
        stoveFrontValvePrice: 15.0,
        coatedSteelPipeReceived: 500,
        coatedSteelPipeConsumed: 485,
        coatedSteelPipePrice: 12.5,
        corrugatedPipeReceived: 300,
        corrugatedPipeConsumed: 295,
        corrugatedPipePrice: 8.0,
        protectionPlateStraightReceived: 200,
        protectionPlateStraightConsumed: 195,
        protectionPlateStraightPrice: 5.5,
        protectionPlateBendReceived: 150,
        protectionPlateBendConsumed: 148,
        protectionPlateBendPrice: 6.0,
        corrugatedHoseLongReceived: 100,
        corrugatedHoseLongConsumed: 98,
        corrugatedHoseLongPrice: 25.0,
        corrugatedHoseShortReceived: 80,
        corrugatedHoseShortConsumed: 78,
        corrugatedHoseShortPrice: 18.0,
        indoorMaterialsReceivedAmount: 18500,
        indoorMaterialsConsumedAmount: 18100
      }
    ],
    summary: {
      stoveFrontValveReceived: 100,
      stoveFrontValveConsumed: 98,
      coatedSteelPipeReceived: 500,
      coatedSteelPipeConsumed: 485,
      corrugatedPipeReceived: 300,
      corrugatedPipeConsumed: 295,
      protectionPlateStraightReceived: 200,
      protectionPlateStraightConsumed: 195,
      protectionPlateBendReceived: 150,
      protectionPlateBendConsumed: 148,
      corrugatedHoseLongReceived: 100,
      corrugatedHoseLongConsumed: 98,
      corrugatedHoseShortReceived: 80,
      corrugatedHoseShortConsumed: 78,
      indoorMaterialsReceivedAmount: 18500,
      indoorMaterialsConsumedAmount: 18100
    }
  }
})

// 超领材料清单表数据
Mock.mock('/api/loose-orders/balance/over-received-materials', 'get', {
  code: 200,
  message: '获取成功',
  data: {
    list: [
      {
        serialNo: 1,
        materialName: '镀锌钢管',
        specification: 'DN25',
        unit: '米',
        quantity: 50,
        unitPrice: 15.5,
        totalAmount: 775
      },
      {
        serialNo: 2,
        materialName: '燃气表',
        specification: 'G2.5',
        unit: '块',
        quantity: 10,
        unitPrice: 120,
        totalAmount: 1200
      }
    ],
    summary: {
      totalQuantity: 60,
      totalAmount: 1975
    }
  }
})

// 管件材料清单表数据
Mock.mock('/api/loose-orders/balance/fittings-materials', 'get', {
  code: 200,
  message: '获取成功',
  data: {
    list: [
      {
        serialNo: 1,
        materialName: '三通',
        specification: 'DN25',
        unit: '个',
        quantity: 200,
        unitPrice: 8.5,
        totalAmount: 1700
      },
      {
        serialNo: 2,
        materialName: '弯头',
        specification: 'DN25',
        unit: '个',
        quantity: 300,
        unitPrice: 6.2,
        totalAmount: 1860
      }
    ],
    summary: {
      totalQuantity: 500,
      totalAmount: 3560
    }
  }
})

// 挂表安装工程管件统计表数据
Mock.mock('/api/loose-orders/balance/meter-installation-fittings', 'get', {
  code: 200,
  message: '获取成功',
  data: {
    list: [
      {
        serialNo: 1,
        customerName: '张三',
        customerCode: 'C001',
        customerAddress: '阳光小区A栋1单元101',
        dispatchTime: '2024-01-15',
        installTime: '2024-01-20',
        fullAddress: '阳光小区A栋1单元101',
        communityName: '阳光小区',
        buildingNo: 'A栋',
        roomNo: '1单元101',
        meterType: 'G2.5',
        quantity: 1,
        unitPrice: 25,
        totalAmount: 25,
        actualMaterialCost: 25
      }
    ],
    summary: {
      totalQuantity: 1,
      totalAmount: 25,
      totalMaterialCost: 25
    }
  }
})

// 零星安装工程管件统计表数据
Mock.mock('/api/loose-orders/balance/minor-installation-fittings', 'get', {
  code: 200,
  message: '获取成功',
  data: {
    list: [
      {
        serialNo: 1,
        customerName: '李四',
        customerCode: 'C002',
        dispatchDate: '2024-01-10',
        constructionDate: '2024-01-15',
        fullAddress: '绿城花园B区2单元202',
        communityName: '绿城花园',
        buildingNo: 'B区',
        roomNo: '2单元202',
        fittingsType: '三通',
        quantity: 2,
        unitPrice: 8.5,
        totalAmount: 17,
        actualMaterialCost: 17
      }
    ],
    summary: {
      totalQuantity: 2,
      totalAmount: 17,
      totalMaterialCost: 17
    }
  }
})

// 二次安装工程管件统计表数据
Mock.mock('/api/loose-orders/balance/secondary-installation-fittings', 'get', {
  code: 200,
  message: '获取成功',
  data: {
    list: [
      {
        serialNo: 1,
        customerName: '王五',
        customerCode: 'C003',
        dispatchDate: '2024-01-05',
        constructionDate: '2024-01-12',
        communityName: '华府天地',
        buildingNo: 'C栋',
        roomNo: '3单元303',
        fittingsType: '弯头',
        quantity: 3,
        unitPrice: 6.2,
        totalAmount: 18.6,
        actualMaterialCost: 18.6
      }
    ],
    summary: {
      totalQuantity: 3,
      totalAmount: 18.6,
      totalMaterialCost: 18.6
    }
  }
})

// 户内安装工程管件统计表数据
Mock.mock('/api/loose-orders/balance/indoor-installation-fittings', 'get', {
  code: 200,
  message: '获取成功',
  data: {
    list: [
      {
        serialNo: 1,
        projectName: '阳光小区A栋',
        fittingsType: '三通',
        quantity: 60,
        unitPrice: 8.5,
        totalAmount: 510
      }
    ],
    summary: {
      totalQuantity: 60,
      totalAmount: 510
    }
  }
})

// 表安装结算半价表数据
Mock.mock('/api/loose-orders/balance/meter-settlement-half', 'get', {
  code: 200,
  message: '获取成功',
  data: {
    list: [
      {
        serialNo: 1,
        projectName: '阳光小区A栋',
        meterType: 'G2.5',
        quantity: 25,
        unitPrice: 12.5,
        totalAmount: 312.5
      }
    ],
    summary: {
      totalQuantity: 25,
      totalAmount: 312.5
    }
  }
})

// 表安装结算全价表数据
Mock.mock('/api/loose-orders/balance/meter-settlement-no-half', 'get', {
  code: 200,
  message: '获取成功',
  data: {
    list: [
      {
        serialNo: 1,
        projectName: '阳光小区A栋',
        meterType: 'G2.5',
        quantity: 25,
        unitPrice: 25,
        totalAmount: 625
      }
    ],
    summary: {
      totalQuantity: 25,
      totalAmount: 625
    }
  }
})

// 燃气零星结算半价表数据
Mock.mock('/api/loose-orders/balance/gas-minor-settlement-half', 'get', {
  code: 200,
  message: '获取成功',
  data: {
    list: [
      {
        serialNo: 1,
        projectName: '阳光小区A栋',
        fittingsType: '三通',
        quantity: 50,
        unitPrice: 4.25,
        totalAmount: 212.5
      }
    ],
    summary: {
      totalQuantity: 50,
      totalAmount: 212.5
    }
  }
})

// 燃气零星结算全价表数据
Mock.mock('/api/loose-orders/balance/gas-minor-settlement-no-half', 'get', {
  code: 200,
  message: '获取成功',
  data: {
    list: [
      {
        serialNo: 1,
        projectName: '阳光小区A栋',
        fittingsType: '三通',
        quantity: 50,
        unitPrice: 8.5,
        totalAmount: 425
      }
    ],
    summary: {
      totalQuantity: 50,
      totalAmount: 425
    }
  }
})

// 燃气零星结算无表表数据
Mock.mock('/api/loose-orders/balance/gas-minor-settlement-no-meter', 'get', {
  code: 200,
  message: '获取成功',
  data: {
    list: [
      {
        serialNo: 1,
        projectName: '阳光小区A栋',
        fittingsType: '三通',
        quantity: 30,
        unitPrice: 8.5,
        totalAmount: 255
      }
    ],
    summary: {
      totalQuantity: 30,
      totalAmount: 255
    }
  }
})

// 甲供材料收据表数据
Mock.mock('/api/loose-orders/balance/supplied-materials-receipt', 'get', {
  code: 200,
  message: '获取成功',
  data: {
    list: [
      {
        serialNo: 1,
        materialName: '镀锌钢管',
        specification: 'DN25',
        unit: '米',
        quantity: 550,
        unitPrice: 15.5,
        totalAmount: 8525
      },
      {
        serialNo: 2,
        materialName: '燃气表',
        specification: 'G2.5',
        unit: '块',
        quantity: 230,
        unitPrice: 120,
        totalAmount: 27600
      }
    ],
    summary: {
      totalQuantity: 780,
      totalAmount: 36125
    }
  }
})

// 确认平账
Mock.mock('/api/loose-orders/balance/confirm', 'post', {
  code: 200,
  message: '平账确认成功',
  data: {
    id: '@id',
    month: '2024-01',
    balanceDate: '@datetime',
    operator: '张三',
    status: 'completed'
  }
})

// 平账记录列表
Mock.mock(/\/api\/loose-orders\/balance\/records(\?.*)?$/, 'get', {
  code: 200,
  message: '获取成功',
  data: {
    records: [
      {
        id: 1,
        month: '2024-01',
        balanceDate: '2024-01-31T15:30:00',
        operator: '张三',
        status: 'completed',
        totalOrders: 88,
        totalAmount: 377000,
        materialCost: 145800,
        laborCost: 66000,
        profit: 165200,
        profitRate: 43.8
      },
      {
        id: 2,
        month: '2024-02',
        balanceDate: '2024-02-29T16:00:00',
        operator: '李四',
        status: 'completed',
        totalOrders: 92,
        totalAmount: 395000,
        materialCost: 152000,
        laborCost: 69000,
        profit: 174000,
        profitRate: 44.1
      },
      {
        id: 3,
        month: '2024-03',
        balanceDate: '2024-03-31T14:30:00',
        operator: '王五',
        status: 'pending',
        totalOrders: 85,
        totalAmount: 365000,
        materialCost: 140000,
        laborCost: 62000,
        profit: 163000,
        profitRate: 44.7
      }
    ],
    total: 3,
    page: 1,
    pageSize: 20
  }
})

// 平账详情
Mock.mock(/\/api\/loose-orders\/balance\/\d+\/detail$/, 'get', {
  code: 200,
  message: '获取成功',
  data: {
    id: 1,
    month: '2024-01',
    balanceDate: '2024-01-31T15:30:00',
    operator: '张三',
    status: 'completed',
    totalOrders: 88,
    totalAmount: 377000,
    materialCost: 145800,
    laborCost: 66000,
    profit: 165200,
    profitRate: 43.8,
    salaryData: [
      {
        workerName: '李师傅',
        workType: '电工',
        workDays: 22,
        totalSalary: 7920,
        paidAmount: 5000,
        pendingAmount: 2920
      },
      {
        workerName: '王师傅',
        workType: '水工',
        workDays: 20,
        totalSalary: 6720,
        paidAmount: 4500,
        pendingAmount: 2220
      }
    ],
    orderData: [
      {
        orderType: '水电安装',
        completedCount: 25,
        totalAmount: 125000,
        profit: 61250,
        profitRate: 49.0
      },
      {
        orderType: '电路维修',
        completedCount: 18,
        totalAmount: 72000,
        profit: 29700,
        profitRate: 41.3
      }
    ],
    materialData: [
      {
        materialType: '甲料',
        usedQuantity: 1200,
        materialCost: 85000,
        percentage: 58.3
      },
      {
        materialType: '商品',
        usedQuantity: 800,
        materialCost: 45000,
        percentage: 30.9
      }
    ]
  }
})

export default Mock 