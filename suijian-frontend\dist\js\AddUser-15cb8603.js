import{E as e}from"./element-plus-ad78a7bf.js";import{l,_ as a,y as s,R as u,J as o,av as t,x as d,z as r,Q as i,aa as n,I as m,O as _,M as p,P as c}from"./vue-vendor-fc5a6493.js";import{_ as f}from"./_plugin-vue_export-helper-1b428a4d.js";import"./utils-c1f3ec4c.js";import"./vendor-48e84fe7.js";const b={class:"add-user-container"},v={class:"permission-actions"},V={class:"form-actions"},h=f(l({__name:"AddUser",setup(l){const f=a({username:"",realName:"",password:"",confirmPassword:"",phone:"",email:"",wechat:"",role:"",permissions:[],status:"active",limitIP:!1,limitTime:!1,remarks:""}),h=[{value:"admin",label:"系统管理员"},{value:"warehouse",label:"仓库管理员"},{value:"order",label:"订单管理员"},{value:"project",label:"工程管理员"},{value:"finance",label:"财务人员"},{value:"user",label:"普通用户"},{value:"custom",label:"自定义角色"}],w=[{value:"dashboard_view",label:"首页 - 查看仪表板"},{value:"material_list",label:"仓库管理 - 物料列表"},{value:"material_apply",label:"仓库管理 - 领料申请"},{value:"material_inbound",label:"仓库管理 - 甲料入库"},{value:"material_return",label:"仓库管理 - 物料退仓"},{value:"auxiliary_purchase",label:"仓库管理 - 辅料采购"},{value:"product_inbound",label:"仓库管理 - 商品入库"},{value:"material_records",label:"仓库管理 - 进出记录"},{value:"product_outbound",label:"仓库管理 - 商品售卖出库"},{value:"material_price",label:"仓库管理 - 物料价格"},{value:"product_price",label:"仓库管理 - 商品价格"},{value:"material_base",label:"仓库管理 - 物料基础库"},{value:"stock_warning",label:"仓库管理 - 库存预警"},{value:"loose_order_list",label:"散户订单 - 订单列表"},{value:"loose_order_assign",label:"散户订单 - 订单分派"},{value:"monthly_balance",label:"散户订单 - 月度平账"},{value:"project_list",label:"工程订单 - 工程列表"},{value:"project_progress",label:"工程订单 - 工程推进"},{value:"employee_list",label:"员工管理 - 员工列表"},{value:"user_management",label:"系统设置 - 用户管理"},{value:"permission_management",label:"系统设置 - 权限管理"},{value:"role_management",label:"系统设置 - 角色管理"},{value:"system_log",label:"系统设置 - 系统日志"}],g=()=>{f.permissions=w.map(e=>e.value)},k=()=>{f.permissions=[]},y=()=>{const e=w.map(e=>e.value);f.permissions=e.filter(e=>!f.permissions.includes(e))},U=()=>{e.success("保存成功")},P=()=>{e.success("保存成功，继续添加"),f.username="",f.realName="",f.password="",f.confirmPassword="",f.remarks=""},j=()=>{e.info("已取消")};return(e,l)=>{const a=t("el-col"),x=t("el-input"),C=t("el-form-item"),I=t("el-row"),q=t("el-option"),N=t("el-select"),T=t("el-checkbox"),z=t("el-checkbox-group"),A=t("el-scrollbar"),E=t("el-button"),J=t("el-card"),L=t("el-radio"),M=t("el-radio-group");return d(),s("div",b,[u(J,{class:"main-card"},{header:o(()=>l[13]||(l[13]=[r("div",{class:"card-header"},[r("span",null,"新增用户")],-1)])),default:o(()=>[u(I,{gutter:20,class:"form-section"},{default:o(()=>[u(a,{span:24},{default:o(()=>l[14]||(l[14]=[r("div",{class:"section-title"},"用户基本信息",-1)])),_:1,__:[14]}),u(a,{span:12},{default:o(()=>[u(C,{label:"用户名:",required:""},{default:o(()=>[u(x,{modelValue:f.username,"onUpdate:modelValue":l[0]||(l[0]=e=>f.username=e),placeholder:"请输入用户名"},null,8,["modelValue"])]),_:1})]),_:1}),u(a,{span:12},{default:o(()=>[u(C,{label:"真实姓名:",required:""},{default:o(()=>[u(x,{modelValue:f.realName,"onUpdate:modelValue":l[1]||(l[1]=e=>f.realName=e),placeholder:"请输入真实姓名"},null,8,["modelValue"])]),_:1})]),_:1}),u(a,{span:12},{default:o(()=>[u(C,{label:"密码:",required:""},{default:o(()=>[u(x,{modelValue:f.password,"onUpdate:modelValue":l[2]||(l[2]=e=>f.password=e),type:"password",placeholder:"请输入密码","show-password":""},null,8,["modelValue"])]),_:1})]),_:1}),u(a,{span:12},{default:o(()=>[u(C,{label:"确认密码:",required:""},{default:o(()=>[u(x,{modelValue:f.confirmPassword,"onUpdate:modelValue":l[3]||(l[3]=e=>f.confirmPassword=e),type:"password",placeholder:"请确认密码","show-password":""},null,8,["modelValue"])]),_:1})]),_:1}),u(a,{span:12},{default:o(()=>[u(C,{label:"手机号:",required:""},{default:o(()=>[u(x,{modelValue:f.phone,"onUpdate:modelValue":l[4]||(l[4]=e=>f.phone=e),placeholder:"请输入手机号"},null,8,["modelValue"])]),_:1})]),_:1}),u(a,{span:12},{default:o(()=>[u(C,{label:"邮箱:"},{default:o(()=>[u(x,{modelValue:f.email,"onUpdate:modelValue":l[5]||(l[5]=e=>f.email=e),placeholder:"请输入邮箱"},null,8,["modelValue"])]),_:1})]),_:1}),u(a,{span:12},{default:o(()=>[u(C,{label:"微信号:"},{default:o(()=>[u(x,{modelValue:f.wechat,"onUpdate:modelValue":l[6]||(l[6]=e=>f.wechat=e),placeholder:"请输入微信号"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),u(I,{gutter:20,class:"form-section"},{default:o(()=>[u(a,{span:24},{default:o(()=>l[15]||(l[15]=[r("div",{class:"section-title"},"角色分配",-1)])),_:1,__:[15]}),u(a,{span:12},{default:o(()=>[u(C,{label:"角色:"},{default:o(()=>[u(N,{modelValue:f.role,"onUpdate:modelValue":l[7]||(l[7]=e=>f.role=e),placeholder:"请选择角色",style:{width:"100%"}},{default:o(()=>[(d(),s(i,null,n(h,e=>u(q,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),"custom"===f.role?(d(),m(I,{key:0,gutter:20,class:"form-section"},{default:o(()=>[u(a,{span:24},{default:o(()=>l[16]||(l[16]=[r("div",{class:"section-title"},"自定义角色权限",-1)])),_:1,__:[16]}),u(a,{span:24},{default:o(()=>[u(J,{class:"permission-card"},{default:o(()=>[l[20]||(l[20]=r("div",{class:"permission-header"},[r("span",null,"选择角色: 自定义角色")],-1)),u(A,{height:"400"},{default:o(()=>[u(z,{modelValue:f.permissions,"onUpdate:modelValue":l[8]||(l[8]=e=>f.permissions=e),class:"permission-list"},{default:o(()=>[(d(),s(i,null,n(w,e=>u(T,{key:e.value,label:e.value,class:"permission-item"},{default:o(()=>[_(c(e.label),1)]),_:2},1032,["label"])),64))]),_:1},8,["modelValue"])]),_:1}),r("div",v,[u(E,{onClick:g},{default:o(()=>l[17]||(l[17]=[_("全选",-1)])),_:1,__:[17]}),u(E,{onClick:k},{default:o(()=>l[18]||(l[18]=[_("全不选",-1)])),_:1,__:[18]}),u(E,{onClick:y},{default:o(()=>l[19]||(l[19]=[_("反选",-1)])),_:1,__:[19]})])]),_:1,__:[20]})]),_:1})]),_:1})):p("",!0),u(I,{gutter:20,class:"form-section"},{default:o(()=>[u(a,{span:24},{default:o(()=>l[21]||(l[21]=[r("div",{class:"section-title"},"状态设置",-1)])),_:1,__:[21]}),u(a,{span:12},{default:o(()=>[u(C,{label:"用户状态:"},{default:o(()=>[u(M,{modelValue:f.status,"onUpdate:modelValue":l[9]||(l[9]=e=>f.status=e)},{default:o(()=>[u(L,{label:"active"},{default:o(()=>l[22]||(l[22]=[_("正常",-1)])),_:1,__:[22]}),u(L,{label:"disabled"},{default:o(()=>l[23]||(l[23]=[_("禁用",-1)])),_:1,__:[23]})]),_:1},8,["modelValue"])]),_:1})]),_:1}),u(a,{span:24},{default:o(()=>[u(C,{label:"登录限制:"},{default:o(()=>[u(T,{modelValue:f.limitIP,"onUpdate:modelValue":l[10]||(l[10]=e=>f.limitIP=e)},{default:o(()=>l[24]||(l[24]=[_("限制登录IP范围",-1)])),_:1,__:[24]},8,["modelValue"]),u(T,{modelValue:f.limitTime,"onUpdate:modelValue":l[11]||(l[11]=e=>f.limitTime=e)},{default:o(()=>l[25]||(l[25]=[_("限制登录时间范围",-1)])),_:1,__:[25]},8,["modelValue"])]),_:1})]),_:1})]),_:1}),u(I,{gutter:20,class:"form-section"},{default:o(()=>[u(a,{span:24},{default:o(()=>l[26]||(l[26]=[r("div",{class:"section-title"},"备注信息",-1)])),_:1,__:[26]}),u(a,{span:24},{default:o(()=>[u(C,{label:"备注:"},{default:o(()=>[u(x,{modelValue:f.remarks,"onUpdate:modelValue":l[12]||(l[12]=e=>f.remarks=e),type:"textarea",rows:3,placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),u(I,{gutter:20,class:"form-section"},{default:o(()=>[u(a,{span:24},{default:o(()=>[u(J,{class:"info-card"},{default:o(()=>l[27]||(l[27]=[r("div",{class:"info-title"},"输入验证提示:",-1),r("div",{class:"info-content"},[r("p",null,"- 用户名: 4-20个字符，只能包含字母、数字、下划线"),r("p",null,"- 密码: 6-20个字符，需包含字母和数字"),r("p",null,"- 手机号: 11位数字，符合手机号格式"),r("p",null,"- 邮箱: 需符合邮箱格式")],-1)])),_:1,__:[27]})]),_:1})]),_:1}),r("div",V,[u(E,{type:"primary",onClick:U},{default:o(()=>l[28]||(l[28]=[_("保存",-1)])),_:1,__:[28]}),u(E,{type:"success",onClick:P},{default:o(()=>l[29]||(l[29]=[_("保存并继续添加",-1)])),_:1,__:[29]}),u(E,{onClick:j},{default:o(()=>l[30]||(l[30]=[_("取消",-1)])),_:1,__:[30]})])]),_:1})])}}}),[["__scopeId","data-v-9158cd84"]]);export{h as default};
